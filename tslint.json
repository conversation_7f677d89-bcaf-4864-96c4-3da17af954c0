{"rulesDirectory": ["node_modules/codelyzer"], "rules": {"callable-types": true, "class-name": true, "comment-format": [true, "check-space"], "component-class-suffix": true, "component-selector": [true, "element", "app", "kebab-case"], "curly": true, "directive-class-suffix": true, "directive-selector": [true, "attribute", "app", "camelCase"], "eofline": true, "forin": true, "import-blacklist": [true], "import-spacing": true, "indent": [true, "spaces"], "interface-over-type-literal": true, "label-position": true, "max-line-length": [true, {"limit": 140, "ignore-pattern": "^import |^export {(.*?)}"}], "member-access": false, "no-arg": true, "no-bitwise": false, "no-console": [true, "debug", "info", "time", "timeEnd", "trace"], "no-construct": true, "no-debugger": true, "no-duplicate-variable": [true, "check-parameters"], "no-empty": false, "no-empty-interface": true, "no-eval": true, "no-inferrable-types": [true, "ignore-params"], "no-input-rename": true, "no-output-rename": true, "no-shadowed-variable": true, "no-string-literal": false, "no-string-throw": true, "no-switch-case-fall-through": true, "no-trailing-whitespace": true, "no-unused-expression": true, "no-use-before-declare": true, "no-var-keyword": true, "object-literal-sort-keys": false, "one-line": [true, "check-open-brace", "check-catch", "check-else", "check-whitespace"], "prefer-const": true, "quotemark": [true, "single"], "radix": true, "semicolon": [true, "always", "ignore-bound-class-methods"], "triple-equals": [true, "allow-null-check"], "typedef-whitespace": [true, {"call-signature": "nospace", "index-signature": "nospace", "parameter": "nospace", "property-declaration": "nospace", "variable-declaration": "nospace"}], "unified-signatures": true, "use-host-property-decorator": true, "use-input-property-decorator": true, "use-life-cycle-interface": true, "use-pipe-transform-interface": true, "use-output-property-decorator": true, "variable-name": false, "whitespace": [true, "check-branch", "check-decl", "check-operator", "check-separator", "check-type"]}}