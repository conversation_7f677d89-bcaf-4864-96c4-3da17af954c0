{"swagger": "2.0", "info": {"title": "Profile API", "contact": {"email": "<EMAIL>"}, "license": {"name": "Persgroep Online Services", "url": "http://www.persgroep.nl"}, "version": "1.0.0"}, "host": "localhost:9400", "schemes": ["http"], "consumes": ["application/json", "application/x-json"], "produces": ["application/json"], "paths": {"/api/job-seeker/city/{zipCode}": {"get": {"summary": "Get the city name that matches given zip code", "operationId": "getCityNameFromZipCode", "parameters": [{"name": "zipCode", "in": "path", "description": "Zip code to search on", "required": true, "type": "string"}], "responses": {"200": {"description": "The requested city name", "schema": {"$ref": "#/definitions/CityResponse"}}, "404": {"description": "The requested city name not found"}}}}, "/api/job-seeker/consent/{consentString}": {"get": {"tags": ["consent"], "summary": "Get consent question by string", "operationId": "getQuestionAction", "parameters": [{"name": "consentString", "in": "path", "description": "The consent string name", "required": true, "type": "string"}], "responses": {"200": {"description": "The requested consent", "schema": {"$ref": "#/definitions/ConsentResponse"}}, "404": {"description": "Question not found"}}}}, "/api/job-seeker/{jobSeekerId}/job/{jobId}": {"post": {"tags": ["jobs"], "summary": "Applies for a job for a jobseeker", "operationId": "<PERSON><PERSON><PERSON>", "parameters": [{"name": "jobSeekerId", "in": "path", "description": "Jobseeker id", "required": true, "type": "string"}, {"name": "jobId", "in": "path", "description": "Job id", "required": true, "type": "string"}, {"name": "applyData", "in": "body", "description": "Apply data", "required": true, "schema": {"$ref": "#/definitions/JobApplicationBody"}}], "responses": {"204": {"description": "Applying succeeded"}, "404": {"description": "Applying failed - job not found"}, "500": {"description": "Applying failed - server error"}}}}, "/api/job-seeker/job/{jobId}": {"get": {"tags": ["jobs"], "summary": "Get job details", "operationId": "get<PERSON>ob", "parameters": [{"name": "jobId", "in": "path", "description": "Job id", "required": true, "type": "string"}], "responses": {"200": {"description": "The requested job", "schema": {"$ref": "#/definitions/JobResponse"}}, "404": {"description": "Job not found"}}}}, "/api/job-seeker/": {"get": {"tags": ["jobseeker"], "summary": "Show a JobSeeker account", "operationId": "showJobSeeker", "responses": {"200": {"description": "The requested JobSeeker account", "schema": {"$ref": "#/definitions/JobSeeker"}}, "404": {"description": "JobSeeker not found"}}}, "post": {"tags": ["jobseeker"], "summary": "Create a JobSeeker account", "operationId": "createJobSeeker", "parameters": [{"name": "jobseeker", "in": "body", "description": "The basic JobSeeker to create", "required": true, "schema": {"properties": {"emailAddress": {"type": "string"}, "phoneNumber": {"type": "string"}}}}, {"name": "X-Site", "in": "header", "description": "Site header", "required": true, "type": "string", "enum": ["nationalevacaturebank.nl", "intermediair.nl"]}], "responses": {"201": {"description": "Successfully created the JobSeeker account", "schema": {"$ref": "#/definitions/JobSeekerId"}}, "409": {"description": "Duplicate JobSeeker account"}}}}, "/api/job-seeker/set-password/{token}": {"put": {"summary": "Set the initial password for a JobSeeker account", "operationId": "setPasswordJobSeeker", "parameters": [{"name": "token", "in": "path", "description": "The token", "required": true, "type": "string"}, {"name": "password", "in": "body", "description": "The password", "required": true, "schema": {"properties": {"password": {"type": "string"}}}}], "responses": {"204": {"description": "Successfully verified the JobSeeker account", "schema": {"$ref": "#/definitions/JobSeeker"}}, "404": {"description": "Token not found"}}}}, "/api/job-seeker/{jobSeekerId}/start-migration": {"put": {"summary": "migration job seeker", "operationId": "migrationJobSeeker", "parameters": [{"name": "jobSeekerId", "in": "path", "description": "The id of the JobSeeker to look up", "required": true, "type": "string"}, {"name": "jobSeeker", "in": "body", "description": "The data to migrate", "required": true, "schema": {"properties": {"experience": {"properties": {"experienceId": {"type": "string"}, "jobTitle": {"type": "string"}, "companyName": {"type": "string"}, "fromDate": {"type": "string"}, "toDate": {"type": "string"}, "description": {"type": "string"}, "city": {"type": "string"}}, "type": "object"}, "education": {"properties": {"educationId": {"type": "string"}, "school": {"type": "string"}, "fromDate": {"type": "string"}, "toDate": {"type": "string"}, "fieldOfStudy": {"type": "string"}, "diploma": {"type": "boolean"}, "grade": {"type": "string"}, "description": {"type": "string"}}, "type": "object"}}}}], "responses": {"204": {"description": "Successfully migrated experience and education for job seeker"}}}}, "/api/job-seeker/{jobSeekerId}/attachment": {"put": {"tags": ["jobseeker"], "summary": "Upload a jobSeeker attachment", "operationId": "uploadAttachment", "parameters": [{"name": "attachment", "in": "body", "description": "The attachment to upload", "required": true, "schema": {"properties": {"filename": {"type": "string"}, "attachment": {"type": "string"}}}}, {"name": "jobSeekerId", "in": "path", "description": "The id of the JobSeeker to look up", "required": true, "type": "string"}], "responses": {"204": {"description": "Atta<PERSON><PERSON> successfully uploaded"}, "400": {"description": "Bad Request"}, "413": {"description": "Attachment size is greater than 6Mb"}, "415": {"description": "Attachment media type is not supported"}}}}, "/api/job-seeker/{jobSeekerId}/attachment/{filename}": {"delete": {"tags": ["jobseeker"], "summary": "Upload a jobSeeker attachment", "operationId": "deleteAttachment", "parameters": [{"name": "filename", "in": "path", "description": "The filename", "required": true, "type": "string"}, {"name": "jobSeekerId", "in": "path", "description": "The id of the JobSeeker to look up", "required": true, "type": "string"}], "responses": {"204": {"description": "Successfully deleted the attachment"}, "400": {"description": "Invalid request"}}}}, "/api/job-seeker/download": {"get": {"tags": ["jobseeker"], "summary": "Downloads the JobSeeker profile in pdf format", "operationId": "downloadProfileJobSeeker", "responses": {"200": {"description": "The binary JobSeeker profile in pdf format"}, "404": {"description": "JobSeeker not found"}}}}, "/api/job-seeker/experience/{experienceId}": {"put": {"tags": ["jobseeker"], "summary": "Approve job seeker experience", "operationId": "approveExperience", "parameters": [{"name": "experienceId", "in": "path", "description": "The experience id", "required": true, "type": "string"}, {"name": "experience", "in": "body", "description": "The experience to be approved", "required": true, "schema": {"$ref": "#/definitions/JobSeekerExperience"}}], "responses": {"204": {"description": "Experience approved"}, "404": {"description": "Jobseeker or experience not found"}}}}, "/api/job-seeker/education/{educationId}": {"put": {"tags": ["jobseeker"], "summary": "Approve job seeker education", "operationId": "approveEducation", "parameters": [{"name": "educationId", "in": "path", "description": "The education id", "required": true, "type": "string"}, {"name": "education", "in": "body", "description": "The education to be approved", "required": true, "schema": {"$ref": "#/definitions/JobSeekerEducation"}}], "responses": {"204": {"description": "Education approved"}, "404": {"description": "Jobseeker or education not found"}}}}, "/api/job-seeker/change-visibility-by-privacy-wizard/": {"put": {"tags": ["jobseeker"], "summary": "Change the job seeker visibility to false by the privacy wizard", "operationId": "changeVisibilityByPrivacyWizard", "responses": {"204": {"description": "Visibility changed"}, "404": {"description": "Jobseeker not found"}}}}, "/api/job-seeker/change-visibility/": {"put": {"tags": ["jobseeker"], "summary": "Change the job seeker visibility", "operationId": "changeVisibility", "parameters": [{"name": "changeVisibility", "in": "body", "description": "Visibility with consent", "required": true, "schema": {"$ref": "#/definitions/ChangeVisibility"}}], "responses": {"204": {"description": "Visibility changed"}, "404": {"description": "Jobseeker not found"}}}}, "/api/job-seeker/create-on-apply": {"post": {"tags": ["jobseeker"], "summary": "Create a JobSeeker account via apply flow", "operationId": "createJobSeekerForApply", "parameters": [{"name": "jobseeker", "in": "body", "description": "The basic JobSeeker to create", "required": true, "schema": {"properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "emailAddress": {"type": "string"}, "phone": {"type": "string"}, "zipCode": {"type": "string"}}}}, {"name": "X-Site", "in": "header", "description": "Site header", "required": true, "type": "string", "enum": ["nationalevacaturebank.nl", "intermediair.nl"]}], "responses": {"201": {"description": "Successfully created the JobSeeker account via apply flow", "schema": {"$ref": "#/definitions/Id"}}, "409": {"description": "Duplicate JobSeeker account"}}}}, "/api/job-seeker/file-cv": {"post": {"tags": ["jobseeker"], "summary": "Download file cv of logged in job seeker", "operationId": "downloadFileCv", "parameters": [{"name": "jobseeker", "in": "body", "description": "The basic JobSeeker to create", "required": true, "schema": {"properties": {"emailAddress": {"type": "string"}, "phoneNumber": {"type": "string"}}}}, {"name": "X-Site", "in": "header", "description": "Site header", "required": true, "type": "string", "enum": ["nationalevacaturebank.nl", "intermediair.nl"]}], "responses": {"200": {"description": "Successful download", "schema": {"$ref": "#/definitions/JobSeekerId"}}, "404": {"description": "Cv file could not be found"}}}}, "/api/job-seeker/{jobSeekerId}/photo": {"put": {"tags": ["jobseeker"], "summary": "Upload a jobSeeker profile picture", "operationId": "uploadPhoto", "parameters": [{"name": "photo", "in": "body", "description": "The photo to upload", "required": true, "schema": {"properties": {"photo": {"type": "string"}}}}, {"name": "jobSeekerId", "in": "path", "description": "The id of the JobSeeker to look up", "required": true, "type": "string"}], "responses": {"204": {"description": "Successfully uploaded the picture"}, "400": {"description": "Invalid request"}}}}, "/api/job-seeker/{jobSeekerId}": {"put": {"tags": ["jobseeker"], "summary": "Update a JobSeeker", "operationId": "updateJobSeeker", "parameters": [{"name": "jobseeker", "in": "body", "description": "The JobSeeker to update", "required": true, "schema": {"properties": {"emailAddress": {"type": "string"}, "phoneNumber": {"type": "string"}}}}, {"name": "jobSeekerId", "in": "path", "description": "The id of the JobSeeker to look up", "required": true, "type": "string"}], "responses": {"204": {"description": "Successfully updated the JobSeeker account"}, "400": {"description": "JobSeeker not found"}}}}, "/api/job-seeker/{jobSeekerId}/optin": {"put": {"summary": "Opt-in to all emails", "operationId": "optinJobSeeker", "parameters": [{"name": "jobSeekerId", "in": "path", "description": "The id of the JobSeeker to look up", "required": true, "type": "string"}], "responses": {"204": {"description": "Successfully opted in the JobSeeker account", "schema": {"$ref": "#/definitions/JobSeeker"}}, "404": {"description": "Token not found"}}}}, "/api/job-seeker/email-settings": {"get": {"summary": "Get email settings for job seeker", "operationId": "emailSettingsAction", "responses": {"200": {"description": "The requested city name", "schema": {"$ref": "#/definitions/SimSettingsResponse"}}, "404": {"description": "The email settings could not be found"}}}}, "/api/status/readiness": {"get": {"summary": "Get readiness report", "operationId": "showReadiness", "responses": {"200": {"description": "Status OK"}}}}, "/api/recruiter/autocomplete/cities": {"get": {"tags": ["autocomplete"], "summary": "Get cities that matches given string", "operationId": "getAutocompleteCities", "parameters": [{"name": "city", "in": "query", "description": "Value to base autocomplete search on", "required": false, "type": "string"}], "responses": {"200": {"description": "The requested search account", "schema": {"$ref": "#/definitions/AutoCompleteCityResponse"}}}}}, "/api/recruiter/autocomplete/job-titles": {"get": {"tags": ["autocomplete"], "summary": "Show the search result", "operationId": "getAutocompleteJobTitles", "parameters": [{"name": "jobTitle", "in": "query", "description": "Value to base autocomplete search on", "required": false, "type": "string"}], "responses": {"200": {"description": "The requested search account", "schema": {"$ref": "#/definitions/AutoCompleteResponse"}}}}}, "/api/recruiter/autocomplete/skills": {"get": {"summary": "Show the search result", "operationId": "getAutocompleteSkills", "parameters": [{"name": "skill", "in": "query", "description": "Value to base autocomplete search on", "required": false, "type": "string"}], "responses": {"200": {"description": "The requested search account", "schema": {"$ref": "#/definitions/AutoCompleteResponse"}}}}}, "/api/recruiter/favorites/list": {"get": {"tags": ["favorites"], "summary": "List the favorites with pagination for a given recruiter", "operationId": "listWithPaginationAction", "parameters": [{"name": "page", "in": "query", "description": "Page for pagination", "required": false, "type": "integer"}, {"name": "limit", "in": "query", "description": "Limit number of items per page", "required": false, "type": "integer"}], "responses": {"200": {"description": "The requested favorites", "schema": {"$ref": "#/definitions/Favorites"}}, "400": {"description": "Bad request"}}}}, "/api/recruiter/favorites/{jobSeekerId}": {"post": {"tags": ["favorites"], "summary": "Save a Favorite", "operationId": "saveJobseekerAsFavorite", "parameters": [{"name": "jobSeekerId", "in": "path", "description": "The favorite to save", "required": true, "type": "string"}], "responses": {"201": {"description": "Successfully saved a favorite"}, "404": {"description": "Account not found"}, "400": {"description": "Bad request"}}}, "delete": {"tags": ["favorites"], "summary": "Delete the given saved favorite", "operationId": "deleteFavorite", "parameters": [{"name": "jobSeekerId", "in": "path", "description": "The id of the saved favorite (jobSeekerId) to delete", "required": true, "type": "string"}], "responses": {"204": {"description": "The saved favorite is deleted"}, "400": {"description": "Bad request"}}}}, "/api/recruiter/job-seeker/{jobSeekerId}": {"get": {"tags": ["recruiter"], "summary": "Show a JobSeeker account", "operationId": "showJobSeekerDetails", "parameters": [{"name": "jobSeekerId", "in": "path", "description": "The id of the JobSeeker to look up", "required": true, "type": "string"}], "responses": {"200": {"description": "The requested JobSeeker account", "schema": {"$ref": "#/definitions/JobSeeker"}}, "403": {"description": "Access denied (unverified_company|invalid_subscription|daily_limit_exceeded)", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "The requested JobSeeker not found", "schema": {"$ref": "#/definitions/Error"}}}}}, "/api/recruiter/job-seeker/{jobSeekerId}/cv": {"get": {"tags": ["recruiter"], "summary": "Get a JobSeeker html cv", "operationId": "showJobSeekerGetCv", "parameters": [{"name": "jobSeekerId", "in": "path", "description": "The id of the JobSeeker to look up", "required": true, "type": "string"}], "responses": {"200": {"description": "The requested JobSeeker html cv", "schema": {"properties": {"fileCvHtml": {"type": "string"}}}}, "403": {"description": "Access denied (unverified_company|invalid_subscription|daily_limit_exceeded)", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "The requested JobSeeker not found", "schema": {"$ref": "#/definitions/Error"}}}}}, "/api/recruiter/job-seeker/limited/{jobSeekerId}": {"get": {"tags": ["jobseeker"], "summary": "Show a limited JobSeeker", "operationId": "limitedJobSeekerAction", "parameters": [{"name": "jobSeekerId", "in": "path", "description": "The ID of job seeker", "required": true, "type": "string"}], "responses": {"200": {"description": "The requested limited JobSeeker", "schema": {"$ref": "#/definitions/JobSeekerLimited"}}, "404": {"description": "JobSeeker not found"}, "500": {"description": "JobSeeker fetch fail - server error"}}}}, "/dashboard/api/recruiter/job-seeker/{jobSeekerId}/share": {"post": {"tags": ["recruiter"], "summary": "Share job seeker profile", "operationId": "shareJobSeekerProfile", "parameters": [{"name": "jobSeekerId", "in": "path", "description": "The ID of job seeker to share", "required": true, "type": "string"}, {"name": "body", "in": "body", "description": "The shared email data", "required": true, "schema": {"properties": {"jobSeekerProfileUrl": {"type": "string"}, "subject": {"type": "string"}, "recipientEmailAddress": {"type": "string"}, "motivation": {"type": "string"}, "isCvAttached": {"type": "boolean"}}}}], "responses": {"204": {"description": "Request to share profile was successfully processed."}, "400": {"description": "Request parameters are invalid or not present."}, "500": {"description": "Internal server error."}}}}, "/api/recruiter/me": {"get": {"tags": ["recruiter"], "summary": "Get recruiter details", "description": "Get recruiters details if possible (authenticated)", "operationId": "get<PERSON><PERSON><PERSON><PERSON>", "responses": {"200": {"description": "Recruiter info", "schema": {"$ref": "#/definitions/Recruiter"}}, "404": {"description": "Rec<PERSON>er", "schema": {"$ref": "#/definitions/Error"}}}}}, "/dashboard/api/products?displayOptions=4": {"get": {"tags": ["recruiter"], "summary": "Get suggestible profile packages for recruiter", "description": "Get suggestible profile packages for recruiter", "operationId": "getSuggestiblePackages", "responses": {"200": {"description": "Suggestible packages", "schema": {"type": "object", "items": {"type": "object"}}}, "404": {"description": "Rec<PERSON>er", "schema": {"$ref": "#/definitions/Error"}}}}}, "/api/recruiter/saved-searches": {"get": {"tags": ["saved-search"], "summary": "List the saved search for a given recruiter", "operationId": "listAction", "responses": {"200": {"description": "The requested saved search", "schema": {"$ref": "#/definitions/SavedSearches"}}, "400": {"description": "Bad request"}}}, "post": {"tags": ["saved-search"], "summary": "Save a Search", "operationId": "saveSearch", "parameters": [{"name": "savedSearch", "in": "body", "description": "The search to save", "required": true, "schema": {"properties": {"accountId": {"type": "string"}, "name": {"type": "string"}, "frequency": {"type": "string"}, "searchParameters": {"properties": {"term": {"properties": {"all": {"type": "array", "items": {"type": "string"}}, "educationName": {"type": "array", "items": {"type": "string"}}, "educationDescription": {"type": "array", "items": {"type": "string"}}, "preferredJob": {"type": "array", "items": {"type": "string"}}, "functionTitle": {"type": "array", "items": {"type": "string"}}, "functionDescription": {"type": "array", "items": {"type": "string"}}, "courses": {"type": "array", "items": {"type": "string"}}}, "type": "object"}, "location": {"type": "string"}, "radius": {"type": "string"}, "updatedDate": {"type": "string"}, "workLevels": {"type": "array", "items": {"type": "string"}}, "workingHours": {"type": "array", "items": {"type": "string"}}, "availability": {"type": "array", "items": {"type": "string"}}, "driverLicenses": {"type": "array", "items": {"type": "string"}}, "languages": {"type": "array", "items": {"type": "string"}}, "careerLevel": {"type": "array", "items": {"type": "string"}}, "functionGroups": {"type": "array", "items": {"type": "string"}}, "requestedSalary": {"type": "array", "items": {"type": "string"}}}, "type": "object"}}}}], "responses": {"201": {"description": "Successfully saved a search"}, "404": {"description": "Account not found"}, "400": {"description": "Bad request"}}}}, "/api/recruiter/saved-searches/{recruiterId}/{savedSearchId}": {"get": {"tags": ["saved-search"], "summary": "Get the saved search for a given recruiter by savedSearchId", "operationId": "savedAction", "parameters": [{"name": "recruiterId", "in": "path", "description": "The recruiter id", "required": true, "type": "string"}, {"name": "savedSearchId", "in": "path", "description": "The saved search id", "required": true, "type": "string"}], "responses": {"200": {"description": "The requested saved search", "schema": {"$ref": "#/definitions/SavedSearches"}}, "400": {"description": "Bad request"}}}}, "/api/recruiter/saved-searches/{savedSearchId}": {"put": {"tags": ["saved-search"], "summary": "Update a Search", "operationId": "updateSearch", "parameters": [{"name": "savedSearchId", "in": "path", "description": "The id of the saved search to delete", "required": true, "type": "string"}, {"name": "savedSearch", "in": "body", "description": "The search to save", "required": true, "schema": {"properties": {"accountId": {"type": "string"}, "name": {"type": "string"}, "frequency": {"type": "string"}, "searchParameters": {"properties": {"term": {"properties": {"all": {"type": "array", "items": {"type": "string"}}, "educationName": {"type": "array", "items": {"type": "string"}}, "educationDescription": {"type": "array", "items": {"type": "string"}}, "preferredJob": {"type": "array", "items": {"type": "string"}}, "functionTitle": {"type": "array", "items": {"type": "string"}}, "functionDescription": {"type": "array", "items": {"type": "string"}}, "courses": {"type": "array", "items": {"type": "string"}}}, "type": "object"}, "location": {"type": "string"}, "radius": {"type": "string"}, "updatedDate": {"type": "string"}, "workLevels": {"type": "array", "items": {"type": "string"}}, "workingHours": {"type": "array", "items": {"type": "string"}}, "availability": {"type": "array", "items": {"type": "string"}}, "driverLicenses": {"type": "array", "items": {"type": "string"}}, "languages": {"type": "array", "items": {"type": "string"}}, "careerLevel": {"type": "array", "items": {"type": "string"}}, "functionGroups": {"type": "array", "items": {"type": "string"}}, "requestedSalary": {"type": "array", "items": {"type": "string"}}}, "type": "object"}}}}], "responses": {"204": {"description": "Successfully saved a search"}, "404": {"description": "Account not found"}, "400": {"description": "Bad request"}}}, "delete": {"tags": ["saved-search"], "summary": "Delete the given saved search", "operationId": "deleteSavedSearches", "parameters": [{"name": "savedSearchId", "in": "path", "description": "The id of the saved search to delete", "required": true, "type": "string"}], "responses": {"204": {"description": "The saved search is deleted"}, "400": {"description": "Bad request"}}}}, "/api/recruiter/search": {"get": {"tags": ["search"], "summary": "Show the search result", "operationId": "showSearch", "parameters": [{"name": "query[all][]", "in": "query", "description": "The search string of the search page for recruiter to search in all fields", "required": false, "type": "string", "default": "php"}, {"name": "query[educationTitle][]", "in": "query", "description": "The search string of the search page for recruiter to search in all education titles", "required": false, "type": "string", "default": "php"}, {"name": "query[educationDescription][]", "in": "query", "description": "The search string of the search page for recruiter to search in all education descriptions", "required": false, "type": "string", "default": "php"}, {"name": "query[preferredJobTitle][]", "in": "query", "description": "The search string of the search page for recruiter to search in all preferred job titles", "required": false, "type": "string", "default": "php"}, {"name": "query[allJobDescriptions][]", "in": "query", "description": "The search string of the search page for recruiter to search in all job descriptions", "required": false, "type": "string", "default": "php"}, {"name": "query[courses][]", "in": "query", "description": "The search string of the search page for recruiter to search in all courses", "required": false, "type": "string", "default": "php"}, {"name": "filters[updatedDate][]", "in": "query", "description": "The search string of the search page for recruiter", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "filters[workLevels][]", "in": "query", "description": "The search string of the search page for recruiter", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "filters[workingHours][]", "in": "query", "description": "The search string of the search page for recruiter", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "filters[availability][]", "in": "query", "description": "The search string of the search page for recruiter", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "filters[driverLicenses][]", "in": "query", "description": "The search string of the search page for recruiter", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "filters[languages][]", "in": "query", "description": "The search string of the search page for recruiter", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "filters[skills][]", "in": "query", "description": "The search string of the search page for recruiter", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "location", "in": "query", "description": "City name", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "Page for pagination", "required": false, "type": "integer"}, {"name": "limit", "in": "query", "description": "Limit number of items per page", "required": false, "type": "integer"}, {"name": "sort", "in": "query", "description": "Sort by relevance or update date", "required": false, "type": "string", "enum": ["relevance", "update"]}], "responses": {"200": {"description": "search results", "schema": {"$ref": "#/definitions/SearchResult"}}}}}, "/api/recruiter/search/filters": {"get": {"tags": ["search"], "summary": "Show the filters and aggregations for the search result", "operationId": "showFilters", "parameters": [{"name": "query[all][]", "in": "query", "description": "The search string of the search page for recruiter to search in all fields", "required": false, "type": "string", "default": "php"}, {"name": "query[educationTitle][]", "in": "query", "description": "The search string of the search page for recruiter to search in all education titles", "required": false, "type": "string", "default": "php"}, {"name": "query[educationDescription][]", "in": "query", "description": "The search string of the search page for recruiter to search in all education descriptions", "required": false, "type": "string", "default": "php"}, {"name": "query[preferredJobTitle][]", "in": "query", "description": "The search string of the search page for recruiter to search in all preferred job titles", "required": false, "type": "string", "default": "php"}, {"name": "query[allJobDescriptions][]", "in": "query", "description": "The search string of the search page for recruiter to search in all job descriptions", "required": false, "type": "string", "default": "php"}, {"name": "query[courses][]", "in": "query", "description": "The search string of the search page for recruiter to search in all courses", "required": false, "type": "string", "default": "php"}, {"name": "filters[updatedDate][]", "in": "query", "description": "The search string of the search page for recruiter", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "filters[workLevels][]", "in": "query", "description": "The search string of the search page for recruiter", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "filters[workingHours][]", "in": "query", "description": "The search string of the search page for recruiter", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "filters[availability][]", "in": "query", "description": "The search string of the search page for recruiter", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "filters[driverLicenses][]", "in": "query", "description": "The search string of the search page for recruiter", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "filters[languages][]", "in": "query", "description": "The search string of the search page for recruiter", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "filters[skills][]", "in": "query", "description": "The search string of the search page for recruiter", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "location", "in": "query", "description": "City name", "required": false, "type": "string"}], "responses": {"200": {"description": "The requested search filters", "schema": {"$ref": "#/definitions/FiltersResult"}}}}}, "/api/recruiter/url-parser/search": {"get": {"tags": ["url-parser"], "summary": "Get parsed data from the legacy search URL", "operationId": "parseSearchUrl", "parameters": [{"name": "url", "in": "query", "description": "Legacy search URL to parse", "required": true, "type": "string"}], "responses": {"200": {"description": "Parsed legacy search URL", "schema": {"$ref": "#/definitions/LegacySearchUrlData"}}}}}, "/api/recruiter/url-parser/job-seeker-details": {"get": {"tags": ["url-parser"], "summary": "Retrieve legacy job seeker ID from the job seeker details URL and translate it into job seeker ID", "operationId": "parseJobSeekerDetailsUrl", "parameters": [{"name": "url", "in": "query", "description": "Job seeker details URL to parse", "required": true, "type": "string"}], "responses": {"200": {"description": "Parsed job seeker details URL", "schema": {"$ref": "#/definitions/LegacyJobSeekerDetailsUrlData"}}}}}}, "definitions": {"CityResponse": {"properties": {"cityName": {"type": "string"}}}, "ConsentResponse": {"properties": {"question": {"type": "string"}, "consentId": {"type": "string"}}}, "Id": {"required": ["id"], "properties": {"id": {"type": "string"}}, "type": "object"}, "JobResponse": {"properties": {"title": {"type": "string"}, "contactName": {"type": "string"}, "id": {"type": "string"}}}, "JobSeekerId": {"required": ["jobSeekerId"], "properties": {"jobSeekerId": {"type": "string"}}, "type": "object"}, "SimSettingsResponse": {"properties": {"simSettings": {"type": "array", "items": {"type": "string"}}}}, "Certification": {"properties": {"institution": {"type": "string"}, "name": {"type": "string"}, "start_date": {"type": "string"}, "expiry_date": {"type": "string"}, "description": {"type": "string"}}}, "ChangeVisibility": {"required": ["visible"], "properties": {"visible": {"type": "boolean"}, "givenConsents": {"type": "array", "items": {"type": "string"}}}, "type": "object"}, "Consents": {"properties": {"givenConsents": {"type": "array", "items": {"type": "string"}}}, "type": "object"}, "JobApplicationBody": {"required": ["motivation"], "properties": {"motivation": {"type": "string"}, "givenConsents": {"type": "array", "items": {"type": "string"}}, "cv": {"$ref": "#/definitions/UploadedCvFile"}, "attachmentFileName": {"type": "string"}}, "type": "object"}, "UploadedCvFile": {"required": ["mimeType", "fileName", "content"], "properties": {"mimeType": {"type": "string"}, "fileName": {"type": "string"}, "content": {"type": "string"}}, "type": "object"}, "JobSeekerEducation": {"required": ["educationId", "school", "city", "fromDate", "toDate", "fieldOfStudy", "diploma", "grade", "description"], "properties": {"educationId": {"type": "string", "example": "b2db7df4-1628-11e7-90a6-308cdf06bc20"}, "school": {"type": "string", "example": "CCV"}, "city": {"type": "string", "example": "Amsterdam"}, "fromDate": {"type": "string", "example": "2013-01-01"}, "toDate": {"type": "string", "example": "2015-01-01"}, "fieldOfStudy": {"type": "string", "example": "Transport"}, "diploma": {"type": "boolean", "example": "true"}, "grade": {"type": "string", "example": "HBO"}, "description": {"type": "string", "example": "This is a description"}}, "type": "object"}, "JobSeekerExperience": {"required": ["experienceId", "jobTitle", "companyName", "fromDate", "toDate", "description"], "properties": {"experienceId": {"type": "string", "example": "b2db7df4-1628-11e7-90a6-308cdf06bc17"}, "jobTitle": {"type": "string", "example": "PHP Developer"}, "companyName": {"type": "string", "example": "Persgroep"}, "fromDate": {"type": "string", "example": "2015-01-01"}, "toDate": {"type": "string", "example": "2017-01-01"}, "description": {"type": "string", "example": "This is a description"}}, "type": "object"}, "User": {"required": ["id"], "properties": {"id": {"type": "string"}, "email": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}}, "type": "object"}, "Favorite": {"properties": {"jobSeekerId": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "jobTitle": {"type": "string"}, "photo": {"type": "string"}}, "type": "object"}, "Favorites": {"properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/Favorites"}}, "total": {"type": "number"}}, "type": "object"}, "LegacyJobSeekerDetailsUrlData": {"properties": {"id": {"type": "string"}}}, "LegacySearchUrlData": {"properties": {"sort": {"type": "string"}, "currentPage": {"type": "integer"}, "pageSize": {"type": "integer"}, "search": {"type": "string"}, "locationQuery": {"type": "string"}, "filters": {"properties": {"radius": {"type": "array", "items": {"type": "string"}}, "updatedDate": {"type": "array", "items": {"type": "string"}}, "workLevels": {"type": "array", "items": {"type": "string"}}, "workingHours": {"type": "array", "items": {"type": "string"}}, "availability": {"type": "array", "items": {"type": "string"}}, "driverLicenses": {"type": "array", "items": {"type": "string"}}, "languages": {"type": "array", "items": {"type": "string"}}}, "type": "object"}}}, "Recruiter": {"properties": {"emailAddress": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "hasDeductibleSubscription": {"type": "boolean"}, "amountOfCredits": {"type": "integer"}, "hasValidProduct": {"type": "boolean"}, "hasSubscription": {"type": "boolean"}, "companyVerified": {"type": "boolean"}}}, "SavedSearch": {"properties": {"accountId": {"type": "string"}, "savedSearchId": {"type": "string"}, "name": {"type": "string"}, "frequency": {"type": "string"}, "searchParameters": {"type": "object", "items": {"$ref": "#/definitions/SearchParametersResource"}}}, "type": "object"}, "SavedSearches": {"properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/SavedSearch"}}}, "type": "object"}, "SearchParametersResource": {"properties": {"term": {"type": "object", "items": {"$ref": "#/definitions/SearchTermsResource"}}, "location": {"type": "string"}, "radius": {"type": "array", "items": {"type": "string"}}, "updatedDate": {"type": "array", "items": {"type": "string"}}, "workLevels": {"type": "array", "items": {"type": "string"}}, "workingHours": {"type": "array", "items": {"type": "string"}}, "availability": {"type": "array", "items": {"type": "string"}}, "driverLicenses": {"type": "string"}, "languages": {"type": "array", "items": {"type": "string"}}, "careerLevel": {"type": "array", "items": {"type": "string"}}, "functionGroups": {"type": "array", "items": {"type": "string"}}, "requestedSalary": {"type": "array", "items": {"type": "string"}}, "provinces": {"type": "array", "items": {"type": "string"}}}, "type": "object"}, "SearchTermsResource": {"properties": {"all": {"type": "array", "items": {"type": "string"}}, "educationTitle": {"type": "array", "items": {"type": "string"}}, "educationDescription": {"type": "array", "items": {"type": "string"}}, "preferredJobTitle": {"type": "array", "items": {"type": "string"}}, "allJobTitles": {"type": "array", "items": {"type": "string"}}, "allJobDescriptions": {"type": "array", "items": {"type": "string"}}, "courses": {"type": "array", "items": {"type": "string"}}}, "type": "object"}, "Attachment": {"required": ["fileName", "name", "extension"], "properties": {"fileName": {"type": "string", "example": "test.docx"}, "name": {"type": "string", "example": "CV <PERSON>"}, "extension": {"type": "string", "example": "docs"}}, "type": "object"}, "AutoCompleteCityResponse": {"required": ["result"], "properties": {"result": {"type": "array", "items": {"type": "string"}}}, "type": "object"}, "AutoCompleteResponse": {"required": ["result"], "properties": {"result": {"type": "array", "items": {"$ref": "#/definitions/AutoCompleteResponseItem"}}}, "type": "object"}, "AutoCompleteResponseItem": {"required": ["text"], "properties": {"text": {"type": "string"}, "score": {"type": "string"}}, "type": "object"}, "Commute": {"required": ["zipCode", "city", "maxTravelDistance"], "properties": {"zipCode": {"type": "string", "example": "1011TB"}, "city": {"type": "string", "example": "Amsterdam"}, "maxTravelDistance": {"type": "integer", "example": "30"}}, "type": "object"}, "DriversLicence": {"required": ["type", "group"], "properties": {"type": {"type": "string", "example": "B - <PERSON><PERSON><PERSON>"}, "group": {"type": "string", "example": "B - <PERSON><PERSON><PERSON>"}}, "type": "object"}, "Education": {"required": ["educationId"], "properties": {"educationId": {"type": "string", "example": "b2db7df4-1628-11e7-90a6-308cdf06bc20"}, "school": {"type": "string", "example": "CCV"}, "city": {"type": "string", "example": "Amsterdam"}, "fromDate": {"type": "string", "example": "2013-01-01 00:00:01"}, "toDate": {"type": "string", "example": "2015-01-01 00:00:01"}, "fieldOfStudy": {"type": "string", "example": "Transport"}, "diploma": {"type": "boolean", "example": "true"}, "grade": {"type": "string", "example": "HBO"}, "description": {"type": "string", "example": "This is a description"}, "descriptionHtml": {"type": "string", "example": "This is a description formatted in html"}}, "type": "object"}, "Error": {"required": ["code", "message"], "properties": {"code": {"type": "integer", "example": 404}, "message": {"type": "string", "example": "The requested job seeker could not be found"}}, "type": "object"}, "Experience": {"required": ["experienceId"], "properties": {"experienceId": {"type": "string", "example": "b2db7df4-1628-11e7-90a6-308cdf06bc17"}, "jobTitle": {"type": "string", "example": "PHP Developer"}, "companyName": {"type": "string", "example": "Persgroep"}, "fromDate": {"type": "string", "example": "2015-01-01 00:00:01"}, "toDate": {"type": "string", "example": "2017-01-01 00:00:01"}, "city": {"type": "string", "example": "Amsterdam"}, "description": {"type": "string", "example": "This is a description"}, "descriptionHtml": {"type": "string", "example": "This is a description formatted in html"}}, "type": "object"}, "Aggregation": {"required": ["buckets"], "properties": {"buckets": {"type": "array", "items": {"$ref": "#/definitions/Bucket"}}}, "type": "object"}, "Aggregations": {"required": ["workingHours", "workLevels", "updatedDate", "languages", "availability", "driverLicenses", "careerLevel", "functionGroups", "requested<PERSON><PERSON><PERSON>"], "properties": {"workingHours": {"$ref": "#/definitions/Aggregation"}, "workLevels": {"$ref": "#/definitions/Aggregation"}, "updatedDate": {"$ref": "#/definitions/Aggregation"}, "languages": {"$ref": "#/definitions/Aggregation"}, "availability": {"$ref": "#/definitions/Aggregation"}, "radius": {"$ref": "#/definitions/Aggregation"}, "driverLicenses": {"$ref": "#/definitions/Aggregation"}, "careerLevel": {"$ref": "#/definitions/Aggregation"}, "functionGroups": {"$ref": "#/definitions/Aggregation"}, "requestedSalary": {"$ref": "#/definitions/Aggregation"}, "provinces": {"$ref": "#/definitions/Aggregation"}}, "type": "object"}, "Bucket": {"required": ["key", "doc_count"], "properties": {"key": {"type": "string"}, "doc_count": {"type": "integer"}}, "type": "object"}, "FiltersResult": {"required": ["aggregations", "location_not_found", "invalid_query"], "properties": {"aggregations": {"$ref": "#/definitions/Aggregations"}, "location_not_found": {"type": "boolean"}, "invalid_query": {"type": "boolean"}}, "type": "object"}, "GeoPoint": {"required": ["lat", "lon"], "properties": {"lat": {"type": "number", "example": "52.367491"}, "lon": {"type": "number", "example": "4.918064"}}, "type": "object"}, "GeoShape": {"properties": {"coordinates": {"type": "array", "items": {"type": "number"}, "example": "[4.918064, 52.367491]"}, "type": {"type": "string", "example": "circle"}, "radius": {"type": "string", "example": "30km"}}, "type": "object"}, "JobSeeker": {"required": ["id", "emailAddress", "firstName", "lastName", "site"], "properties": {"id": {"type": "string"}, "emailAddress": {"type": "string", "example": "<EMAIL>"}, "firstName": {"type": "string", "example": "<PERSON>"}, "lastName": {"type": "string", "example": "<PERSON><PERSON>"}, "site": {"type": "string", "example": "nationalevacaturebank.nl"}, "legacyJobSeekerId": {"type": "integer", "example": "1234"}, "matchMailFrequency": {"type": "string", "example": "weekly"}, "preferredJobs": {"type": "array", "items": {"type": "string"}, "example": "weekly"}, "contractType": {"type": "string", "example": "Vast"}, "commute": {"$ref": "#/definitions/Commute"}, "minWorkingHours": {"type": "integer", "example": "32"}, "maxWorkingHours": {"type": "integer", "example": "40"}, "workingHours": {"type": "array", "items": {"type": "string"}, "example": "['32 tot en met 40 uur']"}, "workLevels": {"type": "array", "items": {"type": "string"}, "example": "['MBO']"}, "availability": {"type": "string", "example": "Per direct"}, "fileCvBlob": {"type": "string"}, "fileCvHtml": {"type": "string"}, "fileCvString": {"type": "string"}, "phoneNumber": {"type": "string", "example": "**********"}, "updatedDate": {"type": "integer", "example": "**********"}, "findable": {"type": "boolean", "example": true}, "findableInSanDiego": {"type": "boolean", "example": true}, "noWorkExperience": {"type": "boolean", "example": false}, "experiences": {"description": "List of experiences ordered descending by to-date and from-date", "type": "array", "items": {"$ref": "#/definitions/Experience"}}, "education": {"description": "List of education ordered descending by to-date and from-date", "type": "array", "items": {"$ref": "#/definitions/Education"}}, "training": {"type": "array", "items": {"$ref": "#/definitions/Training"}}, "languages": {"type": "array", "items": {"$ref": "#/definitions/Language"}}, "hobbies": {"type": "array", "items": {"type": "string"}, "example": "['Surf', 'Cart']"}, "skills": {"type": "array", "items": {"type": "string"}, "example": "['PHP', 'Java']"}, "driverLicenses": {"type": "array", "items": {"$ref": "#/definitions/DriversLicence"}}, "photo": {"type": "string"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/Attachment"}}, "isFavorite": {"type": "boolean", "example": false}, "unapprovedExperienceIds": {"type": "array", "items": {"type": "string"}, "example": "['experience-id-1', 'experience-id-2']"}, "unapprovedEducationIds": {"type": "array", "items": {"type": "string"}, "example": "['education-id-1', 'education-id-2']"}, "migrationStatus": {"type": "string"}, "introductionText": {"type": "string"}, "introductionTextHtml": {"type": "string"}, "viewedDate": {"type": "integer", "example": "**********"}}, "type": "object"}, "JobSeekerLimited": {"properties": {"id": {"type": "string"}, "site": {"type": "string", "example": "nationalevacaturebank.nl"}, "preferredJobs": {"type": "array", "items": {"type": "string"}, "example": "weekly"}, "commute": {"$ref": "#/definitions/Commute"}, "minWorkingHours": {"type": "integer", "example": "32"}, "maxWorkingHours": {"type": "integer", "example": "40"}, "workLevels": {"type": "array", "items": {"type": "string"}, "example": "['MBO']"}, "availability": {"type": "string", "example": "Per direct"}, "driverLicenses": {"type": "array", "items": {"$ref": "#/definitions/DriversLicence"}}}, "type": "object"}, "Language": {"required": ["isoCode", "name", "isNative"], "properties": {"isoCode": {"type": "string", "example": "EN"}, "name": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "isNative": {"type": "boolean", "example": true}}, "type": "object"}, "SearchResult": {"required": ["result", "resultCount", "location_not_found"], "properties": {"result": {"type": "array", "items": {"$ref": "#/definitions/SearchResultItem"}}, "resultCount": {"type": "integer"}, "location_not_found": {"type": "boolean"}, "invalid_query": {"type": "boolean"}}, "type": "object"}, "SearchResultItem": {"required": ["id", "firstName", "lastName", "site"], "properties": {"id": {"type": "string"}, "firstName": {"type": "string", "example": "<PERSON>"}, "lastName": {"type": "string", "example": "<PERSON><PERSON>"}, "site": {"type": "string", "example": "nationalevacaturebank.nl"}, "preferredJobs": {"type": "array", "items": {"type": "string"}, "example": "weekly"}, "commute": {"$ref": "#/definitions/Commute"}, "minWorkingHours": {"type": "integer", "example": "32"}, "maxWorkingHours": {"type": "integer", "example": "40"}, "workLevels": {"type": "array", "items": {"type": "string"}, "example": "['MBO']"}, "availability": {"type": "string", "example": "Per direct"}, "updatedDate": {"type": "integer", "example": "**********"}, "noWorkExperience": {"type": "boolean", "example": false}, "experiences": {"description": "List of experiences ordered descending by to-date and from-date", "type": "array", "items": {"$ref": "#/definitions/Experience"}}, "photo": {"type": "string"}, "isFavorite": {"type": "boolean", "example": false}, "viewedDate": {"type": "integer", "example": "**********"}}, "type": "object"}, "Training": {"required": ["trainingId"], "properties": {"trainingId": {"type": "string", "example": "b2db7df4-1628-11e7-90a6-308cdf06bc20"}, "institute": {"type": "string", "example": "CCV"}, "name": {"type": "string", "example": "Apothekers Assistent"}, "year": {"type": "integer", "example": "2018"}, "month": {"type": "integer", "example": "1"}, "description": {"type": "string", "example": "This is a description"}, "descriptionHtml": {"type": "string", "example": "This is a description formatted in html"}}, "type": "object"}}, "externalDocs": {"description": "Find out more about <PERSON>wagger", "url": "http://swagger.io"}}