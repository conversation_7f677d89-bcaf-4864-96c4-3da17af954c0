const faker = require('faker/locale/nl');

faker.seed(1337);

module.exports = function GenerateJobSeeker() {
    return {
        "id": faker.random.uuid(),
        "firstName": faker.name.firstName(),
        "lastName": faker.name.lastName(),
        "emailAddress": faker.internet.email(),
        "site": "intermediair.nl",
        "legacyJobSeekerId": faker.random.number({max: 1000000}),
        "preferredJobs": [
            faker.name.jobTitle(),
            faker.name.jobTitle(),
            faker.name.jobTitle()
        ],
        "functionGroups": [
            faker.name.jobTitle(),
            faker.name.jobTitle(),
            faker.name.jobTitle()
        ],
        "contractType": "Vast",
        "commute": {
            "zipCode": "1033SN",
            "city": "Amsterdam",
            "maxTravelDistance": 500
        },
        "minWorkingHours": faker.random.number({max: 20}),
        "maxWorkingHours": faker.random.number({min: 32, max: 40}),
        "workingHours": [
            "32 tot en met 40 uur"
        ],
        "availability": "In overleg",
        "workLevels": [
            "HBO",
            "WO"
        ],
        "phoneNumber": faker.phone.phoneNumber,
        "updatedDate": Math.round(faker.date.recent() / 1000),
        "findable": true,
        "findableInSanDiego": true,
        "noWorkExperience": false,
        "photo": "profile-picture-f2991a00-0322-11e7-a50d-957a798eb034-1536832097.png",
        "experiences": [
          {
            "experienceId": "46958fe4-1a0c-11e7-abde-620c5f5a4c44",
            "jobTitle": "medewerker technische dienst",
            "companyName": "Gemeente Zevenaar",
            "fromDate": "2015-07-01 00:00:01",
            "toDate": faker.random.boolean() ? "2017-01-01 00:00:01" : undefined,
            "city": "Zevenaar",
            "description": "#### Best experience ever.\n\nAls medewerker **technische dienst** ben ik verantwoordelijk voor het opstellen, inplannen en laten uitvoeren van al onderhoud aan de tractie en technische middelen van de gemeente. Daarnaast verzorg ik de aansturing en planning van de binnendienst (3 man) geeft ik advies bij vervanging\/aanbesteding en repartie van de tractie\/technische middelen.\r\nNaast deze baan heb ik de afgelopen vier jaar de opleiding technische bedrijfskunde met succes afgerond aan de hogeschool van *Arnhem* en *Nijmegen*.",
            "descriptionHtml": "<h4>Best experience ever.</h4> Als medewerker <b>technische dienst</b> ben ik verantwoordelijk voor het opstellen, inplannen en laten uitvoeren van al onderhoud aan de tractie en technische middelen van de gemeente. Daarnaast verzorg ik de aansturing en planning van de binnendienst (3 man) geeft ik advies bij vervanging\/aanbesteding en repartie van de tractie\/technische middelen.\r\nNaast deze baan heb ik de afgelopen vier jaar de opleiding technische bedrijfskunde met succes afgerond aan de hogeschool van <i>Arnhem</i> en <i>Nijmegen</i>."
          },
          {
            "experienceId": "4695c072-1a0c-11e7-bc07-18c0e6a176b0",
            "jobTitle": "machnie bouwer",
            "companyName": "Bronkhorst hi-tech",
            "fromDate": "2010-05-01 00:00:01",
            "toDate": "2015-07-01 00:00:01",
            "city": "Bronkhorts",
            "description": "Het samenstellen en testen van Hi end flow meters. Werkzaam binnen de afdeling specials.",
            "descriptionHtml": "Het samenstellen en testen van Hi end flow meters. Werkzaam binnen de afdeling specials."
          },
          {
            "experienceId": "4695c072-1a0c-11e7-bc07-18c0e6a176b0",
            "jobTitle": "machnie bouwer",
            "companyName": "Bronkhorst hi-tech (JOB WITH THE SAME DATES)",
            "fromDate": "2010-05-01 00:00:01",
            "toDate": "2015-07-01 00:00:01",
            "city": "Bronkhorts",
            "description": "Het samenstellen en testen van Hi end flow meters. Werkzaam binnen de afdeling specials.",
            "descriptionHtml": "Het samenstellen en testen van Hi end flow meters. Werkzaam binnen de afdeling specials."
          },
          {
            "experienceId": "4695f36c-1a0c-11e7-88bc-38aad413b0da",
            "jobTitle": "Monteur Apache helicopter (OVERLAPPING)",
            "companyName": "Ministerie van defensie",
            "fromDate": "2007-02-01 00:00:01",
            "toDate": "2016-01-01 00:00:01",
            "city": "Gilze Rijen",
            "description": "Het plannen en uitvoeren van het onderhoud aan de **Apache** helikopters.\n\n -Danger -High Voltage.\n\nDit in lokale situaties maar ook in uitzendgebieden.",
            "descriptionHtml": "Het plannen en uitvoeren van het onderhoud aan de <b>Apache</b> helikopters. <ul><li>Danger</li><li>High Voltage</li></ul> Dit in lokale situaties maar ook in uitzendgebieden."
          },
          {
            "experienceId": "4695f36c-1a0c-11e7-88bc-38aad413b0da",
            "jobTitle": "Monteur Apache helicopter",
            "companyName": "Ministerie van defensie",
            "fromDate": "2005-11-01 00:00:01",
            "toDate": "2010-05-01 00:00:01",
            "city": "Gilze Rijen",
            "description": "Het plannen en uitvoeren van het onderhoud aan de **Apache** helikopters.\n\n -Danger -High Voltage.\n\nDit in lokale situaties maar ook in uitzendgebieden.",
            "descriptionHtml": "Het plannen en uitvoeren van het onderhoud aan de <b>Apache</b> helikopters. <ul><li>Danger</li><li>High Voltage</li></ul> Dit in lokale situaties maar ook in uitzendgebieden."
          },
          {
            "experienceId": "4695f36c-1a0c-11e7-88bc-38aad413b0da",
            "jobTitle": "Monteur Apache helicopter",
            "companyName": "Ministerie van defensie",
            "fromDate": "2000-12-01 00:00:01",
            "toDate": "2005-11-01 00:00:01",
            "city": "Gilze Rijen",
            "description": "Het plannen en uitvoeren van het onderhoud aan de **Apache** helikopters.\n\n -Danger -High Voltage.\n\nDit in lokale situaties maar ook in uitzendgebieden.",
            "descriptionHtml": "Het plannen en uitvoeren van het onderhoud aan de <b>Apache</b> helikopters. <ul><li>Danger</li><li>High Voltage</li></ul> Dit in lokale situaties maar ook in uitzendgebieden."
          },
          {
            "experienceId": "46962238-1a0c-11e7-9b5d-fc9aba67b661",
            "jobTitle": "autotechnicus (ONE MONTH JOB)",
            "companyName": "Garage H.Vos NotVisible",
            "fromDate": "2000-09-01 00:00:01",
            "toDate": "2000-09-15 00:00:01",
            "city": "Zevenaar",
            "description": "Verantwoordelijk voor het plannen en uitvoeren van alle onderhouden werkzaamheden. \r\nAdministratie en klantcontact horen hier ook bij",
            "descriptionHtml": "Verantwoordelijk voor het plannen en uitvoeren van alle onderhouden werkzaamheden. \r\nAdministratie en klantcontact horen hier ook bij"
          },
          {
            "experienceId": "46962238-1a0c-11e7-9b5d-fc9aba67b662",
            "jobTitle": "autotechnicus (NESTED)",
            "companyName": "Garage H.Vos Visible",
            "fromDate": "1997-05-01 00:00:01",
            "toDate": "1998-07-01 00:00:01",
            "city": "Zevenaar",
            "description": "**Verantwoordelijk** voor het plannen en uitvoeren van alle onderhouden werkzaamheden. \r\nAdministratie en klantcontact horen hier ook bij",
            "descriptionHtml": "<b>Verantwoordelijk</b> voor het plannen en uitvoeren van alle onderhouden werkzaamheden. \r\nAdministratie en klantcontact horen hier ook bij"
          },
          {
            "experienceId": "46962238-1a0c-11e7-9b5d-fc9aba67b663",
            "jobTitle": "autotechnicus",
            "companyName": "Garage H.Vos Visible",
            "fromDate": "1995-05-01 00:00:01",
            "toDate": "2000-08-01 00:00:01",
            "city": "Zevenaar",
            "description": "Verantwoordelijk voor het plannen en uitvoeren van alle onderhouden werkzaamheden. \r\nAdministratie en klantcontact horen hier ook bij",
            "descriptionHtml": "Verantwoordelijk voor het plannen en uitvoeren van alle onderhouden werkzaamheden. \r\nAdministratie en klantcontact horen hier ook bij"
          }
        ],
        "education": [{
            "educationId": faker.random.uuid(),
            "school": faker.company.companyName(),
            "fromDate": "2007-09-01",
            "toDate": "2008-06-30",
            "fieldOfStudy": "Mastermodule Strategisch Management",
            "grade": "WO"
            },
            {
                "educationId": "10f879c2-355b-11e7-a764-12762ca49a9b",
                "school": "Universiteit Utrecht",
                "fromDate": "2000-09-01",
                "toDate": "2003-06-30",
                "fieldOfStudy": "Master of Management in dienstverlenende",
                "grade": "WO",
                "description": "### Master of Management in dienstverlenende",
                "descriptionHtml": "<h3>Master of Management in dienstverlenende</h3>"
            },
            {
                "educationId": "10f8a866-355b-11e7-840a-ba2f47f0f9d3",
                "school": "Hogeschool Amsterdam",
                "fromDate": "1998-09-01",
                "toDate": "1999-06-30",
                "fieldOfStudy": "Managementopleiding voor leidinggevend middenkader",
                "grade": "HBO",
                "description": "### Master of Management in dienstverlenende",
                "descriptionHtml": "<h3>Master of Management in dienstverlenende</h3>"
            },
            {
                "educationId": "10f8d660-355b-11e7-89f2-3232689394f9",
                "school": "Rotterdam",
                "fromDate": "1990-09-01",
                "toDate": "1993-06-30",
                "fieldOfStudy": "Gestalttherapie",
                "grade": "Cursus",
                "description": "Lorem ipsum dolor sit amet, *consectetur adipiscing elit*, **sed do eiusmod tempor incididunt** ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
                "descriptionHtml": "Lorem ipsum dolor sit amet, <i>consectetur adipiscing elit</i>, <b>sed do eiusmod tempor incididunt</b> ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
            },
            {
                "educationId": "10f905a4-355b-11e7-a9fc-ab7f724e906a",
                "school": "Hogeschool 's-Hertogenbosch",
                "fromDate": "1989-09-01",
                "toDate": "1992-06-30",
                "fieldOfStudy": "HBO MW",
                "grade": "HBO",
                "description": "Mi piace la pasta, *consectetur adipiscing elit*, **sed do eiusmod tempor incididunt** ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
                "descriptionHtml": "Mi piace la pasta, <i>consectetur adipiscing elit</i>, <b>sed do eiusmod tempor incididunt</b> ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
            }
        ],
        "languages": [ { "isoCode": "EN", "name": "Engels", "isNative": false } ],
        "hobbies": [],
        "skills": [],
        "driverLicenses": [ {
            "group": "B - personenauto",
            "type": "B - personenauto"
        } ],
        "attachments": [
            {
              "fileName": "172.31.213.113_201806_18_1928379125030482.2h64zqwrfhc88.pdf",
              "name": "Scrum Certification",
              "extension": "pdf",
              "uploadDate": **********
            },
            {
              "fileName": "university_diploma.png",
              "name": "University diploma",
              "extension": "png"
            }
        ],
        "training": [
            {
                "description": "- Na afloop van de training ontvang je een officieel Agile Scrum Group certificaat. - Persoonlijk & Informeel. - Registered Scrum Alliance. Enthousiaste Trainers. Betaalbaar. Resultaatgericht.",
                "descriptionHtml": "<ul><li>Na afloop van de training ontvang je een officieel Agile Scrum Group certificaat.</li> <li>Persoonlijk & Informeel.</li> <li>Registered Scrum Alliance. Enthousiaste Trainers. Betaalbaar. Resultaatgericht.</li>",
                "institute": "Xebia",
                "month": 12,
                "name": "Scrum Master",
                "trainingId": "b7effe8a-a9fe-11e8-812a-0242ac110005",
                "year": 2016
            },
            {
                "description": "**B1**",
                "descriptionHtml": "<b>B1</b>",
                "institute": "Language Institute Regina Coeli",
                "month": 10,
                "name": "Dutch Language",
                "trainingId": "44ed22f6-b6b4-11e8-9084-0242ac110005",
                "year": 2017
            }
        ],
        "viewedDate": 1544690971,
        "isFavorite": faker.random.boolean(),
        "unapprovedEducationIds": [
          "10f8a866-355b-11e7-840a-ba2f47f0f9d3",
          "10f905a4-355b-11e7-a9fc-ab7f724e906a"
        ],
        "unapprovedExperienceIds": [
            "4695c072-1a0c-11e7-bc07-18c0e6a176b0",
            "46962238-1a0c-11e7-9b5d-fc9aba67b661"
        ],
        "introductionText": "### Presentation This is **my** introduction, *so you know*. - Frontend Developer - Scrum Master - Music Critic",
        "introductionTextHtml": "<h3>Presentation</h3><p>This is <b>my</b> introduction, <i>so you know</i>.</p><ul><li>Frontend Developer</li> <li>Scrum Master</li> <li>Music Critic</li></ul>"
    }
};
