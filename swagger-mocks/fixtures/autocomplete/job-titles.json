{"result": [{"text": "php programmeur", "score": "112"}, {"text": "php developer", "score": "63"}, {"text": "php on<PERSON>wi<PERSON><PERSON><PERSON>", "score": "23"}, {"text": "php-programmeur", "score": "22"}, {"text": "php programmer", "score": "13"}, {"text": "junior php programmeur", "score": "12"}, {"text": "junior php developer", "score": "9"}, {"text": "webdeveloper php", "score": "9"}, {"text": "php", "score": "9"}, {"text": "senior php developer", "score": "8"}, {"text": "php webdeveloper", "score": "8"}, {"text": "php software engineer", "score": "6"}, {"text": "senior php programmeur", "score": "6"}, {"text": "programmeur php", "score": "5"}, {"text": "lead php developer", "score": "5"}, {"text": "freelance php programmeur", "score": "4"}]}