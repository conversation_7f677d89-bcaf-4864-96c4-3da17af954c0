const fs =  require('fs-extra')
const path = require('path')
const mocksPath = './server/mocks';

if(fs.existsSync(path.resolve(__dirname, mocksPath))){
  console.log('- Removing existing mocks folder', mocksPath);
  fs.removeSync(path.resolve(__dirname, mocksPath));
}

fs.mkdirSync(path.resolve(__dirname, mocksPath));

const GenerateJobSeeker = require('./fixtures/jobseekers/jobseeker');
const GenerateJobSeekerWithCv = require('./fixtures/jobseekers/jobseeker-with-cv');

const generateMock = (mockName, mockObject) => {
    const filePath = path.resolve(__dirname, './server/mocks/' + mockName + '.json');

    fs.writeFile(
        filePath,
        JSON.stringify(mockObject),
        (err) => {
            if (err) throw err;
            console.log(mockName + ' mock file has been saved!');
        }
    );
}

const mapToSearchResult = (jobseeker) => {
  delete jobseeker.attachments;
  delete jobseeker.commute.maxTravelDistance;
  delete jobseeker.commute.zipCode;
  delete jobseeker.contractType;
  delete jobseeker.driverLicenses;
  delete jobseeker.education;
  delete jobseeker.emailAddress;
  delete jobseeker.fileCvString;
  delete jobseeker.findable;
  delete jobseeker.findableInSanDiego;
  delete jobseeker.functionGroups;
  delete jobseeker.hobbies;
  delete jobseeker.introductionText;
  delete jobseeker.introductionTextHtml;
  delete jobseeker.languages;
  delete jobseeker.legacyJobSeekerId;
  delete jobseeker.photo;
  delete jobseeker.skills;
  delete jobseeker.training;
  delete jobseeker.unapprovedEducationIds;
  delete jobseeker.unapprovedExperienceIds;
  delete jobseeker.workingHours;

  return jobseeker;
}

const jobseekers = Array(25).fill(null).map(() => {
    const jobseeker = GenerateJobSeeker();
    generateMock(`js-${jobseeker.id}`, jobseeker);

    return mapToSearchResult(jobseeker);
});

const jobseekerWithCv = GenerateJobSeekerWithCv();
generateMock(`js-${jobseekerWithCv.id}`, jobseekerWithCv);
jobseekers.unshift(mapToSearchResult(jobseekerWithCv));

const searchresult = require('./fixtures/search/search-result');
searchresult.result = jobseekers;

// RECRUITER-API
generateMock('search-result', searchresult);
generateMock('filters-result', require('./fixtures/filters/filters-result.json'));
generateMock('auto-complete-job-titles', require('./fixtures/autocomplete/job-titles.json'));
generateMock('auto-complete-skills', require('./fixtures/autocomplete/skills.json'));
generateMock('auto-complete-cities', require('./fixtures/autocomplete/cities.json'));
generateMock('legacy-url-job-seeker-detail', require('./fixtures/legacy-url-parser/job-seeker-detail.json'));
generateMock('legacy-url-search', require('./fixtures/legacy-url-parser/search.json'));
generateMock('saved-search', require('./fixtures/saved-search/saved-search.json'));
generateMock('favorites', require('./fixtures/favorites/favorites.json'));
generateMock('jobseeker-with-cv', jobseekerWithCv);
generateMock('jobseeker-cv', require('./fixtures/jobseekers/jobseeker-cv.json'));
generateMock('job-seeker-profile-limited', require('./fixtures/jobseekers/jobseeker-profile-limited.json'));
generateMock('recruiter', require('./fixtures/recruiter/default.json'));
generateMock('suggestible-packages', require('./fixtures/recruiter/suggestible-packages.json'));
