var util            = require('util'),
    path            = require('path'),
    express         = require('express'),
    bodyParser      = require('body-parser'),
    swagger         = require('swagger-express-middleware'),
    Middleware      = swagger.Middleware,
    MemoryDataStore = swagger.MemoryDataStore,
    Resource        = swagger.Resource;

const app = express();
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
const middleware = new Middleware(app);

// RECRUITER API
const searchResults = require('./mocks/search-result.json');
const filtersResults = require('./mocks/filters-result.json');
const skillsResults = require('./mocks/auto-complete-skills.json');
const jobTitlesResults = require('./mocks/auto-complete-job-titles.json');
const cityResults = require('./mocks/auto-complete-cities.json');
const legacySearch = require('./mocks/legacy-url-search.json');
const legacyJobSeekerDetail = require('./mocks/legacy-url-job-seeker-detail.json');
const savedSearch = require('./mocks/saved-search.json');
const favorites = require('./mocks/favorites.json');
const jobseekerWithCv = require('./mocks/jobseeker-with-cv.json');
const jobseekerCv = require('./mocks/jobseeker-cv.json');
const jobSeekerLimited = require('./mocks/job-seeker-profile-limited.json');
const recruiter = require('./mocks/recruiter.json');
const suggestiblePackages = require('./mocks/suggestible-packages.json');

middleware.init(path.join(__dirname, '../swagger.json'), function() {
    let myDB = new MemoryDataStore();

    searchResults.result.map((jobseekerSearch) => {
        const jobseeker  = require(`./mocks/js-${jobseekerSearch.id}.json`);

        myDB.save(
            new Resource('/api/recruiter/job-seeker/' + jobseekerSearch.id, jobseeker)
        );
    });

    myDB.save(
        new Resource('/api/recruiter/search', searchResults),
        new Resource('/api/recruiter/search/filters', filtersResults),
        new Resource(`/api/recruiter/job-seeker/${searchResults.result[0].id}`, jobseekerWithCv),
        new Resource(`/api/recruiter/job-seeker/${searchResults.result[0].id}/cv`, jobseekerCv),
        new Resource('/api/recruiter/autocomplete/skills', skillsResults),
        new Resource('/api/recruiter/autocomplete/job-titles', jobTitlesResults),
        new Resource('/api/recruiter/autocomplete/cities', cityResults),
        new Resource('/api/recruiter/url-parser/job-seeker-details', legacyJobSeekerDetail),
        new Resource('/api/recruiter/url-parser/search', legacySearch),
        new Resource('/api/recruiter/saved-searches', savedSearch),
        new Resource('/api/recruiter/favorites', favorites),
        new Resource(`/api/recruiter/job-seeker/limited/${searchResults.result[9].id}`, jobSeekerLimited),
        new Resource(`/api/recruiter/job-seeker/limited/${searchResults.result[10].id}`, jobSeekerLimited),
        new Resource(`/api/recruiter/job-seeker/limited/${searchResults.result[11].id}`, jobSeekerLimited),
        new Resource('/api/recruiter/saved-searches', savedSearch),
        new Resource('/api/recruiter/favorites/list', favorites),
        new Resource('/api/recruiter/me', recruiter),
        new Resource('/dashboard/api/products?displayOptions=4', suggestiblePackages)
    );

    app.get(`/api/recruiter/job-seeker/${searchResults.result[9].id}`, function (req, res) {
        res.status(403).send({ code: 403, message: "daily_limit_exceeded" });
    });

    app.get(`/api/recruiter/job-seeker/${searchResults.result[10].id}`, function (req, res) {
        res.status(403).send({ code: 403, message: "unverified_company" });
    });

    app.get(`/api/recruiter/job-seeker/${searchResults.result[11].id}`, function (req, res) {
        res.status(403).send({ code: 403, message: "invalid_subscription" });
    });

    app.post('/api/recruiter/favorites/*', function (req, res) {
        res.status(201).send({});
    });

    app.delete('/api/recruiter/favorites/*', function (req, res) {
        res.status(204).send({});
    });

    app.use(middleware.metadata());

    app.use(
        middleware.CORS(),
        middleware.validateRequest()
    );
    app.use(middleware.mock(myDB));

    app.listen(10208, function() {
        console.log('The Swagger Mock Server is now running at http://localhost:10208');
    });
});
