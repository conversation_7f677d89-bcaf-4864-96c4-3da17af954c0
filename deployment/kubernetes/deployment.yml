apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{PROJECT}}
  labels:
    app: {{PROJECT}}
spec:
  replicas: 2
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  selector:
    matchLabels:
      app: {{PROJECT}}
  template:
    metadata:
      labels:
        app: {{PROJECT}}
    spec:
      containers:
        - name: {{PROJECT}}
          image: {{IMAGE_URL}}:{{BUILD_NUMBER}}
          ports:
            - containerPort: 80
          resources:
            limits:
              cpu: 200m
              memory: 60Mi
            requests:
              cpu: 10m
              memory: 40Mi
          env:
            - name: NGINX_PATH
              valueFrom:
                configMapKeyRef:
                  name: {{PROJECT}}-config
                  key: nginx.path
          imagePullPolicy: Always
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 5
            periodSeconds: 5
          livenessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 5
            periodSeconds: 5
