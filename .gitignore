# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
/dist
/tmp
/out-tsc
/swagger-mocks/server/mocks

# dependencies
/node_modules

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
jobseeker-frontend.iml

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.editorconfig

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
testem.log
/typings

coverage

# System Files
.DS_Store
Thumbs.db
/.node-gyp/
/.local/
/.npm/
/ts-node/
/.gradle/
/ts-node-1fc90f464345a82d8eb8eee0683b64cadfb994e7/
/recruiter-frontend.iml

docker/nginx/html/

# Cypress
/cypress/screenshots
/cypress/videos
/.Trash-*

# Other
.vscode/
yarn-error.log
