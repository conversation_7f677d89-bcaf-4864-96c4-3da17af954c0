{"plugins": ["stylelint-order"], "rules": {"at-rule-name-case": "lower", "at-rule-name-space-after": "always-single-line", "at-rule-semicolon-newline-after": "always", "color-no-invalid-hex": true, "color-hex-case": "lower", "color-hex-length": "short", "comment-empty-line-before": "never", "comment-no-empty": true, "comment-whitespace-inside": "always", "block-closing-brace-newline-after": "always", "block-closing-brace-newline-before": "always-multi-line", "block-no-empty": true, "block-opening-brace-newline-after": "always-multi-line", "block-opening-brace-newline-before": "never-single-line", "declaration-block-no-duplicate-properties": true, "declaration-block-no-shorthand-property-overrides": true, "declaration-block-semicolon-newline-after": "always-multi-line", "declaration-block-semicolon-space-after": "always-single-line", "declaration-block-semicolon-space-before": "never", "declaration-block-single-line-max-declarations": 1, "declaration-block-trailing-semicolon": "always", "declaration-colon-newline-after": "always-multi-line", "declaration-colon-space-after": "always-single-line", "declaration-colon-space-before": "never", "font-family-name-quotes": "always-unless-keyword", "font-family-no-duplicate-names": true, "font-family-no-missing-generic-family-keyword": true, "font-weight-notation": "numeric", "function-comma-newline-after": ["always-multi-line", {"warn": true}], "function-comma-newline-before": "never-multi-line", "function-comma-space-after": ["always-single-line", {"warn": true}], "function-comma-space-before": "never", "function-linear-gradient-no-nonstandard-direction": true, "function-max-empty-lines": 0, "function-name-case": "lower", "function-parentheses-newline-inside": ["always-multi-line", {"warn": true}], "function-parentheses-space-inside": ["never-single-line", {"warn": true}], "function-url-quotes": "always", "function-whitespace-after": "always", "indentation": 2, "max-empty-lines": 1, "media-feature-colon-space-after": "always", "media-feature-colon-space-before": "never", "media-feature-name-case": "lower", "media-feature-range-operator-space-after": "always", "media-feature-range-operator-space-before": "always", "media-query-list-comma-newline-after": ["always-multi-line", {"warn": true}], "media-query-list-comma-newline-before": "never-multi-line", "media-query-list-comma-space-after": ["always-single-line", {"warn": true}], "media-query-list-comma-space-before": "never", "no-eol-whitespace": true, "no-missing-end-of-source-newline": true, "number-no-trailing-zeros": true, "number-leading-zero": "always", "order/order": ["custom-properties", "declarations"], "order/properties-alphabetical-order": true, "string-no-newline": true, "string-quotes": "double", "value-list-comma-newline-after": "always-multi-line", "value-list-comma-newline-before": "never-multi-line", "value-list-comma-space-after": "always-single-line", "value-list-comma-space-before": "never", "value-list-max-empty-lines": 0}}