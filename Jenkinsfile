node('ndp') {
    checkout scm

    def props = readProperties file: '.env'

    try {
        stage('Setup') {
            sh './gradlew newLoginToECR'
            sh './gradlew setup'
        }

        stage('Unit Testing') {
            // Separated e2e from unit testing because they use the same
            // engine, which use the same port.
            // This way there are no port conflicts.
            try {
                parallel lint: {
                    sh './gradlew lint'
                },
                unit: {
                    sh './gradlew testUnit'
                }
            } catch (e) {
                echo 'Unit Tests failed'
                sh './gradlew stopWebE2E'
                throw e
            }
        }


        stage('E2E Testing') {
            try {
                cypressE2e: {
                  script {
                      try {
                          sh './gradlew cypressE2e'
                      } catch (err) {
                          echo err
                          slackSend channel: props.SLACK_CHANNEL_QA, color: '#c11238', message: SLACK_MESSAGE + " " + err, teamDomain: props.SLACK_TEAM_DOMAIN, token: props.SLACK_TOKEN
                      }
                  }
                }
            } catch (e) {
                echo 'E2E Tests failed'
                throw e
            } finally {
                sh './gradlew stopWebE2E'
            }
        }

        stage('Build') {
            sh './gradlew clean'

            lock("${env.PROJECT}-build") {
                parallel buildB2bAcceptanceBundle: {
                    createBundle('b2b', 'acc', env.BUILD_NUMBER, props.STATIC_HOST_B2B_ACC)
                },
                buildB2bProductionBundle: {
                    createBundle('b2b', 'pro', env.BUILD_NUMBER, props.STATIC_HOST_B2B_PRO)
                }
                // ,
                // buildNvbAcceptanceBundle: {
                //     createBundle('nvb', 'acc', env.BUILD_NUMBER, props.STATIC_HOST_NVB_ACC)
                // },
                // buildNvbProductionBundle: {
                //     createBundle('nvb', 'pro', env.BUILD_NUMBER, props.STATIC_HOST_NVB_PRO)
                // },
                // buildIolAcceptanceBundle: {
                //     createBundle('iol', 'acc', env.BUILD_NUMBER, props.STATIC_HOST_IOL_ACC)
                // },
                // buildIolProductionBundle: {
                //     createBundle('iol', 'pro', env.BUILD_NUMBER, props.STATIC_HOST_IOL_PRO)
                // }
            }
        }

        stage('Push Image') {
            parallel pushB2bNginxImage: {
                sh "./gradlew pushNginxImageToEc2ContainerRegistry -Ptarget=b2b -PbuildNumber=${env.BUILD_NUMBER}"
            }
            // ,
            // pushNvbNginxImage: {
            //     sh "./gradlew pushNginxImageToEc2ContainerRegistry -Ptarget=nvb -PbuildNumber=${env.BUILD_NUMBER}"
            // },
            // pushIolNginxImage: {
            //     sh "./gradlew pushNginxImageToEc2ContainerRegistry -Ptarget=iol -PbuildNumber=${env.BUILD_NUMBER}"
            // }
        }

        stage('Deploy Acceptance') {
            parallel pushB2bBundleIntoS3BucketAcc: {
                copyBundleToS3('b2b', 'acc', props.AWS_S3_BUCKET_B2B_ACC, env.BUILD_NUMBER)
            },
            // pushNvbBundleIntoS3BucketAcc: {
            //     copyBundleToS3('nvb', 'acc', props.AWS_S3_BUCKET_NVB_ACC, env.BUILD_NUMBER)
            // },
            // pushIolBundleIntoS3BucketAcc: {
            //     copyBundleToS3('iol', 'acc', props.AWS_S3_BUCKET_IOL_ACC, env.BUILD_NUMBER)
            // },        
            deployB2bToK8sAcc: {
                deploy('b2b', 'acc', env.BUILD_NUMBER)
            // },
            // deployNvbToK8sAcc: {
            //     deploy('nvb', 'acc', env.BUILD_NUMBER)
            // },
            // deployIolToK8sAcc: {
            //     deploy('iol', 'acc', env.BUILD_NUMBER)
            }
        }

        stage('Deploy Production') {
            parallel pushB2bBundleIntoS3BucketPro: {
                copyBundleToS3('b2b', 'pro', props.AWS_S3_BUCKET_B2B_PRO, env.BUILD_NUMBER)
            },
            // pushNvbBundleIntoS3BucketPro: {
            //     copyBundleToS3('nvb', 'pro', props.AWS_S3_BUCKET_NVB_PRO, env.BUILD_NUMBER)
            // },
            // pushIolBundleIntoS3BucketPro: {
            //     copyBundleToS3('iol', 'pro', props.AWS_S3_BUCKET_IOL_PRO, env.BUILD_NUMBER)
            // },
            deployB2bToK8sPro: {
                deploy('b2b', 'pro', env.BUILD_NUMBER)
            // },
            // deployNvbToK8sPro: {
            //     deploy('nvb', 'pro', env.BUILD_NUMBER)
            // },
            // deployIolToK8sPro: {
            //     deploy('iol', 'pro', env.BUILD_NUMBER)
            }
        }
    } catch (Throwable err) {
         currentBuild.result = 'FAILURE'
         throw err
    }
}

def deploy(String target, String env, String buildNumber) {
    try {
        sh "./gradlew deploy -PbuildNumber=${buildNumber} -PtargetEnvironment=${env} -Ptarget=${target}"
    } catch (exception) {
        sh "./gradlew rollbackDeployment -PtargetEnvironment=${env} -Ptarget=${target}"

        throw exception
    }
}

def copyBundleToS3(String target, String env, String bucket,  String buildNumber) {
  try {
      sh "./gradlew copyBundleIntoS3Bucket -PbuildNumber=${buildNumber} -PtargetEnvironment=${env} -Pbucket=${bucket} -Ptarget=${target}"
  } catch (exception) {
      throw exception
  }
}

def createBundle(String target, String env,  String buildNumber, String staticHost) {
  sh "./gradlew createDistributionBundle -PbuildNumber=${buildNumber} -PtargetEnvironment=${env} -Ptarget=${target} -PstaticHost=${staticHost}"
}
