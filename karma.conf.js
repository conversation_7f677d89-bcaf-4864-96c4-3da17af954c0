// Karma configuration file, see link for more information
// https://karma-runner.github.io/1.0/config/configuration-file.html

module.exports = function (config) {
  config.set({
    basePath: '',
    frameworks: ['jasmine', '@angular-devkit/build-angular'],
    plugins: [
      require('karma-jasmine'),
      // Not using PhantomJS because it's too outdated and it's just failing to run.
      // Chrome seems to work fine.
      require('karma-chrome-launcher' ),
      require('karma-jasmine-html-reporter'),
      require('karma-coverage-istanbul-reporter'),
      require('@angular-devkit/build-angular/plugins/karma')
    ],
    client:{
      clearContext: false // leave Jasmine Spec Runner output visible in browser
    },
    coverageIstanbulReporter: {
      dir: require('path').join(__dirname, 'coverage'),
      reports: [ 'html', 'lcovonly', 'text-summary', 'json-summary' ],
      fixWebpackSourcePaths: true,
      thresholds: {
        emitWarning: false,
        global: {
          statements: 99.94,
          lines: 99.93,
          branches: 90,
          functions: 99.85
        }
      }
    },
    reporters: config.angularCli && config.angularCli.codeCoverage
              ? ['progress', 'coverage-istanbul']
              : ['progress', 'kjhtml'],
    port: 10201,
    colors: true,
    logLevel: config.LOG_INFO,
    autoWatch: true,
    browsers: ['Chrome_NoSandbox'],
    customLaunchers: {
      Chrome_NoSandbox: {
        base: 'ChromeHeadless',
        // Required, otherwise Chrome complains and refuses to run.
        // This is for security reasons, but anyway:
        // 1. We are running inside a container.
        // 2. We are using this just to run tests behind a vpn.
        // 3. This project may soon be replaced by something more up to date.
        flags: [
          '--disable-web-security',
          '--disable-gpu',
          '--no-sandbox']
      }},
    singleRun: false
  });
};
