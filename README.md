# Recruiter Front-end

Single page application used by recruiters to find job seekers.

### Usage

#### Pre-requisites
* docker
* docker-compose >= 1.7.0
* java 8
* awscli

#### Pre-requirements
A docker hub account is required and needs to have development permissions.
A AWS CLI installation needs to be setup
```
$ aws configure

```

#### Allocated ports
```
10200-10299
```

| port | description |
| ------ | ------ |
| 10200 | development |
| 10201 | cli |
| 10208 | mocks |
| 10209 | e2e/cypress |

#### PWA

Scoped to `/old-recruiter/`

| file |
| ------ |
| {BASE_HREF}/ngsw-worker.js |
| {BASE_HREF}/ngsw.json |
| {BASE_HREF}/manifest.json |

#### Installation
To install the project, run `./gradlew setup`
See [Installation issues](#installation-issues)

#### Usage

###### Run Mocked Backend
You can run the application with `./gradlew runNvb` or `./gradlew runIol` for NVB or IOL version of application respectively.
This will run the application with a mock Profile-api server.

###### Run with real backend
1. run the project with `./gradlew runLocalStack`
2. login on NVB production and copy `PHPSESSID` cookie (key and value) to your localhost

The app will be served on http://localhost:10200. For now only NVB can work with production data.


###### Stopping

In both cases the app will be started on http://localhost:10200

You can stop the project with `./gradlew stop`

#### Lint
`./gradlew lint`

#### Tests
* Unit: `./gradlew testUnit`  
It will generate a coverage report at the root of the repository in
the folder `coverage`. Run `./gradlew testUnitWatch` to watch for
filesystem changes and run the tests on every change.

* End-to-End: `./gradlew cypressE2e`  
End-to-End tests are run against production like environment in Cypress.

#### Installation issues
When having a new installation of Ubuntu 18.04 LTS you could encounter some installation issues.

1) All gradle commands does not work: Make sure you have a compatible Java version (with Java 10 it did not worked, Java 8 is ok ).
- Use [sdkman](https://sdkman.io/) to install Java. It will also set JAVA_HOME environment, which is required.

- Install Java 8
```
sudo apt-add-repository ppa:webupd8team/java`
sudo apt-get update
sudo apt-get install oracle-java8-installer
export JAVA_HOME=/usr/lib/jvm/java-8-oracle
```

2) After updating Linux it can happens that you have still old docker images, remove them:

```
docker rm -f $(docker ps -a -q)
docker rmi -f $(docker images -q)
```

3) Angular watch does not working:

- If you are using IntelliJ IDEA (or some other IDE with buildin watchers), make sure that IDE watchers do not conflict with webpack watcher. Try to edit the file with Text Editor, if it will trigger the watch, then the problem is IDE.

- [Increasing inotify watchers may help](https://github.com/angular/angular-cli/issues/8313#issuecomment-362728855 )
```
echo fs.inotify.max_user_watches=524288 | sudo tee /etc/sysctl.d/40-max-user-watches.conf && sudo sysctl --system
```

# Jenkins
  - http://ndp-jenkins-master.persgroep.digital:8080/view/Profiles/job/APP%20Recruiter%20Frontend/
