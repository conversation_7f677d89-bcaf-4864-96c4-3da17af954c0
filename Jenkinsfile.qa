pipeline {
    agent {
        node {
            label 'ndp'
            customWorkspace "$JENKINS_HOME/workspace/APP Recruiter Frontend"
        }
    }
    options {
        buildDiscarder(logRotator(daysToKeepStr: '1'))
        disableConcurrentBuilds()
    }
    triggers {
        cron("TZ=Europe/Amsterdam\nH 6 * * 1-5")
    }
    tools {
        nodejs 'node'
    }
    stages {
        stage('Analysis') {
            steps {
                sh "./gradlew fixLCOVPaths sonarqube"
            }
        }
    }
}
