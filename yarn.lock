# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.2.0":
  version "2.2.1"
  resolved "http://npm-registry.persgroep.digital/@ampproject/remapping/-/remapping-2.2.1.tgz#99e8e11851128b8702cd57c33684f1d0f260b630"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@angular-devkit/architect@0.13.10":
  version "0.13.10"
  resolved "http://npm-registry.persgroep.digital/@angular-devkit/architect/-/architect-0.13.10.tgz#947d3f588e15cc2df3736ff841977a74832a9db7"
  dependencies:
    "@angular-devkit/core" "7.3.10"
    rxjs "6.3.3"

"@angular-devkit/build-angular@~0.13.3":
  version "0.13.10"
  resolved "http://npm-registry.persgroep.digital/@angular-devkit/build-angular/-/build-angular-0.13.10.tgz#bdcace32679e5b873709e4f17bfdf5e71168c735"
  dependencies:
    "@angular-devkit/architect" "0.13.10"
    "@angular-devkit/build-optimizer" "0.13.10"
    "@angular-devkit/build-webpack" "0.13.10"
    "@angular-devkit/core" "7.3.10"
    "@ngtools/webpack" "7.3.10"
    ajv "6.9.1"
    autoprefixer "9.4.6"
    circular-dependency-plugin "5.0.2"
    clean-css "4.2.1"
    copy-webpack-plugin "5.1.1"
    file-loader "3.0.1"
    glob "7.1.3"
    istanbul-instrumenter-loader "3.0.1"
    karma-source-map-support "1.3.0"
    less "3.9.0"
    less-loader "4.1.0"
    license-webpack-plugin "2.1.0"
    loader-utils "1.2.3"
    mini-css-extract-plugin "0.5.0"
    minimatch "3.0.4"
    open "6.0.0"
    parse5 "4.0.0"
    postcss "7.0.14"
    postcss-import "12.0.1"
    postcss-loader "3.0.0"
    raw-loader "1.0.0"
    rxjs "6.3.3"
    sass-loader "7.1.0"
    semver "5.6.0"
    source-map-loader "0.2.4"
    source-map-support "0.5.10"
    speed-measure-webpack-plugin "1.3.1"
    stats-webpack-plugin "0.7.0"
    style-loader "0.23.1"
    stylus "0.54.5"
    stylus-loader "3.0.2"
    terser-webpack-plugin "1.4.3"
    tree-kill "1.2.2"
    webpack "4.29.0"
    webpack-dev-middleware "3.5.1"
    webpack-dev-server "3.1.14"
    webpack-merge "4.2.1"
    webpack-sources "1.3.0"
    webpack-subresource-integrity "1.1.0-rc.6"
  optionalDependencies:
    node-sass "4.13.1"

"@angular-devkit/build-optimizer@0.13.10":
  version "0.13.10"
  resolved "http://npm-registry.persgroep.digital/@angular-devkit/build-optimizer/-/build-optimizer-0.13.10.tgz#c4c23ec8283a2cd9587246337dad3ce7a2114cf2"
  dependencies:
    loader-utils "1.2.3"
    source-map "0.5.6"
    typescript "3.2.4"
    webpack-sources "1.3.0"

"@angular-devkit/build-webpack@0.13.10":
  version "0.13.10"
  resolved "http://npm-registry.persgroep.digital/@angular-devkit/build-webpack/-/build-webpack-0.13.10.tgz#566db98ca04a42bf46bbd0d22528a51bd9b4cd4c"
  dependencies:
    "@angular-devkit/architect" "0.13.10"
    "@angular-devkit/core" "7.3.10"
    rxjs "6.3.3"

"@angular-devkit/core@7.3.10":
  version "7.3.10"
  resolved "http://npm-registry.persgroep.digital/@angular-devkit/core/-/core-7.3.10.tgz#35f34b54de35c7b3ffec37dd9bc88e369ebc29ea"
  dependencies:
    ajv "6.9.1"
    chokidar "2.0.4"
    fast-json-stable-stringify "2.0.0"
    rxjs "6.3.3"
    source-map "0.7.3"

"@angular-devkit/schematics@7.3.10":
  version "7.3.10"
  resolved "http://npm-registry.persgroep.digital/@angular-devkit/schematics/-/schematics-7.3.10.tgz#2511595093c7d742b5d07acd40dccc6309e15817"
  dependencies:
    "@angular-devkit/core" "7.3.10"
    rxjs "6.3.3"

"@angular/cli@^7.3.3":
  version "7.3.10"
  resolved "http://npm-registry.persgroep.digital/@angular/cli/-/cli-7.3.10.tgz#19125713fd51bb6dcfa7f681f6a3facd6d930589"
  dependencies:
    "@angular-devkit/architect" "0.13.10"
    "@angular-devkit/core" "7.3.10"
    "@angular-devkit/schematics" "7.3.10"
    "@schematics/angular" "7.3.10"
    "@schematics/update" "0.13.10"
    "@yarnpkg/lockfile" "1.1.0"
    ini "1.3.5"
    inquirer "6.2.1"
    npm-package-arg "6.1.0"
    open "6.0.0"
    pacote "9.4.0"
    semver "5.6.0"
    symbol-observable "1.2.0"

"@angular/common@^7.2.6":
  version "7.2.16"
  resolved "http://npm-registry.persgroep.digital/@angular/common/-/common-7.2.16.tgz#72127b5ed7d75a400c58b9bd6c2cd691f47c8096"
  dependencies:
    tslib "^1.9.0"

"@angular/compiler-cli@^7.2.6":
  version "7.2.16"
  resolved "http://npm-registry.persgroep.digital/@angular/compiler-cli/-/compiler-cli-7.2.16.tgz#7936acb5bcc2e53739c09b475de6eb3b27fea9c9"
  dependencies:
    canonical-path "1.0.0"
    chokidar "^2.1.1"
    convert-source-map "^1.5.1"
    dependency-graph "^0.7.2"
    magic-string "^0.25.0"
    minimist "^1.2.0"
    reflect-metadata "^0.1.2"
    shelljs "^0.8.1"
    source-map "^0.6.1"
    tslib "^1.9.0"
    yargs "13.1.0"

"@angular/compiler@^7.2.6":
  version "7.2.16"
  resolved "http://npm-registry.persgroep.digital/@angular/compiler/-/compiler-7.2.16.tgz#081f58e9f50399ff0eef346fff37c9c6201cda8d"
  dependencies:
    tslib "^1.9.0"

"@angular/core@^7.2.6":
  version "7.2.16"
  resolved "http://npm-registry.persgroep.digital/@angular/core/-/core-7.2.16.tgz#317f707bc75ca5a90ba470c34e49e6adb7026e85"
  dependencies:
    tslib "^1.9.0"

"@angular/forms@^7.2.6":
  version "7.2.16"
  resolved "http://npm-registry.persgroep.digital/@angular/forms/-/forms-7.2.16.tgz#18769a2203edf87a1cc279dc2145da62b82f144e"
  dependencies:
    tslib "^1.9.0"

"@angular/http@^7.2.6":
  version "7.2.16"
  resolved "http://npm-registry.persgroep.digital/@angular/http/-/http-7.2.16.tgz#f05337dd56faa86ce05b0dc378218a00fb337040"
  dependencies:
    tslib "^1.9.0"

"@angular/platform-browser-dynamic@^7.2.6":
  version "7.2.16"
  resolved "http://npm-registry.persgroep.digital/@angular/platform-browser-dynamic/-/platform-browser-dynamic-7.2.16.tgz#57f7f11cece3a3109a821d20b3456d94419718d4"
  dependencies:
    tslib "^1.9.0"

"@angular/platform-browser@^7.2.6":
  version "7.2.16"
  resolved "http://npm-registry.persgroep.digital/@angular/platform-browser/-/platform-browser-7.2.16.tgz#c14d47108e47ac6b2ebbad06fdd69f952b29df32"
  dependencies:
    tslib "^1.9.0"

"@angular/pwa@^0.13.3":
  version "0.13.10"
  resolved "http://npm-registry.persgroep.digital/@angular/pwa/-/pwa-0.13.10.tgz#1010cad116d544469ae7d6822079491d9c3da824"
  dependencies:
    "@angular-devkit/core" "7.3.10"
    "@angular-devkit/schematics" "7.3.10"
    "@schematics/angular" "7.3.10"
    parse5-html-rewriting-stream "5.1.0"
    rxjs "6.3.3"

"@angular/router@^7.2.6":
  version "7.2.16"
  resolved "http://npm-registry.persgroep.digital/@angular/router/-/router-7.2.16.tgz#7518a387ff959c13ba2648ce52e0bcd48f3292ca"
  dependencies:
    tslib "^1.9.0"

"@angular/service-worker@^7.2.6":
  version "7.2.16"
  resolved "http://npm-registry.persgroep.digital/@angular/service-worker/-/service-worker-7.2.16.tgz#f9c42b3b26b280a00cdb71937470e6048fb9c972"
  dependencies:
    tslib "^1.9.0"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.18.6", "@babel/code-frame@^7.21.4":
  version "7.21.4"
  resolved "http://npm-registry.persgroep.digital/@babel/code-frame/-/code-frame-7.21.4.tgz#d0fa9e4413aca81f2b23b9442797bda1826edb39"
  dependencies:
    "@babel/highlight" "^7.18.6"

"@babel/compat-data@^7.20.5", "@babel/compat-data@^7.21.5":
  version "7.21.7"
  resolved "http://npm-registry.persgroep.digital/@babel/compat-data/-/compat-data-7.21.7.tgz#61caffb60776e49a57ba61a88f02bedd8714f6bc"

"@babel/core@7.1.0":
  version "7.1.0"
  resolved "http://npm-registry.persgroep.digital/@babel/core/-/core-7.1.0.tgz#08958f1371179f62df6966d8a614003d11faeb04"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/generator" "^7.0.0"
    "@babel/helpers" "^7.1.0"
    "@babel/parser" "^7.1.0"
    "@babel/template" "^7.1.0"
    "@babel/traverse" "^7.1.0"
    "@babel/types" "^7.0.0"
    convert-source-map "^1.1.0"
    debug "^3.1.0"
    json5 "^0.5.0"
    lodash "^4.17.10"
    resolve "^1.3.2"
    semver "^5.4.1"
    source-map "^0.5.0"

"@babel/core@>=7.2.2":
  version "7.21.8"
  resolved "http://npm-registry.persgroep.digital/@babel/core/-/core-7.21.8.tgz#2a8c7f0f53d60100ba4c32470ba0281c92aa9aa4"
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.21.4"
    "@babel/generator" "^7.21.5"
    "@babel/helper-compilation-targets" "^7.21.5"
    "@babel/helper-module-transforms" "^7.21.5"
    "@babel/helpers" "^7.21.5"
    "@babel/parser" "^7.21.8"
    "@babel/template" "^7.20.7"
    "@babel/traverse" "^7.21.5"
    "@babel/types" "^7.21.5"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.2"
    semver "^6.3.0"

"@babel/generator@^7.0.0", "@babel/generator@^7.21.5", "@babel/generator@^7.4.0":
  version "7.21.5"
  resolved "http://npm-registry.persgroep.digital/@babel/generator/-/generator-7.21.5.tgz#c0c0e5449504c7b7de8236d99338c3e2a340745f"
  dependencies:
    "@babel/types" "^7.21.5"
    "@jridgewell/gen-mapping" "^0.3.2"
    "@jridgewell/trace-mapping" "^0.3.17"
    jsesc "^2.5.1"

"@babel/helper-annotate-as-pure@^7.18.6":
  version "7.18.6"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.18.6.tgz#eaa49f6f80d5a33f9a5dd2276e6d6e451be0a6bb"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.18.6":
  version "7.21.5"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.21.5.tgz#817f73b6c59726ab39f6ba18c234268a519e5abb"
  dependencies:
    "@babel/types" "^7.21.5"

"@babel/helper-compilation-targets@^7.18.9", "@babel/helper-compilation-targets@^7.20.7", "@babel/helper-compilation-targets@^7.21.5":
  version "7.21.5"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-compilation-targets/-/helper-compilation-targets-7.21.5.tgz#631e6cc784c7b660417421349aac304c94115366"
  dependencies:
    "@babel/compat-data" "^7.21.5"
    "@babel/helper-validator-option" "^7.21.0"
    browserslist "^4.21.3"
    lru-cache "^5.1.1"
    semver "^6.3.0"

"@babel/helper-create-regexp-features-plugin@^7.18.6":
  version "7.21.8"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.21.8.tgz#a7886f61c2e29e21fd4aaeaf1e473deba6b571dc"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    regexpu-core "^5.3.1"
    semver "^6.3.0"

"@babel/helper-environment-visitor@^7.18.9", "@babel/helper-environment-visitor@^7.21.5":
  version "7.21.5"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-environment-visitor/-/helper-environment-visitor-7.21.5.tgz#c769afefd41d171836f7cb63e295bedf689d48ba"

"@babel/helper-function-name@^7.1.0", "@babel/helper-function-name@^7.18.9", "@babel/helper-function-name@^7.19.0", "@babel/helper-function-name@^7.21.0":
  version "7.21.0"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-function-name/-/helper-function-name-7.21.0.tgz#d552829b10ea9f120969304023cd0645fa00b1b4"
  dependencies:
    "@babel/template" "^7.20.7"
    "@babel/types" "^7.21.0"

"@babel/helper-hoist-variables@^7.18.6":
  version "7.18.6"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-hoist-variables/-/helper-hoist-variables-7.18.6.tgz#d4d2c8fb4baeaa5c68b99cc8245c56554f926678"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-member-expression-to-functions@^7.0.0", "@babel/helper-member-expression-to-functions@^7.21.5":
  version "7.21.5"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.21.5.tgz#3b1a009af932e586af77c1030fba9ee0bde396c0"
  dependencies:
    "@babel/types" "^7.21.5"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.18.6", "@babel/helper-module-imports@^7.21.4":
  version "7.21.4"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-module-imports/-/helper-module-imports-7.21.4.tgz#ac88b2f76093637489e718a90cec6cf8a9b029af"
  dependencies:
    "@babel/types" "^7.21.4"

"@babel/helper-module-transforms@^7.18.6", "@babel/helper-module-transforms@^7.20.11", "@babel/helper-module-transforms@^7.21.5":
  version "7.21.5"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-module-transforms/-/helper-module-transforms-7.21.5.tgz#d937c82e9af68d31ab49039136a222b17ac0b420"
  dependencies:
    "@babel/helper-environment-visitor" "^7.21.5"
    "@babel/helper-module-imports" "^7.21.4"
    "@babel/helper-simple-access" "^7.21.5"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/helper-validator-identifier" "^7.19.1"
    "@babel/template" "^7.20.7"
    "@babel/traverse" "^7.21.5"
    "@babel/types" "^7.21.5"

"@babel/helper-optimise-call-expression@^7.0.0", "@babel/helper-optimise-call-expression@^7.18.6":
  version "7.18.6"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.18.6.tgz#9369aa943ee7da47edab2cb4e838acf09d290ffe"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.18.9", "@babel/helper-plugin-utils@^7.19.0", "@babel/helper-plugin-utils@^7.20.2", "@babel/helper-plugin-utils@^7.21.5", "@babel/helper-plugin-utils@^7.8.0":
  version "7.21.5"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-plugin-utils/-/helper-plugin-utils-7.21.5.tgz#345f2377d05a720a4e5ecfa39cbf4474a4daed56"

"@babel/helper-remap-async-to-generator@^7.18.9":
  version "7.18.9"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.18.9.tgz#997458a0e3357080e54e1d79ec347f8a8cd28519"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-wrap-function" "^7.18.9"
    "@babel/types" "^7.18.9"

"@babel/helper-replace-supers@^7.1.0", "@babel/helper-replace-supers@^7.18.6", "@babel/helper-replace-supers@^7.20.7":
  version "7.21.5"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-replace-supers/-/helper-replace-supers-7.21.5.tgz#a6ad005ba1c7d9bc2973dfde05a1bba7065dde3c"
  dependencies:
    "@babel/helper-environment-visitor" "^7.21.5"
    "@babel/helper-member-expression-to-functions" "^7.21.5"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/template" "^7.20.7"
    "@babel/traverse" "^7.21.5"
    "@babel/types" "^7.21.5"

"@babel/helper-simple-access@^7.21.5":
  version "7.21.5"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-simple-access/-/helper-simple-access-7.21.5.tgz#d697a7971a5c39eac32c7e63c0921c06c8a249ee"
  dependencies:
    "@babel/types" "^7.21.5"

"@babel/helper-skip-transparent-expression-wrappers@^7.20.0":
  version "7.20.0"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.20.0.tgz#fbe4c52f60518cab8140d77101f0e63a8a230684"
  dependencies:
    "@babel/types" "^7.20.0"

"@babel/helper-split-export-declaration@^7.18.6":
  version "7.18.6"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.18.6.tgz#7367949bc75b20c6d5a5d4a97bba2824ae8ef075"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-string-parser@^7.21.5":
  version "7.21.5"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-string-parser/-/helper-string-parser-7.21.5.tgz#2b3eea65443c6bdc31c22d037c65f6d323b6b2bd"

"@babel/helper-validator-identifier@^7.18.6", "@babel/helper-validator-identifier@^7.19.1":
  version "7.19.1"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz#7eea834cf32901ffdc1a7ee555e2f9c27e249ca2"

"@babel/helper-validator-option@^7.21.0":
  version "7.21.0"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-validator-option/-/helper-validator-option-7.21.0.tgz#8224c7e13ace4bafdc4004da2cf064ef42673180"

"@babel/helper-wrap-function@^7.18.9":
  version "7.20.5"
  resolved "http://npm-registry.persgroep.digital/@babel/helper-wrap-function/-/helper-wrap-function-7.20.5.tgz#75e2d84d499a0ab3b31c33bcfe59d6b8a45f62e3"
  dependencies:
    "@babel/helper-function-name" "^7.19.0"
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.20.5"
    "@babel/types" "^7.20.5"

"@babel/helpers@^7.1.0", "@babel/helpers@^7.21.5":
  version "7.21.5"
  resolved "http://npm-registry.persgroep.digital/@babel/helpers/-/helpers-7.21.5.tgz#5bac66e084d7a4d2d9696bdf0175a93f7fb63c08"
  dependencies:
    "@babel/template" "^7.20.7"
    "@babel/traverse" "^7.21.5"
    "@babel/types" "^7.21.5"

"@babel/highlight@^7.18.6":
  version "7.18.6"
  resolved "http://npm-registry.persgroep.digital/@babel/highlight/-/highlight-7.18.6.tgz#81158601e93e2563795adcbfbdf5d64be3f2ecdf"
  dependencies:
    "@babel/helper-validator-identifier" "^7.18.6"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.1.0", "@babel/parser@^7.20.7", "@babel/parser@^7.21.5", "@babel/parser@^7.21.8", "@babel/parser@^7.4.3":
  version "7.21.8"
  resolved "http://npm-registry.persgroep.digital/@babel/parser/-/parser-7.21.8.tgz#642af7d0333eab9c0ad70b14ac5e76dbde7bfdf8"

"@babel/plugin-proposal-async-generator-functions@^7.1.0":
  version "7.20.7"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.20.7.tgz#bfb7276d2d573cb67ba379984a2334e262ba5326"
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-remap-async-to-generator" "^7.18.9"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-proposal-class-properties@7.1.0":
  version "7.1.0"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.1.0.tgz#9af01856b1241db60ec8838d84691aa0bd1e8df4"
  dependencies:
    "@babel/helper-function-name" "^7.1.0"
    "@babel/helper-member-expression-to-functions" "^7.0.0"
    "@babel/helper-optimise-call-expression" "^7.0.0"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-replace-supers" "^7.1.0"
    "@babel/plugin-syntax-class-properties" "^7.0.0"

"@babel/plugin-proposal-json-strings@^7.0.0":
  version "7.18.6"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.18.6.tgz#7e8788c1811c393aff762817e7dbf1ebd0c05f0b"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-proposal-object-rest-spread@7.0.0":
  version "7.0.0"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.0.0.tgz#9a17b547f64d0676b6c9cecd4edf74a82ab85e7e"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-object-rest-spread" "^7.0.0"

"@babel/plugin-proposal-object-rest-spread@^7.0.0":
  version "7.20.7"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.20.7.tgz#aa662940ef425779c75534a5c41e9d936edc390a"
  dependencies:
    "@babel/compat-data" "^7.20.5"
    "@babel/helper-compilation-targets" "^7.20.7"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.20.7"

"@babel/plugin-proposal-optional-catch-binding@^7.0.0":
  version "7.18.6"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.18.6.tgz#f9400d0e6a3ea93ba9ef70b09e72dd6da638a2cb"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-unicode-property-regex@^7.0.0":
  version "7.18.6"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.18.6.tgz#af613d2cd5e643643b65cded64207b15c85cb78e"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-syntax-async-generators@^7.0.0", "@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz#a983fb1aeb2ec3f6ed042a210f640e90e786fe0d"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.0.0":
  version "7.12.13"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz#b5c987274c4a3a82b89714796931a6b53544ae10"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz#01ca21b668cd8218c9e640cb6dd88c5412b2c96a"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.21.4":
  version "7.21.4"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.21.4.tgz#f264ed7bf40ffc9ec239edabc17a50c4f5b6fea2"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-syntax-object-rest-spread@^7.0.0", "@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz#60e225edcbd98a640332a2e72dd3e66f1af55871"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.0.0", "@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz#6111a265bcfb020eb9efd0fdfd7d26402b9ed6c1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-transform-arrow-functions@^7.0.0":
  version "7.21.5"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.21.5.tgz#9bb42a53de447936a57ba256fbf537fc312b6929"
  dependencies:
    "@babel/helper-plugin-utils" "^7.21.5"

"@babel/plugin-transform-async-to-generator@^7.1.0":
  version "7.20.7"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.20.7.tgz#dfee18623c8cb31deb796aa3ca84dda9cea94354"
  dependencies:
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-remap-async-to-generator" "^7.18.9"

"@babel/plugin-transform-block-scoped-functions@^7.0.0":
  version "7.18.6"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.18.6.tgz#9187bf4ba302635b9d70d986ad70f038726216a8"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-block-scoping@^7.0.0":
  version "7.21.0"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.21.0.tgz#e737b91037e5186ee16b76e7ae093358a5634f02"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-classes@^7.1.0":
  version "7.21.0"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-classes/-/plugin-transform-classes-7.21.0.tgz#f469d0b07a4c5a7dbb21afad9e27e57b47031665"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-compilation-targets" "^7.20.7"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.21.0"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-replace-supers" "^7.20.7"
    "@babel/helper-split-export-declaration" "^7.18.6"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.0.0":
  version "7.21.5"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.21.5.tgz#3a2d8bb771cd2ef1cd736435f6552fe502e11b44"
  dependencies:
    "@babel/helper-plugin-utils" "^7.21.5"
    "@babel/template" "^7.20.7"

"@babel/plugin-transform-destructuring@^7.0.0":
  version "7.21.3"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.21.3.tgz#73b46d0fd11cd6ef57dea8a381b1215f4959d401"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-dotall-regex@^7.0.0":
  version "7.18.6"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.18.6.tgz#b286b3e7aae6c7b861e45bed0a2fafd6b1a4fef8"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-duplicate-keys@^7.0.0":
  version "7.18.9"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.18.9.tgz#687f15ee3cdad6d85191eb2a372c4528eaa0ae0e"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-exponentiation-operator@^7.1.0":
  version "7.18.6"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.18.6.tgz#421c705f4521888c65e91fdd1af951bfefd4dacd"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-for-of@^7.0.0":
  version "7.21.5"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.21.5.tgz#e890032b535f5a2e237a18535f56a9fdaa7b83fc"
  dependencies:
    "@babel/helper-plugin-utils" "^7.21.5"

"@babel/plugin-transform-function-name@^7.1.0":
  version "7.18.9"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.18.9.tgz#cc354f8234e62968946c61a46d6365440fc764e0"
  dependencies:
    "@babel/helper-compilation-targets" "^7.18.9"
    "@babel/helper-function-name" "^7.18.9"
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-literals@^7.0.0":
  version "7.18.9"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-literals/-/plugin-transform-literals-7.18.9.tgz#72796fdbef80e56fba3c6a699d54f0de557444bc"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-modules-amd@^7.1.0":
  version "7.20.11"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.20.11.tgz#3daccca8e4cc309f03c3a0c4b41dc4b26f55214a"
  dependencies:
    "@babel/helper-module-transforms" "^7.20.11"
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-modules-commonjs@^7.1.0":
  version "7.21.5"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.21.5.tgz#d69fb947eed51af91de82e4708f676864e5e47bc"
  dependencies:
    "@babel/helper-module-transforms" "^7.21.5"
    "@babel/helper-plugin-utils" "^7.21.5"
    "@babel/helper-simple-access" "^7.21.5"

"@babel/plugin-transform-modules-systemjs@^7.0.0":
  version "7.20.11"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.20.11.tgz#467ec6bba6b6a50634eea61c9c232654d8a4696e"
  dependencies:
    "@babel/helper-hoist-variables" "^7.18.6"
    "@babel/helper-module-transforms" "^7.20.11"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-validator-identifier" "^7.19.1"

"@babel/plugin-transform-modules-umd@^7.1.0":
  version "7.18.6"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.18.6.tgz#81d3832d6034b75b54e62821ba58f28ed0aab4b9"
  dependencies:
    "@babel/helper-module-transforms" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-new-target@^7.0.0":
  version "7.18.6"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.18.6.tgz#d128f376ae200477f37c4ddfcc722a8a1b3246a8"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-object-super@^7.1.0":
  version "7.18.6"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.18.6.tgz#fb3c6ccdd15939b6ff7939944b51971ddc35912c"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/helper-replace-supers" "^7.18.6"

"@babel/plugin-transform-parameters@^7.1.0", "@babel/plugin-transform-parameters@^7.20.7":
  version "7.21.3"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.21.3.tgz#18fc4e797cf6d6d972cb8c411dbe8a809fa157db"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-react-display-name@^7.0.0":
  version "7.18.6"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.18.6.tgz#8b1125f919ef36ebdfff061d664e266c666b9415"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-react-jsx-self@^7.0.0":
  version "7.21.0"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.21.0.tgz#ec98d4a9baafc5a1eb398da4cf94afbb40254a54"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-react-jsx-source@^7.0.0":
  version "7.19.6"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.19.6.tgz#88578ae8331e5887e8ce28e4c9dc83fb29da0b86"
  dependencies:
    "@babel/helper-plugin-utils" "^7.19.0"

"@babel/plugin-transform-react-jsx@^7.0.0":
  version "7.21.5"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.21.5.tgz#bd98f3b429688243e4fa131fe1cbb2ef31ce6f38"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-module-imports" "^7.21.4"
    "@babel/helper-plugin-utils" "^7.21.5"
    "@babel/plugin-syntax-jsx" "^7.21.4"
    "@babel/types" "^7.21.5"

"@babel/plugin-transform-regenerator@^7.0.0":
  version "7.21.5"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.21.5.tgz#576c62f9923f94bcb1c855adc53561fd7913724e"
  dependencies:
    "@babel/helper-plugin-utils" "^7.21.5"
    regenerator-transform "^0.15.1"

"@babel/plugin-transform-runtime@7.1.0":
  version "7.1.0"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.1.0.tgz#9f76920d42551bb577e2dc594df229b5f7624b63"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/helper-plugin-utils" "^7.0.0"
    resolve "^1.8.1"
    semver "^5.5.1"

"@babel/plugin-transform-shorthand-properties@^7.0.0":
  version "7.18.6"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.18.6.tgz#6d6df7983d67b195289be24909e3f12a8f664dc9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-spread@^7.0.0":
  version "7.20.7"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-spread/-/plugin-transform-spread-7.20.7.tgz#c2d83e0b99d3bf83e07b11995ee24bf7ca09401e"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.20.0"

"@babel/plugin-transform-sticky-regex@^7.0.0":
  version "7.18.6"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.18.6.tgz#c6706eb2b1524028e317720339583ad0f444adcc"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-template-literals@^7.0.0":
  version "7.18.9"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.18.9.tgz#04ec6f10acdaa81846689d63fae117dd9c243a5e"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-typeof-symbol@^7.0.0":
  version "7.18.9"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.18.9.tgz#c8cea68263e45addcd6afc9091429f80925762c0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-unicode-regex@^7.0.0":
  version "7.18.6"
  resolved "http://npm-registry.persgroep.digital/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.18.6.tgz#194317225d8c201bbae103364ffe9e2cea36cdca"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/polyfill@^7.2.3":
  version "7.12.1"
  resolved "http://npm-registry.persgroep.digital/@babel/polyfill/-/polyfill-7.12.1.tgz#1f2d6371d1261bbd961f3c5d5909150e12d0bd96"
  dependencies:
    core-js "^2.6.5"
    regenerator-runtime "^0.13.4"

"@babel/preset-env@7.1.0":
  version "7.1.0"
  resolved "http://npm-registry.persgroep.digital/@babel/preset-env/-/preset-env-7.1.0.tgz#e67ea5b0441cfeab1d6f41e9b5c79798800e8d11"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-async-generator-functions" "^7.1.0"
    "@babel/plugin-proposal-json-strings" "^7.0.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.0.0"
    "@babel/plugin-proposal-optional-catch-binding" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.0.0"
    "@babel/plugin-syntax-async-generators" "^7.0.0"
    "@babel/plugin-syntax-object-rest-spread" "^7.0.0"
    "@babel/plugin-syntax-optional-catch-binding" "^7.0.0"
    "@babel/plugin-transform-arrow-functions" "^7.0.0"
    "@babel/plugin-transform-async-to-generator" "^7.1.0"
    "@babel/plugin-transform-block-scoped-functions" "^7.0.0"
    "@babel/plugin-transform-block-scoping" "^7.0.0"
    "@babel/plugin-transform-classes" "^7.1.0"
    "@babel/plugin-transform-computed-properties" "^7.0.0"
    "@babel/plugin-transform-destructuring" "^7.0.0"
    "@babel/plugin-transform-dotall-regex" "^7.0.0"
    "@babel/plugin-transform-duplicate-keys" "^7.0.0"
    "@babel/plugin-transform-exponentiation-operator" "^7.1.0"
    "@babel/plugin-transform-for-of" "^7.0.0"
    "@babel/plugin-transform-function-name" "^7.1.0"
    "@babel/plugin-transform-literals" "^7.0.0"
    "@babel/plugin-transform-modules-amd" "^7.1.0"
    "@babel/plugin-transform-modules-commonjs" "^7.1.0"
    "@babel/plugin-transform-modules-systemjs" "^7.0.0"
    "@babel/plugin-transform-modules-umd" "^7.1.0"
    "@babel/plugin-transform-new-target" "^7.0.0"
    "@babel/plugin-transform-object-super" "^7.1.0"
    "@babel/plugin-transform-parameters" "^7.1.0"
    "@babel/plugin-transform-regenerator" "^7.0.0"
    "@babel/plugin-transform-shorthand-properties" "^7.0.0"
    "@babel/plugin-transform-spread" "^7.0.0"
    "@babel/plugin-transform-sticky-regex" "^7.0.0"
    "@babel/plugin-transform-template-literals" "^7.0.0"
    "@babel/plugin-transform-typeof-symbol" "^7.0.0"
    "@babel/plugin-transform-unicode-regex" "^7.0.0"
    browserslist "^4.1.0"
    invariant "^2.2.2"
    js-levenshtein "^1.1.3"
    semver "^5.3.0"

"@babel/preset-react@7.0.0":
  version "7.0.0"
  resolved "http://npm-registry.persgroep.digital/@babel/preset-react/-/preset-react-7.0.0.tgz#e86b4b3d99433c7b3e9e91747e2653958bc6b3c0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-transform-react-display-name" "^7.0.0"
    "@babel/plugin-transform-react-jsx" "^7.0.0"
    "@babel/plugin-transform-react-jsx-self" "^7.0.0"
    "@babel/plugin-transform-react-jsx-source" "^7.0.0"

"@babel/regjsgen@^0.8.0":
  version "0.8.0"
  resolved "http://npm-registry.persgroep.digital/@babel/regjsgen/-/regjsgen-0.8.0.tgz#f0ba69b075e1f05fb2825b7fad991e7adbb18310"

"@babel/runtime@7.0.0":
  version "7.0.0"
  resolved "http://npm-registry.persgroep.digital/@babel/runtime/-/runtime-7.0.0.tgz#adeb78fedfc855aa05bc041640f3f6f98e85424c"
  dependencies:
    regenerator-runtime "^0.12.0"

"@babel/runtime@^7.12.5", "@babel/runtime@^7.14.0", "@babel/runtime@^7.8.4":
  version "7.21.5"
  resolved "http://npm-registry.persgroep.digital/@babel/runtime/-/runtime-7.21.5.tgz#8492dddda9644ae3bda3b45eabe87382caee7200"
  dependencies:
    regenerator-runtime "^0.13.11"

"@babel/template@^7.1.0", "@babel/template@^7.18.10", "@babel/template@^7.20.7", "@babel/template@^7.4.0":
  version "7.20.7"
  resolved "http://npm-registry.persgroep.digital/@babel/template/-/template-7.20.7.tgz#a15090c2839a83b02aa996c0b4994005841fd5a8"
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"

"@babel/traverse@^7.1.0", "@babel/traverse@^7.20.5", "@babel/traverse@^7.21.5", "@babel/traverse@^7.4.3":
  version "7.21.5"
  resolved "http://npm-registry.persgroep.digital/@babel/traverse/-/traverse-7.21.5.tgz#ad22361d352a5154b498299d523cf72998a4b133"
  dependencies:
    "@babel/code-frame" "^7.21.4"
    "@babel/generator" "^7.21.5"
    "@babel/helper-environment-visitor" "^7.21.5"
    "@babel/helper-function-name" "^7.21.0"
    "@babel/helper-hoist-variables" "^7.18.6"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/parser" "^7.21.5"
    "@babel/types" "^7.21.5"
    debug "^4.1.0"
    globals "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.18.6", "@babel/types@^7.18.9", "@babel/types@^7.20.0", "@babel/types@^7.20.5", "@babel/types@^7.20.7", "@babel/types@^7.21.0", "@babel/types@^7.21.4", "@babel/types@^7.21.5", "@babel/types@^7.4.0":
  version "7.21.5"
  resolved "http://npm-registry.persgroep.digital/@babel/types/-/types-7.21.5.tgz#18dfbd47c39d3904d5db3d3dc2cc80bedb60e5b6"
  dependencies:
    "@babel/helper-string-parser" "^7.21.5"
    "@babel/helper-validator-identifier" "^7.19.1"
    to-fast-properties "^2.0.0"

"@cypress/browserify-preprocessor@^1.1.2":
  version "1.1.2"
  resolved "http://npm-registry.persgroep.digital/@cypress/browserify-preprocessor/-/browserify-preprocessor-1.1.2.tgz#5c82f9552564a6fb3b9993b50ef58fdf9f4a2148"
  dependencies:
    "@babel/core" "7.1.0"
    "@babel/plugin-proposal-class-properties" "7.1.0"
    "@babel/plugin-proposal-object-rest-spread" "7.0.0"
    "@babel/plugin-transform-runtime" "7.1.0"
    "@babel/preset-env" "7.1.0"
    "@babel/preset-react" "7.0.0"
    "@babel/runtime" "7.0.0"
    babel-plugin-add-module-exports "1.0.0"
    babelify "10.0.0"
    bluebird "3.5.2"
    browserify "16.2.2"
    cjsxify "0.3.0"
    debug "4.0.1"
    fs-extra "7.0.0"
    watchify "3.11.0"

"@cypress/listr-verbose-renderer@0.4.1":
  version "0.4.1"
  resolved "http://npm-registry.persgroep.digital/@cypress/listr-verbose-renderer/-/listr-verbose-renderer-0.4.1.tgz#a77492f4b11dcc7c446a34b3e28721afd33c642a"
  dependencies:
    chalk "^1.1.3"
    cli-cursor "^1.0.2"
    date-fns "^1.27.2"
    figures "^1.7.0"

"@cypress/xvfb@1.2.4":
  version "1.2.4"
  resolved "http://npm-registry.persgroep.digital/@cypress/xvfb/-/xvfb-1.2.4.tgz#2daf42e8275b39f4aa53c14214e557bd14e7748a"
  dependencies:
    debug "^3.1.0"
    lodash.once "^4.1.1"

"@dpgr/pdf-forger@1.0.8":
  version "1.0.8"
  resolved "http://npm-registry.persgroep.digital/@dpgr/pdf-forger/-/pdf-forger-1.0.8.tgz#ae4a93653a246d86b022fac8f4e4ec726f1efddc"
  dependencies:
    "@types/markdown-it" "^12.2.3"
    jspdf "^2.5.1"
    jspdf-autotable "^3.5.28"
    markdown-it "^13.0.1"
    markdown-it-plain-text "^0.3.0"

"@jridgewell/gen-mapping@^0.3.0", "@jridgewell/gen-mapping@^0.3.2":
  version "0.3.3"
  resolved "http://npm-registry.persgroep.digital/@jridgewell/gen-mapping/-/gen-mapping-0.3.3.tgz#7e02e6eb5df901aaedb08514203b096614024098"
  dependencies:
    "@jridgewell/set-array" "^1.0.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@3.1.0":
  version "3.1.0"
  resolved "http://npm-registry.persgroep.digital/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz#2203b118c157721addfe69d47b70465463066d78"

"@jridgewell/set-array@^1.0.1":
  version "1.1.2"
  resolved "http://npm-registry.persgroep.digital/@jridgewell/set-array/-/set-array-1.1.2.tgz#7c6cf998d6d20b914c0a55a91ae928ff25965e72"

"@jridgewell/sourcemap-codec@1.4.14":
  version "1.4.14"
  resolved "http://npm-registry.persgroep.digital/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz#add4c98d341472a289190b424efbdb096991bb24"

"@jridgewell/sourcemap-codec@^1.4.10":
  version "1.4.15"
  resolved "http://npm-registry.persgroep.digital/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz#d7c6e6755c78567a951e04ab52ef0fd26de59f32"

"@jridgewell/trace-mapping@^0.3.17", "@jridgewell/trace-mapping@^0.3.9":
  version "0.3.18"
  resolved "http://npm-registry.persgroep.digital/@jridgewell/trace-mapping/-/trace-mapping-0.3.18.tgz#25783b2086daf6ff1dcb53c9249ae480e4dd4cd6"
  dependencies:
    "@jridgewell/resolve-uri" "3.1.0"
    "@jridgewell/sourcemap-codec" "1.4.14"

"@mrmlnc/readdir-enhanced@^2.2.1":
  version "2.2.1"
  resolved "http://npm-registry.persgroep.digital/@mrmlnc/readdir-enhanced/-/readdir-enhanced-2.2.1.tgz#524af240d1a360527b730475ecfa1344aa540dde"
  dependencies:
    call-me-maybe "^1.0.1"
    glob-to-regexp "^0.3.0"

"@ngrx/core@^1.2.0":
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/@ngrx/core/-/core-1.2.0.tgz#882b46abafa2e0e6d887cb71a1b2c2fa3e6d0dc6"

"@ngrx/effects@^7.2.0":
  version "7.4.0"
  resolved "http://npm-registry.persgroep.digital/@ngrx/effects/-/effects-7.4.0.tgz#7d1977538cf85e42ab48fd648acdfbc8b52f93d3"

"@ngrx/store-devtools@^7.2.0":
  version "7.4.0"
  resolved "http://npm-registry.persgroep.digital/@ngrx/store-devtools/-/store-devtools-7.4.0.tgz#5a73469c70322351c4224f4529c5123f587a5997"

"@ngrx/store@^7.2.0":
  version "7.4.0"
  resolved "http://npm-registry.persgroep.digital/@ngrx/store/-/store-7.4.0.tgz#525a343aa45d7f6ca60f3301a23a27669c14bbce"

"@ngtools/webpack@7.3.10":
  version "7.3.10"
  resolved "http://npm-registry.persgroep.digital/@ngtools/webpack/-/webpack-7.3.10.tgz#8de9ada73faf1d254fca3d03519a31cde1858e77"
  dependencies:
    "@angular-devkit/core" "7.3.10"
    enhanced-resolve "4.1.0"
    rxjs "6.3.3"
    tree-kill "1.2.2"
    webpack-sources "1.3.0"

"@nodelib/fs.stat@^1.1.2":
  version "1.1.3"
  resolved "http://npm-registry.persgroep.digital/@nodelib/fs.stat/-/fs.stat-1.1.3.tgz#2b5a3ab3f918cca48a8c754c08168e3f03eba61b"

"@schematics/angular@7.3.10":
  version "7.3.10"
  resolved "http://npm-registry.persgroep.digital/@schematics/angular/-/angular-7.3.10.tgz#0f94e9d206c6a240f832f1cd5d0e6b65deb0dc28"
  dependencies:
    "@angular-devkit/core" "7.3.10"
    "@angular-devkit/schematics" "7.3.10"
    typescript "3.2.4"

"@schematics/update@0.13.10":
  version "0.13.10"
  resolved "http://npm-registry.persgroep.digital/@schematics/update/-/update-0.13.10.tgz#9b2af40454dba42136a4dcec438fe77e224cfdd2"
  dependencies:
    "@angular-devkit/core" "7.3.10"
    "@angular-devkit/schematics" "7.3.10"
    "@yarnpkg/lockfile" "1.1.0"
    ini "1.3.5"
    pacote "9.4.0"
    rxjs "6.3.3"
    semver "5.6.0"
    semver-intersect "1.4.0"

"@types/chai-as-promised@7.1.0":
  version "7.1.0"
  resolved "http://npm-registry.persgroep.digital/@types/chai-as-promised/-/chai-as-promised-7.1.0.tgz#010b04cde78eacfb6e72bfddb3e58fe23c2e78b9"
  dependencies:
    "@types/chai" "*"

"@types/chai@*", "@types/chai@^4.1.7":
  version "4.3.5"
  resolved "http://npm-registry.persgroep.digital/@types/chai/-/chai-4.3.5.tgz#ae69bcbb1bebb68c4ac0b11e9d8ed04526b3562b"

"@types/cucumber@^4.0.5":
  version "4.0.7"
  resolved "http://npm-registry.persgroep.digital/@types/cucumber/-/cucumber-4.0.7.tgz#e452f5298bb77f3ae8fdb65bc00d94165c4d7e51"

"@types/glob@^7.1.1":
  version "7.2.0"
  resolved "http://npm-registry.persgroep.digital/@types/glob/-/glob-7.2.0.tgz#bc1b5bf3aa92f25bd5dd39f35c57361bdce5b2eb"
  dependencies:
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/jasmine@*":
  version "4.3.1"
  resolved "http://npm-registry.persgroep.digital/@types/jasmine/-/jasmine-4.3.1.tgz#2d8ab5601c2fe7d9673dcb157e03f128ab5c5fff"

"@types/jasmine@3.3.9":
  version "3.3.9"
  resolved "http://npm-registry.persgroep.digital/@types/jasmine/-/jasmine-3.3.9.tgz#ee886a9113db567e722a8a0c37f0e6283f2cefa3"

"@types/jasminewd2@^2.0.6":
  version "2.0.10"
  resolved "http://npm-registry.persgroep.digital/@types/jasminewd2/-/jasminewd2-2.0.10.tgz#ae31c237aa6421bde30f1058b1d20f4577e54443"
  dependencies:
    "@types/jasmine" "*"

"@types/linkify-it@*":
  version "3.0.2"
  resolved "http://npm-registry.persgroep.digital/@types/linkify-it/-/linkify-it-3.0.2.tgz#fd2cd2edbaa7eaac7e7f3c1748b52a19143846c9"

"@types/markdown-it@^12.2.3":
  version "12.2.3"
  resolved "http://npm-registry.persgroep.digital/@types/markdown-it/-/markdown-it-12.2.3.tgz#0d6f6e5e413f8daaa26522904597be3d6cd93b51"
  dependencies:
    "@types/linkify-it" "*"
    "@types/mdurl" "*"

"@types/mdurl@*":
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/@types/mdurl/-/mdurl-1.0.2.tgz#e2ce9d83a613bacf284c7be7d491945e39e1f8e9"

"@types/minimatch@*":
  version "5.1.2"
  resolved "http://npm-registry.persgroep.digital/@types/minimatch/-/minimatch-5.1.2.tgz#07508b45797cb81ec3f273011b054cd0755eddca"

"@types/node@*":
  version "20.1.4"
  resolved "http://npm-registry.persgroep.digital/@types/node/-/node-20.1.4.tgz#83f148d2d1f5fe6add4c53358ba00d97fc4cdb71"

"@types/node@~11.9.4":
  version "11.9.6"
  resolved "http://npm-registry.persgroep.digital/@types/node/-/node-11.9.6.tgz#c632bbcc780a1349673a6e2e9b9dfa8c369d3c74"

"@types/raf@^3.4.0":
  version "3.4.0"
  resolved "http://npm-registry.persgroep.digital/@types/raf/-/raf-3.4.0.tgz#2b72cbd55405e071f1c4d29992638e022b20acc2"

"@types/selenium-webdriver@^3.0.15":
  version "3.0.21"
  resolved "http://npm-registry.persgroep.digital/@types/selenium-webdriver/-/selenium-webdriver-3.0.21.tgz#af7a272e7f92a89cd432005dd1cb80956ebe4d9b"

"@types/source-list-map@*":
  version "0.1.2"
  resolved "http://npm-registry.persgroep.digital/@types/source-list-map/-/source-list-map-0.1.2.tgz#0078836063ffaf17412349bba364087e0ac02ec9"

"@types/unist@*", "@types/unist@^2.0.0":
  version "2.0.6"
  resolved "http://npm-registry.persgroep.digital/@types/unist/-/unist-2.0.6.tgz#250a7b16c3b91f672a24552ec64678eeb1d3a08d"

"@types/vfile-message@*":
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/@types/vfile-message/-/vfile-message-2.0.0.tgz#690e46af0fdfc1f9faae00cd049cc888957927d5"
  dependencies:
    vfile-message "*"

"@types/vfile@^3.0.0":
  version "3.0.2"
  resolved "http://npm-registry.persgroep.digital/@types/vfile/-/vfile-3.0.2.tgz#19c18cd232df11ce6fa6ad80259bc86c366b09b9"
  dependencies:
    "@types/node" "*"
    "@types/unist" "*"
    "@types/vfile-message" "*"

"@types/webpack-sources@^0.1.5":
  version "0.1.9"
  resolved "http://npm-registry.persgroep.digital/@types/webpack-sources/-/webpack-sources-0.1.9.tgz#da69b06eb34f6432e6658acb5a6893c55d983920"
  dependencies:
    "@types/node" "*"
    "@types/source-list-map" "*"
    source-map "^0.6.1"

"@webassemblyjs/ast@1.7.11":
  version "1.7.11"
  resolved "http://npm-registry.persgroep.digital/@webassemblyjs/ast/-/ast-1.7.11.tgz#b988582cafbb2b095e8b556526f30c90d057cace"
  dependencies:
    "@webassemblyjs/helper-module-context" "1.7.11"
    "@webassemblyjs/helper-wasm-bytecode" "1.7.11"
    "@webassemblyjs/wast-parser" "1.7.11"

"@webassemblyjs/floating-point-hex-parser@1.7.11":
  version "1.7.11"
  resolved "http://npm-registry.persgroep.digital/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.7.11.tgz#a69f0af6502eb9a3c045555b1a6129d3d3f2e313"

"@webassemblyjs/helper-api-error@1.7.11":
  version "1.7.11"
  resolved "http://npm-registry.persgroep.digital/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.11.tgz#c7b6bb8105f84039511a2b39ce494f193818a32a"

"@webassemblyjs/helper-buffer@1.7.11":
  version "1.7.11"
  resolved "http://npm-registry.persgroep.digital/@webassemblyjs/helper-buffer/-/helper-buffer-1.7.11.tgz#3122d48dcc6c9456ed982debe16c8f37101df39b"

"@webassemblyjs/helper-code-frame@1.7.11":
  version "1.7.11"
  resolved "http://npm-registry.persgroep.digital/@webassemblyjs/helper-code-frame/-/helper-code-frame-1.7.11.tgz#cf8f106e746662a0da29bdef635fcd3d1248364b"
  dependencies:
    "@webassemblyjs/wast-printer" "1.7.11"

"@webassemblyjs/helper-fsm@1.7.11":
  version "1.7.11"
  resolved "http://npm-registry.persgroep.digital/@webassemblyjs/helper-fsm/-/helper-fsm-1.7.11.tgz#df38882a624080d03f7503f93e3f17ac5ac01181"

"@webassemblyjs/helper-module-context@1.7.11":
  version "1.7.11"
  resolved "http://npm-registry.persgroep.digital/@webassemblyjs/helper-module-context/-/helper-module-context-1.7.11.tgz#d874d722e51e62ac202476935d649c802fa0e209"

"@webassemblyjs/helper-wasm-bytecode@1.7.11":
  version "1.7.11"
  resolved "http://npm-registry.persgroep.digital/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.7.11.tgz#dd9a1e817f1c2eb105b4cf1013093cb9f3c9cb06"

"@webassemblyjs/helper-wasm-section@1.7.11":
  version "1.7.11"
  resolved "http://npm-registry.persgroep.digital/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.11.tgz#9c9ac41ecf9fbcfffc96f6d2675e2de33811e68a"
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/helper-buffer" "1.7.11"
    "@webassemblyjs/helper-wasm-bytecode" "1.7.11"
    "@webassemblyjs/wasm-gen" "1.7.11"

"@webassemblyjs/ieee754@1.7.11":
  version "1.7.11"
  resolved "http://npm-registry.persgroep.digital/@webassemblyjs/ieee754/-/ieee754-1.7.11.tgz#c95839eb63757a31880aaec7b6512d4191ac640b"
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.7.11":
  version "1.7.11"
  resolved "http://npm-registry.persgroep.digital/@webassemblyjs/leb128/-/leb128-1.7.11.tgz#d7267a1ee9c4594fd3f7e37298818ec65687db63"
  dependencies:
    "@xtuc/long" "4.2.1"

"@webassemblyjs/utf8@1.7.11":
  version "1.7.11"
  resolved "http://npm-registry.persgroep.digital/@webassemblyjs/utf8/-/utf8-1.7.11.tgz#06d7218ea9fdc94a6793aa92208160db3d26ee82"

"@webassemblyjs/wasm-edit@1.7.11":
  version "1.7.11"
  resolved "http://npm-registry.persgroep.digital/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.11.tgz#8c74ca474d4f951d01dbae9bd70814ee22a82005"
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/helper-buffer" "1.7.11"
    "@webassemblyjs/helper-wasm-bytecode" "1.7.11"
    "@webassemblyjs/helper-wasm-section" "1.7.11"
    "@webassemblyjs/wasm-gen" "1.7.11"
    "@webassemblyjs/wasm-opt" "1.7.11"
    "@webassemblyjs/wasm-parser" "1.7.11"
    "@webassemblyjs/wast-printer" "1.7.11"

"@webassemblyjs/wasm-gen@1.7.11":
  version "1.7.11"
  resolved "http://npm-registry.persgroep.digital/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.11.tgz#9bbba942f22375686a6fb759afcd7ac9c45da1a8"
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/helper-wasm-bytecode" "1.7.11"
    "@webassemblyjs/ieee754" "1.7.11"
    "@webassemblyjs/leb128" "1.7.11"
    "@webassemblyjs/utf8" "1.7.11"

"@webassemblyjs/wasm-opt@1.7.11":
  version "1.7.11"
  resolved "http://npm-registry.persgroep.digital/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.11.tgz#b331e8e7cef8f8e2f007d42c3a36a0580a7d6ca7"
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/helper-buffer" "1.7.11"
    "@webassemblyjs/wasm-gen" "1.7.11"
    "@webassemblyjs/wasm-parser" "1.7.11"

"@webassemblyjs/wasm-parser@1.7.11":
  version "1.7.11"
  resolved "http://npm-registry.persgroep.digital/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.11.tgz#6e3d20fa6a3519f6b084ef9391ad58211efb0a1a"
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/helper-api-error" "1.7.11"
    "@webassemblyjs/helper-wasm-bytecode" "1.7.11"
    "@webassemblyjs/ieee754" "1.7.11"
    "@webassemblyjs/leb128" "1.7.11"
    "@webassemblyjs/utf8" "1.7.11"

"@webassemblyjs/wast-parser@1.7.11":
  version "1.7.11"
  resolved "http://npm-registry.persgroep.digital/@webassemblyjs/wast-parser/-/wast-parser-1.7.11.tgz#25bd117562ca8c002720ff8116ef9072d9ca869c"
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/floating-point-hex-parser" "1.7.11"
    "@webassemblyjs/helper-api-error" "1.7.11"
    "@webassemblyjs/helper-code-frame" "1.7.11"
    "@webassemblyjs/helper-fsm" "1.7.11"
    "@xtuc/long" "4.2.1"

"@webassemblyjs/wast-printer@1.7.11":
  version "1.7.11"
  resolved "http://npm-registry.persgroep.digital/@webassemblyjs/wast-printer/-/wast-printer-1.7.11.tgz#c4245b6de242cb50a2cc950174fdbf65c78d7813"
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/wast-parser" "1.7.11"
    "@xtuc/long" "4.2.1"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/@xtuc/ieee754/-/ieee754-1.2.0.tgz#eef014a3145ae477a1cbc00cd1e552336dceb790"

"@xtuc/long@4.2.1":
  version "4.2.1"
  resolved "http://npm-registry.persgroep.digital/@xtuc/long/-/long-4.2.1.tgz#5c85d662f76fa1d34575766c5dcd6615abcd30d8"

"@yarnpkg/lockfile@1.1.0":
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/@yarnpkg/lockfile/-/lockfile-1.1.0.tgz#e77a97fbd345b76d83245edcd17d393b1b41fb31"

JSONStream@^1.0.3, JSONStream@^1.3.4:
  version "1.3.5"
  resolved "http://npm-registry.persgroep.digital/JSONStream/-/JSONStream-1.3.5.tgz#3208c1f08d3a4d99261ab64f92302bc15e111ca0"
  dependencies:
    jsonparse "^1.2.0"
    through ">=2.2.7 <3"

abbrev@1:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/abbrev/-/abbrev-1.1.1.tgz#f8f2c887ad10bf67f634f005b6987fed3179aac8"

accepts@~1.3.4, accepts@~1.3.5, accepts@~1.3.8:
  version "1.3.8"
  resolved "http://npm-registry.persgroep.digital/accepts/-/accepts-1.3.8.tgz#0bf0be125b67014adcb0b0921e62db7bffe16b2e"
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-dynamic-import@^4.0.0:
  version "4.0.0"
  resolved "http://npm-registry.persgroep.digital/acorn-dynamic-import/-/acorn-dynamic-import-4.0.0.tgz#482210140582a36b83c3e342e1cfebcaa9240948"

acorn-node@^1.2.0, acorn-node@^1.3.0, acorn-node@^1.5.2, acorn-node@^1.8.2:
  version "1.8.2"
  resolved "http://npm-registry.persgroep.digital/acorn-node/-/acorn-node-1.8.2.tgz#114c95d64539e53dede23de8b9d96df7c7ae2af8"
  dependencies:
    acorn "^7.0.0"
    acorn-walk "^7.0.0"
    xtend "^4.0.2"

acorn-walk@^7.0.0:
  version "7.2.0"
  resolved "http://npm-registry.persgroep.digital/acorn-walk/-/acorn-walk-7.2.0.tgz#0de889a601203909b0fbe07b8938dc21d2e967bc"

acorn@^6.0.5:
  version "6.4.2"
  resolved "http://npm-registry.persgroep.digital/acorn/-/acorn-6.4.2.tgz#35866fd710528e92de10cf06016498e47e39e1e6"

acorn@^7.0.0:
  version "7.4.1"
  resolved "http://npm-registry.persgroep.digital/acorn/-/acorn-7.4.1.tgz#feaed255973d2e77555b83dbc08851a6c63520fa"

after@0.8.2:
  version "0.8.2"
  resolved "http://npm-registry.persgroep.digital/after/-/after-0.8.2.tgz#fedb394f9f0e02aa9768e702bda23b505fae7e1f"

agent-base@4, agent-base@^4.3.0:
  version "4.3.0"
  resolved "http://npm-registry.persgroep.digital/agent-base/-/agent-base-4.3.0.tgz#8165f01c436009bccad0b1d122f05ed770efc6ee"
  dependencies:
    es6-promisify "^5.0.0"

agent-base@~4.2.1:
  version "4.2.1"
  resolved "http://npm-registry.persgroep.digital/agent-base/-/agent-base-4.2.1.tgz#d89e5999f797875674c07d87f260fc41e83e8ca9"
  dependencies:
    es6-promisify "^5.0.0"

agentkeepalive@^3.4.1:
  version "3.5.2"
  resolved "http://npm-registry.persgroep.digital/agentkeepalive/-/agentkeepalive-3.5.2.tgz#a113924dd3fa24a0bc3b78108c450c2abee00f67"
  dependencies:
    humanize-ms "^1.2.1"

ajv-errors@^1.0.0:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/ajv-errors/-/ajv-errors-1.0.1.tgz#f35986aceb91afadec4102fbd85014950cefa64d"

ajv-keywords@^3.1.0:
  version "3.5.2"
  resolved "http://npm-registry.persgroep.digital/ajv-keywords/-/ajv-keywords-3.5.2.tgz#31f29da5ab6e00d1c2d329acf7b5929614d5014d"

ajv@6.9.1:
  version "6.9.1"
  resolved "http://npm-registry.persgroep.digital/ajv/-/ajv-6.9.1.tgz#a4d3683d74abc5670e75f0b16520f70a20ea8dc1"
  dependencies:
    fast-deep-equal "^2.0.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^5.0.0:
  version "5.5.2"
  resolved "http://npm-registry.persgroep.digital/ajv/-/ajv-5.5.2.tgz#73b5eeca3fab653e3d3f9422b341ad42205dc965"
  dependencies:
    co "^4.6.0"
    fast-deep-equal "^1.0.0"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.3.0"

ajv@^6.1.0, ajv@^6.10.2, ajv@^6.12.3:
  version "6.12.6"
  resolved "http://npm-registry.persgroep.digital/ajv/-/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

amdefine@>=0.0.4:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/amdefine/-/amdefine-1.0.1.tgz#4a5282ac164729e93619bcfd3ad151f817ce91f5"

ansi-colors@^3.0.0:
  version "3.2.4"
  resolved "http://npm-registry.persgroep.digital/ansi-colors/-/ansi-colors-3.2.4.tgz#e3a3da4bfbae6c86a9c285625de124a234026fbf"

ansi-escapes@^1.0.0:
  version "1.4.0"
  resolved "http://npm-registry.persgroep.digital/ansi-escapes/-/ansi-escapes-1.4.0.tgz#d3a8a83b319aa67793662b13e761c7911422306e"

ansi-escapes@^3.0.0:
  version "3.2.0"
  resolved "http://npm-registry.persgroep.digital/ansi-escapes/-/ansi-escapes-3.2.0.tgz#8780b98ff9dbf5638152d1f1fe5c1d7b4442976b"

ansi-html@0.0.7:
  version "0.0.7"
  resolved "http://npm-registry.persgroep.digital/ansi-html/-/ansi-html-0.0.7.tgz#813584021962a9e9e6fd039f940d12f56ca7859e"

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "http://npm-registry.persgroep.digital/ansi-regex/-/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"

ansi-regex@^3.0.0:
  version "3.0.1"
  resolved "http://npm-registry.persgroep.digital/ansi-regex/-/ansi-regex-3.0.1.tgz#123d6479e92ad45ad897d4054e3c7ca7db4944e1"

ansi-regex@^4.1.0:
  version "4.1.1"
  resolved "http://npm-registry.persgroep.digital/ansi-regex/-/ansi-regex-4.1.1.tgz#164daac87ab2d6f6db3a29875e2d1766582dabed"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://npm-registry.persgroep.digital/ansi-regex/-/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "http://npm-registry.persgroep.digital/ansi-styles/-/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"

ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "http://npm-registry.persgroep.digital/ansi-styles/-/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  dependencies:
    color-convert "^1.9.0"

any-promise@^1.0.0:
  version "1.3.0"
  resolved "http://npm-registry.persgroep.digital/any-promise/-/any-promise-1.3.0.tgz#abc6afeedcea52e809cdc0376aed3ce39635d17f"

anymatch@^1.3.0:
  version "1.3.2"
  resolved "http://npm-registry.persgroep.digital/anymatch/-/anymatch-1.3.2.tgz#553dcb8f91e3c889845dfdba34c77721b90b9d7a"
  dependencies:
    micromatch "^2.1.5"
    normalize-path "^2.0.0"

anymatch@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/anymatch/-/anymatch-2.0.0.tgz#bcb24b4f37934d9aa7ac17b4adaf89e7c76ef2eb"
  dependencies:
    micromatch "^3.1.4"
    normalize-path "^2.1.1"

anymatch@~3.1.2:
  version "3.1.3"
  resolved "http://npm-registry.persgroep.digital/anymatch/-/anymatch-3.1.3.tgz#790c58b19ba1720a84205b57c618d5ad8524973e"
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

app-root-path@^2.2.1:
  version "2.2.1"
  resolved "http://npm-registry.persgroep.digital/app-root-path/-/app-root-path-2.2.1.tgz#d0df4a682ee408273583d43f6f79e9892624bc9a"

append-transform@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/append-transform/-/append-transform-1.0.0.tgz#046a52ae582a228bd72f58acfbe2967c678759ab"
  dependencies:
    default-require-extensions "^2.0.0"

aproba@^1.0.3, aproba@^1.1.1:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/aproba/-/aproba-1.2.0.tgz#6802e6264efd18c790a1b0d517f0f2627bf2c94a"

are-we-there-yet@~1.1.2:
  version "1.1.7"
  resolved "http://npm-registry.persgroep.digital/are-we-there-yet/-/are-we-there-yet-1.1.7.tgz#b15474a932adab4ff8a50d9adfa7e4e926f21146"
  dependencies:
    delegates "^1.0.0"
    readable-stream "^2.0.6"

arg@^4.1.0:
  version "4.1.3"
  resolved "http://npm-registry.persgroep.digital/arg/-/arg-4.1.3.tgz#269fc7ad5b8e42cb63c896d5666017261c144089"

argparse@^1.0.7:
  version "1.0.10"
  resolved "http://npm-registry.persgroep.digital/argparse/-/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/argparse/-/argparse-2.0.1.tgz#246f50f3ca78a3240f6c997e8a9bd1eac49e4b38"

aria-query@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/aria-query/-/aria-query-3.0.0.tgz#65b3fcc1ca1155a8c9ae64d6eee297f15d5133cc"
  dependencies:
    ast-types-flow "0.0.7"
    commander "^2.11.0"

arr-diff@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/arr-diff/-/arr-diff-2.0.0.tgz#8f3b827f955a8bd669697e4a4256ac3ceae356cf"
  dependencies:
    arr-flatten "^1.0.1"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "http://npm-registry.persgroep.digital/arr-diff/-/arr-diff-4.0.0.tgz#d6461074febfec71e7e15235761a329a5dc7c520"

arr-flatten@^1.0.1, arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/arr-flatten/-/arr-flatten-1.1.0.tgz#36048bbff4e7b47e136644316c99669ea5ae91f1"

arr-union@^3.1.0:
  version "3.1.0"
  resolved "http://npm-registry.persgroep.digital/arr-union/-/arr-union-3.1.0.tgz#e39b09aea9def866a8f206e288af63919bae39c4"

array-find-index@^1.0.1:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/array-find-index/-/array-find-index-1.0.2.tgz#df010aa1287e164bbda6f9723b0a96a1ec4187a1"

array-flatten@1.1.1:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/array-flatten/-/array-flatten-1.1.1.tgz#9a5f699051b1e7073328f2a008968b64ea2955d2"

array-flatten@^2.1.0:
  version "2.1.2"
  resolved "http://npm-registry.persgroep.digital/array-flatten/-/array-flatten-2.1.2.tgz#24ef80a28c1a893617e2149b0c6d0d788293b099"

array-union@^1.0.1, array-union@^1.0.2:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/array-union/-/array-union-1.0.2.tgz#9a34410e4f4e3da23dea375be5be70f24778ec39"
  dependencies:
    array-uniq "^1.0.1"

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/array-uniq/-/array-uniq-1.0.3.tgz#af6ac877a25cc7f74e058894753858dfdb24fdb6"

array-unique@^0.2.1:
  version "0.2.1"
  resolved "http://npm-registry.persgroep.digital/array-unique/-/array-unique-0.2.1.tgz#a1d97ccafcbc2625cc70fadceb36a50c58b01a53"

array-unique@^0.3.2:
  version "0.3.2"
  resolved "http://npm-registry.persgroep.digital/array-unique/-/array-unique-0.3.2.tgz#a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428"

arraybuffer.slice@~0.0.7:
  version "0.0.7"
  resolved "http://npm-registry.persgroep.digital/arraybuffer.slice/-/arraybuffer.slice-0.0.7.tgz#3bbc4275dd584cc1b10809b89d4e8b63a69e7675"

arrify@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/arrify/-/arrify-1.0.1.tgz#898508da2226f380df904728456849c1501a4b0d"

asap@~2.0.3:
  version "2.0.6"
  resolved "http://npm-registry.persgroep.digital/asap/-/asap-2.0.6.tgz#e50347611d7e690943208bbdafebcbc2fb866d46"

asn1.js@^5.2.0:
  version "5.4.1"
  resolved "http://npm-registry.persgroep.digital/asn1.js/-/asn1.js-5.4.1.tgz#11a980b84ebb91781ce35b0fdc2ee294e3783f07"
  dependencies:
    bn.js "^4.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"
    safer-buffer "^2.1.0"

asn1@~0.2.3:
  version "0.2.6"
  resolved "http://npm-registry.persgroep.digital/asn1/-/asn1-0.2.6.tgz#0d3a7bb6e64e02a90c0303b31f292868ea09a08d"
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/assert-plus/-/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"

assert@^1.1.1, assert@^1.4.0:
  version "1.5.0"
  resolved "http://npm-registry.persgroep.digital/assert/-/assert-1.5.0.tgz#55c109aaf6e0aefdb3dc4b71240c70bf574b18eb"
  dependencies:
    object-assign "^4.1.1"
    util "0.10.3"

assertion-error-formatter@^2.0.1:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/assertion-error-formatter/-/assertion-error-formatter-2.0.1.tgz#6bbdffaec8e2fa9e2b0eb158bfe353132d7c0a9b"
  dependencies:
    diff "^3.0.0"
    pad-right "^0.2.2"
    repeat-string "^1.6.1"

assertion-error@^1.1.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/assertion-error/-/assertion-error-1.1.0.tgz#e60b6b0e8f301bd97e5375215bda406c85118c0b"

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/assign-symbols/-/assign-symbols-1.0.0.tgz#59667f41fadd4f20ccbc2bb96b8d4f7f78ec0367"

ast-types-flow@0.0.7:
  version "0.0.7"
  resolved "http://npm-registry.persgroep.digital/ast-types-flow/-/ast-types-flow-0.0.7.tgz#f70b735c6bca1a5c9c22d982c3e39e7feba3bdad"

astral-regex@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/astral-regex/-/astral-regex-1.0.0.tgz#6c8c3fb827dd43ee3918f27b82782ab7658a6fd9"

async-each@^1.0.0, async-each@^1.0.1:
  version "1.0.6"
  resolved "http://npm-registry.persgroep.digital/async-each/-/async-each-1.0.6.tgz#52f1d9403818c179b7561e11a5d1b77eb2160e77"

async-foreach@^0.1.3:
  version "0.1.3"
  resolved "http://npm-registry.persgroep.digital/async-foreach/-/async-foreach-0.1.3.tgz#36121f845c0578172de419a97dbeb1d16ec34542"

async-limiter@~1.0.0:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/async-limiter/-/async-limiter-1.0.1.tgz#dd379e94f0db8310b08291f9d64c3209766617fd"

async@2.4.0:
  version "2.4.0"
  resolved "http://npm-registry.persgroep.digital/async/-/async-2.4.0.tgz#4990200f18ea5b837c2cc4f8c031a6985c385611"
  dependencies:
    lodash "^4.14.0"

async@^2.5.0, async@^2.6.2, async@^2.6.4:
  version "2.6.4"
  resolved "http://npm-registry.persgroep.digital/async/-/async-2.6.4.tgz#706b7ff6084664cd7eae713f6f965433b5504221"
  dependencies:
    lodash "^4.17.14"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://npm-registry.persgroep.digital/asynckit/-/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"

atob@^2.1.2:
  version "2.1.2"
  resolved "http://npm-registry.persgroep.digital/atob/-/atob-2.1.2.tgz#6d9517eb9e030d2436666651e86bd9f6f13533c9"

autoprefixer@9.4.6:
  version "9.4.6"
  resolved "http://npm-registry.persgroep.digital/autoprefixer/-/autoprefixer-9.4.6.tgz#0ace275e33b37de16b09a5547dbfe73a98c1d446"
  dependencies:
    browserslist "^4.4.1"
    caniuse-lite "^1.0.30000929"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    postcss "^7.0.13"
    postcss-value-parser "^3.3.1"

autoprefixer@^9.0.0:
  version "9.8.8"
  resolved "http://npm-registry.persgroep.digital/autoprefixer/-/autoprefixer-9.8.8.tgz#fd4bd4595385fa6f06599de749a4d5f7a474957a"
  dependencies:
    browserslist "^4.12.0"
    caniuse-lite "^1.0.30001109"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    picocolors "^0.2.1"
    postcss "^7.0.32"
    postcss-value-parser "^4.1.0"

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "http://npm-registry.persgroep.digital/aws-sign2/-/aws-sign2-0.7.0.tgz#b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8"

aws4@^1.8.0:
  version "1.12.0"
  resolved "http://npm-registry.persgroep.digital/aws4/-/aws4-1.12.0.tgz#ce1c9d143389679e253b314241ea9aa5cec980d3"

axobject-query@2.0.2:
  version "2.0.2"
  resolved "http://npm-registry.persgroep.digital/axobject-query/-/axobject-query-2.0.2.tgz#ea187abe5b9002b377f925d8bf7d1c561adf38f9"
  dependencies:
    ast-types-flow "0.0.7"

babel-code-frame@^6.22.0, babel-code-frame@^6.26.0:
  version "6.26.0"
  resolved "http://npm-registry.persgroep.digital/babel-code-frame/-/babel-code-frame-6.26.0.tgz#63fd43f7dc1e3bb7ce35947db8fe369a3f58c74b"
  dependencies:
    chalk "^1.1.3"
    esutils "^2.0.2"
    js-tokens "^3.0.2"

babel-generator@^6.18.0:
  version "6.26.1"
  resolved "http://npm-registry.persgroep.digital/babel-generator/-/babel-generator-6.26.1.tgz#1844408d3b8f0d35a404ea7ac180f087a601bd90"
  dependencies:
    babel-messages "^6.23.0"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    detect-indent "^4.0.0"
    jsesc "^1.3.0"
    lodash "^4.17.4"
    source-map "^0.5.7"
    trim-right "^1.0.1"

babel-messages@^6.23.0:
  version "6.23.0"
  resolved "http://npm-registry.persgroep.digital/babel-messages/-/babel-messages-6.23.0.tgz#f3cdf4703858035b2a2951c6ec5edf6c62f2630e"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-add-module-exports@1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/babel-plugin-add-module-exports/-/babel-plugin-add-module-exports-1.0.0.tgz#72b5424d941a336c6a35357f373d8b8366263031"
  optionalDependencies:
    chokidar "^2.0.4"

babel-runtime@^6.11.6, babel-runtime@^6.22.0, babel-runtime@^6.26.0:
  version "6.26.0"
  resolved "http://npm-registry.persgroep.digital/babel-runtime/-/babel-runtime-6.26.0.tgz#965c7058668e82b55d7bfe04ff2337bc8b5647fe"
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

babel-template@^6.16.0:
  version "6.26.0"
  resolved "http://npm-registry.persgroep.digital/babel-template/-/babel-template-6.26.0.tgz#de03e2d16396b069f46dd9fff8521fb1a0e35e02"
  dependencies:
    babel-runtime "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    lodash "^4.17.4"

babel-traverse@^6.18.0, babel-traverse@^6.26.0:
  version "6.26.0"
  resolved "http://npm-registry.persgroep.digital/babel-traverse/-/babel-traverse-6.26.0.tgz#46a9cbd7edcc62c8e5c064e2d2d8d0f4035766ee"
  dependencies:
    babel-code-frame "^6.26.0"
    babel-messages "^6.23.0"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    debug "^2.6.8"
    globals "^9.18.0"
    invariant "^2.2.2"
    lodash "^4.17.4"

babel-types@^6.18.0, babel-types@^6.26.0:
  version "6.26.0"
  resolved "http://npm-registry.persgroep.digital/babel-types/-/babel-types-6.26.0.tgz#a3b073f94ab49eb6fa55cd65227a334380632497"
  dependencies:
    babel-runtime "^6.26.0"
    esutils "^2.0.2"
    lodash "^4.17.4"
    to-fast-properties "^1.0.3"

babelify@10.0.0:
  version "10.0.0"
  resolved "http://npm-registry.persgroep.digital/babelify/-/babelify-10.0.0.tgz#fe73b1a22583f06680d8d072e25a1e0d1d1d7fb5"

babylon@^6.18.0:
  version "6.18.0"
  resolved "http://npm-registry.persgroep.digital/babylon/-/babylon-6.18.0.tgz#af2f3b88fa6f5c1e4c634d1a0f8eac4f55b395e3"

backo2@1.0.2:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/backo2/-/backo2-1.0.2.tgz#31ab1ac8b129363463e35b3ebb69f4dfcfba7947"

bail@^1.0.0:
  version "1.0.5"
  resolved "http://npm-registry.persgroep.digital/bail/-/bail-1.0.5.tgz#b6fa133404a392cbc1f8c4bf63f5953351e7a776"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/balanced-match/-/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"

base64-arraybuffer@0.1.5:
  version "0.1.5"
  resolved "http://npm-registry.persgroep.digital/base64-arraybuffer/-/base64-arraybuffer-0.1.5.tgz#73926771923b5a19747ad666aa5cd4bf9c6e9ce8"

base64-arraybuffer@^1.0.2:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/base64-arraybuffer/-/base64-arraybuffer-1.0.2.tgz#1c37589a7c4b0746e34bd1feb951da2df01c1bdc"

base64-js@^1.0.2, base64-js@^1.3.1:
  version "1.5.1"
  resolved "http://npm-registry.persgroep.digital/base64-js/-/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"

base64id@1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/base64id/-/base64id-1.0.0.tgz#47688cb99bb6804f0e06d3e763b1c32e57d8e6b6"

base@^0.11.1:
  version "0.11.2"
  resolved "http://npm-registry.persgroep.digital/base/-/base-0.11.2.tgz#7bde5ced145b6d551a90db87f83c558b4eb48a8f"
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

batch@0.6.1:
  version "0.6.1"
  resolved "http://npm-registry.persgroep.digital/batch/-/batch-0.6.1.tgz#dc34314f4e679318093fc760272525f94bf25c16"

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz#a4301d389b6a43f9b67ff3ca11a3f6637e360e9e"
  dependencies:
    tweetnacl "^0.14.3"

becke-ch--regex--s0-0-v1--base--pl--lib@^1.2.0:
  version "1.4.0"
  resolved "http://npm-registry.persgroep.digital/becke-ch--regex--s0-0-v1--base--pl--lib/-/becke-ch--regex--s0-0-v1--base--pl--lib-1.4.0.tgz#429ceebbfa5f7e936e78d73fbdc7da7162b20e20"

better-assert@~1.0.0:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/better-assert/-/better-assert-1.0.2.tgz#40866b9e1b9e0b55b481894311e68faffaebc522"
  dependencies:
    callsite "1.0.0"

big.js@^5.2.2:
  version "5.2.2"
  resolved "http://npm-registry.persgroep.digital/big.js/-/big.js-5.2.2.tgz#65f0af382f578bcdc742bd9c281e9cb2d7768328"

binary-extensions@^1.0.0:
  version "1.13.1"
  resolved "http://npm-registry.persgroep.digital/binary-extensions/-/binary-extensions-1.13.1.tgz#598afe54755b2868a5330d2aff9d4ebb53209b65"

binary-extensions@^2.0.0:
  version "2.2.0"
  resolved "http://npm-registry.persgroep.digital/binary-extensions/-/binary-extensions-2.2.0.tgz#75f502eeaf9ffde42fc98829645be4ea76bd9e2d"

bindings@^1.5.0:
  version "1.5.0"
  resolved "http://npm-registry.persgroep.digital/bindings/-/bindings-1.5.0.tgz#10353c9e945334bc0511a6d90b38fbc7c9c504df"
  dependencies:
    file-uri-to-path "1.0.0"

blob@0.0.5:
  version "0.0.5"
  resolved "http://npm-registry.persgroep.digital/blob/-/blob-0.0.5.tgz#d680eeef25f8cd91ad533f5b01eed48e64caf683"

block-stream@*:
  version "0.0.9"
  resolved "http://npm-registry.persgroep.digital/block-stream/-/block-stream-0.0.9.tgz#13ebfe778a03205cfe03751481ebb4b3300c126a"
  dependencies:
    inherits "~2.0.0"

bluebird@3.5.0:
  version "3.5.0"
  resolved "http://npm-registry.persgroep.digital/bluebird/-/bluebird-3.5.0.tgz#791420d7f551eea2897453a8a77653f96606d67c"

bluebird@3.5.2:
  version "3.5.2"
  resolved "http://npm-registry.persgroep.digital/bluebird/-/bluebird-3.5.2.tgz#1be0908e054a751754549c270489c1505d4ab15a"

bluebird@^3.3.0, bluebird@^3.4.1, bluebird@^3.5.1, bluebird@^3.5.3, bluebird@^3.5.5:
  version "3.7.2"
  resolved "http://npm-registry.persgroep.digital/bluebird/-/bluebird-3.7.2.tgz#9f229c15be272454ffa973ace0dbee79a1b0c36f"

bn.js@^4.0.0, bn.js@^4.1.0, bn.js@^4.11.9:
  version "4.12.0"
  resolved "http://npm-registry.persgroep.digital/bn.js/-/bn.js-4.12.0.tgz#775b3f278efbb9718eec7361f483fb36fbbfea88"

bn.js@^5.0.0, bn.js@^5.1.1:
  version "5.2.1"
  resolved "http://npm-registry.persgroep.digital/bn.js/-/bn.js-5.2.1.tgz#0bc527a6a0d18d0aa8d5b0538ce4a77dccfa7b70"

body-parser@1.20.1:
  version "1.20.1"
  resolved "http://npm-registry.persgroep.digital/body-parser/-/body-parser-1.20.1.tgz#b1812a8912c195cd371a3ee5e66faa2338a5c668"
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.11.0"
    raw-body "2.5.1"
    type-is "~1.6.18"
    unpipe "1.0.0"

body-parser@^1.16.1, body-parser@^1.18.3, body-parser@^1.19.0:
  version "1.20.2"
  resolved "http://npm-registry.persgroep.digital/body-parser/-/body-parser-1.20.2.tgz#6feb0e21c4724d06de7ff38da36dad4f57a747fd"
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.5"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.11.0"
    raw-body "2.5.2"
    type-is "~1.6.18"
    unpipe "1.0.0"

bonjour@^3.5.0:
  version "3.5.0"
  resolved "http://npm-registry.persgroep.digital/bonjour/-/bonjour-3.5.0.tgz#8e890a183d8ee9a2393b3844c691a42bcf7bc9f5"
  dependencies:
    array-flatten "^2.1.0"
    deep-equal "^1.0.1"
    dns-equal "^1.0.0"
    dns-txt "^2.0.2"
    multicast-dns "^6.0.1"
    multicast-dns-service-types "^1.1.0"

bootstrap@^4.3.1:
  version "4.6.2"
  resolved "http://npm-registry.persgroep.digital/bootstrap/-/bootstrap-4.6.2.tgz#8e0cd61611728a5bf65a3a2b8d6ff6c77d5d7479"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://npm-registry.persgroep.digital/brace-expansion/-/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^1.8.2:
  version "1.8.5"
  resolved "http://npm-registry.persgroep.digital/braces/-/braces-1.8.5.tgz#ba77962e12dff969d6b76711e914b737857bf6a7"
  dependencies:
    expand-range "^1.8.1"
    preserve "^0.2.0"
    repeat-element "^1.1.2"

braces@^2.3.0, braces@^2.3.1, braces@^2.3.2:
  version "2.3.2"
  resolved "http://npm-registry.persgroep.digital/braces/-/braces-2.3.2.tgz#5979fd3f14cd531565e5fa2df1abfff1dfaee729"
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@~3.0.2:
  version "3.0.2"
  resolved "http://npm-registry.persgroep.digital/braces/-/braces-3.0.2.tgz#3454e1a462ee8d599e236df336cd9ea4f8afe107"
  dependencies:
    fill-range "^7.0.1"

brorand@^1.0.1, brorand@^1.1.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/brorand/-/brorand-1.1.0.tgz#12c25efe40a45e3c323eb8675a0a0ce57b22371f"

browser-pack@^6.0.1:
  version "6.1.0"
  resolved "http://npm-registry.persgroep.digital/browser-pack/-/browser-pack-6.1.0.tgz#c34ba10d0b9ce162b5af227c7131c92c2ecd5774"
  dependencies:
    JSONStream "^1.0.3"
    combine-source-map "~0.8.0"
    defined "^1.0.0"
    safe-buffer "^5.1.1"
    through2 "^2.0.0"
    umd "^3.0.0"

browser-resolve@^1.11.0:
  version "1.11.3"
  resolved "http://npm-registry.persgroep.digital/browser-resolve/-/browser-resolve-1.11.3.tgz#9b7cbb3d0f510e4cb86bdbd796124d28b5890af6"
  dependencies:
    resolve "1.1.7"

browser-resolve@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/browser-resolve/-/browser-resolve-2.0.0.tgz#99b7304cb392f8d73dba741bb2d7da28c6d7842b"
  dependencies:
    resolve "^1.17.0"

browserify-aes@^1.0.0, browserify-aes@^1.0.4:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/browserify-aes/-/browserify-aes-1.2.0.tgz#326734642f403dabc3003209853bb70ad428ef48"
  dependencies:
    buffer-xor "^1.0.3"
    cipher-base "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.3"
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

browserify-cipher@^1.0.0:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/browserify-cipher/-/browserify-cipher-1.0.1.tgz#8d6474c1b870bfdabcd3bcfcc1934a10e94f15f0"
  dependencies:
    browserify-aes "^1.0.4"
    browserify-des "^1.0.0"
    evp_bytestokey "^1.0.0"

browserify-des@^1.0.0:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/browserify-des/-/browserify-des-1.0.2.tgz#3af4f1f59839403572f1c66204375f7a7f703e9c"
  dependencies:
    cipher-base "^1.0.1"
    des.js "^1.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

browserify-rsa@^4.0.0, browserify-rsa@^4.0.1:
  version "4.1.0"
  resolved "http://npm-registry.persgroep.digital/browserify-rsa/-/browserify-rsa-4.1.0.tgz#b2fd06b5b75ae297f7ce2dc651f918f5be158c8d"
  dependencies:
    bn.js "^5.0.0"
    randombytes "^2.0.1"

browserify-sign@^4.0.0:
  version "4.2.1"
  resolved "http://npm-registry.persgroep.digital/browserify-sign/-/browserify-sign-4.2.1.tgz#eaf4add46dd54be3bb3b36c0cf15abbeba7956c3"
  dependencies:
    bn.js "^5.1.1"
    browserify-rsa "^4.0.1"
    create-hash "^1.2.0"
    create-hmac "^1.1.7"
    elliptic "^6.5.3"
    inherits "^2.0.4"
    parse-asn1 "^5.1.5"
    readable-stream "^3.6.0"
    safe-buffer "^5.2.0"

browserify-zlib@^0.2.0, browserify-zlib@~0.2.0:
  version "0.2.0"
  resolved "http://npm-registry.persgroep.digital/browserify-zlib/-/browserify-zlib-0.2.0.tgz#2869459d9aa3be245fe8fe2ca1f46e2e7f54d73f"
  dependencies:
    pako "~1.0.5"

browserify@16.2.2:
  version "16.2.2"
  resolved "http://npm-registry.persgroep.digital/browserify/-/browserify-16.2.2.tgz#4b1f66ba0e54fa39dbc5aa4be9629142143d91b0"
  dependencies:
    JSONStream "^1.0.3"
    assert "^1.4.0"
    browser-pack "^6.0.1"
    browser-resolve "^1.11.0"
    browserify-zlib "~0.2.0"
    buffer "^5.0.2"
    cached-path-relative "^1.0.0"
    concat-stream "^1.6.0"
    console-browserify "^1.1.0"
    constants-browserify "~1.0.0"
    crypto-browserify "^3.0.0"
    defined "^1.0.0"
    deps-sort "^2.0.0"
    domain-browser "^1.2.0"
    duplexer2 "~0.1.2"
    events "^2.0.0"
    glob "^7.1.0"
    has "^1.0.0"
    htmlescape "^1.1.0"
    https-browserify "^1.0.0"
    inherits "~2.0.1"
    insert-module-globals "^7.0.0"
    labeled-stream-splicer "^2.0.0"
    mkdirp "^0.5.0"
    module-deps "^6.0.0"
    os-browserify "~0.3.0"
    parents "^1.0.1"
    path-browserify "~0.0.0"
    process "~0.11.0"
    punycode "^1.3.2"
    querystring-es3 "~0.2.0"
    read-only-stream "^2.0.0"
    readable-stream "^2.0.2"
    resolve "^1.1.4"
    shasum "^1.0.0"
    shell-quote "^1.6.1"
    stream-browserify "^2.0.0"
    stream-http "^2.0.0"
    string_decoder "^1.1.1"
    subarg "^1.0.0"
    syntax-error "^1.1.1"
    through2 "^2.0.0"
    timers-browserify "^1.0.1"
    tty-browserify "0.0.1"
    url "~0.11.0"
    util "~0.10.1"
    vm-browserify "^1.0.0"
    xtend "^4.0.0"

browserify@^16.1.0:
  version "16.5.2"
  resolved "http://npm-registry.persgroep.digital/browserify/-/browserify-16.5.2.tgz#d926835e9280fa5fd57f5bc301f2ef24a972ddfe"
  dependencies:
    JSONStream "^1.0.3"
    assert "^1.4.0"
    browser-pack "^6.0.1"
    browser-resolve "^2.0.0"
    browserify-zlib "~0.2.0"
    buffer "~5.2.1"
    cached-path-relative "^1.0.0"
    concat-stream "^1.6.0"
    console-browserify "^1.1.0"
    constants-browserify "~1.0.0"
    crypto-browserify "^3.0.0"
    defined "^1.0.0"
    deps-sort "^2.0.0"
    domain-browser "^1.2.0"
    duplexer2 "~0.1.2"
    events "^2.0.0"
    glob "^7.1.0"
    has "^1.0.0"
    htmlescape "^1.1.0"
    https-browserify "^1.0.0"
    inherits "~2.0.1"
    insert-module-globals "^7.0.0"
    labeled-stream-splicer "^2.0.0"
    mkdirp-classic "^0.5.2"
    module-deps "^6.2.3"
    os-browserify "~0.3.0"
    parents "^1.0.1"
    path-browserify "~0.0.0"
    process "~0.11.0"
    punycode "^1.3.2"
    querystring-es3 "~0.2.0"
    read-only-stream "^2.0.0"
    readable-stream "^2.0.2"
    resolve "^1.1.4"
    shasum "^1.0.0"
    shell-quote "^1.6.1"
    stream-browserify "^2.0.0"
    stream-http "^3.0.0"
    string_decoder "^1.1.1"
    subarg "^1.0.0"
    syntax-error "^1.1.1"
    through2 "^2.0.0"
    timers-browserify "^1.0.1"
    tty-browserify "0.0.1"
    url "~0.11.0"
    util "~0.10.1"
    vm-browserify "^1.0.0"
    xtend "^4.0.0"

browserlist@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/browserlist/-/browserlist-1.0.1.tgz#8c52c4fd5ba49c7464b9e0f85d519598b5833ed9"
  dependencies:
    chalk "^2.4.1"

browserslist@^4.1.0, browserslist@^4.12.0, browserslist@^4.21.3, browserslist@^4.4.1:
  version "4.21.5"
  resolved "http://npm-registry.persgroep.digital/browserslist/-/browserslist-4.21.5.tgz#75c5dae60063ee641f977e00edd3cfb2fb7af6a7"
  dependencies:
    caniuse-lite "^1.0.30001449"
    electron-to-chromium "^1.4.284"
    node-releases "^2.0.8"
    update-browserslist-db "^1.0.10"

btoa@^1.2.1:
  version "1.2.1"
  resolved "http://npm-registry.persgroep.digital/btoa/-/btoa-1.2.1.tgz#01a9909f8b2c93f6bf680ba26131eb30f7fa3d73"

buffer-alloc-unsafe@^1.1.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/buffer-alloc-unsafe/-/buffer-alloc-unsafe-1.1.0.tgz#bd7dc26ae2972d0eda253be061dba992349c19f0"

buffer-alloc@^1.2.0:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/buffer-alloc/-/buffer-alloc-1.2.0.tgz#890dd90d923a873e08e10e5fd51a57e5b7cce0ec"
  dependencies:
    buffer-alloc-unsafe "^1.1.0"
    buffer-fill "^1.0.0"

buffer-crc32@~0.2.3:
  version "0.2.13"
  resolved "http://npm-registry.persgroep.digital/buffer-crc32/-/buffer-crc32-0.2.13.tgz#0d333e3f00eac50aa1454abd30ef8c2a5d9a7242"

buffer-fill@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/buffer-fill/-/buffer-fill-1.0.0.tgz#f8f78b76789888ef39f205cd637f68e702122b2c"

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "http://npm-registry.persgroep.digital/buffer-from/-/buffer-from-1.1.2.tgz#2b146a6fd72e80b4f55d255f35ed59a3a9a41bd5"

buffer-indexof@^1.0.0:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/buffer-indexof/-/buffer-indexof-1.1.1.tgz#52fabcc6a606d1a00302802648ef68f639da268c"

buffer-xor@^1.0.3:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/buffer-xor/-/buffer-xor-1.0.3.tgz#26e61ed1422fb70dd42e6e36729ed51d855fe8d9"

buffer@^4.3.0:
  version "4.9.2"
  resolved "http://npm-registry.persgroep.digital/buffer/-/buffer-4.9.2.tgz#230ead344002988644841ab0244af8c44bbe3ef8"
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

buffer@^5.0.2:
  version "5.7.1"
  resolved "http://npm-registry.persgroep.digital/buffer/-/buffer-5.7.1.tgz#ba62e7c13133053582197160851a8f648e99eed0"
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

buffer@~5.2.1:
  version "5.2.1"
  resolved "http://npm-registry.persgroep.digital/buffer/-/buffer-5.2.1.tgz#dd57fa0f109ac59c602479044dca7b8b3d0b71d6"
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"

builtin-modules@^1.1.1:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/builtin-modules/-/builtin-modules-1.1.1.tgz#270f076c5a72c02f5b65a47df94c5fe3a278892f"

builtin-status-codes@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz#85982878e21b98e1c66425e03d0174788f569ee8"

builtins@^1.0.3:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/builtins/-/builtins-1.0.3.tgz#cb94faeb61c8696451db36534e1422f94f0aee88"

busboy@~0.2.9:
  version "0.2.14"
  resolved "http://npm-registry.persgroep.digital/busboy/-/busboy-0.2.14.tgz#6c2a622efcf47c57bbbe1e2a9c37ad36c7925453"
  dependencies:
    dicer "0.2.5"
    readable-stream "1.1.x"

bytes@3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/bytes/-/bytes-3.0.0.tgz#d32815404d689699f85a4ea4fa8755dd13a96048"

bytes@3.1.2:
  version "3.1.2"
  resolved "http://npm-registry.persgroep.digital/bytes/-/bytes-3.1.2.tgz#8b0beeb98605adf1b128fa4386403c009e0221a5"

cacache@^11.3.2, cacache@^11.3.3:
  version "11.3.3"
  resolved "http://npm-registry.persgroep.digital/cacache/-/cacache-11.3.3.tgz#8bd29df8c6a718a6ebd2d010da4d7972ae3bbadc"
  dependencies:
    bluebird "^3.5.5"
    chownr "^1.1.1"
    figgy-pudding "^3.5.1"
    glob "^7.1.4"
    graceful-fs "^4.1.15"
    lru-cache "^5.1.1"
    mississippi "^3.0.0"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    promise-inflight "^1.0.1"
    rimraf "^2.6.3"
    ssri "^6.0.1"
    unique-filename "^1.1.1"
    y18n "^4.0.0"

cacache@^12.0.2, cacache@^12.0.3:
  version "12.0.4"
  resolved "http://npm-registry.persgroep.digital/cacache/-/cacache-12.0.4.tgz#668bcbd105aeb5f1d92fe25570ec9525c8faa40c"
  dependencies:
    bluebird "^3.5.5"
    chownr "^1.1.1"
    figgy-pudding "^3.5.1"
    glob "^7.1.4"
    graceful-fs "^4.1.15"
    infer-owner "^1.0.3"
    lru-cache "^5.1.1"
    mississippi "^3.0.0"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    promise-inflight "^1.0.1"
    rimraf "^2.6.3"
    ssri "^6.0.1"
    unique-filename "^1.1.1"
    y18n "^4.0.0"

cache-base@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/cache-base/-/cache-base-1.0.1.tgz#0a7f46416831c8b662ee36fe4e7c59d76f666ab2"
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

cached-path-relative@^1.0.0, cached-path-relative@^1.0.2:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/cached-path-relative/-/cached-path-relative-1.1.0.tgz#865576dfef39c0d6a7defde794d078f5308e3ef3"

cachedir@1.3.0:
  version "1.3.0"
  resolved "http://npm-registry.persgroep.digital/cachedir/-/cachedir-1.3.0.tgz#5e01928bf2d95b5edd94b0942188246740e0dbc4"
  dependencies:
    os-homedir "^1.0.1"

call-bind@^1.0.0, call-bind@^1.0.2:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/call-bind/-/call-bind-1.0.2.tgz#b1d4e89e688119c3c9a903ad30abb2f6a919be3c"
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

call-me-maybe@^1.0.1:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/call-me-maybe/-/call-me-maybe-1.0.2.tgz#03f964f19522ba643b1b0693acb9152fe2074baa"

caller-callsite@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/caller-callsite/-/caller-callsite-2.0.0.tgz#847e0fce0a223750a9a027c54b33731ad3154134"
  dependencies:
    callsites "^2.0.0"

caller-path@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/caller-path/-/caller-path-2.0.0.tgz#468f83044e369ab2010fac5f06ceee15bb2cb1f4"
  dependencies:
    caller-callsite "^2.0.0"

callsite@1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/callsite/-/callsite-1.0.0.tgz#280398e5d664bd74038b6f0905153e6e8af1bc20"

callsites@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/callsites/-/callsites-2.0.0.tgz#06eb84f00eea413da86affefacbffb36093b3c50"

camelcase-keys@^2.0.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/camelcase-keys/-/camelcase-keys-2.1.0.tgz#308beeaffdf28119051efa1d932213c91b8f92e7"
  dependencies:
    camelcase "^2.0.0"
    map-obj "^1.0.0"

camelcase-keys@^4.0.0:
  version "4.2.0"
  resolved "http://npm-registry.persgroep.digital/camelcase-keys/-/camelcase-keys-4.2.0.tgz#a2aa5fb1af688758259c32c141426d78923b9b77"
  dependencies:
    camelcase "^4.1.0"
    map-obj "^2.0.0"
    quick-lru "^1.0.0"

camelcase@^2.0.0:
  version "2.1.1"
  resolved "http://npm-registry.persgroep.digital/camelcase/-/camelcase-2.1.1.tgz#7c1d16d679a1bbe59ca02cacecfb011e201f5a1f"

camelcase@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/camelcase/-/camelcase-3.0.0.tgz#32fc4b9fcdaf845fcdf7e73bb97cac2261f0ab0a"

camelcase@^4.1.0:
  version "4.1.0"
  resolved "http://npm-registry.persgroep.digital/camelcase/-/camelcase-4.1.0.tgz#d545635be1e33c542649c69173e5de6acfae34dd"

camelcase@^5.0.0:
  version "5.3.1"
  resolved "http://npm-registry.persgroep.digital/camelcase/-/camelcase-5.3.1.tgz#e3c9b31569e106811df242f715725a1f4c494320"

caniuse-lite@^1.0.30000929, caniuse-lite@^1.0.30001031, caniuse-lite@^1.0.30001109, caniuse-lite@^1.0.30001449:
  version "1.0.30001487"
  resolved "http://npm-registry.persgroep.digital/caniuse-lite/-/caniuse-lite-1.0.30001487.tgz#d882d1a34d89c11aea53b8cdc791931bdab5fe1b"

canonical-path@1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/canonical-path/-/canonical-path-1.0.0.tgz#fcb470c23958def85081856be7a86e904f180d1d"

canvg@^3.0.6:
  version "3.0.10"
  resolved "http://npm-registry.persgroep.digital/canvg/-/canvg-3.0.10.tgz#8e52a2d088b6ffa23ac78970b2a9eebfae0ef4b3"
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@types/raf" "^3.4.0"
    core-js "^3.8.3"
    raf "^3.4.1"
    regenerator-runtime "^0.13.7"
    rgbcolor "^1.0.1"
    stackblur-canvas "^2.0.0"
    svg-pathdata "^6.0.3"

caseless@~0.12.0:
  version "0.12.0"
  resolved "http://npm-registry.persgroep.digital/caseless/-/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"

ccount@^1.0.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/ccount/-/ccount-1.1.0.tgz#246687debb6014735131be8abab2d93898f8d043"

chai-as-promised@^7.1.1:
  version "7.1.1"
  resolved "http://npm-registry.persgroep.digital/chai-as-promised/-/chai-as-promised-7.1.1.tgz#08645d825deb8696ee61725dbf590c012eb00ca0"
  dependencies:
    check-error "^1.0.2"

chai@^4.1.2, chai@^4.2.0:
  version "4.3.7"
  resolved "http://npm-registry.persgroep.digital/chai/-/chai-4.3.7.tgz#ec63f6df01829088e8bf55fca839bcd464a8ec51"
  dependencies:
    assertion-error "^1.1.0"
    check-error "^1.0.2"
    deep-eql "^4.1.2"
    get-func-name "^2.0.0"
    loupe "^2.3.1"
    pathval "^1.1.1"
    type-detect "^4.0.5"

chalk@2.4.2, chalk@^2.0.0, chalk@^2.0.1, chalk@^2.3.0, chalk@^2.4.1, chalk@^2.4.2:
  version "2.4.2"
  resolved "http://npm-registry.persgroep.digital/chalk/-/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^1.0.0, chalk@^1.1.1, chalk@^1.1.3:
  version "1.1.3"
  resolved "http://npm-registry.persgroep.digital/chalk/-/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

character-entities-html4@^1.0.0:
  version "1.1.4"
  resolved "http://npm-registry.persgroep.digital/character-entities-html4/-/character-entities-html4-1.1.4.tgz#0e64b0a3753ddbf1fdc044c5fd01d0199a02e125"

character-entities-legacy@^1.0.0:
  version "1.1.4"
  resolved "http://npm-registry.persgroep.digital/character-entities-legacy/-/character-entities-legacy-1.1.4.tgz#94bc1845dce70a5bb9d2ecc748725661293d8fc1"

character-entities@^1.0.0:
  version "1.2.4"
  resolved "http://npm-registry.persgroep.digital/character-entities/-/character-entities-1.2.4.tgz#e12c3939b7eaf4e5b15e7ad4c5e28e1d48c5b16b"

character-reference-invalid@^1.0.0:
  version "1.1.4"
  resolved "http://npm-registry.persgroep.digital/character-reference-invalid/-/character-reference-invalid-1.1.4.tgz#083329cda0eae272ab3dbbf37e9a382c13af1560"

chardet@^0.7.0:
  version "0.7.0"
  resolved "http://npm-registry.persgroep.digital/chardet/-/chardet-0.7.0.tgz#90094849f0937f2eedc2425d0d28a9e5f0cbad9e"

check-error@^1.0.2:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/check-error/-/check-error-1.0.2.tgz#574d312edd88bb5dd8912e9286dd6c0aed4aac82"

check-more-types@2.24.0:
  version "2.24.0"
  resolved "http://npm-registry.persgroep.digital/check-more-types/-/check-more-types-2.24.0.tgz#1420ffb10fd444dcfc79b43891bbfffd32a84600"

chokidar@2.0.4:
  version "2.0.4"
  resolved "http://npm-registry.persgroep.digital/chokidar/-/chokidar-2.0.4.tgz#356ff4e2b0e8e43e322d18a372460bbcf3accd26"
  dependencies:
    anymatch "^2.0.0"
    async-each "^1.0.0"
    braces "^2.3.0"
    glob-parent "^3.1.0"
    inherits "^2.0.1"
    is-binary-path "^1.0.0"
    is-glob "^4.0.0"
    lodash.debounce "^4.0.8"
    normalize-path "^2.1.1"
    path-is-absolute "^1.0.0"
    readdirp "^2.0.0"
    upath "^1.0.5"
  optionalDependencies:
    fsevents "^1.2.2"

chokidar@^1.0.0:
  version "1.7.0"
  resolved "http://npm-registry.persgroep.digital/chokidar/-/chokidar-1.7.0.tgz#798e689778151c8076b4b360e5edd28cda2bb468"
  dependencies:
    anymatch "^1.3.0"
    async-each "^1.0.0"
    glob-parent "^2.0.0"
    inherits "^2.0.1"
    is-binary-path "^1.0.0"
    is-glob "^2.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.0.0"
  optionalDependencies:
    fsevents "^1.0.0"

chokidar@^2.0.0, chokidar@^2.0.3, chokidar@^2.0.4, chokidar@^2.1.1, chokidar@^2.1.8:
  version "2.1.8"
  resolved "http://npm-registry.persgroep.digital/chokidar/-/chokidar-2.1.8.tgz#804b3a7b6a99358c3c5c61e71d8728f041cff917"
  dependencies:
    anymatch "^2.0.0"
    async-each "^1.0.1"
    braces "^2.3.2"
    glob-parent "^3.1.0"
    inherits "^2.0.3"
    is-binary-path "^1.0.0"
    is-glob "^4.0.0"
    normalize-path "^3.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.2.1"
    upath "^1.1.1"
  optionalDependencies:
    fsevents "^1.2.7"

chokidar@^3.4.1:
  version "3.5.3"
  resolved "http://npm-registry.persgroep.digital/chokidar/-/chokidar-3.5.3.tgz#1cf37c8707b932bd1af1ae22c0432e2acd1903bd"
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chownr@^1.1.1, chownr@^1.1.4:
  version "1.1.4"
  resolved "http://npm-registry.persgroep.digital/chownr/-/chownr-1.1.4.tgz#6fc9d7b42d32a583596337666e7d08084da2cc6b"

chrome-trace-event@^1.0.0:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz#1015eced4741e15d06664a957dbbf50d041e26ac"

ci-info@^1.5.0:
  version "1.6.0"
  resolved "http://npm-registry.persgroep.digital/ci-info/-/ci-info-1.6.0.tgz#2ca20dbb9ceb32d4524a683303313f0304b1e497"

cipher-base@^1.0.0, cipher-base@^1.0.1, cipher-base@^1.0.3:
  version "1.0.4"
  resolved "http://npm-registry.persgroep.digital/cipher-base/-/cipher-base-1.0.4.tgz#8760e4ecc272f4c363532f926d874aae2c1397de"
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

circular-dependency-plugin@5.0.2:
  version "5.0.2"
  resolved "http://npm-registry.persgroep.digital/circular-dependency-plugin/-/circular-dependency-plugin-5.0.2.tgz#da168c0b37e7b43563fb9f912c1c007c213389ef"

cjsxify@0.3.0:
  version "0.3.0"
  resolved "http://npm-registry.persgroep.digital/cjsxify/-/cjsxify-0.3.0.tgz#058fb39988df05f69a8ee602c39d7821989a5b44"
  dependencies:
    coffee-react-transform "^3.1.0"
    coffee-script "^1.9.0"
    convert-source-map "^0.4.1"
    through "^2.3.6"

class-utils@^0.3.5:
  version "0.3.6"
  resolved "http://npm-registry.persgroep.digital/class-utils/-/class-utils-0.3.6.tgz#f93369ae8b9a7ce02fd41faad0ca83033190c463"
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

clean-css@4.2.1:
  version "4.2.1"
  resolved "http://npm-registry.persgroep.digital/clean-css/-/clean-css-4.2.1.tgz#2d411ef76b8569b6d0c84068dabe85b0aa5e5c17"
  dependencies:
    source-map "~0.6.0"

cli-cursor@^1.0.2:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/cli-cursor/-/cli-cursor-1.0.2.tgz#64da3f7d56a54412e59794bd62dc35295e8f2987"
  dependencies:
    restore-cursor "^1.0.1"

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/cli-cursor/-/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
  dependencies:
    restore-cursor "^2.0.0"

cli-spinners@^0.1.2:
  version "0.1.2"
  resolved "http://npm-registry.persgroep.digital/cli-spinners/-/cli-spinners-0.1.2.tgz#bb764d88e185fb9e1e6a2a1f19772318f605e31c"

cli-table3@^0.5.1:
  version "0.5.1"
  resolved "http://npm-registry.persgroep.digital/cli-table3/-/cli-table3-0.5.1.tgz#0252372d94dfc40dbd8df06005f48f31f656f202"
  dependencies:
    object-assign "^4.1.0"
    string-width "^2.1.1"
  optionalDependencies:
    colors "^1.1.2"

cli-table@^0.3.1:
  version "0.3.11"
  resolved "http://npm-registry.persgroep.digital/cli-table/-/cli-table-0.3.11.tgz#ac69cdecbe81dccdba4889b9a18b7da312a9d3ee"
  dependencies:
    colors "1.0.3"

cli-truncate@^0.2.1:
  version "0.2.1"
  resolved "http://npm-registry.persgroep.digital/cli-truncate/-/cli-truncate-0.2.1.tgz#9f15cfbb0705005369216c626ac7d05ab90dd574"
  dependencies:
    slice-ansi "0.0.4"
    string-width "^1.0.1"

cli-width@^2.0.0:
  version "2.2.1"
  resolved "http://npm-registry.persgroep.digital/cli-width/-/cli-width-2.2.1.tgz#b0433d0b4e9c847ef18868a4ef16fd5fc8271c48"

cliui@^3.2.0:
  version "3.2.0"
  resolved "http://npm-registry.persgroep.digital/cliui/-/cliui-3.2.0.tgz#120601537a916d29940f934da3b48d585a39213d"
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wrap-ansi "^2.0.0"

cliui@^4.0.0:
  version "4.1.0"
  resolved "http://npm-registry.persgroep.digital/cliui/-/cliui-4.1.0.tgz#348422dbe82d800b3022eef4f6ac10bf2e4d1b49"
  dependencies:
    string-width "^2.1.1"
    strip-ansi "^4.0.0"
    wrap-ansi "^2.0.0"

clone-deep@^2.0.1:
  version "2.0.2"
  resolved "http://npm-registry.persgroep.digital/clone-deep/-/clone-deep-2.0.2.tgz#00db3a1e173656730d1188c3d6aced6d7ea97713"
  dependencies:
    for-own "^1.0.0"
    is-plain-object "^2.0.4"
    kind-of "^6.0.0"
    shallow-clone "^1.0.0"

clone-regexp@^1.0.0:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/clone-regexp/-/clone-regexp-1.0.1.tgz#051805cd33173375d82118fc0918606da39fd60f"
  dependencies:
    is-regexp "^1.0.0"
    is-supported-regexp-flag "^1.0.0"

clone@^2.1.1, clone@^2.1.2:
  version "2.1.2"
  resolved "http://npm-registry.persgroep.digital/clone/-/clone-2.1.2.tgz#1b7f4b9f591f1e8f83670401600345a02887435f"

co@^4.6.0:
  version "4.6.0"
  resolved "http://npm-registry.persgroep.digital/co/-/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/code-point-at/-/code-point-at-1.1.0.tgz#0d070b4d043a5bea33a2f1a40e2edb3d9a4ccf77"

"codelyzer@>=5.0.0-rc.0 <6.0.0||>=2.3.1 <3.0.0||>=4.0.0-beta <5.0.0":
  version "5.2.2"
  resolved "http://npm-registry.persgroep.digital/codelyzer/-/codelyzer-5.2.2.tgz#d0530a455784e6bea0b6d7e97166c73c30a5347f"
  dependencies:
    app-root-path "^2.2.1"
    aria-query "^3.0.0"
    axobject-query "2.0.2"
    css-selector-tokenizer "^0.7.1"
    cssauron "^1.4.0"
    damerau-levenshtein "^1.0.4"
    semver-dsl "^1.0.1"
    source-map "^0.5.7"
    sprintf-js "^1.1.2"

coffee-react-transform@^3.1.0:
  version "3.3.0"
  resolved "http://npm-registry.persgroep.digital/coffee-react-transform/-/coffee-react-transform-3.3.0.tgz#f1f90fa22de8d767fca2793e3b70f0f7d7a2e467"

coffee-script@^1.9.0:
  version "1.12.7"
  resolved "http://npm-registry.persgroep.digital/coffee-script/-/coffee-script-1.12.7.tgz#c05dae0cb79591d05b3070a8433a98c9a89ccc53"

collapse-white-space@^1.0.2:
  version "1.0.6"
  resolved "http://npm-registry.persgroep.digital/collapse-white-space/-/collapse-white-space-1.0.6.tgz#e63629c0016665792060dbbeb79c42239d2c5287"

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/collection-visit/-/collection-visit-1.0.0.tgz#4bc0373c164bc3291b4d368c829cf1a80a59dca0"
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.9.0:
  version "1.9.3"
  resolved "http://npm-registry.persgroep.digital/color-convert/-/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  dependencies:
    color-name "1.1.3"

color-name@1.1.3:
  version "1.1.3"
  resolved "http://npm-registry.persgroep.digital/color-name/-/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"

colors@1.0.3:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/colors/-/colors-1.0.3.tgz#0433f44d809680fdeb60ed260f1b0c262e82a40b"

colors@1.1.2:
  version "1.1.2"
  resolved "http://npm-registry.persgroep.digital/colors/-/colors-1.1.2.tgz#168a4701756b6a7f51a12ce0c97bfa28c084ed63"

colors@^1.1.0, colors@^1.1.2:
  version "1.4.0"
  resolved "http://npm-registry.persgroep.digital/colors/-/colors-1.4.0.tgz#c50491479d4c1bdaed2c9ced32cf7c7dc2360f78"

combine-source-map@^0.8.0, combine-source-map@~0.8.0:
  version "0.8.0"
  resolved "http://npm-registry.persgroep.digital/combine-source-map/-/combine-source-map-0.8.0.tgz#a58d0df042c186fcf822a8e8015f5450d2d79a8b"
  dependencies:
    convert-source-map "~1.1.0"
    inline-source-map "~0.6.0"
    lodash.memoize "~3.0.3"
    source-map "~0.5.3"

combined-stream@^1.0.6, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "http://npm-registry.persgroep.digital/combined-stream/-/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  dependencies:
    delayed-stream "~1.0.0"

commander@2.15.1:
  version "2.15.1"
  resolved "http://npm-registry.persgroep.digital/commander/-/commander-2.15.1.tgz#df46e867d0fc2aec66a34662b406a9ccafff5b0f"

commander@^2.11.0, commander@^2.12.1, commander@^2.20.0, commander@^2.7.1, commander@^2.9.0:
  version "2.20.3"
  resolved "http://npm-registry.persgroep.digital/commander/-/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"

common-tags@1.8.0:
  version "1.8.0"
  resolved "http://npm-registry.persgroep.digital/common-tags/-/common-tags-1.8.0.tgz#8e3153e542d4a39e9b10554434afaaf98956a937"

commondir@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/commondir/-/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"

compare-versions@^3.4.0:
  version "3.6.0"
  resolved "http://npm-registry.persgroep.digital/compare-versions/-/compare-versions-3.6.0.tgz#1a5689913685e5a87637b8d3ffca75514ec41d62"

component-bind@1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/component-bind/-/component-bind-1.0.0.tgz#00c608ab7dcd93897c0009651b1d3a8e1e73bbd1"

component-emitter@1.2.1:
  version "1.2.1"
  resolved "http://npm-registry.persgroep.digital/component-emitter/-/component-emitter-1.2.1.tgz#137918d6d78283f7df7a6b7c5a63e140e69425e6"

component-emitter@^1.2.1:
  version "1.3.0"
  resolved "http://npm-registry.persgroep.digital/component-emitter/-/component-emitter-1.3.0.tgz#16e4070fba8ae29b679f2215853ee181ab2eabc0"

component-inherit@0.0.3:
  version "0.0.3"
  resolved "http://npm-registry.persgroep.digital/component-inherit/-/component-inherit-0.0.3.tgz#645fc4adf58b72b649d5cae65135619db26ff143"

compressible@~2.0.16:
  version "2.0.18"
  resolved "http://npm-registry.persgroep.digital/compressible/-/compressible-2.0.18.tgz#af53cca6b070d4c3c0750fbd77286a6d7cc46fba"
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.5.2:
  version "1.7.4"
  resolved "http://npm-registry.persgroep.digital/compression/-/compression-1.7.4.tgz#95523eff170ca57c29a0ca41e6fe131f41e5bb8f"
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://npm-registry.persgroep.digital/concat-map/-/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"

concat-stream@1.6.2, concat-stream@^1.5.0, concat-stream@^1.6.0, concat-stream@^1.6.1, concat-stream@^1.6.2, concat-stream@~1.6.0:
  version "1.6.2"
  resolved "http://npm-registry.persgroep.digital/concat-stream/-/concat-stream-1.6.2.tgz#904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34"
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

connect-history-api-fallback@^1.3.0:
  version "1.6.0"
  resolved "http://npm-registry.persgroep.digital/connect-history-api-fallback/-/connect-history-api-fallback-1.6.0.tgz#8b32089359308d111115d81cad3fceab888f97bc"

connect@^3.6.0:
  version "3.7.0"
  resolved "http://npm-registry.persgroep.digital/connect/-/connect-3.7.0.tgz#5d49348910caa5e07a01800b030d0c35f20484f8"
  dependencies:
    debug "2.6.9"
    finalhandler "1.1.2"
    parseurl "~1.3.3"
    utils-merge "1.0.1"

console-browserify@^1.1.0:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/console-browserify/-/console-browserify-1.2.0.tgz#67063cef57ceb6cf4993a2ab3a55840ae8c49336"

console-control-strings@^1.0.0, console-control-strings@~1.1.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/console-control-strings/-/console-control-strings-1.1.0.tgz#3d7cf4464db6446ea644bf4b39507f9851008e8e"

constants-browserify@^1.0.0, constants-browserify@~1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/constants-browserify/-/constants-browserify-1.0.0.tgz#c20b96d8c617748aaf1c16021760cd27fcb8cb75"

content-disposition@0.5.4:
  version "0.5.4"
  resolved "http://npm-registry.persgroep.digital/content-disposition/-/content-disposition-0.5.4.tgz#8b82b4efac82512a02bb0b1dcec9d2c5e8eb5bfe"
  dependencies:
    safe-buffer "5.2.1"

content-type@~1.0.4, content-type@~1.0.5:
  version "1.0.5"
  resolved "http://npm-registry.persgroep.digital/content-type/-/content-type-1.0.5.tgz#8b773162656d1d1086784c8f23a54ce6d73d7918"

convert-source-map@^0.4.1:
  version "0.4.1"
  resolved "http://npm-registry.persgroep.digital/convert-source-map/-/convert-source-map-0.4.1.tgz#f919a0099fe31f80fc5a1d0eb303161b394070c7"

convert-source-map@^1.1.0, convert-source-map@^1.5.0, convert-source-map@^1.5.1, convert-source-map@^1.7.0:
  version "1.9.0"
  resolved "http://npm-registry.persgroep.digital/convert-source-map/-/convert-source-map-1.9.0.tgz#7faae62353fb4213366d0ca98358d22e8368b05f"

convert-source-map@~1.1.0:
  version "1.1.3"
  resolved "http://npm-registry.persgroep.digital/convert-source-map/-/convert-source-map-1.1.3.tgz#4829c877e9fe49b3161f3bf3673888e204699860"

cookie-parser@^1.4.4:
  version "1.4.6"
  resolved "http://npm-registry.persgroep.digital/cookie-parser/-/cookie-parser-1.4.6.tgz#3ac3a7d35a7a03bbc7e365073a26074824214594"
  dependencies:
    cookie "0.4.1"
    cookie-signature "1.0.6"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "http://npm-registry.persgroep.digital/cookie-signature/-/cookie-signature-1.0.6.tgz#e303a882b342cc3ee8ca513a79999734dab3ae2c"

cookie@0.3.1:
  version "0.3.1"
  resolved "http://npm-registry.persgroep.digital/cookie/-/cookie-0.3.1.tgz#e7e0a1f9ef43b4c8ba925c5c5a96e806d16873bb"

cookie@0.4.1:
  version "0.4.1"
  resolved "http://npm-registry.persgroep.digital/cookie/-/cookie-0.4.1.tgz#afd713fe26ebd21ba95ceb61f9a8116e50a537d1"

cookie@0.5.0:
  version "0.5.0"
  resolved "http://npm-registry.persgroep.digital/cookie/-/cookie-0.5.0.tgz#d1f5d71adec6558c58f389987c366aa47e994f8b"

copy-concurrently@^1.0.0:
  version "1.0.5"
  resolved "http://npm-registry.persgroep.digital/copy-concurrently/-/copy-concurrently-1.0.5.tgz#92297398cae34937fcafd6ec8139c18051f0b5e0"
  dependencies:
    aproba "^1.1.1"
    fs-write-stream-atomic "^1.0.8"
    iferr "^0.1.5"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.0"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "http://npm-registry.persgroep.digital/copy-descriptor/-/copy-descriptor-0.1.1.tgz#676f6eb3c39997c2ee1ac3a924fd6124748f578d"

copy-webpack-plugin@5.1.1:
  version "5.1.1"
  resolved "http://npm-registry.persgroep.digital/copy-webpack-plugin/-/copy-webpack-plugin-5.1.1.tgz#5481a03dea1123d88a988c6ff8b78247214f0b88"
  dependencies:
    cacache "^12.0.3"
    find-cache-dir "^2.1.0"
    glob-parent "^3.1.0"
    globby "^7.1.1"
    is-glob "^4.0.1"
    loader-utils "^1.2.3"
    minimatch "^3.0.4"
    normalize-path "^3.0.0"
    p-limit "^2.2.1"
    schema-utils "^1.0.0"
    serialize-javascript "^2.1.2"
    webpack-log "^2.0.0"

core-js@^2.2.0, core-js@^2.4.0, core-js@^2.6.5:
  version "2.6.12"
  resolved "http://npm-registry.persgroep.digital/core-js/-/core-js-2.6.12.tgz#d9333dfa7b065e347cc5682219d6f690859cc2ec"

core-js@^3.6.0, core-js@^3.8.3:
  version "3.30.2"
  resolved "http://npm-registry.persgroep.digital/core-js/-/core-js-3.30.2.tgz#6528abfda65e5ad728143ea23f7a14f0dcf503fc"

core-util-is@1.0.2:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/core-util-is/-/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/core-util-is/-/core-util-is-1.0.3.tgz#a6042d3634c2b27e9328f837b965fac83808db85"

cosmiconfig@^4.0.0:
  version "4.0.0"
  resolved "http://npm-registry.persgroep.digital/cosmiconfig/-/cosmiconfig-4.0.0.tgz#760391549580bbd2df1e562bc177b13c290972dc"
  dependencies:
    is-directory "^0.3.1"
    js-yaml "^3.9.0"
    parse-json "^4.0.0"
    require-from-string "^2.0.1"

cosmiconfig@^5.0.0:
  version "5.2.1"
  resolved "http://npm-registry.persgroep.digital/cosmiconfig/-/cosmiconfig-5.2.1.tgz#040f726809c591e77a17c0a3626ca45b4f168b1a"
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.13.1"
    parse-json "^4.0.0"

create-ecdh@^4.0.0:
  version "4.0.4"
  resolved "http://npm-registry.persgroep.digital/create-ecdh/-/create-ecdh-4.0.4.tgz#d6e7f4bffa66736085a0762fd3a632684dabcc4e"
  dependencies:
    bn.js "^4.1.0"
    elliptic "^6.5.3"

create-hash@^1.1.0, create-hash@^1.1.2, create-hash@^1.2.0:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/create-hash/-/create-hash-1.2.0.tgz#889078af11a63756bcfb59bd221996be3a9ef196"
  dependencies:
    cipher-base "^1.0.1"
    inherits "^2.0.1"
    md5.js "^1.3.4"
    ripemd160 "^2.0.1"
    sha.js "^2.4.0"

create-hmac@^1.1.0, create-hmac@^1.1.4, create-hmac@^1.1.7:
  version "1.1.7"
  resolved "http://npm-registry.persgroep.digital/create-hmac/-/create-hmac-1.1.7.tgz#69170c78b3ab957147b2b8b04572e47ead2243ff"
  dependencies:
    cipher-base "^1.0.3"
    create-hash "^1.1.0"
    inherits "^2.0.1"
    ripemd160 "^2.0.0"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

cross-spawn@^3.0.0:
  version "3.0.1"
  resolved "http://npm-registry.persgroep.digital/cross-spawn/-/cross-spawn-3.0.1.tgz#1256037ecb9f0c5f79e3d6ef135e30770184b982"
  dependencies:
    lru-cache "^4.0.1"
    which "^1.2.9"

cross-spawn@^6.0.0, cross-spawn@^6.0.5:
  version "6.0.5"
  resolved "http://npm-registry.persgroep.digital/cross-spawn/-/cross-spawn-6.0.5.tgz#4a5ec7c64dfae22c3a14124dbacdee846d80cbc4"
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

crypto-browserify@^3.0.0, crypto-browserify@^3.11.0:
  version "3.12.0"
  resolved "http://npm-registry.persgroep.digital/crypto-browserify/-/crypto-browserify-3.12.0.tgz#396cf9f3137f03e4b8e532c58f698254e00f80ec"
  dependencies:
    browserify-cipher "^1.0.0"
    browserify-sign "^4.0.0"
    create-ecdh "^4.0.0"
    create-hash "^1.1.0"
    create-hmac "^1.1.0"
    diffie-hellman "^5.0.0"
    inherits "^2.0.1"
    pbkdf2 "^3.0.3"
    public-encrypt "^4.0.0"
    randombytes "^2.0.0"
    randomfill "^1.0.3"

css-line-break@^2.1.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/css-line-break/-/css-line-break-2.1.0.tgz#bfef660dfa6f5397ea54116bb3cb4873edbc4fa0"
  dependencies:
    utrie "^1.0.2"

css-parse@1.7.x:
  version "1.7.0"
  resolved "http://npm-registry.persgroep.digital/css-parse/-/css-parse-1.7.0.tgz#321f6cf73782a6ff751111390fc05e2c657d8c9b"

css-selector-tokenizer@^0.7.1:
  version "0.7.3"
  resolved "http://npm-registry.persgroep.digital/css-selector-tokenizer/-/css-selector-tokenizer-0.7.3.tgz#735f26186e67c749aaf275783405cf0661fae8f1"
  dependencies:
    cssesc "^3.0.0"
    fastparse "^1.1.2"

cssauron@^1.4.0:
  version "1.4.0"
  resolved "http://npm-registry.persgroep.digital/cssauron/-/cssauron-1.4.0.tgz#a6602dff7e04a8306dc0db9a551e92e8b5662ad8"
  dependencies:
    through X.X.X

cssesc@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/cssesc/-/cssesc-3.0.0.tgz#37741919903b868565e1c09ea747445cd18983ee"

cucumber-expressions@^5.0.13:
  version "5.0.18"
  resolved "http://npm-registry.persgroep.digital/cucumber-expressions/-/cucumber-expressions-5.0.18.tgz#6c70779efd3aebc5e9e7853938b1110322429596"
  dependencies:
    becke-ch--regex--s0-0-v1--base--pl--lib "^1.2.0"

cucumber-expressions@^6.0.0, cucumber-expressions@^6.0.1:
  version "6.6.2"
  resolved "http://npm-registry.persgroep.digital/cucumber-expressions/-/cucumber-expressions-6.6.2.tgz#d89640eccc72a78380b6c210eae36a64e7462b81"
  dependencies:
    becke-ch--regex--s0-0-v1--base--pl--lib "^1.2.0"

cucumber-tag-expressions@^1.1.1:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/cucumber-tag-expressions/-/cucumber-tag-expressions-1.1.1.tgz#7f5c7b70009bc2b666591bfe64854578bedee85a"

cucumber@^4.2.1:
  version "4.2.1"
  resolved "http://npm-registry.persgroep.digital/cucumber/-/cucumber-4.2.1.tgz#64cfff6150bbe6b5e94b173470057353d6206719"
  dependencies:
    assertion-error-formatter "^2.0.1"
    babel-runtime "^6.11.6"
    bluebird "^3.4.1"
    cli-table "^0.3.1"
    colors "^1.1.2"
    commander "^2.9.0"
    cucumber-expressions "^5.0.13"
    cucumber-tag-expressions "^1.1.1"
    duration "^0.2.0"
    escape-string-regexp "^1.0.5"
    figures "2.0.0"
    gherkin "^5.0.0"
    glob "^7.0.0"
    indent-string "^3.1.0"
    is-generator "^1.0.2"
    is-stream "^1.1.0"
    knuth-shuffle-seeded "^1.0.6"
    lodash "^4.17.4"
    mz "^2.4.0"
    progress "^2.0.0"
    resolve "^1.3.3"
    serialize-error "^2.1.0"
    stack-chain "^2.0.0"
    stacktrace-js "^2.0.0"
    string-argv "0.0.2"
    title-case "^2.1.1"
    util-arity "^1.0.2"
    verror "^1.9.0"

cucumber@^5.1.0:
  version "5.1.0"
  resolved "http://npm-registry.persgroep.digital/cucumber/-/cucumber-5.1.0.tgz#7b166812c255bec7eac4b0df7007a40d089c895d"
  dependencies:
    "@babel/polyfill" "^7.2.3"
    assertion-error-formatter "^2.0.1"
    bluebird "^3.4.1"
    cli-table3 "^0.5.1"
    colors "^1.1.2"
    commander "^2.9.0"
    cross-spawn "^6.0.5"
    cucumber-expressions "^6.0.0"
    cucumber-tag-expressions "^1.1.1"
    duration "^0.2.1"
    escape-string-regexp "^1.0.5"
    figures "2.0.0"
    gherkin "^5.0.0"
    glob "^7.1.3"
    indent-string "^3.1.0"
    is-generator "^1.0.2"
    is-stream "^1.1.0"
    knuth-shuffle-seeded "^1.0.6"
    lodash "^4.17.10"
    mz "^2.4.0"
    progress "^2.0.0"
    resolve "^1.3.3"
    serialize-error "^3.0.0"
    stack-chain "^2.0.0"
    stacktrace-js "^2.0.0"
    string-argv "0.1.1"
    title-case "^2.1.1"
    util-arity "^1.0.2"
    verror "^1.9.0"

currently-unhandled@^0.4.1:
  version "0.4.1"
  resolved "http://npm-registry.persgroep.digital/currently-unhandled/-/currently-unhandled-0.4.1.tgz#988df33feab191ef799a61369dd76c17adf957ea"
  dependencies:
    array-find-index "^1.0.1"

custom-event@~1.0.0:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/custom-event/-/custom-event-1.0.1.tgz#5d02a46850adf1b4a317946a3928fccb5bfd0425"

cyclist@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/cyclist/-/cyclist-1.0.1.tgz#596e9698fd0c80e12038c2b82d6eb1b35b6224d9"

cypress-cucumber-preprocessor@1.11.0:
  version "1.11.0"
  resolved "http://npm-registry.persgroep.digital/cypress-cucumber-preprocessor/-/cypress-cucumber-preprocessor-1.11.0.tgz#bad39f372ce1a8ae429922b1d3d02ddb8ad10011"
  dependencies:
    "@cypress/browserify-preprocessor" "^1.1.2"
    chai "^4.1.2"
    chokidar "^2.0.4"
    cosmiconfig "^4.0.0"
    cucumber "^4.2.1"
    cucumber-expressions "^6.0.1"
    cucumber-tag-expressions "^1.1.1"
    debug "^3.0.1"
    gherkin "^5.1.0"
    glob "^7.1.2"
    through "^2.3.8"

cypress@3.2.0:
  version "3.2.0"
  resolved "http://npm-registry.persgroep.digital/cypress/-/cypress-3.2.0.tgz#c2d5befd5077dab6fb52ad70721e0868ac057001"
  dependencies:
    "@cypress/listr-verbose-renderer" "0.4.1"
    "@cypress/xvfb" "1.2.4"
    bluebird "3.5.0"
    cachedir "1.3.0"
    chalk "2.4.2"
    check-more-types "2.24.0"
    commander "2.15.1"
    common-tags "1.8.0"
    debug "3.1.0"
    execa "0.10.0"
    executable "4.1.1"
    extract-zip "1.6.7"
    fs-extra "4.0.1"
    getos "3.1.0"
    glob "7.1.3"
    is-ci "1.2.1"
    is-installed-globally "0.1.0"
    lazy-ass "1.6.0"
    listr "0.12.0"
    lodash "4.17.11"
    log-symbols "2.2.0"
    minimist "1.2.0"
    moment "2.24.0"
    ramda "0.24.1"
    request "2.88.0"
    request-progress "0.4.0"
    supports-color "5.5.0"
    tmp "0.0.33"
    url "0.11.0"
    yauzl "2.10.0"

d@1, d@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/d/-/d-1.0.1.tgz#8698095372d58dbee346ffd0c7093f99f8f9eb5a"
  dependencies:
    es5-ext "^0.10.50"
    type "^1.0.1"

damerau-levenshtein@^1.0.4:
  version "1.0.8"
  resolved "http://npm-registry.persgroep.digital/damerau-levenshtein/-/damerau-levenshtein-1.0.8.tgz#b43d286ccbd36bc5b2f7ed41caf2d0aba1f8a6e7"

dash-ast@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/dash-ast/-/dash-ast-1.0.0.tgz#12029ba5fb2f8aa6f0a861795b23c1b4b6c27d37"

dashdash@^1.12.0:
  version "1.14.1"
  resolved "http://npm-registry.persgroep.digital/dashdash/-/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
  dependencies:
    assert-plus "^1.0.0"

date-fns@^1.27.2:
  version "1.30.1"
  resolved "http://npm-registry.persgroep.digital/date-fns/-/date-fns-1.30.1.tgz#2e71bf0b119153dbb4cc4e88d9ea5acfb50dc05c"

date-format@^2.0.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/date-format/-/date-format-2.1.0.tgz#31d5b5ea211cf5fd764cd38baf9d033df7e125cf"

debug@*, debug@^4.0.0, debug@^4.1.0, debug@^4.1.1:
  version "4.3.4"
  resolved "http://npm-registry.persgroep.digital/debug/-/debug-4.3.4.tgz#1319f6579357f2338d3337d2cdd4914bb5dcc865"
  dependencies:
    ms "2.1.2"

debug@2.6.9, debug@^2.2.0, debug@^2.3.3, debug@^2.6.8, debug@^2.6.9:
  version "2.6.9"
  resolved "http://npm-registry.persgroep.digital/debug/-/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  dependencies:
    ms "2.0.0"

debug@3.1.0, debug@~3.1.0:
  version "3.1.0"
  resolved "http://npm-registry.persgroep.digital/debug/-/debug-3.1.0.tgz#5bb5a0672628b64149566ba16819e61518c67261"
  dependencies:
    ms "2.0.0"

debug@4.0.1:
  version "4.0.1"
  resolved "http://npm-registry.persgroep.digital/debug/-/debug-4.0.1.tgz#f9bb36d439b8d1f0dd52d8fb6b46e4ebb8c1cd5b"
  dependencies:
    ms "^2.1.1"

debug@^3.0.1, debug@^3.1.0, debug@^3.2.5, debug@^3.2.6, debug@^3.2.7:
  version "3.2.7"
  resolved "http://npm-registry.persgroep.digital/debug/-/debug-3.2.7.tgz#72580b7e9145fb39b6676f9c5e5fb100b934179a"
  dependencies:
    ms "^2.1.1"

decamelize-keys@^1.0.0:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/decamelize-keys/-/decamelize-keys-1.1.1.tgz#04a2d523b2f18d80d0158a43b895d56dff8d19d8"
  dependencies:
    decamelize "^1.1.0"
    map-obj "^1.0.0"

decamelize@^1.1.0, decamelize@^1.1.1, decamelize@^1.1.2, decamelize@^1.2.0:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/decamelize/-/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"

decamelize@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/decamelize/-/decamelize-2.0.0.tgz#656d7bbc8094c4c788ea53c5840908c9c7d063c7"
  dependencies:
    xregexp "4.0.0"

decode-uri-component@^0.2.0:
  version "0.2.2"
  resolved "http://npm-registry.persgroep.digital/decode-uri-component/-/decode-uri-component-0.2.2.tgz#e69dbe25d37941171dd540e024c444cd5188e1e9"

deep-eql@^4.1.2:
  version "4.1.3"
  resolved "http://npm-registry.persgroep.digital/deep-eql/-/deep-eql-4.1.3.tgz#7c7775513092f7df98d8df9996dd085eb668cc6d"
  dependencies:
    type-detect "^4.0.0"

deep-equal@^1.0.1:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/deep-equal/-/deep-equal-1.1.1.tgz#b5c98c942ceffaf7cb051e24e1434a25a2e6076a"
  dependencies:
    is-arguments "^1.0.4"
    is-date-object "^1.0.1"
    is-regex "^1.0.4"
    object-is "^1.0.1"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.2.0"

default-gateway@^2.6.0:
  version "2.7.2"
  resolved "http://npm-registry.persgroep.digital/default-gateway/-/default-gateway-2.7.2.tgz#b7ef339e5e024b045467af403d50348db4642d0f"
  dependencies:
    execa "^0.10.0"
    ip-regex "^2.1.0"

default-require-extensions@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/default-require-extensions/-/default-require-extensions-2.0.0.tgz#f5f8fbb18a7d6d50b21f641f649ebb522cfe24f7"
  dependencies:
    strip-bom "^3.0.0"

define-properties@^1.1.3, define-properties@^1.1.4, define-properties@^1.2.0:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/define-properties/-/define-properties-1.2.0.tgz#52988570670c9eacedd8064f4a990f2405849bd5"
  dependencies:
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

define-property@^0.2.5:
  version "0.2.5"
  resolved "http://npm-registry.persgroep.digital/define-property/-/define-property-0.2.5.tgz#c35b1ef918ec3c990f9a5bc57be04aacec5c8116"
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/define-property/-/define-property-1.0.0.tgz#769ebaaf3f4a63aad3af9e8d304c9bbe79bfb0e6"
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "http://npm-registry.persgroep.digital/define-property/-/define-property-2.0.2.tgz#d459689e8d654ba77e02a817f8710d702cb16e9d"
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

defined@^1.0.0:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/defined/-/defined-1.0.1.tgz#c0b9db27bfaffd95d6f61399419b893df0f91ebf"

del@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/del/-/del-3.0.0.tgz#53ecf699ffcbcb39637691ab13baf160819766e5"
  dependencies:
    globby "^6.1.0"
    is-path-cwd "^1.0.0"
    is-path-in-cwd "^1.0.0"
    p-map "^1.1.1"
    pify "^3.0.0"
    rimraf "^2.2.8"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/delayed-stream/-/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"

delegates@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/delegates/-/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a"

depd@2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/depd/-/depd-2.0.0.tgz#b696163cc757560d09cf22cc8fad1571b79e76df"

depd@~1.1.2:
  version "1.1.2"
  resolved "http://npm-registry.persgroep.digital/depd/-/depd-1.1.2.tgz#9bcd52e14c097763e749b274c4346ed2e560b5a9"

dependency-graph@^0.7.2:
  version "0.7.2"
  resolved "http://npm-registry.persgroep.digital/dependency-graph/-/dependency-graph-0.7.2.tgz#91db9de6eb72699209d88aea4c1fd5221cac1c49"

deps-sort@^2.0.0:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/deps-sort/-/deps-sort-2.0.1.tgz#9dfdc876d2bcec3386b6829ac52162cda9fa208d"
  dependencies:
    JSONStream "^1.0.3"
    shasum-object "^1.0.0"
    subarg "^1.0.0"
    through2 "^2.0.0"

des.js@^1.0.0:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/des.js/-/des.js-1.0.1.tgz#5382142e1bdc53f85d86d53e5f4aa7deb91e0843"
  dependencies:
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

destroy@1.2.0:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/destroy/-/destroy-1.2.0.tgz#4803735509ad8be552934c67df614f94e66fa015"

detect-indent@^4.0.0:
  version "4.0.0"
  resolved "http://npm-registry.persgroep.digital/detect-indent/-/detect-indent-4.0.0.tgz#f76d064352cdf43a1cb6ce619c4ee3a9475de208"
  dependencies:
    repeating "^2.0.0"

detect-node@^2.0.4:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/detect-node/-/detect-node-2.1.0.tgz#c9c70775a49c3d03bc2c06d9a73be550f978f8b1"

detective@^5.2.0:
  version "5.2.1"
  resolved "http://npm-registry.persgroep.digital/detective/-/detective-5.2.1.tgz#6af01eeda11015acb0e73f933242b70f24f91034"
  dependencies:
    acorn-node "^1.8.2"
    defined "^1.0.0"
    minimist "^1.2.6"

di@^0.0.1:
  version "0.0.1"
  resolved "http://npm-registry.persgroep.digital/di/-/di-0.0.1.tgz#806649326ceaa7caa3306d75d985ea2748ba913c"

dicer@0.2.5:
  version "0.2.5"
  resolved "http://npm-registry.persgroep.digital/dicer/-/dicer-0.2.5.tgz#5996c086bb33218c812c090bddc09cd12facb70f"
  dependencies:
    readable-stream "1.1.x"
    streamsearch "0.1.2"

diff@^3.0.0, diff@^3.2.0:
  version "3.5.0"
  resolved "http://npm-registry.persgroep.digital/diff/-/diff-3.5.0.tgz#800c0dd1e0a8bfbc95835c202ad220fe317e5a12"

diff@^4.0.1:
  version "4.0.2"
  resolved "http://npm-registry.persgroep.digital/diff/-/diff-4.0.2.tgz#60f3aecb89d5fae520c11aa19efc2bb982aade7d"

diffie-hellman@^5.0.0:
  version "5.0.3"
  resolved "http://npm-registry.persgroep.digital/diffie-hellman/-/diffie-hellman-5.0.3.tgz#40e8ee98f55a2149607146921c63e1ae5f3d2875"
  dependencies:
    bn.js "^4.1.0"
    miller-rabin "^4.0.0"
    randombytes "^2.0.0"

dir-glob@^2.0.0, dir-glob@^2.2.2:
  version "2.2.2"
  resolved "http://npm-registry.persgroep.digital/dir-glob/-/dir-glob-2.2.2.tgz#fa09f0694153c8918b18ba0deafae94769fc50c4"
  dependencies:
    path-type "^3.0.0"

dns-equal@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/dns-equal/-/dns-equal-1.0.0.tgz#b39e7f1da6eb0a75ba9c17324b34753c47e0654d"

dns-packet@^1.3.1:
  version "1.3.4"
  resolved "http://npm-registry.persgroep.digital/dns-packet/-/dns-packet-1.3.4.tgz#e3455065824a2507ba886c55a89963bb107dec6f"
  dependencies:
    ip "^1.1.0"
    safe-buffer "^5.0.1"

dns-txt@^2.0.2:
  version "2.0.2"
  resolved "http://npm-registry.persgroep.digital/dns-txt/-/dns-txt-2.0.2.tgz#b91d806f5d27188e4ab3e7d107d881a1cc4642b6"
  dependencies:
    buffer-indexof "^1.0.0"

dom-serialize@^2.2.0:
  version "2.2.1"
  resolved "http://npm-registry.persgroep.digital/dom-serialize/-/dom-serialize-2.2.1.tgz#562ae8999f44be5ea3076f5419dcd59eb43ac95b"
  dependencies:
    custom-event "~1.0.0"
    ent "~2.2.0"
    extend "^3.0.0"
    void-elements "^2.0.0"

dom-serializer@0:
  version "0.2.2"
  resolved "http://npm-registry.persgroep.digital/dom-serializer/-/dom-serializer-0.2.2.tgz#1afb81f533717175d478655debc5e332d9f9bb51"
  dependencies:
    domelementtype "^2.0.1"
    entities "^2.0.0"

domain-browser@^1.1.1, domain-browser@^1.2.0:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/domain-browser/-/domain-browser-1.2.0.tgz#3d31f50191a6749dd1375a7f522e823d42e54eda"

domelementtype@1, domelementtype@^1.3.1:
  version "1.3.1"
  resolved "http://npm-registry.persgroep.digital/domelementtype/-/domelementtype-1.3.1.tgz#d048c44b37b0d10a7f2a3d5fee3f4333d790481f"

domelementtype@^2.0.1:
  version "2.3.0"
  resolved "http://npm-registry.persgroep.digital/domelementtype/-/domelementtype-2.3.0.tgz#5c45e8e869952626331d7aab326d01daf65d589d"

domhandler@^2.3.0:
  version "2.4.2"
  resolved "http://npm-registry.persgroep.digital/domhandler/-/domhandler-2.4.2.tgz#8805097e933d65e85546f726d60f5eb88b44f803"
  dependencies:
    domelementtype "1"

dompurify@^2.2.0:
  version "2.4.5"
  resolved "http://npm-registry.persgroep.digital/dompurify/-/dompurify-2.4.5.tgz#0e89a27601f0bad978f9a924e7a05d5d2cccdd87"

domutils@^1.5.1:
  version "1.7.0"
  resolved "http://npm-registry.persgroep.digital/domutils/-/domutils-1.7.0.tgz#56ea341e834e06e6748af7a1cb25da67ea9f8c2a"
  dependencies:
    dom-serializer "0"
    domelementtype "1"

dot-prop@^5.2.0:
  version "5.3.0"
  resolved "http://npm-registry.persgroep.digital/dot-prop/-/dot-prop-5.3.0.tgz#90ccce708cd9cd82cc4dc8c3ddd9abdd55b20e88"
  dependencies:
    is-obj "^2.0.0"

duplexer2@^0.1.2, duplexer2@~0.1.0, duplexer2@~0.1.2:
  version "0.1.4"
  resolved "http://npm-registry.persgroep.digital/duplexer2/-/duplexer2-0.1.4.tgz#8b12dab878c0d69e3e7891051662a32fc6bddcc1"
  dependencies:
    readable-stream "^2.0.2"

duplexify@^3.4.2, duplexify@^3.6.0:
  version "3.7.1"
  resolved "http://npm-registry.persgroep.digital/duplexify/-/duplexify-3.7.1.tgz#2a4df5317f6ccfd91f86d6fd25d8d8a103b88309"
  dependencies:
    end-of-stream "^1.0.0"
    inherits "^2.0.1"
    readable-stream "^2.0.0"
    stream-shift "^1.0.0"

duration@^0.2.0, duration@^0.2.1:
  version "0.2.2"
  resolved "http://npm-registry.persgroep.digital/duration/-/duration-0.2.2.tgz#ddf149bc3bc6901150fe9017111d016b3357f529"
  dependencies:
    d "1"
    es5-ext "~0.10.46"

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "http://npm-registry.persgroep.digital/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz#3a83a904e54353287874c564b7549386849a98c9"
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

ee-first@1.1.1:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/ee-first/-/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"

electron-to-chromium@^1.4.284:
  version "1.4.394"
  resolved "http://npm-registry.persgroep.digital/electron-to-chromium/-/electron-to-chromium-1.4.394.tgz#989abe104a40366755648876cde2cdeda9f31133"

elegant-spinner@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/elegant-spinner/-/elegant-spinner-1.0.1.tgz#db043521c95d7e303fd8f345bedc3349cfb0729e"

elliptic@^6.5.3:
  version "6.5.4"
  resolved "http://npm-registry.persgroep.digital/elliptic/-/elliptic-6.5.4.tgz#da37cebd31e79a1367e941b592ed1fbebd58abbb"
  dependencies:
    bn.js "^4.11.9"
    brorand "^1.1.0"
    hash.js "^1.0.0"
    hmac-drbg "^1.0.1"
    inherits "^2.0.4"
    minimalistic-assert "^1.0.1"
    minimalistic-crypto-utils "^1.0.1"

emoji-regex@^7.0.1:
  version "7.0.3"
  resolved "http://npm-registry.persgroep.digital/emoji-regex/-/emoji-regex-7.0.3.tgz#933a04052860c85e83c122479c4748a8e4c72156"

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://npm-registry.persgroep.digital/emoji-regex/-/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"

emojis-list@^2.0.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/emojis-list/-/emojis-list-2.1.0.tgz#4daa4d9db00f9819880c79fa457ae5b09a1fd389"

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/emojis-list/-/emojis-list-3.0.0.tgz#5570662046ad29e2e916e71aae260abdff4f6a78"

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/encodeurl/-/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"

encoding@^0.1.11:
  version "0.1.13"
  resolved "http://npm-registry.persgroep.digital/encoding/-/encoding-0.1.13.tgz#56574afdd791f54a8e9b2785c0582a2d26210fa9"
  dependencies:
    iconv-lite "^0.6.2"

end-of-stream@^1.0.0, end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "http://npm-registry.persgroep.digital/end-of-stream/-/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"
  dependencies:
    once "^1.4.0"

engine.io-client@~3.2.0:
  version "3.2.1"
  resolved "http://npm-registry.persgroep.digital/engine.io-client/-/engine.io-client-3.2.1.tgz#6f54c0475de487158a1a7c77d10178708b6add36"
  dependencies:
    component-emitter "1.2.1"
    component-inherit "0.0.3"
    debug "~3.1.0"
    engine.io-parser "~2.1.1"
    has-cors "1.1.0"
    indexof "0.0.1"
    parseqs "0.0.5"
    parseuri "0.0.5"
    ws "~3.3.1"
    xmlhttprequest-ssl "~1.5.4"
    yeast "0.1.2"

engine.io-parser@~2.1.0, engine.io-parser@~2.1.1:
  version "2.1.3"
  resolved "http://npm-registry.persgroep.digital/engine.io-parser/-/engine.io-parser-2.1.3.tgz#757ab970fbf2dfb32c7b74b033216d5739ef79a6"
  dependencies:
    after "0.8.2"
    arraybuffer.slice "~0.0.7"
    base64-arraybuffer "0.1.5"
    blob "0.0.5"
    has-binary2 "~1.0.2"

engine.io@~3.2.0:
  version "3.2.1"
  resolved "http://npm-registry.persgroep.digital/engine.io/-/engine.io-3.2.1.tgz#b60281c35484a70ee0351ea0ebff83ec8c9522a2"
  dependencies:
    accepts "~1.3.4"
    base64id "1.0.0"
    cookie "0.3.1"
    debug "~3.1.0"
    engine.io-parser "~2.1.0"
    ws "~3.3.1"

enhanced-resolve@4.1.0:
  version "4.1.0"
  resolved "http://npm-registry.persgroep.digital/enhanced-resolve/-/enhanced-resolve-4.1.0.tgz#41c7e0bfdfe74ac1ffe1e57ad6a5c6c9f3742a7f"
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.4.0"
    tapable "^1.0.0"

enhanced-resolve@^4.1.0:
  version "4.5.0"
  resolved "http://npm-registry.persgroep.digital/enhanced-resolve/-/enhanced-resolve-4.5.0.tgz#2f3cfd84dbe3b487f18f2db2ef1e064a571ca5ec"
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.5.0"
    tapable "^1.0.0"

ent@~2.2.0:
  version "2.2.0"
  resolved "http://npm-registry.persgroep.digital/ent/-/ent-2.2.0.tgz#e964219325a21d05f44466a2f686ed6ce5f5dd1d"

entities@^1.1.1:
  version "1.1.2"
  resolved "http://npm-registry.persgroep.digital/entities/-/entities-1.1.2.tgz#bdfa735299664dfafd34529ed4f8522a275fea56"

entities@^2.0.0:
  version "2.2.0"
  resolved "http://npm-registry.persgroep.digital/entities/-/entities-2.2.0.tgz#098dc90ebb83d8dffa089d55256b351d34c4da55"

entities@~3.0.1:
  version "3.0.1"
  resolved "http://npm-registry.persgroep.digital/entities/-/entities-3.0.1.tgz#2b887ca62585e96db3903482d336c1006c3001d4"

err-code@^1.0.0:
  version "1.1.2"
  resolved "http://npm-registry.persgroep.digital/err-code/-/err-code-1.1.2.tgz#06e0116d3028f6aef4806849eb0ea6a748ae6960"

errno@^0.1.1, errno@^0.1.3, errno@~0.1.7:
  version "0.1.8"
  resolved "http://npm-registry.persgroep.digital/errno/-/errno-0.1.8.tgz#8bb3e9c7d463be4976ff888f76b4809ebc2e811f"
  dependencies:
    prr "~1.0.1"

error-ex@^1.2.0, error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://npm-registry.persgroep.digital/error-ex/-/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.6:
  version "2.1.4"
  resolved "http://npm-registry.persgroep.digital/error-stack-parser/-/error-stack-parser-2.1.4.tgz#229cb01cdbfa84440bfa91876285b94680188286"
  dependencies:
    stackframe "^1.3.4"

es5-ext@^0.10.35, es5-ext@^0.10.50, es5-ext@~0.10.46:
  version "0.10.62"
  resolved "http://npm-registry.persgroep.digital/es5-ext/-/es5-ext-0.10.62.tgz#5e6adc19a6da524bf3d1e02bbc8960e5eb49a9a5"
  dependencies:
    es6-iterator "^2.0.3"
    es6-symbol "^3.1.3"
    next-tick "^1.1.0"

es6-iterator@^2.0.3:
  version "2.0.3"
  resolved "http://npm-registry.persgroep.digital/es6-iterator/-/es6-iterator-2.0.3.tgz#a7de889141a05a94b0854403b2d0a0fbfa98f3b7"
  dependencies:
    d "1"
    es5-ext "^0.10.35"
    es6-symbol "^3.1.1"

es6-promise@^4.0.3:
  version "4.2.8"
  resolved "http://npm-registry.persgroep.digital/es6-promise/-/es6-promise-4.2.8.tgz#4eb21594c972bc40553d276e510539143db53e0a"

es6-promisify@^5.0.0:
  version "5.0.0"
  resolved "http://npm-registry.persgroep.digital/es6-promisify/-/es6-promisify-5.0.0.tgz#5109d62f3e56ea967c4b63505aef08291c8a5203"
  dependencies:
    es6-promise "^4.0.3"

es6-symbol@^3.1.1, es6-symbol@^3.1.3:
  version "3.1.3"
  resolved "http://npm-registry.persgroep.digital/es6-symbol/-/es6-symbol-3.1.3.tgz#bad5d3c1bcdac28269f4cb331e431c78ac705d18"
  dependencies:
    d "^1.0.1"
    ext "^1.1.2"

escalade@^3.1.1:
  version "3.1.1"
  resolved "http://npm-registry.persgroep.digital/escalade/-/escalade-3.1.1.tgz#d8cfdc7000965c5a0174b4a82eaa5c0552742e40"

escape-html@~1.0.3:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/escape-html/-/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "http://npm-registry.persgroep.digital/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"

eslint-scope@^4.0.0:
  version "4.0.3"
  resolved "http://npm-registry.persgroep.digital/eslint-scope/-/eslint-scope-4.0.3.tgz#ca03833310f6889a3264781aa82e63eb9cfe7848"
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

esprima@^4.0.0:
  version "4.0.1"
  resolved "http://npm-registry.persgroep.digital/esprima/-/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"

esrecurse@^4.1.0:
  version "4.3.0"
  resolved "http://npm-registry.persgroep.digital/esrecurse/-/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "http://npm-registry.persgroep.digital/estraverse/-/estraverse-4.3.0.tgz#398ad3f3c5a24948be7725e83d11a7de28cdbd1d"

estraverse@^5.2.0:
  version "5.3.0"
  resolved "http://npm-registry.persgroep.digital/estraverse/-/estraverse-5.3.0.tgz#2eea5290702f26ab8fe5370370ff86c965d21123"

esutils@^2.0.2:
  version "2.0.3"
  resolved "http://npm-registry.persgroep.digital/esutils/-/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"

etag@~1.8.1:
  version "1.8.1"
  resolved "http://npm-registry.persgroep.digital/etag/-/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"

eventemitter3@^4.0.0:
  version "4.0.7"
  resolved "http://npm-registry.persgroep.digital/eventemitter3/-/eventemitter3-4.0.7.tgz#2de9b68f6528d5644ef5c59526a1b4a07306169f"

events@^2.0.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/events/-/events-2.1.0.tgz#2a9a1e18e6106e0e812aa9ebd4a819b3c29c0ba5"

events@^3.0.0:
  version "3.3.0"
  resolved "http://npm-registry.persgroep.digital/events/-/events-3.3.0.tgz#31a95ad0a924e2d2c419a813aeb2c4e878ea7400"

eventsource@^1.0.7:
  version "1.1.2"
  resolved "http://npm-registry.persgroep.digital/eventsource/-/eventsource-1.1.2.tgz#bc75ae1c60209e7cb1541231980460343eaea7c2"

evp_bytestokey@^1.0.0, evp_bytestokey@^1.0.3:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz#7fcbdb198dc71959432efe13842684e0525acb02"
  dependencies:
    md5.js "^1.3.4"
    safe-buffer "^5.1.1"

execa@0.10.0, execa@^0.10.0:
  version "0.10.0"
  resolved "http://npm-registry.persgroep.digital/execa/-/execa-0.10.0.tgz#ff456a8f53f90f8eccc71a96d11bdfc7f082cb50"
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^3.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/execa/-/execa-1.0.0.tgz#c6236a5bb4df6d6f15e88e7f017798216749ddd8"
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execall@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/execall/-/execall-1.0.0.tgz#73d0904e395b3cab0658b08d09ec25307f29bb73"
  dependencies:
    clone-regexp "^1.0.0"

executable@4.1.1:
  version "4.1.1"
  resolved "http://npm-registry.persgroep.digital/executable/-/executable-4.1.1.tgz#41532bff361d3e57af4d763b70582db18f5d133c"
  dependencies:
    pify "^2.2.0"

exit-hook@^1.0.0:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/exit-hook/-/exit-hook-1.1.1.tgz#f05ca233b48c05d54fff07765df8507e95c02ff8"

expand-brackets@^0.1.4:
  version "0.1.5"
  resolved "http://npm-registry.persgroep.digital/expand-brackets/-/expand-brackets-0.1.5.tgz#df07284e342a807cd733ac5af72411e581d1177b"
  dependencies:
    is-posix-bracket "^0.1.0"

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "http://npm-registry.persgroep.digital/expand-brackets/-/expand-brackets-2.1.4.tgz#b77735e315ce30f6b6eff0f83b04151a22449622"
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

expand-range@^1.8.1:
  version "1.8.2"
  resolved "http://npm-registry.persgroep.digital/expand-range/-/expand-range-1.8.2.tgz#a299effd335fe2721ebae8e257ec79644fc85337"
  dependencies:
    fill-range "^2.1.0"

express@^4.16.2, express@^4.16.4:
  version "4.18.2"
  resolved "http://npm-registry.persgroep.digital/express/-/express-4.18.2.tgz#3fabe08296e930c796c19e3c516979386ba9fd59"
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.20.1"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.5.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "2.0.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.2.0"
    fresh "0.5.2"
    http-errors "2.0.0"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.7"
    qs "6.11.0"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.18.0"
    serve-static "1.15.0"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

ext@^1.1.2:
  version "1.7.0"
  resolved "http://npm-registry.persgroep.digital/ext/-/ext-1.7.0.tgz#0ea4383c0103d60e70be99e9a7f11027a33c4f5f"
  dependencies:
    type "^2.7.2"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/extend-shallow/-/extend-shallow-2.0.1.tgz#51af7d614ad9a9f610ea1bafbb989d6b1c56890f"
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "http://npm-registry.persgroep.digital/extend-shallow/-/extend-shallow-3.0.2.tgz#26a71aaf073b39fb2127172746131c2704028db8"
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend@^3.0.0, extend@~3.0.2:
  version "3.0.2"
  resolved "http://npm-registry.persgroep.digital/extend/-/extend-3.0.2.tgz#f8b1136b4071fbd8eb140aff858b1019ec2915fa"

external-editor@^3.0.0:
  version "3.1.0"
  resolved "http://npm-registry.persgroep.digital/external-editor/-/external-editor-3.1.0.tgz#cb03f740befae03ea4d283caed2741a83f335495"
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extglob@^0.3.1:
  version "0.3.2"
  resolved "http://npm-registry.persgroep.digital/extglob/-/extglob-0.3.2.tgz#2e18ff3d2f49ab2765cec9023f011daa8d8349a1"
  dependencies:
    is-extglob "^1.0.0"

extglob@^2.0.4:
  version "2.0.4"
  resolved "http://npm-registry.persgroep.digital/extglob/-/extglob-2.0.4.tgz#ad00fe4dc612a9232e8718711dc5cb5ab0285543"
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

extract-zip@1.6.7:
  version "1.6.7"
  resolved "http://npm-registry.persgroep.digital/extract-zip/-/extract-zip-1.6.7.tgz#a840b4b8af6403264c8db57f4f1a74333ef81fe9"
  dependencies:
    concat-stream "1.6.2"
    debug "2.6.9"
    mkdirp "0.5.1"
    yauzl "2.4.1"

extract-zip@^1.6.5:
  version "1.7.0"
  resolved "http://npm-registry.persgroep.digital/extract-zip/-/extract-zip-1.7.0.tgz#556cc3ae9df7f452c493a0cfb51cc30277940927"
  dependencies:
    concat-stream "^1.6.2"
    debug "^2.6.9"
    mkdirp "^0.5.4"
    yauzl "^2.10.0"

extsprintf@1.3.0:
  version "1.3.0"
  resolved "http://npm-registry.persgroep.digital/extsprintf/-/extsprintf-1.3.0.tgz#96918440e3041a7a414f8c52e3c574eb3c3e1e05"

extsprintf@^1.2.0:
  version "1.4.1"
  resolved "http://npm-registry.persgroep.digital/extsprintf/-/extsprintf-1.4.1.tgz#8d172c064867f235c0c84a596806d279bf4bcc07"

faker@^4.1.0:
  version "4.1.0"
  resolved "http://npm-registry.persgroep.digital/faker/-/faker-4.1.0.tgz#1e45bbbecc6774b3c195fad2835109c6d748cc3f"

fast-deep-equal@^1.0.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/fast-deep-equal/-/fast-deep-equal-1.1.0.tgz#c053477817c86b51daa853c81e059b733d023614"

fast-deep-equal@^2.0.1:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/fast-deep-equal/-/fast-deep-equal-2.0.1.tgz#7b05218ddf9667bf7f370bf7fdb2cb15fdd0aa49"

fast-deep-equal@^3.1.1:
  version "3.1.3"
  resolved "http://npm-registry.persgroep.digital/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"

fast-glob@^2.2.6:
  version "2.2.7"
  resolved "http://npm-registry.persgroep.digital/fast-glob/-/fast-glob-2.2.7.tgz#6953857c3afa475fff92ee6015d52da70a4cd39d"
  dependencies:
    "@mrmlnc/readdir-enhanced" "^2.2.1"
    "@nodelib/fs.stat" "^1.1.2"
    glob-parent "^3.1.0"
    is-glob "^4.0.0"
    merge2 "^1.2.3"
    micromatch "^3.1.10"

fast-json-stable-stringify@2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/fast-json-stable-stringify/-/fast-json-stable-stringify-2.0.0.tgz#d5142c0caee6b1189f87d3a76111064f86c8bbf2"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"

fast-safe-stringify@^2.0.7:
  version "2.1.1"
  resolved "http://npm-registry.persgroep.digital/fast-safe-stringify/-/fast-safe-stringify-2.1.1.tgz#c406a83b6e70d9e35ce3b30a81141df30aeba884"

fastparse@^1.1.2:
  version "1.1.2"
  resolved "http://npm-registry.persgroep.digital/fastparse/-/fastparse-1.1.2.tgz#91728c5a5942eced8531283c79441ee4122c35a9"

faye-websocket@^0.10.0:
  version "0.10.0"
  resolved "http://npm-registry.persgroep.digital/faye-websocket/-/faye-websocket-0.10.0.tgz#4e492f8d04dfb6f89003507f6edbf2d501e7c6f4"
  dependencies:
    websocket-driver ">=0.5.1"

faye-websocket@~0.11.1:
  version "0.11.4"
  resolved "http://npm-registry.persgroep.digital/faye-websocket/-/faye-websocket-0.11.4.tgz#7f0d9275cfdd86a1c963dc8b65fcc451edcbb1da"
  dependencies:
    websocket-driver ">=0.5.1"

fd-slicer@~1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/fd-slicer/-/fd-slicer-1.0.1.tgz#8b5bcbd9ec327c5041bf9ab023fd6750f1177e65"
  dependencies:
    pend "~1.2.0"

fd-slicer@~1.1.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/fd-slicer/-/fd-slicer-1.1.0.tgz#25c7c89cb1f9077f8891bbe61d8f390eae256f1e"
  dependencies:
    pend "~1.2.0"

fflate@^0.4.8:
  version "0.4.8"
  resolved "http://npm-registry.persgroep.digital/fflate/-/fflate-0.4.8.tgz#f90b82aefbd8ac174213abb338bd7ef848f0f5ae"

figgy-pudding@^3.4.1, figgy-pudding@^3.5.1:
  version "3.5.2"
  resolved "http://npm-registry.persgroep.digital/figgy-pudding/-/figgy-pudding-3.5.2.tgz#b4eee8148abb01dcf1d1ac34367d59e12fa61d6e"

figures@2.0.0, figures@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/figures/-/figures-2.0.0.tgz#3ab1a2d2a62c8bfb431a0c94cb797a2fce27c962"
  dependencies:
    escape-string-regexp "^1.0.5"

figures@^1.7.0:
  version "1.7.0"
  resolved "http://npm-registry.persgroep.digital/figures/-/figures-1.7.0.tgz#cbe1e3affcf1cd44b80cadfed28dc793a9701d2e"
  dependencies:
    escape-string-regexp "^1.0.5"
    object-assign "^4.1.0"

file-entry-cache@^4.0.0:
  version "4.0.0"
  resolved "http://npm-registry.persgroep.digital/file-entry-cache/-/file-entry-cache-4.0.0.tgz#633567d15364aefe0b299e1e217735e8f3a9f6e8"
  dependencies:
    flat-cache "^2.0.1"

file-loader@3.0.1:
  version "3.0.1"
  resolved "http://npm-registry.persgroep.digital/file-loader/-/file-loader-3.0.1.tgz#f8e0ba0b599918b51adfe45d66d1e771ad560faa"
  dependencies:
    loader-utils "^1.0.2"
    schema-utils "^1.0.0"

file-uri-to-path@1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz#553a7b8446ff6f684359c445f1e37a05dacc33dd"

filename-regex@^2.0.0:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/filename-regex/-/filename-regex-2.0.1.tgz#c1c4b9bee3e09725ddb106b75c1e301fe2f18b26"

fileset@^2.0.3:
  version "2.0.3"
  resolved "http://npm-registry.persgroep.digital/fileset/-/fileset-2.0.3.tgz#8e7548a96d3cc2327ee5e674168723a333bba2a0"
  dependencies:
    glob "^7.0.3"
    minimatch "^3.0.3"

fill-range@^2.1.0:
  version "2.2.4"
  resolved "http://npm-registry.persgroep.digital/fill-range/-/fill-range-2.2.4.tgz#eb1e773abb056dcd8df2bfdf6af59b8b3a936565"
  dependencies:
    is-number "^2.1.0"
    isobject "^2.0.0"
    randomatic "^3.0.0"
    repeat-element "^1.1.2"
    repeat-string "^1.5.2"

fill-range@^4.0.0:
  version "4.0.0"
  resolved "http://npm-registry.persgroep.digital/fill-range/-/fill-range-4.0.0.tgz#d544811d428f98eb06a63dc402d2403c328c38f7"
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "http://npm-registry.persgroep.digital/fill-range/-/fill-range-7.0.1.tgz#1919a6a7c75fe38b2c7c77e5198535da9acdda40"
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@1.1.2:
  version "1.1.2"
  resolved "http://npm-registry.persgroep.digital/finalhandler/-/finalhandler-1.1.2.tgz#b7e7d000ffd11938d0fdb053506f6ebabe9f587d"
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    statuses "~1.5.0"
    unpipe "~1.0.0"

finalhandler@1.2.0:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/finalhandler/-/finalhandler-1.2.0.tgz#7d23fe5731b207b4640e4fcd00aec1f9207a7b32"
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    statuses "2.0.1"
    unpipe "~1.0.0"

find-cache-dir@^2.1.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/find-cache-dir/-/find-cache-dir-2.1.0.tgz#8d0f94cd13fe43c6c7c261a0d86115ca918c05f7"
  dependencies:
    commondir "^1.0.1"
    make-dir "^2.0.0"
    pkg-dir "^3.0.0"

find-up@^1.0.0:
  version "1.1.2"
  resolved "http://npm-registry.persgroep.digital/find-up/-/find-up-1.1.2.tgz#6b2e9822b1a2ce0a60ab64d610eccad53cb24d0f"
  dependencies:
    path-exists "^2.0.0"
    pinkie-promise "^2.0.0"

find-up@^2.0.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/find-up/-/find-up-2.1.0.tgz#45d1b7e506c717ddd482775a2b77920a3c0c57a7"
  dependencies:
    locate-path "^2.0.0"

find-up@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/find-up/-/find-up-3.0.0.tgz#49169f1d7993430646da61ecc5ae355c21c97b73"
  dependencies:
    locate-path "^3.0.0"

flat-cache@^2.0.1:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/flat-cache/-/flat-cache-2.0.1.tgz#5d296d6f04bda44a4630a301413bdbc2ec085ec0"
  dependencies:
    flatted "^2.0.0"
    rimraf "2.6.3"
    write "1.0.3"

flatted@^2.0.0:
  version "2.0.2"
  resolved "http://npm-registry.persgroep.digital/flatted/-/flatted-2.0.2.tgz#4575b21e2bcee7434aa9be662f4b7b5f9c2b5138"

flush-write-stream@^1.0.0:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/flush-write-stream/-/flush-write-stream-1.1.1.tgz#8dd7d873a1babc207d94ead0c2e0e44276ebf2e8"
  dependencies:
    inherits "^2.0.3"
    readable-stream "^2.3.6"

follow-redirects@^1.0.0:
  version "1.15.2"
  resolved "http://npm-registry.persgroep.digital/follow-redirects/-/follow-redirects-1.15.2.tgz#b460864144ba63f2681096f274c4e57026da2c13"

for-in@^0.1.3:
  version "0.1.8"
  resolved "http://npm-registry.persgroep.digital/for-in/-/for-in-0.1.8.tgz#d8773908e31256109952b1fdb9b3fa867d2775e1"

for-in@^1.0.1, for-in@^1.0.2:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/for-in/-/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80"

for-own@^0.1.4:
  version "0.1.5"
  resolved "http://npm-registry.persgroep.digital/for-own/-/for-own-0.1.5.tgz#5265c681a4f294dabbf17c9509b6763aa84510ce"
  dependencies:
    for-in "^1.0.1"

for-own@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/for-own/-/for-own-1.0.0.tgz#c63332f415cedc4b04dbfe70cf836494c53cb44b"
  dependencies:
    for-in "^1.0.1"

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "http://npm-registry.persgroep.digital/forever-agent/-/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"

form-data@~2.3.2:
  version "2.3.3"
  resolved "http://npm-registry.persgroep.digital/form-data/-/form-data-2.3.3.tgz#dcce52c05f644f298c6a7ab936bd724ceffbf3a6"
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

forwarded@0.2.0:
  version "0.2.0"
  resolved "http://npm-registry.persgroep.digital/forwarded/-/forwarded-0.2.0.tgz#2269936428aad4c15c7ebe9779a84bf0b2a81811"

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "http://npm-registry.persgroep.digital/fragment-cache/-/fragment-cache-0.2.1.tgz#4290fad27f13e89be7f33799c6bc5a0abfff0d19"
  dependencies:
    map-cache "^0.2.2"

fresh@0.5.2:
  version "0.5.2"
  resolved "http://npm-registry.persgroep.digital/fresh/-/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"

from2@^2.1.0:
  version "2.3.0"
  resolved "http://npm-registry.persgroep.digital/from2/-/from2-2.3.0.tgz#8bfb5502bde4a4d36cfdeea007fcca21d7e382af"
  dependencies:
    inherits "^2.0.1"
    readable-stream "^2.0.0"

fs-access@^1.0.0:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/fs-access/-/fs-access-1.0.1.tgz#d6a87f262271cefebec30c553407fb995da8777a"
  dependencies:
    null-check "^1.0.0"

fs-extra@4.0.1:
  version "4.0.1"
  resolved "http://npm-registry.persgroep.digital/fs-extra/-/fs-extra-4.0.1.tgz#7fc0c6c8957f983f57f306a24e5b9ddd8d0dd880"
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^3.0.0"
    universalify "^0.1.0"

fs-extra@7.0.0:
  version "7.0.0"
  resolved "http://npm-registry.persgroep.digital/fs-extra/-/fs-extra-7.0.0.tgz#8cc3f47ce07ef7b3593a11b9fb245f7e34c041d6"
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-extra@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/fs-extra/-/fs-extra-1.0.0.tgz#cd3ce5f7e7cb6145883fcae3191e9877f8587950"
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^2.1.0"
    klaw "^1.0.0"

fs-extra@^7.0.1:
  version "7.0.1"
  resolved "http://npm-registry.persgroep.digital/fs-extra/-/fs-extra-7.0.1.tgz#4f189c44aa123b895f722804f55ea23eadc348e9"
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-minipass@^1.2.7:
  version "1.2.7"
  resolved "http://npm-registry.persgroep.digital/fs-minipass/-/fs-minipass-1.2.7.tgz#ccff8570841e7fe4265693da88936c55aed7f7c7"
  dependencies:
    minipass "^2.6.0"

fs-write-stream-atomic@^1.0.8:
  version "1.0.10"
  resolved "http://npm-registry.persgroep.digital/fs-write-stream-atomic/-/fs-write-stream-atomic-1.0.10.tgz#b47df53493ef911df75731e70a9ded0189db40c9"
  dependencies:
    graceful-fs "^4.1.2"
    iferr "^0.1.5"
    imurmurhash "^0.1.4"
    readable-stream "1 || 2"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/fs.realpath/-/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"

fsevents@^1.0.0, fsevents@^1.2.2, fsevents@^1.2.7:
  version "1.2.13"
  resolved "http://npm-registry.persgroep.digital/fsevents/-/fsevents-1.2.13.tgz#f325cb0455592428bcf11b383370ef70e3bfcc38"
  dependencies:
    bindings "^1.5.0"
    nan "^2.12.1"

fsevents@~2.3.2:
  version "2.3.2"
  resolved "http://npm-registry.persgroep.digital/fsevents/-/fsevents-2.3.2.tgz#8a526f78b8fdf4623b709e0b975c52c24c02fd1a"

fstream@^1.0.0, fstream@^1.0.12:
  version "1.0.12"
  resolved "http://npm-registry.persgroep.digital/fstream/-/fstream-1.0.12.tgz#4e8ba8ee2d48be4f7d0de505455548eae5932045"
  dependencies:
    graceful-fs "^4.1.2"
    inherits "~2.0.0"
    mkdirp ">=0.5 0"
    rimraf "2"

function-bind@^1.1.1:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/function-bind/-/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"

functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "http://npm-registry.persgroep.digital/functions-have-names/-/functions-have-names-1.2.3.tgz#0404fe4ee2ba2f607f0e0ec3c80bae994133b834"

gauge@~2.7.3:
  version "2.7.4"
  resolved "http://npm-registry.persgroep.digital/gauge/-/gauge-2.7.4.tgz#2c03405c7538c39d7eb37b317022e325fb018bf7"
  dependencies:
    aproba "^1.0.3"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.0"
    object-assign "^4.1.0"
    signal-exit "^3.0.0"
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wide-align "^1.1.0"

gaze@^1.0.0:
  version "1.1.3"
  resolved "http://npm-registry.persgroep.digital/gaze/-/gaze-1.1.3.tgz#c441733e13b927ac8c0ff0b4c3b033f28812924a"
  dependencies:
    globule "^1.0.0"

genfun@^5.0.0:
  version "5.0.0"
  resolved "http://npm-registry.persgroep.digital/genfun/-/genfun-5.0.0.tgz#9dd9710a06900a5c4a5bf57aca5da4e52fe76537"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "http://npm-registry.persgroep.digital/gensync/-/gensync-1.0.0-beta.2.tgz#32a6ee76c3d7f52d46b2b1ae5d93fea8580a25e0"

get-assigned-identifiers@^1.2.0:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/get-assigned-identifiers/-/get-assigned-identifiers-1.2.0.tgz#6dbf411de648cbaf8d9169ebb0d2d576191e2ff1"

get-caller-file@^1.0.1:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/get-caller-file/-/get-caller-file-1.0.3.tgz#f978fa4c90d1dfe7ff2d6beda2a515e713bdcf4a"

get-caller-file@^2.0.1:
  version "2.0.5"
  resolved "http://npm-registry.persgroep.digital/get-caller-file/-/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"

get-func-name@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/get-func-name/-/get-func-name-2.0.0.tgz#ead774abee72e20409433a066366023dd6887a41"

get-intrinsic@^1.0.2, get-intrinsic@^1.1.1:
  version "1.2.1"
  resolved "http://npm-registry.persgroep.digital/get-intrinsic/-/get-intrinsic-1.2.1.tgz#d295644fed4505fc9cde952c37ee12b477a83d82"
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"

get-stdin@^4.0.1:
  version "4.0.1"
  resolved "http://npm-registry.persgroep.digital/get-stdin/-/get-stdin-4.0.1.tgz#b968c6b0a04384324902e8bf1a5df32579a450fe"

get-stdin@^6.0.0:
  version "6.0.0"
  resolved "http://npm-registry.persgroep.digital/get-stdin/-/get-stdin-6.0.0.tgz#9e09bf712b360ab9225e812048f71fde9c89657b"

get-stream@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/get-stream/-/get-stream-3.0.0.tgz#8e943d1358dc37555054ecbe2edb05aa174ede14"

get-stream@^4.0.0, get-stream@^4.1.0:
  version "4.1.0"
  resolved "http://npm-registry.persgroep.digital/get-stream/-/get-stream-4.1.0.tgz#c1b255575f3dc21d59bfc79cd3d2b46b1c3a54b5"
  dependencies:
    pump "^3.0.0"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "http://npm-registry.persgroep.digital/get-value/-/get-value-2.0.6.tgz#dc15ca1c672387ca76bd37ac0a395ba2042a2c28"

getos@3.1.0:
  version "3.1.0"
  resolved "http://npm-registry.persgroep.digital/getos/-/getos-3.1.0.tgz#db3aa4df15a3295557ce5e81aa9e3e5cdfaa6567"
  dependencies:
    async "2.4.0"

getpass@^0.1.1:
  version "0.1.7"
  resolved "http://npm-registry.persgroep.digital/getpass/-/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
  dependencies:
    assert-plus "^1.0.0"

gherkin@^5.0.0, gherkin@^5.1.0:
  version "5.1.0"
  resolved "http://npm-registry.persgroep.digital/gherkin/-/gherkin-5.1.0.tgz#684bbb03add24eaf7bdf544f58033eb28fb3c6d5"

glob-base@^0.3.0:
  version "0.3.0"
  resolved "http://npm-registry.persgroep.digital/glob-base/-/glob-base-0.3.0.tgz#dbb164f6221b1c0b1ccf82aea328b497df0ea3c4"
  dependencies:
    glob-parent "^2.0.0"
    is-glob "^2.0.0"

glob-parent@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/glob-parent/-/glob-parent-2.0.0.tgz#81383d72db054fcccf5336daa902f182f6edbb28"
  dependencies:
    is-glob "^2.0.0"

glob-parent@^3.1.0:
  version "3.1.0"
  resolved "http://npm-registry.persgroep.digital/glob-parent/-/glob-parent-3.1.0.tgz#9e6af6299d8d3bd2bd40430832bd113df906c5ae"
  dependencies:
    is-glob "^3.1.0"
    path-dirname "^1.0.0"

glob-parent@~5.1.2:
  version "5.1.2"
  resolved "http://npm-registry.persgroep.digital/glob-parent/-/glob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  dependencies:
    is-glob "^4.0.1"

glob-to-regexp@^0.3.0:
  version "0.3.0"
  resolved "http://npm-registry.persgroep.digital/glob-to-regexp/-/glob-to-regexp-0.3.0.tgz#8c5a1494d2066c570cc3bfe4496175acc4d502ab"

glob@7.0.x:
  version "7.0.6"
  resolved "http://npm-registry.persgroep.digital/glob/-/glob-7.0.6.tgz#211bafaf49e525b8cd93260d14ab136152b3f57a"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.2"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@7.1.3:
  version "7.1.3"
  resolved "http://npm-registry.persgroep.digital/glob/-/glob-7.1.3.tgz#3960832d3f1574108342dafd3a67b332c0969df1"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^7.0.0, glob@^7.0.3, glob@^7.1.0, glob@^7.1.1, glob@^7.1.2, glob@^7.1.3, glob@^7.1.4:
  version "7.2.3"
  resolved "http://npm-registry.persgroep.digital/glob/-/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@~7.1.1:
  version "7.1.7"
  resolved "http://npm-registry.persgroep.digital/glob/-/glob-7.1.7.tgz#3b193e9233f01d42d0b3f78294bbeeb418f94a90"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global-dirs@^0.1.0:
  version "0.1.1"
  resolved "http://npm-registry.persgroep.digital/global-dirs/-/global-dirs-0.1.1.tgz#b319c0dd4607f353f3be9cca4c72fc148c49f445"
  dependencies:
    ini "^1.3.4"

global-modules@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/global-modules/-/global-modules-2.0.0.tgz#997605ad2345f27f51539bea26574421215c7780"
  dependencies:
    global-prefix "^3.0.0"

global-prefix@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/global-prefix/-/global-prefix-3.0.0.tgz#fc85f73064df69f50421f47f883fe5b913ba9b97"
  dependencies:
    ini "^1.3.5"
    kind-of "^6.0.2"
    which "^1.3.1"

globals@^11.1.0:
  version "11.12.0"
  resolved "http://npm-registry.persgroep.digital/globals/-/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"

globals@^9.18.0:
  version "9.18.0"
  resolved "http://npm-registry.persgroep.digital/globals/-/globals-9.18.0.tgz#aa3896b3e69b487f17e31ed2143d69a8e30c2d8a"

globby@^6.1.0:
  version "6.1.0"
  resolved "http://npm-registry.persgroep.digital/globby/-/globby-6.1.0.tgz#f5a6d70e8395e21c858fb0489d64df02424d506c"
  dependencies:
    array-union "^1.0.1"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

globby@^7.1.1:
  version "7.1.1"
  resolved "http://npm-registry.persgroep.digital/globby/-/globby-7.1.1.tgz#fb2ccff9401f8600945dfada97440cca972b8680"
  dependencies:
    array-union "^1.0.1"
    dir-glob "^2.0.0"
    glob "^7.1.2"
    ignore "^3.3.5"
    pify "^3.0.0"
    slash "^1.0.0"

globby@^9.0.0:
  version "9.2.0"
  resolved "http://npm-registry.persgroep.digital/globby/-/globby-9.2.0.tgz#fd029a706c703d29bdd170f4b6db3a3f7a7cb63d"
  dependencies:
    "@types/glob" "^7.1.1"
    array-union "^1.0.2"
    dir-glob "^2.2.2"
    fast-glob "^2.2.6"
    glob "^7.1.3"
    ignore "^4.0.3"
    pify "^4.0.1"
    slash "^2.0.0"

globjoin@^0.1.4:
  version "0.1.4"
  resolved "http://npm-registry.persgroep.digital/globjoin/-/globjoin-0.1.4.tgz#2f4494ac8919e3767c5cbb691e9f463324285d43"

globule@^1.0.0:
  version "1.3.4"
  resolved "http://npm-registry.persgroep.digital/globule/-/globule-1.3.4.tgz#7c11c43056055a75a6e68294453c17f2796170fb"
  dependencies:
    glob "~7.1.1"
    lodash "^4.17.21"
    minimatch "~3.0.2"

gonzales-pe@^4.2.3:
  version "4.3.0"
  resolved "http://npm-registry.persgroep.digital/gonzales-pe/-/gonzales-pe-4.3.0.tgz#fe9dec5f3c557eead09ff868c65826be54d067b3"
  dependencies:
    minimist "^1.2.5"

graceful-fs@^4.1.11, graceful-fs@^4.1.15, graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.1.9:
  version "4.2.11"
  resolved "http://npm-registry.persgroep.digital/graceful-fs/-/graceful-fs-4.2.11.tgz#4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3"

handle-thing@^2.0.0:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/handle-thing/-/handle-thing-2.0.1.tgz#857f79ce359580c340d43081cc648970d0bb234e"

har-schema@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/har-schema/-/har-schema-2.0.0.tgz#a94c2224ebcac04782a0d9035521f24735b7ec92"

har-validator@~5.1.0, har-validator@~5.1.3:
  version "5.1.5"
  resolved "http://npm-registry.persgroep.digital/har-validator/-/har-validator-5.1.5.tgz#1f0803b9f8cb20c0fa13822df1ecddb36bde1efd"
  dependencies:
    ajv "^6.12.3"
    har-schema "^2.0.0"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/has-ansi/-/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  dependencies:
    ansi-regex "^2.0.0"

has-binary2@~1.0.2:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/has-binary2/-/has-binary2-1.0.3.tgz#7776ac627f3ea77250cfc332dab7ddf5e4f5d11d"
  dependencies:
    isarray "2.0.1"

has-cors@1.1.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/has-cors/-/has-cors-1.1.0.tgz#5e474793f7ea9843d1bb99c23eef49ff126fff39"

has-flag@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/has-flag/-/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"

has-property-descriptors@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/has-property-descriptors/-/has-property-descriptors-1.0.0.tgz#610708600606d36961ed04c196193b6a607fa861"
  dependencies:
    get-intrinsic "^1.1.1"

has-proto@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/has-proto/-/has-proto-1.0.1.tgz#1885c1305538958aff469fef37937c22795408e0"

has-symbols@^1.0.2, has-symbols@^1.0.3:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/has-symbols/-/has-symbols-1.0.3.tgz#bb7b2c4349251dce87b125f7bdf874aa7c8b39f8"

has-tostringtag@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/has-tostringtag/-/has-tostringtag-1.0.0.tgz#7e133818a7d394734f941e73c3d3f9291e658b25"
  dependencies:
    has-symbols "^1.0.2"

has-unicode@^2.0.0:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/has-unicode/-/has-unicode-2.0.1.tgz#e0e6fe6a28cf51138855e086d1691e771de2a8b9"

has-value@^0.3.1:
  version "0.3.1"
  resolved "http://npm-registry.persgroep.digital/has-value/-/has-value-0.3.1.tgz#7b1f58bada62ca827ec0a2078025654845995e1f"
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/has-value/-/has-value-1.0.0.tgz#18b281da585b1c5c51def24c930ed29a0be6b177"
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "http://npm-registry.persgroep.digital/has-values/-/has-values-0.1.4.tgz#6d61de95d91dfca9b9a02089ad384bff8f62b771"

has-values@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/has-values/-/has-values-1.0.0.tgz#95b0b63fec2146619a6fe57fe75628d5a39efe4f"
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.0, has@^1.0.3:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/has/-/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
  dependencies:
    function-bind "^1.1.1"

hash-base@^3.0.0:
  version "3.1.0"
  resolved "http://npm-registry.persgroep.digital/hash-base/-/hash-base-3.1.0.tgz#55c381d9e06e1d2997a883b4a3fddfe7f0d3af33"
  dependencies:
    inherits "^2.0.4"
    readable-stream "^3.6.0"
    safe-buffer "^5.2.0"

hash.js@^1.0.0, hash.js@^1.0.3:
  version "1.1.7"
  resolved "http://npm-registry.persgroep.digital/hash.js/-/hash.js-1.1.7.tgz#0babca538e8d4ee4a0f8988d68866537a003cf42"
  dependencies:
    inherits "^2.0.3"
    minimalistic-assert "^1.0.1"

hasha@^2.2.0:
  version "2.2.0"
  resolved "http://npm-registry.persgroep.digital/hasha/-/hasha-2.2.0.tgz#78d7cbfc1e6d66303fe79837365984517b2f6ee1"
  dependencies:
    is-stream "^1.0.1"
    pinkie-promise "^2.0.0"

hmac-drbg@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/hmac-drbg/-/hmac-drbg-1.0.1.tgz#d2745701025a6c775a6c545793ed502fc0c649a1"
  dependencies:
    hash.js "^1.0.3"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.1"

hosted-git-info@^2.1.4, hosted-git-info@^2.6.0, hosted-git-info@^2.7.1:
  version "2.8.9"
  resolved "http://npm-registry.persgroep.digital/hosted-git-info/-/hosted-git-info-2.8.9.tgz#dffc0bf9a21c02209090f2aa69429e1414daf3f9"

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "http://npm-registry.persgroep.digital/hpack.js/-/hpack.js-2.1.6.tgz#87774c0949e513f42e84575b3c45681fade2a0b2"
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

html-entities@^1.2.0:
  version "1.4.0"
  resolved "http://npm-registry.persgroep.digital/html-entities/-/html-entities-1.4.0.tgz#cfbd1b01d2afaf9adca1b10ae7dffab98c71d2dc"

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "http://npm-registry.persgroep.digital/html-escaper/-/html-escaper-2.0.2.tgz#dfd60027da36a36dfcbe236262c00a5822681453"

html-tags@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/html-tags/-/html-tags-2.0.0.tgz#10b30a386085f43cede353cc8fa7cb0deeea668b"

html2canvas@^1.0.0-rc.5:
  version "1.4.1"
  resolved "http://npm-registry.persgroep.digital/html2canvas/-/html2canvas-1.4.1.tgz#7cef1888311b5011d507794a066041b14669a543"
  dependencies:
    css-line-break "^2.1.0"
    text-segmentation "^1.0.3"

htmlescape@^1.1.0:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/htmlescape/-/htmlescape-1.1.1.tgz#3a03edc2214bca3b66424a3e7959349509cb0351"

htmlparser2@^3.10.0:
  version "3.10.1"
  resolved "http://npm-registry.persgroep.digital/htmlparser2/-/htmlparser2-3.10.1.tgz#bd679dc3f59897b6a34bb10749c855bb53a9392f"
  dependencies:
    domelementtype "^1.3.1"
    domhandler "^2.3.0"
    domutils "^1.5.1"
    entities "^1.1.1"
    inherits "^2.0.1"
    readable-stream "^3.1.1"

http-cache-semantics@^3.8.1:
  version "3.8.1"
  resolved "http://npm-registry.persgroep.digital/http-cache-semantics/-/http-cache-semantics-3.8.1.tgz#39b0e16add9b605bf0a9ef3d9daaf4843b4cacd2"

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "http://npm-registry.persgroep.digital/http-deceiver/-/http-deceiver-1.2.7.tgz#fa7168944ab9a519d337cb0bec7284dc3e723d87"

http-errors@2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/http-errors/-/http-errors-2.0.0.tgz#b7774a1486ef73cf7667ac9ae0858c012c57b9d3"
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "http://npm-registry.persgroep.digital/http-errors/-/http-errors-1.6.3.tgz#8b55680bb4be283a0b5bf4ea2e38580be1d9320d"
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-parser-js@>=0.5.1:
  version "0.5.8"
  resolved "http://npm-registry.persgroep.digital/http-parser-js/-/http-parser-js-0.5.8.tgz#af23090d9ac4e24573de6f6aecc9d84a48bf20e3"

http-proxy-agent@^2.1.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/http-proxy-agent/-/http-proxy-agent-2.1.0.tgz#e4821beef5b2142a2026bd73926fe537631c5405"
  dependencies:
    agent-base "4"
    debug "3.1.0"

http-proxy-middleware@~0.18.0:
  version "0.18.0"
  resolved "http://npm-registry.persgroep.digital/http-proxy-middleware/-/http-proxy-middleware-0.18.0.tgz#0987e6bb5a5606e5a69168d8f967a87f15dd8aab"
  dependencies:
    http-proxy "^1.16.2"
    is-glob "^4.0.0"
    lodash "^4.17.5"
    micromatch "^3.1.9"

http-proxy@^1.13.0, http-proxy@^1.16.2:
  version "1.18.1"
  resolved "http://npm-registry.persgroep.digital/http-proxy/-/http-proxy-1.18.1.tgz#401541f0534884bbf95260334e72f88ee3976549"
  dependencies:
    eventemitter3 "^4.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/http-signature/-/http-signature-1.2.0.tgz#9aecd925114772f3d95b65a60abb8f7c18fbace1"
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-browserify@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/https-browserify/-/https-browserify-1.0.0.tgz#ec06c10e0a34c0f2faf199f7fd7fc78fffd03c73"

https-proxy-agent@^2.2.1:
  version "2.2.4"
  resolved "http://npm-registry.persgroep.digital/https-proxy-agent/-/https-proxy-agent-2.2.4.tgz#4ee7a737abd92678a293d9b34a1af4d0d08c787b"
  dependencies:
    agent-base "^4.3.0"
    debug "^3.1.0"

humanize-ms@^1.2.1:
  version "1.2.1"
  resolved "http://npm-registry.persgroep.digital/humanize-ms/-/humanize-ms-1.2.1.tgz#c46e3159a293f6b896da29316d8b6fe8bb79bbed"
  dependencies:
    ms "^2.0.0"

iconv-lite@0.4.24, iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "http://npm-registry.persgroep.digital/iconv-lite/-/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.6.2:
  version "0.6.3"
  resolved "http://npm-registry.persgroep.digital/iconv-lite/-/iconv-lite-0.6.3.tgz#a52f80bf38da1952eb5c681790719871a1a72501"
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

ieee754@^1.1.13, ieee754@^1.1.4:
  version "1.2.1"
  resolved "http://npm-registry.persgroep.digital/ieee754/-/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"

iferr@^0.1.5:
  version "0.1.5"
  resolved "http://npm-registry.persgroep.digital/iferr/-/iferr-0.1.5.tgz#c60eed69e6d8fdb6b3104a1fcbca1c192dc5b501"

ignore-walk@^3.0.1:
  version "3.0.4"
  resolved "http://npm-registry.persgroep.digital/ignore-walk/-/ignore-walk-3.0.4.tgz#c9a09f69b7c7b479a5d74ac1a3c0d4236d2a6335"
  dependencies:
    minimatch "^3.0.4"

ignore@^3.3.5:
  version "3.3.10"
  resolved "http://npm-registry.persgroep.digital/ignore/-/ignore-3.3.10.tgz#0a97fb876986e8081c631160f8f9f389157f0043"

ignore@^4.0.3:
  version "4.0.6"
  resolved "http://npm-registry.persgroep.digital/ignore/-/ignore-4.0.6.tgz#750e3db5862087b4737ebac8207ffd1ef27b25fc"

ignore@^5.0.4:
  version "5.2.4"
  resolved "http://npm-registry.persgroep.digital/ignore/-/ignore-5.2.4.tgz#a291c0c6178ff1b960befe47fcdec301674a6324"

image-size@~0.5.0:
  version "0.5.5"
  resolved "http://npm-registry.persgroep.digital/image-size/-/image-size-0.5.5.tgz#09dfd4ab9d20e29eb1c3e80b8990378df9e3cb9c"

import-cwd@^2.0.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/import-cwd/-/import-cwd-2.1.0.tgz#aa6cf36e722761285cb371ec6519f53e2435b0a9"
  dependencies:
    import-from "^2.1.0"

import-fresh@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/import-fresh/-/import-fresh-2.0.0.tgz#d81355c15612d386c61f9ddd3922d4304822a546"
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

import-from@^2.1.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/import-from/-/import-from-2.1.0.tgz#335db7f2a7affd53aaa471d4b8021dee36b7f3b1"
  dependencies:
    resolve-from "^3.0.0"

import-lazy@^3.1.0:
  version "3.1.0"
  resolved "http://npm-registry.persgroep.digital/import-lazy/-/import-lazy-3.1.0.tgz#891279202c8a2280fdbd6674dbd8da1a1dfc67cc"

import-local@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/import-local/-/import-local-2.0.0.tgz#55070be38a5993cf18ef6db7e961f5bee5c5a09d"
  dependencies:
    pkg-dir "^3.0.0"
    resolve-cwd "^2.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://npm-registry.persgroep.digital/imurmurhash/-/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"

in-publish@^2.0.0:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/in-publish/-/in-publish-2.0.1.tgz#948b1a535c8030561cea522f73f78f4be357e00c"

indent-string@^2.1.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/indent-string/-/indent-string-2.1.0.tgz#8e2d48348742121b4a8218b7a137e9a52049dc80"
  dependencies:
    repeating "^2.0.0"

indent-string@^3.0.0, indent-string@^3.1.0:
  version "3.2.0"
  resolved "http://npm-registry.persgroep.digital/indent-string/-/indent-string-3.2.0.tgz#4a5fd6d27cc332f37e5419a504dbb837105c9289"

indexes-of@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/indexes-of/-/indexes-of-1.0.1.tgz#f30f716c8e2bd346c7b67d3df3915566a7c05607"

indexof@0.0.1:
  version "0.0.1"
  resolved "http://npm-registry.persgroep.digital/indexof/-/indexof-0.0.1.tgz#82dc336d232b9062179d05ab3293a66059fd435d"

infer-owner@^1.0.3:
  version "1.0.4"
  resolved "http://npm-registry.persgroep.digital/infer-owner/-/infer-owner-1.0.4.tgz#c4cefcaa8e51051c2a40ba2ce8a3d27295af9467"

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://npm-registry.persgroep.digital/inflight/-/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.0, inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.0, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.4"
  resolved "http://npm-registry.persgroep.digital/inherits/-/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"

inherits@2.0.1:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/inherits/-/inherits-2.0.1.tgz#b17d08d326b4423e568eff719f91b0b1cbdf69f1"

inherits@2.0.3:
  version "2.0.3"
  resolved "http://npm-registry.persgroep.digital/inherits/-/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"

ini@1.3.5:
  version "1.3.5"
  resolved "http://npm-registry.persgroep.digital/ini/-/ini-1.3.5.tgz#eee25f56db1c9ec6085e0c22778083f596abf927"

ini@^1.3.4, ini@^1.3.5:
  version "1.3.8"
  resolved "http://npm-registry.persgroep.digital/ini/-/ini-1.3.8.tgz#a29da425b48806f34767a4efce397269af28432c"

inline-source-map@~0.6.0:
  version "0.6.2"
  resolved "http://npm-registry.persgroep.digital/inline-source-map/-/inline-source-map-0.6.2.tgz#f9393471c18a79d1724f863fa38b586370ade2a5"
  dependencies:
    source-map "~0.5.3"

inquirer@6.2.1:
  version "6.2.1"
  resolved "http://npm-registry.persgroep.digital/inquirer/-/inquirer-6.2.1.tgz#9943fc4882161bdb0b0c9276769c75b32dbfcd52"
  dependencies:
    ansi-escapes "^3.0.0"
    chalk "^2.0.0"
    cli-cursor "^2.1.0"
    cli-width "^2.0.0"
    external-editor "^3.0.0"
    figures "^2.0.0"
    lodash "^4.17.10"
    mute-stream "0.0.7"
    run-async "^2.2.0"
    rxjs "^6.1.0"
    string-width "^2.1.0"
    strip-ansi "^5.0.0"
    through "^2.3.6"

insert-module-globals@^7.0.0:
  version "7.2.1"
  resolved "http://npm-registry.persgroep.digital/insert-module-globals/-/insert-module-globals-7.2.1.tgz#d5e33185181a4e1f33b15f7bf100ee91890d5cb3"
  dependencies:
    JSONStream "^1.0.3"
    acorn-node "^1.5.2"
    combine-source-map "^0.8.0"
    concat-stream "^1.6.1"
    is-buffer "^1.1.0"
    path-is-absolute "^1.0.1"
    process "~0.11.0"
    through2 "^2.0.0"
    undeclared-identifiers "^1.1.2"
    xtend "^4.0.0"

internal-ip@^3.0.1:
  version "3.0.1"
  resolved "http://npm-registry.persgroep.digital/internal-ip/-/internal-ip-3.0.1.tgz#df5c99876e1d2eb2ea2d74f520e3f669a00ece27"
  dependencies:
    default-gateway "^2.6.0"
    ipaddr.js "^1.5.2"

interpret@^1.0.0:
  version "1.4.0"
  resolved "http://npm-registry.persgroep.digital/interpret/-/interpret-1.4.0.tgz#665ab8bc4da27a774a40584e812e3e0fa45b1a1e"

invariant@^2.2.2:
  version "2.2.4"
  resolved "http://npm-registry.persgroep.digital/invariant/-/invariant-2.2.4.tgz#610f3c92c9359ce1db616e538008d23ff35158e6"
  dependencies:
    loose-envify "^1.0.0"

invert-kv@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/invert-kv/-/invert-kv-1.0.0.tgz#104a8e4aaca6d3d8cd157a8ef8bfab2d7a3ffdb6"

invert-kv@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/invert-kv/-/invert-kv-2.0.0.tgz#7393f5afa59ec9ff5f67a27620d11c226e3eec02"

ip-regex@^2.1.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/ip-regex/-/ip-regex-2.1.0.tgz#fa78bf5d2e6913c911ce9f819ee5146bb6d844e9"

ip@1.1.5:
  version "1.1.5"
  resolved "http://npm-registry.persgroep.digital/ip/-/ip-1.1.5.tgz#bdded70114290828c0a039e72ef25f5aaec4354a"

ip@^1.1.0, ip@^1.1.5:
  version "1.1.8"
  resolved "http://npm-registry.persgroep.digital/ip/-/ip-1.1.8.tgz#ae05948f6b075435ed3307acce04629da8cdbf48"

ipaddr.js@1.9.1, ipaddr.js@^1.5.2:
  version "1.9.1"
  resolved "http://npm-registry.persgroep.digital/ipaddr.js/-/ipaddr.js-1.9.1.tgz#bff38543eeb8984825079ff3a2a8e6cbd46781b3"

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "http://npm-registry.persgroep.digital/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz#a9e12cb3ae8d876727eeef3843f8a0897b5c98d6"
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz#169c2f6d3df1f992618072365c9b0ea1f6878656"
  dependencies:
    kind-of "^6.0.0"

is-alphabetical@^1.0.0:
  version "1.0.4"
  resolved "http://npm-registry.persgroep.digital/is-alphabetical/-/is-alphabetical-1.0.4.tgz#9e7d6b94916be22153745d184c298cbf986a686d"

is-alphanumeric@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/is-alphanumeric/-/is-alphanumeric-1.0.0.tgz#4a9cef71daf4c001c1d81d63d140cf53fd6889f4"

is-alphanumerical@^1.0.0:
  version "1.0.4"
  resolved "http://npm-registry.persgroep.digital/is-alphanumerical/-/is-alphanumerical-1.0.4.tgz#7eb9a2431f855f6b1ef1a78e326df515696c4dbf"
  dependencies:
    is-alphabetical "^1.0.0"
    is-decimal "^1.0.0"

is-arguments@^1.0.4:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/is-arguments/-/is-arguments-1.1.1.tgz#15b3f88fda01f2a97fec84ca761a560f123efa9b"
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://npm-registry.persgroep.digital/is-arrayish/-/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/is-binary-path/-/is-binary-path-1.0.1.tgz#75f16642b480f187a711c814161fd3a4a7655898"
  dependencies:
    binary-extensions "^1.0.0"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/is-binary-path/-/is-binary-path-2.1.0.tgz#ea1f7f3b80f064236e83470f86c09c254fb45b09"
  dependencies:
    binary-extensions "^2.0.0"

is-buffer@^1.1.0, is-buffer@^1.1.5:
  version "1.1.6"
  resolved "http://npm-registry.persgroep.digital/is-buffer/-/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"

is-buffer@^2.0.0:
  version "2.0.5"
  resolved "http://npm-registry.persgroep.digital/is-buffer/-/is-buffer-2.0.5.tgz#ebc252e400d22ff8d77fa09888821a24a658c191"

is-ci@1.2.1:
  version "1.2.1"
  resolved "http://npm-registry.persgroep.digital/is-ci/-/is-ci-1.2.1.tgz#e3779c8ee17fccf428488f6e281187f2e632841c"
  dependencies:
    ci-info "^1.5.0"

is-core-module@^2.11.0:
  version "2.12.0"
  resolved "http://npm-registry.persgroep.digital/is-core-module/-/is-core-module-2.12.0.tgz#36ad62f6f73c8253fd6472517a12483cf03e7ec4"
  dependencies:
    has "^1.0.3"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "http://npm-registry.persgroep.digital/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz#0b5ee648388e2c860282e793f1856fec3f301b56"
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz#d84876321d0e7add03990406abbbbd36ba9268c7"
  dependencies:
    kind-of "^6.0.0"

is-date-object@^1.0.1:
  version "1.0.5"
  resolved "http://npm-registry.persgroep.digital/is-date-object/-/is-date-object-1.0.5.tgz#0841d5536e724c25597bf6ea62e1bd38298df31f"
  dependencies:
    has-tostringtag "^1.0.0"

is-decimal@^1.0.0:
  version "1.0.4"
  resolved "http://npm-registry.persgroep.digital/is-decimal/-/is-decimal-1.0.4.tgz#65a3a5958a1c5b63a706e1b333d7cd9f630d3fa5"

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "http://npm-registry.persgroep.digital/is-descriptor/-/is-descriptor-0.1.6.tgz#366d8240dde487ca51823b1ab9f07a10a78251ca"
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/is-descriptor/-/is-descriptor-1.0.2.tgz#3b159746a66604b04f8c81524ba365c5f14d86ec"
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-directory@^0.3.1:
  version "0.3.1"
  resolved "http://npm-registry.persgroep.digital/is-directory/-/is-directory-0.3.1.tgz#61339b6f2475fc772fd9c9d83f5c8575dc154ae1"

is-dotfile@^1.0.0:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/is-dotfile/-/is-dotfile-1.0.3.tgz#a6a2f32ffd2dfb04f5ca25ecd0f6b83cf798a1e1"

is-equal-shallow@^0.1.3:
  version "0.1.3"
  resolved "http://npm-registry.persgroep.digital/is-equal-shallow/-/is-equal-shallow-0.1.3.tgz#2238098fc221de0bcfa5d9eac4c45d638aa1c534"
  dependencies:
    is-primitive "^2.0.0"

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "http://npm-registry.persgroep.digital/is-extendable/-/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/is-extendable/-/is-extendable-1.0.1.tgz#a7470f9e426733d81bd81e1155264e3a3507cab4"
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/is-extglob/-/is-extglob-1.0.0.tgz#ac468177c4943405a092fc8f29760c6ffc6206c0"

is-extglob@^2.1.0, is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://npm-registry.persgroep.digital/is-extglob/-/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"

is-finite@^1.0.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/is-finite/-/is-finite-1.1.0.tgz#904135c77fb42c0641d6aa1bcdbc4daa8da082f3"

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz#ef9e31386f031a7f0d643af82fde50c457ef00cb"
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"

is-generator@^1.0.2:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/is-generator/-/is-generator-1.0.3.tgz#c14c21057ed36e328db80347966c693f886389f3"

is-glob@^2.0.0, is-glob@^2.0.1:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/is-glob/-/is-glob-2.0.1.tgz#d096f926a3ded5600f3fdfd91198cb0888c2d863"
  dependencies:
    is-extglob "^1.0.0"

is-glob@^3.1.0:
  version "3.1.0"
  resolved "http://npm-registry.persgroep.digital/is-glob/-/is-glob-3.1.0.tgz#7ba5ae24217804ac70707b96922567486cc3e84a"
  dependencies:
    is-extglob "^2.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@~4.0.1:
  version "4.0.3"
  resolved "http://npm-registry.persgroep.digital/is-glob/-/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  dependencies:
    is-extglob "^2.1.1"

is-hexadecimal@^1.0.0:
  version "1.0.4"
  resolved "http://npm-registry.persgroep.digital/is-hexadecimal/-/is-hexadecimal-1.0.4.tgz#cc35c97588da4bd49a8eedd6bc4082d44dcb23a7"

is-installed-globally@0.1.0:
  version "0.1.0"
  resolved "http://npm-registry.persgroep.digital/is-installed-globally/-/is-installed-globally-0.1.0.tgz#0dfd98f5a9111716dd535dda6492f67bf3d25a80"
  dependencies:
    global-dirs "^0.1.0"
    is-path-inside "^1.0.0"

is-number@^2.1.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/is-number/-/is-number-2.1.0.tgz#01fcbbb393463a548f2f466cce16dece49db908f"
  dependencies:
    kind-of "^3.0.2"

is-number@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/is-number/-/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195"
  dependencies:
    kind-of "^3.0.2"

is-number@^4.0.0:
  version "4.0.0"
  resolved "http://npm-registry.persgroep.digital/is-number/-/is-number-4.0.0.tgz#0026e37f5454d73e356dfe6564699867c6a7f0ff"

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://npm-registry.persgroep.digital/is-number/-/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"

is-obj@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/is-obj/-/is-obj-2.0.0.tgz#473fb05d973705e3fd9620545018ca8e22ef4982"

is-path-cwd@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/is-path-cwd/-/is-path-cwd-1.0.0.tgz#d225ec23132e89edd38fda767472e62e65f1106d"

is-path-in-cwd@^1.0.0:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/is-path-in-cwd/-/is-path-in-cwd-1.0.1.tgz#5ac48b345ef675339bd6c7a48a912110b241cf52"
  dependencies:
    is-path-inside "^1.0.0"

is-path-inside@^1.0.0:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/is-path-inside/-/is-path-inside-1.0.1.tgz#8ef5b7de50437a3fdca6b4e865ef7aa55cb48036"
  dependencies:
    path-is-inside "^1.0.1"

is-plain-obj@^1.1.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/is-plain-obj/-/is-plain-obj-1.1.0.tgz#71a50c8429dfca773c92a390a4a03b39fcd51d3e"

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "http://npm-registry.persgroep.digital/is-plain-object/-/is-plain-object-2.0.4.tgz#2c163b3fafb1b606d9d17928f05c2a1c38e07677"
  dependencies:
    isobject "^3.0.1"

is-posix-bracket@^0.1.0:
  version "0.1.1"
  resolved "http://npm-registry.persgroep.digital/is-posix-bracket/-/is-posix-bracket-0.1.1.tgz#3334dc79774368e92f016e6fbc0a88f5cd6e6bc4"

is-primitive@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/is-primitive/-/is-primitive-2.0.0.tgz#207bab91638499c07b2adf240a41a87210034575"

is-promise@^2.1.0:
  version "2.2.2"
  resolved "http://npm-registry.persgroep.digital/is-promise/-/is-promise-2.2.2.tgz#39ab959ccbf9a774cf079f7b40c7a26f763135f1"

is-regex@^1.0.4:
  version "1.1.4"
  resolved "http://npm-registry.persgroep.digital/is-regex/-/is-regex-1.1.4.tgz#eef5663cd59fa4c0ae339505323df6854bb15958"
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-regexp@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/is-regexp/-/is-regexp-1.0.0.tgz#fd2d883545c46bac5a633e7b9a09e87fa2cb5069"

is-stream@^1.0.1, is-stream@^1.1.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/is-stream/-/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"

is-supported-regexp-flag@^1.0.0:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/is-supported-regexp-flag/-/is-supported-regexp-flag-1.0.1.tgz#21ee16518d2c1dd3edd3e9a0d57e50207ac364ca"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/is-typedarray/-/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"

is-utf8@^0.2.0:
  version "0.2.1"
  resolved "http://npm-registry.persgroep.digital/is-utf8/-/is-utf8-0.2.1.tgz#4b0da1442104d1b336340e80797e865cf39f7d72"

is-whitespace-character@^1.0.0:
  version "1.0.4"
  resolved "http://npm-registry.persgroep.digital/is-whitespace-character/-/is-whitespace-character-1.0.4.tgz#0858edd94a95594c7c9dd0b5c174ec6e45ee4aa7"

is-windows@^1.0.2:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/is-windows/-/is-windows-1.0.2.tgz#d1850eb9791ecd18e6182ce12a30f396634bb19d"

is-word-character@^1.0.0:
  version "1.0.4"
  resolved "http://npm-registry.persgroep.digital/is-word-character/-/is-word-character-1.0.4.tgz#ce0e73216f98599060592f62ff31354ddbeb0230"

is-wsl@^1.1.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/is-wsl/-/is-wsl-1.1.0.tgz#1f16e4aa22b04d1336b66188a66af3c600c3a66d"

isarray@0.0.1:
  version "0.0.1"
  resolved "http://npm-registry.persgroep.digital/isarray/-/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"

isarray@1.0.0, isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/isarray/-/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"

isarray@2.0.1:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/isarray/-/isarray-2.0.1.tgz#a37d94ed9cda2d59865c9f76fe596ee1f338741e"

isbinaryfile@^3.0.0:
  version "3.0.3"
  resolved "http://npm-registry.persgroep.digital/isbinaryfile/-/isbinaryfile-3.0.3.tgz#5d6def3edebf6e8ca8cae9c30183a804b5f8be80"
  dependencies:
    buffer-alloc "^1.2.0"

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/isexe/-/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"

isobject@^2.0.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/isobject/-/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "http://npm-registry.persgroep.digital/isobject/-/isobject-3.0.1.tgz#4e431e92b11a9731636aa1f9c8d1ccbcfdab78df"

isstream@~0.1.2:
  version "0.1.2"
  resolved "http://npm-registry.persgroep.digital/isstream/-/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"

istanbul-api@^2.1.6:
  version "2.1.7"
  resolved "http://npm-registry.persgroep.digital/istanbul-api/-/istanbul-api-2.1.7.tgz#82786b79f3b93d481349c7aa1e2c2b4eeb48c8a8"
  dependencies:
    async "^2.6.2"
    compare-versions "^3.4.0"
    fileset "^2.0.3"
    istanbul-lib-coverage "^2.0.5"
    istanbul-lib-hook "^2.0.7"
    istanbul-lib-instrument "^3.3.0"
    istanbul-lib-report "^2.0.8"
    istanbul-lib-source-maps "^3.0.6"
    istanbul-reports "^2.2.5"
    js-yaml "^3.13.1"
    make-dir "^2.1.0"
    minimatch "^3.0.4"
    once "^1.4.0"

istanbul-instrumenter-loader@3.0.1:
  version "3.0.1"
  resolved "http://npm-registry.persgroep.digital/istanbul-instrumenter-loader/-/istanbul-instrumenter-loader-3.0.1.tgz#9957bd59252b373fae5c52b7b5188e6fde2a0949"
  dependencies:
    convert-source-map "^1.5.0"
    istanbul-lib-instrument "^1.7.3"
    loader-utils "^1.1.0"
    schema-utils "^0.3.0"

istanbul-lib-coverage@^1.2.1:
  version "1.2.1"
  resolved "http://npm-registry.persgroep.digital/istanbul-lib-coverage/-/istanbul-lib-coverage-1.2.1.tgz#ccf7edcd0a0bb9b8f729feeb0930470f9af664f0"

istanbul-lib-coverage@^2.0.5:
  version "2.0.5"
  resolved "http://npm-registry.persgroep.digital/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.5.tgz#675f0ab69503fad4b1d849f736baaca803344f49"

istanbul-lib-hook@^2.0.7:
  version "2.0.7"
  resolved "http://npm-registry.persgroep.digital/istanbul-lib-hook/-/istanbul-lib-hook-2.0.7.tgz#c95695f383d4f8f60df1f04252a9550e15b5b133"
  dependencies:
    append-transform "^1.0.0"

istanbul-lib-instrument@^1.7.3:
  version "1.10.2"
  resolved "http://npm-registry.persgroep.digital/istanbul-lib-instrument/-/istanbul-lib-instrument-1.10.2.tgz#1f55ed10ac3c47f2bdddd5307935126754d0a9ca"
  dependencies:
    babel-generator "^6.18.0"
    babel-template "^6.16.0"
    babel-traverse "^6.18.0"
    babel-types "^6.18.0"
    babylon "^6.18.0"
    istanbul-lib-coverage "^1.2.1"
    semver "^5.3.0"

istanbul-lib-instrument@^3.3.0:
  version "3.3.0"
  resolved "http://npm-registry.persgroep.digital/istanbul-lib-instrument/-/istanbul-lib-instrument-3.3.0.tgz#a5f63d91f0bbc0c3e479ef4c5de027335ec6d630"
  dependencies:
    "@babel/generator" "^7.4.0"
    "@babel/parser" "^7.4.3"
    "@babel/template" "^7.4.0"
    "@babel/traverse" "^7.4.3"
    "@babel/types" "^7.4.0"
    istanbul-lib-coverage "^2.0.5"
    semver "^6.0.0"

istanbul-lib-report@^2.0.8:
  version "2.0.8"
  resolved "http://npm-registry.persgroep.digital/istanbul-lib-report/-/istanbul-lib-report-2.0.8.tgz#5a8113cd746d43c4889eba36ab10e7d50c9b4f33"
  dependencies:
    istanbul-lib-coverage "^2.0.5"
    make-dir "^2.1.0"
    supports-color "^6.1.0"

istanbul-lib-source-maps@^3.0.6:
  version "3.0.6"
  resolved "http://npm-registry.persgroep.digital/istanbul-lib-source-maps/-/istanbul-lib-source-maps-3.0.6.tgz#284997c48211752ec486253da97e3879defba8c8"
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^2.0.5"
    make-dir "^2.1.0"
    rimraf "^2.6.3"
    source-map "^0.6.1"

istanbul-reports@^2.2.5:
  version "2.2.7"
  resolved "http://npm-registry.persgroep.digital/istanbul-reports/-/istanbul-reports-2.2.7.tgz#5d939f6237d7b48393cc0959eab40cd4fd056931"
  dependencies:
    html-escaper "^2.0.0"

jasmine-core@^3.3:
  version "3.99.1"
  resolved "http://npm-registry.persgroep.digital/jasmine-core/-/jasmine-core-3.99.1.tgz#5bfa4b2d76618868bfac4c8ff08bb26fffa4120d"

jasmine-core@~3.3.0:
  version "3.3.0"
  resolved "http://npm-registry.persgroep.digital/jasmine-core/-/jasmine-core-3.3.0.tgz#dea1cdc634bc93c7e0d4ad27185df30fa971b10e"

jasmine-spec-reporter@^4.2.1:
  version "4.2.1"
  resolved "http://npm-registry.persgroep.digital/jasmine-spec-reporter/-/jasmine-spec-reporter-4.2.1.tgz#1d632aec0341670ad324f92ba84b4b32b35e9e22"
  dependencies:
    colors "1.1.2"

js-base64@^2.1.8:
  version "2.6.4"
  resolved "http://npm-registry.persgroep.digital/js-base64/-/js-base64-2.6.4.tgz#f4e686c5de1ea1f867dbcad3d46d969428df98c4"

js-levenshtein@^1.1.3:
  version "1.1.6"
  resolved "http://npm-registry.persgroep.digital/js-levenshtein/-/js-levenshtein-1.1.6.tgz#c6cee58eb3550372df8deb85fad5ce66ce01d59d"

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://npm-registry.persgroep.digital/js-tokens/-/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"

js-tokens@^3.0.2:
  version "3.0.2"
  resolved "http://npm-registry.persgroep.digital/js-tokens/-/js-tokens-3.0.2.tgz#9866df395102130e38f7f996bceb65443209c25b"

js-yaml@^3.13.1, js-yaml@^3.7.0, js-yaml@^3.9.0:
  version "3.14.1"
  resolved "http://npm-registry.persgroep.digital/js-yaml/-/js-yaml-3.14.1.tgz#dae812fdb3825fa306609a8717383c50c36a0537"
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "http://npm-registry.persgroep.digital/jsbn/-/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"

jsesc@^1.3.0:
  version "1.3.0"
  resolved "http://npm-registry.persgroep.digital/jsesc/-/jsesc-1.3.0.tgz#46c3fec8c1892b12b0833db9bc7622176dbab34b"

jsesc@^2.5.1:
  version "2.5.2"
  resolved "http://npm-registry.persgroep.digital/jsesc/-/jsesc-2.5.2.tgz#80564d2e483dacf6e8ef209650a67df3f0c283a4"

jsesc@~0.5.0:
  version "0.5.0"
  resolved "http://npm-registry.persgroep.digital/jsesc/-/jsesc-0.5.0.tgz#e7dee66e35d6fc16f710fe91d5cf69f70f08911d"

json-parse-better-errors@^1.0.0, json-parse-better-errors@^1.0.1, json-parse-better-errors@^1.0.2:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9"

json-schema-ref-parser@^7.1.3:
  version "7.1.4"
  resolved "http://npm-registry.persgroep.digital/json-schema-ref-parser/-/json-schema-ref-parser-7.1.4.tgz#abb3f2613911e9060dc2268477b40591753facf0"
  dependencies:
    call-me-maybe "^1.0.1"
    js-yaml "^3.13.1"
    ono "^6.0.0"

json-schema-traverse@^0.3.0:
  version "0.3.1"
  resolved "http://npm-registry.persgroep.digital/json-schema-traverse/-/json-schema-traverse-0.3.1.tgz#349a6d44c53a51de89b40805c5d5e59b417d3340"

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://npm-registry.persgroep.digital/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"

json-schema@0.4.0:
  version "0.4.0"
  resolved "http://npm-registry.persgroep.digital/json-schema/-/json-schema-0.4.0.tgz#f7de4cf6efab838ebaeb3236474cbba5a1930ab5"

json-stable-stringify@~0.0.0:
  version "0.0.1"
  resolved "http://npm-registry.persgroep.digital/json-stable-stringify/-/json-stable-stringify-0.0.1.tgz#611c23e814db375527df851193db59dd2af27f45"
  dependencies:
    jsonify "~0.0.0"

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "http://npm-registry.persgroep.digital/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"

json3@^3.3.2:
  version "3.3.3"
  resolved "http://npm-registry.persgroep.digital/json3/-/json3-3.3.3.tgz#7fc10e375fc5ae42c4705a5cc0aa6f62be305b81"

json5@^0.5.0:
  version "0.5.1"
  resolved "http://npm-registry.persgroep.digital/json5/-/json5-0.5.1.tgz#1eade7acc012034ad84e2396767ead9fa5495821"

json5@^1.0.1:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/json5/-/json5-1.0.2.tgz#63d98d60f21b313b77c4d6da18bfa69d80e1d593"
  dependencies:
    minimist "^1.2.0"

json5@^2.2.2:
  version "2.2.3"
  resolved "http://npm-registry.persgroep.digital/json5/-/json5-2.2.3.tgz#78cd6f1a19bdc12b73db5ad0c61efd66c1e29283"

jsonfile@^2.1.0:
  version "2.4.0"
  resolved "http://npm-registry.persgroep.digital/jsonfile/-/jsonfile-2.4.0.tgz#3736a2b428b87bbda0cc83b53fa3d633a35c2ae8"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonfile@^3.0.0:
  version "3.0.1"
  resolved "http://npm-registry.persgroep.digital/jsonfile/-/jsonfile-3.0.1.tgz#a5ecc6f65f53f662c4415c7675a0331d0992ec66"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "http://npm-registry.persgroep.digital/jsonfile/-/jsonfile-4.0.0.tgz#8771aae0799b64076b76640fca058f9c10e33ecb"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonify@~0.0.0:
  version "0.0.1"
  resolved "http://npm-registry.persgroep.digital/jsonify/-/jsonify-0.0.1.tgz#2aa3111dae3d34a0f151c63f3a45d995d9420978"

jsonparse@^1.2.0:
  version "1.3.1"
  resolved "http://npm-registry.persgroep.digital/jsonparse/-/jsonparse-1.3.1.tgz#3f4dae4a91fac315f71062f8521cc239f1366280"

jspdf-autotable@^3.5.28:
  version "3.5.28"
  resolved "http://npm-registry.persgroep.digital/jspdf-autotable/-/jspdf-autotable-3.5.28.tgz#5463acbfa7a5b182453afea8f8aeac48bc96bfdb"

jspdf@^2.5.1:
  version "2.5.1"
  resolved "http://npm-registry.persgroep.digital/jspdf/-/jspdf-2.5.1.tgz#00c85250abf5447a05f3b32ab9935ab4a56592cc"
  dependencies:
    "@babel/runtime" "^7.14.0"
    atob "^2.1.2"
    btoa "^1.2.1"
    fflate "^0.4.8"
  optionalDependencies:
    canvg "^3.0.6"
    core-js "^3.6.0"
    dompurify "^2.2.0"
    html2canvas "^1.0.0-rc.5"

jsprim@^1.2.2:
  version "1.4.2"
  resolved "http://npm-registry.persgroep.digital/jsprim/-/jsprim-1.4.2.tgz#712c65533a15c878ba59e9ed5f0e26d5b77c5feb"
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.4.0"
    verror "1.10.0"

karma-chrome-launcher@^2.2.0:
  version "2.2.0"
  resolved "http://npm-registry.persgroep.digital/karma-chrome-launcher/-/karma-chrome-launcher-2.2.0.tgz#cf1b9d07136cc18fe239327d24654c3dbc368acf"
  dependencies:
    fs-access "^1.0.0"
    which "^1.2.1"

karma-cli@~2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/karma-cli/-/karma-cli-2.0.0.tgz#481548d28661af4cc68f3d8e09708f17d2cba931"
  dependencies:
    resolve "^1.3.3"

karma-coverage-istanbul-reporter@^2.0.5:
  version "2.1.1"
  resolved "http://npm-registry.persgroep.digital/karma-coverage-istanbul-reporter/-/karma-coverage-istanbul-reporter-2.1.1.tgz#37a775fbfbb3cbe98cebf19605c94c6277c3b88a"
  dependencies:
    istanbul-api "^2.1.6"
    minimatch "^3.0.4"

karma-jasmine-html-reporter@^1.4.0:
  version "1.7.0"
  resolved "http://npm-registry.persgroep.digital/karma-jasmine-html-reporter/-/karma-jasmine-html-reporter-1.7.0.tgz#52c489a74d760934a1089bfa5ea4a8fcb84cc28b"

karma-jasmine@~2.0.1:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/karma-jasmine/-/karma-jasmine-2.0.1.tgz#26e3e31f2faf272dd80ebb0e1898914cc3a19763"
  dependencies:
    jasmine-core "^3.3"

karma-phantomjs-launcher@^1.0.4:
  version "1.0.4"
  resolved "http://npm-registry.persgroep.digital/karma-phantomjs-launcher/-/karma-phantomjs-launcher-1.0.4.tgz#d23ca34801bda9863ad318e3bb4bd4062b13acd2"
  dependencies:
    lodash "^4.0.1"
    phantomjs-prebuilt "^2.1.7"

karma-source-map-support@1.3.0:
  version "1.3.0"
  resolved "http://npm-registry.persgroep.digital/karma-source-map-support/-/karma-source-map-support-1.3.0.tgz#36dd4d8ca154b62ace95696236fae37caf0a7dde"
  dependencies:
    source-map-support "^0.5.5"

karma@~4.0.0:
  version "4.0.1"
  resolved "http://npm-registry.persgroep.digital/karma/-/karma-4.0.1.tgz#2581d6caa0d4cd28b65131561b47bad6d5478773"
  dependencies:
    bluebird "^3.3.0"
    body-parser "^1.16.1"
    braces "^2.3.2"
    chokidar "^2.0.3"
    colors "^1.1.0"
    connect "^3.6.0"
    core-js "^2.2.0"
    di "^0.0.1"
    dom-serialize "^2.2.0"
    flatted "^2.0.0"
    glob "^7.1.1"
    graceful-fs "^4.1.2"
    http-proxy "^1.13.0"
    isbinaryfile "^3.0.0"
    lodash "^4.17.11"
    log4js "^4.0.0"
    mime "^2.3.1"
    minimatch "^3.0.2"
    optimist "^0.6.1"
    qjobs "^1.1.4"
    range-parser "^1.2.0"
    rimraf "^2.6.0"
    safe-buffer "^5.0.1"
    socket.io "2.1.1"
    source-map "^0.6.1"
    tmp "0.0.33"
    useragent "2.3.0"

kew@^0.7.0:
  version "0.7.0"
  resolved "http://npm-registry.persgroep.digital/kew/-/kew-0.7.0.tgz#79d93d2d33363d6fdd2970b335d9141ad591d79b"

killable@^1.0.0:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/killable/-/killable-1.0.1.tgz#4c8ce441187a061c7474fb87ca08e2a638194892"

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "http://npm-registry.persgroep.digital/kind-of/-/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "http://npm-registry.persgroep.digital/kind-of/-/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57"
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"
  resolved "http://npm-registry.persgroep.digital/kind-of/-/kind-of-5.1.0.tgz#729c91e2d857b7a419a1f9aa65685c4c33f5845d"

kind-of@^6.0.0, kind-of@^6.0.2:
  version "6.0.3"
  resolved "http://npm-registry.persgroep.digital/kind-of/-/kind-of-6.0.3.tgz#07c05034a6c349fa06e24fa35aa76db4580ce4dd"

klaw@^1.0.0:
  version "1.3.1"
  resolved "http://npm-registry.persgroep.digital/klaw/-/klaw-1.3.1.tgz#4088433b46b3b1ba259d78785d8e96f73ba02439"
  optionalDependencies:
    graceful-fs "^4.1.9"

known-css-properties@^0.11.0:
  version "0.11.0"
  resolved "http://npm-registry.persgroep.digital/known-css-properties/-/known-css-properties-0.11.0.tgz#0da784f115ea77c76b81536d7052e90ee6c86a8a"

knuth-shuffle-seeded@^1.0.6:
  version "1.0.6"
  resolved "http://npm-registry.persgroep.digital/knuth-shuffle-seeded/-/knuth-shuffle-seeded-1.0.6.tgz#01f1b65733aa7540ee08d8b0174164d22081e4e1"
  dependencies:
    seed-random "~2.2.0"

labeled-stream-splicer@^2.0.0:
  version "2.0.2"
  resolved "http://npm-registry.persgroep.digital/labeled-stream-splicer/-/labeled-stream-splicer-2.0.2.tgz#42a41a16abcd46fd046306cf4f2c3576fffb1c21"
  dependencies:
    inherits "^2.0.1"
    stream-splicer "^2.0.0"

lazy-ass@1.6.0:
  version "1.6.0"
  resolved "http://npm-registry.persgroep.digital/lazy-ass/-/lazy-ass-1.6.0.tgz#7999655e8646c17f089fdd187d150d3324d54513"

lcid@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/lcid/-/lcid-1.0.0.tgz#308accafa0bc483a3867b4b6f2b9506251d1b835"
  dependencies:
    invert-kv "^1.0.0"

lcid@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/lcid/-/lcid-2.0.0.tgz#6ef5d2df60e52f82eb228a4c373e8d1f397253cf"
  dependencies:
    invert-kv "^2.0.0"

less-loader@4.1.0:
  version "4.1.0"
  resolved "http://npm-registry.persgroep.digital/less-loader/-/less-loader-4.1.0.tgz#2c1352c5b09a4f84101490274fd51674de41363e"
  dependencies:
    clone "^2.1.1"
    loader-utils "^1.1.0"
    pify "^3.0.0"

less@3.9.0:
  version "3.9.0"
  resolved "http://npm-registry.persgroep.digital/less/-/less-3.9.0.tgz#b7511c43f37cf57dc87dffd9883ec121289b1474"
  dependencies:
    clone "^2.1.2"
  optionalDependencies:
    errno "^0.1.1"
    graceful-fs "^4.1.2"
    image-size "~0.5.0"
    mime "^1.4.1"
    mkdirp "^0.5.0"
    promise "^7.1.1"
    request "^2.83.0"
    source-map "~0.6.0"

leven@^2.1.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/leven/-/leven-2.1.0.tgz#c2e7a9f772094dee9d34202ae8acce4687875580"

license-webpack-plugin@2.1.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/license-webpack-plugin/-/license-webpack-plugin-2.1.0.tgz#83acaa6e89c3c5316effdd80cb4ec9c5cd8efc2f"
  dependencies:
    "@types/webpack-sources" "^0.1.5"
    webpack-sources "^1.2.0"

linkify-it@^4.0.1:
  version "4.0.1"
  resolved "http://npm-registry.persgroep.digital/linkify-it/-/linkify-it-4.0.1.tgz#01f1d5e508190d06669982ba31a7d9f56a5751ec"
  dependencies:
    uc.micro "^1.0.1"

listr-silent-renderer@^1.1.1:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/listr-silent-renderer/-/listr-silent-renderer-1.1.1.tgz#924b5a3757153770bf1a8e3fbf74b8bbf3f9242e"

listr-update-renderer@^0.2.0:
  version "0.2.0"
  resolved "http://npm-registry.persgroep.digital/listr-update-renderer/-/listr-update-renderer-0.2.0.tgz#ca80e1779b4e70266807e8eed1ad6abe398550f9"
  dependencies:
    chalk "^1.1.3"
    cli-truncate "^0.2.1"
    elegant-spinner "^1.0.1"
    figures "^1.7.0"
    indent-string "^3.0.0"
    log-symbols "^1.0.2"
    log-update "^1.0.2"
    strip-ansi "^3.0.1"

listr-verbose-renderer@^0.4.0:
  version "0.4.1"
  resolved "http://npm-registry.persgroep.digital/listr-verbose-renderer/-/listr-verbose-renderer-0.4.1.tgz#8206f4cf6d52ddc5827e5fd14989e0e965933a35"
  dependencies:
    chalk "^1.1.3"
    cli-cursor "^1.0.2"
    date-fns "^1.27.2"
    figures "^1.7.0"

listr@0.12.0:
  version "0.12.0"
  resolved "http://npm-registry.persgroep.digital/listr/-/listr-0.12.0.tgz#6bce2c0f5603fa49580ea17cd6a00cc0e5fa451a"
  dependencies:
    chalk "^1.1.3"
    cli-truncate "^0.2.1"
    figures "^1.7.0"
    indent-string "^2.1.0"
    is-promise "^2.1.0"
    is-stream "^1.1.0"
    listr-silent-renderer "^1.1.1"
    listr-update-renderer "^0.2.0"
    listr-verbose-renderer "^0.4.0"
    log-symbols "^1.0.2"
    log-update "^1.0.2"
    ora "^0.2.3"
    p-map "^1.1.1"
    rxjs "^5.0.0-beta.11"
    stream-to-observable "^0.1.0"
    strip-ansi "^3.0.1"

load-json-file@^1.0.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/load-json-file/-/load-json-file-1.1.0.tgz#956905708d58b4bab4c2261b04f59f31c99374c0"
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"
    strip-bom "^2.0.0"

load-json-file@^4.0.0:
  version "4.0.0"
  resolved "http://npm-registry.persgroep.digital/load-json-file/-/load-json-file-4.0.0.tgz#2f5f45ab91e33216234fd53adab668eb4ec0993b"
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^4.0.0"
    pify "^3.0.0"
    strip-bom "^3.0.0"

loader-runner@^2.3.0:
  version "2.4.0"
  resolved "http://npm-registry.persgroep.digital/loader-runner/-/loader-runner-2.4.0.tgz#ed47066bfe534d7e84c4c7b9998c2a75607d9357"

loader-utils@1.2.3:
  version "1.2.3"
  resolved "http://npm-registry.persgroep.digital/loader-utils/-/loader-utils-1.2.3.tgz#1ff5dc6911c9f0a062531a4c04b609406108c2c7"
  dependencies:
    big.js "^5.2.2"
    emojis-list "^2.0.0"
    json5 "^1.0.1"

loader-utils@^1.0.1, loader-utils@^1.0.2, loader-utils@^1.1.0, loader-utils@^1.2.3:
  version "1.4.2"
  resolved "http://npm-registry.persgroep.digital/loader-utils/-/loader-utils-1.4.2.tgz#29a957f3a63973883eb684f10ffd3d151fec01a3"
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^1.0.1"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/locate-path/-/locate-path-2.0.0.tgz#2b568b265eec944c6d9c0de9c3dbbbca0354cd8e"
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/locate-path/-/locate-path-3.0.0.tgz#dbec3b3ab759758071b58fe59fc41871af21400e"
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

lodash.clonedeep@^4.5.0:
  version "4.5.0"
  resolved "http://npm-registry.persgroep.digital/lodash.clonedeep/-/lodash.clonedeep-4.5.0.tgz#e23f3f9c4f8fbdde872529c1071857a086e5ccef"

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "http://npm-registry.persgroep.digital/lodash.debounce/-/lodash.debounce-4.0.8.tgz#82d79bff30a67c4005ffd5e2515300ad9ca4d7af"

lodash.get@^4.4.2:
  version "4.4.2"
  resolved "http://npm-registry.persgroep.digital/lodash.get/-/lodash.get-4.4.2.tgz#2d177f652fa31e939b4438d5341499dfa3825e99"

lodash.isequal@^4.5.0:
  version "4.5.0"
  resolved "http://npm-registry.persgroep.digital/lodash.isequal/-/lodash.isequal-4.5.0.tgz#415c4478f2bcc30120c22ce10ed3226f7d3e18e0"

lodash.memoize@~3.0.3:
  version "3.0.4"
  resolved "http://npm-registry.persgroep.digital/lodash.memoize/-/lodash.memoize-3.0.4.tgz#2dcbd2c287cbc0a55cc42328bd0c736150d53e3f"

lodash.once@^4.1.1:
  version "4.1.1"
  resolved "http://npm-registry.persgroep.digital/lodash.once/-/lodash.once-4.1.1.tgz#0dd3971213c7c56df880977d504c88fb471a97ac"

lodash.tail@^4.1.1:
  version "4.1.1"
  resolved "http://npm-registry.persgroep.digital/lodash.tail/-/lodash.tail-4.1.1.tgz#d2333a36d9e7717c8ad2f7cacafec7c32b444664"

lodash@4.17.11:
  version "4.17.11"
  resolved "http://npm-registry.persgroep.digital/lodash/-/lodash-4.17.11.tgz#b39ea6229ef607ecd89e2c8df12536891cac9b8d"

lodash@^4.0.0, lodash@^4.0.1, lodash@^4.14.0, lodash@^4.17.10, lodash@^4.17.11, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.21, lodash@^4.17.4, lodash@^4.17.5:
  version "4.17.21"
  resolved "http://npm-registry.persgroep.digital/lodash/-/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"

log-symbols@2.2.0, log-symbols@^2.0.0, log-symbols@^2.2.0:
  version "2.2.0"
  resolved "http://npm-registry.persgroep.digital/log-symbols/-/log-symbols-2.2.0.tgz#5740e1c5d6f0dfda4ad9323b5332107ef6b4c40a"
  dependencies:
    chalk "^2.0.1"

log-symbols@^1.0.2:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/log-symbols/-/log-symbols-1.0.2.tgz#376ff7b58ea3086a0f09facc74617eca501e1a18"
  dependencies:
    chalk "^1.0.0"

log-update@^1.0.2:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/log-update/-/log-update-1.0.2.tgz#19929f64c4093d2d2e7075a1dad8af59c296b8d1"
  dependencies:
    ansi-escapes "^1.0.0"
    cli-cursor "^1.0.2"

log4js@^4.0.0:
  version "4.5.1"
  resolved "http://npm-registry.persgroep.digital/log4js/-/log4js-4.5.1.tgz#e543625e97d9e6f3e6e7c9fc196dd6ab2cae30b5"
  dependencies:
    date-format "^2.0.0"
    debug "^4.1.1"
    flatted "^2.0.0"
    rfdc "^1.1.4"
    streamroller "^1.0.6"

loglevel@^1.4.1:
  version "1.8.1"
  resolved "http://npm-registry.persgroep.digital/loglevel/-/loglevel-1.8.1.tgz#5c621f83d5b48c54ae93b6156353f555963377b4"

longest-streak@^2.0.1:
  version "2.0.4"
  resolved "http://npm-registry.persgroep.digital/longest-streak/-/longest-streak-2.0.4.tgz#b8599957da5b5dab64dee3fe316fa774597d90e4"

loose-envify@^1.0.0:
  version "1.4.0"
  resolved "http://npm-registry.persgroep.digital/loose-envify/-/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

loud-rejection@^1.0.0:
  version "1.6.0"
  resolved "http://npm-registry.persgroep.digital/loud-rejection/-/loud-rejection-1.6.0.tgz#5b46f80147edee578870f086d04821cf998e551f"
  dependencies:
    currently-unhandled "^0.4.1"
    signal-exit "^3.0.0"

loupe@^2.3.1:
  version "2.3.6"
  resolved "http://npm-registry.persgroep.digital/loupe/-/loupe-2.3.6.tgz#76e4af498103c532d1ecc9be102036a21f787b53"
  dependencies:
    get-func-name "^2.0.0"

lower-case@^1.1.1:
  version "1.1.4"
  resolved "http://npm-registry.persgroep.digital/lower-case/-/lower-case-1.1.4.tgz#9a2cabd1b9e8e0ae993a4bf7d5875c39c42e8eac"

lru-cache@4.1.x, lru-cache@^4.0.1:
  version "4.1.5"
  resolved "http://npm-registry.persgroep.digital/lru-cache/-/lru-cache-4.1.5.tgz#8bbe50ea85bed59bc9e33dcab8235ee9bcf443cd"
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "http://npm-registry.persgroep.digital/lru-cache/-/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  dependencies:
    yallist "^3.0.2"

magic-string@^0.25.0:
  version "0.25.9"
  resolved "http://npm-registry.persgroep.digital/magic-string/-/magic-string-0.25.9.tgz#de7f9faf91ef8a1c91d02c2e5314c8277dbcdd1c"
  dependencies:
    sourcemap-codec "^1.4.8"

make-dir@^2.0.0, make-dir@^2.1.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/make-dir/-/make-dir-2.1.0.tgz#5f0310e18b8be898cc07009295a30ae41e91e6f5"
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-error@^1.1.1:
  version "1.3.6"
  resolved "http://npm-registry.persgroep.digital/make-error/-/make-error-1.3.6.tgz#2eb2e37ea9b67c4891f684a1394799af484cf7a2"

make-fetch-happen@^4.0.1, make-fetch-happen@^4.0.2:
  version "4.0.2"
  resolved "http://npm-registry.persgroep.digital/make-fetch-happen/-/make-fetch-happen-4.0.2.tgz#2d156b11696fb32bffbafe1ac1bc085dd6c78a79"
  dependencies:
    agentkeepalive "^3.4.1"
    cacache "^11.3.3"
    http-cache-semantics "^3.8.1"
    http-proxy-agent "^2.1.0"
    https-proxy-agent "^2.2.1"
    lru-cache "^5.1.1"
    mississippi "^3.0.0"
    node-fetch-npm "^2.0.2"
    promise-retry "^1.1.1"
    socks-proxy-agent "^4.0.0"
    ssri "^6.0.0"

map-age-cleaner@^0.1.1:
  version "0.1.3"
  resolved "http://npm-registry.persgroep.digital/map-age-cleaner/-/map-age-cleaner-0.1.3.tgz#7d583a7306434c055fe474b0f45078e6e1b4b92a"
  dependencies:
    p-defer "^1.0.0"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "http://npm-registry.persgroep.digital/map-cache/-/map-cache-0.2.2.tgz#c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf"

map-obj@^1.0.0, map-obj@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/map-obj/-/map-obj-1.0.1.tgz#d933ceb9205d82bdcf4886f6742bdc2b4dea146d"

map-obj@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/map-obj/-/map-obj-2.0.0.tgz#a65cd29087a92598b8791257a523e021222ac1f9"

map-visit@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/map-visit/-/map-visit-1.0.0.tgz#ecdca8f13144e660f1b5bd41f12f3479d98dfb8f"
  dependencies:
    object-visit "^1.0.0"

markdown-escapes@^1.0.0:
  version "1.0.4"
  resolved "http://npm-registry.persgroep.digital/markdown-escapes/-/markdown-escapes-1.0.4.tgz#c95415ef451499d7602b91095f3c8e8975f78535"

markdown-it-plain-text@^0.3.0:
  version "0.3.0"
  resolved "http://npm-registry.persgroep.digital/markdown-it-plain-text/-/markdown-it-plain-text-0.3.0.tgz#374abfcc1c95ae3d7a5e09365558d43184e1e7db"

markdown-it@^13.0.1:
  version "13.0.1"
  resolved "http://npm-registry.persgroep.digital/markdown-it/-/markdown-it-13.0.1.tgz#c6ecc431cacf1a5da531423fc6a42807814af430"
  dependencies:
    argparse "^2.0.1"
    entities "~3.0.1"
    linkify-it "^4.0.1"
    mdurl "^1.0.1"
    uc.micro "^1.0.5"

markdown-table@^1.1.0:
  version "1.1.3"
  resolved "http://npm-registry.persgroep.digital/markdown-table/-/markdown-table-1.1.3.tgz#9fcb69bcfdb8717bfd0398c6ec2d93036ef8de60"

math-random@^1.0.1:
  version "1.0.4"
  resolved "http://npm-registry.persgroep.digital/math-random/-/math-random-1.0.4.tgz#5dd6943c938548267016d4e34f057583080c514c"

mathml-tag-names@^2.0.1:
  version "2.1.3"
  resolved "http://npm-registry.persgroep.digital/mathml-tag-names/-/mathml-tag-names-2.1.3.tgz#4ddadd67308e780cf16a47685878ee27b736a0a3"

md5.js@^1.3.4:
  version "1.3.5"
  resolved "http://npm-registry.persgroep.digital/md5.js/-/md5.js-1.3.5.tgz#b5d07b8e3216e3e27cd728d72f70d1e6a342005f"
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

mdast-util-compact@^1.0.0:
  version "1.0.4"
  resolved "http://npm-registry.persgroep.digital/mdast-util-compact/-/mdast-util-compact-1.0.4.tgz#d531bb7667b5123abf20859be086c4d06c894593"
  dependencies:
    unist-util-visit "^1.1.0"

mdurl@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/mdurl/-/mdurl-1.0.1.tgz#fe85b2ec75a59037f2adfec100fd6c601761152e"

media-typer@0.3.0:
  version "0.3.0"
  resolved "http://npm-registry.persgroep.digital/media-typer/-/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"

mem@^4.0.0:
  version "4.3.0"
  resolved "http://npm-registry.persgroep.digital/mem/-/mem-4.3.0.tgz#461af497bc4ae09608cdb2e60eefb69bff744178"
  dependencies:
    map-age-cleaner "^0.1.1"
    mimic-fn "^2.0.0"
    p-is-promise "^2.0.0"

memory-fs@^0.4.0, memory-fs@~0.4.1:
  version "0.4.1"
  resolved "http://npm-registry.persgroep.digital/memory-fs/-/memory-fs-0.4.1.tgz#3a9a20b8462523e447cfbc7e8bb80ed667bfc552"
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

memory-fs@^0.5.0:
  version "0.5.0"
  resolved "http://npm-registry.persgroep.digital/memory-fs/-/memory-fs-0.5.0.tgz#324c01288b88652966d161db77838720845a8e3c"
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

meow@^3.7.0:
  version "3.7.0"
  resolved "http://npm-registry.persgroep.digital/meow/-/meow-3.7.0.tgz#72cb668b425228290abbfa856892587308a801fb"
  dependencies:
    camelcase-keys "^2.0.0"
    decamelize "^1.1.2"
    loud-rejection "^1.0.0"
    map-obj "^1.0.1"
    minimist "^1.1.3"
    normalize-package-data "^2.3.4"
    object-assign "^4.0.1"
    read-pkg-up "^1.0.1"
    redent "^1.0.0"
    trim-newlines "^1.0.0"

meow@^5.0.0:
  version "5.0.0"
  resolved "http://npm-registry.persgroep.digital/meow/-/meow-5.0.0.tgz#dfc73d63a9afc714a5e371760eb5c88b91078aa4"
  dependencies:
    camelcase-keys "^4.0.0"
    decamelize-keys "^1.0.0"
    loud-rejection "^1.0.0"
    minimist-options "^3.0.1"
    normalize-package-data "^2.3.4"
    read-pkg-up "^3.0.0"
    redent "^2.0.0"
    trim-newlines "^2.0.0"
    yargs-parser "^10.0.0"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/merge-descriptors/-/merge-descriptors-1.0.1.tgz#b00aaa556dd8b44568150ec9d1b953f3f90cbb61"

merge2@^1.2.3:
  version "1.4.1"
  resolved "http://npm-registry.persgroep.digital/merge2/-/merge2-1.4.1.tgz#4368892f885e907455a6fd7dc55c0c9d404990ae"

methods@~1.1.2:
  version "1.1.2"
  resolved "http://npm-registry.persgroep.digital/methods/-/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"

micromatch@^2.1.5:
  version "2.3.11"
  resolved "http://npm-registry.persgroep.digital/micromatch/-/micromatch-2.3.11.tgz#86677c97d1720b363431d04d0d15293bd38c1565"
  dependencies:
    arr-diff "^2.0.0"
    array-unique "^0.2.1"
    braces "^1.8.2"
    expand-brackets "^0.1.4"
    extglob "^0.3.1"
    filename-regex "^2.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.1"
    kind-of "^3.0.2"
    normalize-path "^2.0.1"
    object.omit "^2.0.0"
    parse-glob "^3.0.4"
    regex-cache "^0.4.2"

micromatch@^3.1.10, micromatch@^3.1.4, micromatch@^3.1.8, micromatch@^3.1.9:
  version "3.1.10"
  resolved "http://npm-registry.persgroep.digital/micromatch/-/micromatch-3.1.10.tgz#70859bc95c9840952f359a068a3fc49f9ecfac23"
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

miller-rabin@^4.0.0:
  version "4.0.1"
  resolved "http://npm-registry.persgroep.digital/miller-rabin/-/miller-rabin-4.0.1.tgz#f080351c865b0dc562a8462966daa53543c78a4d"
  dependencies:
    bn.js "^4.0.0"
    brorand "^1.0.1"

mime-db@1.52.0, "mime-db@>= 1.43.0 < 2":
  version "1.52.0"
  resolved "http://npm-registry.persgroep.digital/mime-db/-/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"

mime-db@~1.12.0:
  version "1.12.0"
  resolved "http://npm-registry.persgroep.digital/mime-db/-/mime-db-1.12.0.tgz#3d0c63180f458eb10d325aaa37d7c58ae312e9d7"

mime-types@^2.1.12, mime-types@~2.1.17, mime-types@~2.1.19, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  resolved "http://npm-registry.persgroep.digital/mime-types/-/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  dependencies:
    mime-db "1.52.0"

mime-types@~2.0.9:
  version "2.0.14"
  resolved "http://npm-registry.persgroep.digital/mime-types/-/mime-types-2.0.14.tgz#310e159db23e077f8bb22b748dabfa4957140aa6"
  dependencies:
    mime-db "~1.12.0"

mime@1.6.0, mime@^1.4.1:
  version "1.6.0"
  resolved "http://npm-registry.persgroep.digital/mime/-/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"

mime@^2.3.1:
  version "2.6.0"
  resolved "http://npm-registry.persgroep.digital/mime/-/mime-2.6.0.tgz#a2a682a95cd4d0cb1d6257e28f83da7e35800367"

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/mimic-fn/-/mimic-fn-1.2.0.tgz#820c86a39334640e99516928bd03fca88057d022"

mimic-fn@^2.0.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/mimic-fn/-/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"

mini-css-extract-plugin@0.5.0:
  version "0.5.0"
  resolved "http://npm-registry.persgroep.digital/mini-css-extract-plugin/-/mini-css-extract-plugin-0.5.0.tgz#ac0059b02b9692515a637115b0cc9fed3a35c7b0"
  dependencies:
    loader-utils "^1.1.0"
    schema-utils "^1.0.0"
    webpack-sources "^1.1.0"

minimalistic-assert@^1.0.0, minimalistic-assert@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz#2e194de044626d4a10e7f7fbc00ce73e83e4d5c7"

minimalistic-crypto-utils@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz#f6c00c1c0b082246e5c4d99dfb8c7c083b2b582a"

minimatch@3.0.4:
  version "3.0.4"
  resolved "http://npm-registry.persgroep.digital/minimatch/-/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^3.0.2, minimatch@^3.0.3, minimatch@^3.0.4, minimatch@^3.1.1:
  version "3.1.2"
  resolved "http://npm-registry.persgroep.digital/minimatch/-/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  dependencies:
    brace-expansion "^1.1.7"

minimatch@~3.0.2:
  version "3.0.8"
  resolved "http://npm-registry.persgroep.digital/minimatch/-/minimatch-3.0.8.tgz#5e6a59bd11e2ab0de1cfb843eb2d82e546c321c1"
  dependencies:
    brace-expansion "^1.1.7"

minimist-options@^3.0.1:
  version "3.0.2"
  resolved "http://npm-registry.persgroep.digital/minimist-options/-/minimist-options-3.0.2.tgz#fba4c8191339e13ecf4d61beb03f070103f3d954"
  dependencies:
    arrify "^1.0.1"
    is-plain-obj "^1.1.0"

minimist@0.0.8:
  version "0.0.8"
  resolved "http://npm-registry.persgroep.digital/minimist/-/minimist-0.0.8.tgz#857fcabfc3397d2625b8228262e86aa7a011b05d"

minimist@1.2.0:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/minimist/-/minimist-1.2.0.tgz#a35008b20f41383eec1fb914f4cd5df79a264284"

minimist@^1.1.0, minimist@^1.1.3, minimist@^1.2.0, minimist@^1.2.5, minimist@^1.2.6:
  version "1.2.8"
  resolved "http://npm-registry.persgroep.digital/minimist/-/minimist-1.2.8.tgz#c1a464e7693302e082a075cee0c057741ac4772c"

minimist@~0.0.1:
  version "0.0.10"
  resolved "http://npm-registry.persgroep.digital/minimist/-/minimist-0.0.10.tgz#de3f98543dbf96082be48ad1a0c7cda836301dcf"

minipass@^2.3.5, minipass@^2.6.0, minipass@^2.9.0:
  version "2.9.0"
  resolved "http://npm-registry.persgroep.digital/minipass/-/minipass-2.9.0.tgz#e713762e7d3e32fed803115cf93e04bca9fcc9a6"
  dependencies:
    safe-buffer "^5.1.2"
    yallist "^3.0.0"

minizlib@^1.3.3:
  version "1.3.3"
  resolved "http://npm-registry.persgroep.digital/minizlib/-/minizlib-1.3.3.tgz#2290de96818a34c29551c8a8d301216bd65a861d"
  dependencies:
    minipass "^2.9.0"

mississippi@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/mississippi/-/mississippi-3.0.0.tgz#ea0a3291f97e0b5e8776b363d5f0a12d94c67022"
  dependencies:
    concat-stream "^1.5.0"
    duplexify "^3.4.2"
    end-of-stream "^1.1.0"
    flush-write-stream "^1.0.0"
    from2 "^2.1.0"
    parallel-transform "^1.1.0"
    pump "^3.0.0"
    pumpify "^1.3.3"
    stream-each "^1.1.0"
    through2 "^2.0.0"

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "http://npm-registry.persgroep.digital/mixin-deep/-/mixin-deep-1.3.2.tgz#1120b43dc359a785dce65b55b82e257ccf479566"
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mixin-object@^2.0.1:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/mixin-object/-/mixin-object-2.0.1.tgz#4fb949441dab182540f1fe035ba60e1947a5e57e"
  dependencies:
    for-in "^0.1.3"
    is-extendable "^0.1.1"

mkdirp-classic@^0.5.2:
  version "0.5.3"
  resolved "http://npm-registry.persgroep.digital/mkdirp-classic/-/mkdirp-classic-0.5.3.tgz#fa10c9115cc6d8865be221ba47ee9bed78601113"

mkdirp@0.5.1:
  version "0.5.1"
  resolved "http://npm-registry.persgroep.digital/mkdirp/-/mkdirp-0.5.1.tgz#30057438eac6cf7f8c4767f38648d6697d75c903"
  dependencies:
    minimist "0.0.8"

mkdirp@0.5.x, "mkdirp@>=0.5 0", mkdirp@^0.5.0, mkdirp@^0.5.1, mkdirp@^0.5.4, mkdirp@^0.5.5, mkdirp@^0.5.6, mkdirp@~0.5.0:
  version "0.5.6"
  resolved "http://npm-registry.persgroep.digital/mkdirp/-/mkdirp-0.5.6.tgz#7def03d2432dcae4ba1d611445c48396062255f6"
  dependencies:
    minimist "^1.2.6"

mkdirp@~0.3.5:
  version "0.3.5"
  resolved "http://npm-registry.persgroep.digital/mkdirp/-/mkdirp-0.3.5.tgz#de3e5f8961c88c787ee1368df849ac4413eca8d7"

module-deps@^6.0.0, module-deps@^6.2.3:
  version "6.2.3"
  resolved "http://npm-registry.persgroep.digital/module-deps/-/module-deps-6.2.3.tgz#15490bc02af4b56cf62299c7c17cba32d71a96ee"
  dependencies:
    JSONStream "^1.0.3"
    browser-resolve "^2.0.0"
    cached-path-relative "^1.0.2"
    concat-stream "~1.6.0"
    defined "^1.0.0"
    detective "^5.2.0"
    duplexer2 "^0.1.2"
    inherits "^2.0.1"
    parents "^1.0.0"
    readable-stream "^2.0.2"
    resolve "^1.4.0"
    stream-combiner2 "^1.1.1"
    subarg "^1.0.0"
    through2 "^2.0.0"
    xtend "^4.0.0"

moment@2.24.0:
  version "2.24.0"
  resolved "http://npm-registry.persgroep.digital/moment/-/moment-2.24.0.tgz#0d055d53f5052aa653c9f6eb68bb5d12bf5c2b5b"

move-concurrently@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/move-concurrently/-/move-concurrently-1.0.1.tgz#be2c005fda32e0b29af1f05d7c4b33214c701f92"
  dependencies:
    aproba "^1.1.1"
    copy-concurrently "^1.0.0"
    fs-write-stream-atomic "^1.0.8"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.3"

ms@2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/ms/-/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"

ms@2.1.2:
  version "2.1.2"
  resolved "http://npm-registry.persgroep.digital/ms/-/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"

ms@2.1.3, ms@^2.0.0, ms@^2.1.1:
  version "2.1.3"
  resolved "http://npm-registry.persgroep.digital/ms/-/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"

multer@^0.1.8:
  version "0.1.8"
  resolved "http://npm-registry.persgroep.digital/multer/-/multer-0.1.8.tgz#551b8a6015093701bcacc964916b1ae06578f37b"
  dependencies:
    busboy "~0.2.9"
    mkdirp "~0.3.5"
    qs "~1.2.2"
    type-is "~1.5.2"

multicast-dns-service-types@^1.1.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/multicast-dns-service-types/-/multicast-dns-service-types-1.1.0.tgz#899f11d9686e5e05cb91b35d5f0e63b773cfc901"

multicast-dns@^6.0.1:
  version "6.2.3"
  resolved "http://npm-registry.persgroep.digital/multicast-dns/-/multicast-dns-6.2.3.tgz#a0ec7bd9055c4282f790c3c82f4e28db3b31b229"
  dependencies:
    dns-packet "^1.3.1"
    thunky "^1.0.2"

mute-stream@0.0.7:
  version "0.0.7"
  resolved "http://npm-registry.persgroep.digital/mute-stream/-/mute-stream-0.0.7.tgz#3075ce93bc21b8fab43e1bc4da7e8115ed1e7bab"

mz@^2.4.0:
  version "2.7.0"
  resolved "http://npm-registry.persgroep.digital/mz/-/mz-2.7.0.tgz#95008057a56cafadc2bc63dde7f9ff6955948e32"
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nan@^2.12.1, nan@^2.13.2:
  version "2.17.0"
  resolved "http://npm-registry.persgroep.digital/nan/-/nan-2.17.0.tgz#c0150a2368a182f033e9aa5195ec76ea41a199cb"

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "http://npm-registry.persgroep.digital/nanomatch/-/nanomatch-1.2.13.tgz#b87a8aa4fc0de8fe6be88895b38983ff265bd119"
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

negotiator@0.6.3:
  version "0.6.3"
  resolved "http://npm-registry.persgroep.digital/negotiator/-/negotiator-0.6.3.tgz#58e323a72fedc0d6f9cd4d31fe49f51479590ccd"

neo-async@^2.5.0:
  version "2.6.2"
  resolved "http://npm-registry.persgroep.digital/neo-async/-/neo-async-2.6.2.tgz#b4aafb93e3aeb2d8174ca53cf163ab7d7308305f"

next-tick@^1.1.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/next-tick/-/next-tick-1.1.0.tgz#1836ee30ad56d67ef281b22bd199f709449b35eb"

ngrx-store-logger@^0.2.2:
  version "0.2.4"
  resolved "http://npm-registry.persgroep.digital/ngrx-store-logger/-/ngrx-store-logger-0.2.4.tgz#c5e266e78d1775f645548c362030e68260d2e6ba"

nice-try@^1.0.4:
  version "1.0.5"
  resolved "http://npm-registry.persgroep.digital/nice-try/-/nice-try-1.0.5.tgz#a3378a7696ce7d223e88fc9b764bd7ef1089e366"

no-case@^2.2.0:
  version "2.3.2"
  resolved "http://npm-registry.persgroep.digital/no-case/-/no-case-2.3.2.tgz#60b813396be39b3f1288a4c1ed5d1e7d28b464ac"
  dependencies:
    lower-case "^1.1.1"

node-eta@^0.1.1:
  version "0.1.1"
  resolved "http://npm-registry.persgroep.digital/node-eta/-/node-eta-0.1.1.tgz#4066109b39371c761c72b7ebda9a9ea0a5de121f"

node-fetch-npm@^2.0.2:
  version "2.0.4"
  resolved "http://npm-registry.persgroep.digital/node-fetch-npm/-/node-fetch-npm-2.0.4.tgz#6507d0e17a9ec0be3bec516958a497cec54bf5a4"
  dependencies:
    encoding "^0.1.11"
    json-parse-better-errors "^1.0.0"
    safe-buffer "^5.1.1"

node-forge@^0.10.0:
  version "0.10.0"
  resolved "http://npm-registry.persgroep.digital/node-forge/-/node-forge-0.10.0.tgz#32dea2afb3e9926f02ee5ce8794902691a676bf3"

node-gyp@^3.8.0:
  version "3.8.0"
  resolved "http://npm-registry.persgroep.digital/node-gyp/-/node-gyp-3.8.0.tgz#540304261c330e80d0d5edce253a68cb3964218c"
  dependencies:
    fstream "^1.0.0"
    glob "^7.0.3"
    graceful-fs "^4.1.2"
    mkdirp "^0.5.0"
    nopt "2 || 3"
    npmlog "0 || 1 || 2 || 3 || 4"
    osenv "0"
    request "^2.87.0"
    rimraf "2"
    semver "~5.3.0"
    tar "^2.0.0"
    which "1"

node-libs-browser@^2.0.0:
  version "2.2.1"
  resolved "http://npm-registry.persgroep.digital/node-libs-browser/-/node-libs-browser-2.2.1.tgz#b64f513d18338625f90346d27b0d235e631f6425"
  dependencies:
    assert "^1.1.1"
    browserify-zlib "^0.2.0"
    buffer "^4.3.0"
    console-browserify "^1.1.0"
    constants-browserify "^1.0.0"
    crypto-browserify "^3.11.0"
    domain-browser "^1.1.1"
    events "^3.0.0"
    https-browserify "^1.0.0"
    os-browserify "^0.3.0"
    path-browserify "0.0.1"
    process "^0.11.10"
    punycode "^1.2.4"
    querystring-es3 "^0.2.0"
    readable-stream "^2.3.3"
    stream-browserify "^2.0.1"
    stream-http "^2.7.2"
    string_decoder "^1.0.0"
    timers-browserify "^2.0.4"
    tty-browserify "0.0.0"
    url "^0.11.0"
    util "^0.11.0"
    vm-browserify "^1.0.1"

node-releases@^2.0.8:
  version "2.0.10"
  resolved "http://npm-registry.persgroep.digital/node-releases/-/node-releases-2.0.10.tgz#c311ebae3b6a148c89b1813fd7c4d3c024ef537f"

node-sass@4.13.1:
  version "4.13.1"
  resolved "http://npm-registry.persgroep.digital/node-sass/-/node-sass-4.13.1.tgz#9db5689696bb2eec2c32b98bfea4c7a2e992d0a3"
  dependencies:
    async-foreach "^0.1.3"
    chalk "^1.1.1"
    cross-spawn "^3.0.0"
    gaze "^1.0.0"
    get-stdin "^4.0.1"
    glob "^7.0.3"
    in-publish "^2.0.0"
    lodash "^4.17.15"
    meow "^3.7.0"
    mkdirp "^0.5.1"
    nan "^2.13.2"
    node-gyp "^3.8.0"
    npmlog "^4.0.0"
    request "^2.88.0"
    sass-graph "^2.2.4"
    stdout-stream "^1.4.0"
    "true-case-path" "^1.0.2"

"nopt@2 || 3":
  version "3.0.6"
  resolved "http://npm-registry.persgroep.digital/nopt/-/nopt-3.0.6.tgz#c6465dbf08abcd4db359317f79ac68a646b28ff9"
  dependencies:
    abbrev "1"

normalize-package-data@^2.3.2, normalize-package-data@^2.3.4, normalize-package-data@^2.4.0:
  version "2.5.0"
  resolved "http://npm-registry.persgroep.digital/normalize-package-data/-/normalize-package-data-2.5.0.tgz#e66db1838b200c1dfc233225d12cb36520e234a8"
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^2.0.0, normalize-path@^2.0.1, normalize-path@^2.1.1:
  version "2.1.1"
  resolved "http://npm-registry.persgroep.digital/normalize-path/-/normalize-path-2.1.1.tgz#1ab28b556e198363a8c1a6f7e6fa20137fe6aed9"
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/normalize-path/-/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "http://npm-registry.persgroep.digital/normalize-range/-/normalize-range-0.1.2.tgz#2d10c06bdfd312ea9777695a4d28439456b75942"

normalize-selector@^0.2.0:
  version "0.2.0"
  resolved "http://npm-registry.persgroep.digital/normalize-selector/-/normalize-selector-0.2.0.tgz#d0b145eb691189c63a78d201dc4fdb1293ef0c03"

npm-bundled@^1.0.1:
  version "1.1.2"
  resolved "http://npm-registry.persgroep.digital/npm-bundled/-/npm-bundled-1.1.2.tgz#944c78789bd739035b70baa2ca5cc32b8d860bc1"
  dependencies:
    npm-normalize-package-bin "^1.0.1"

npm-normalize-package-bin@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/npm-normalize-package-bin/-/npm-normalize-package-bin-1.0.1.tgz#6e79a41f23fd235c0623218228da7d9c23b8f6e2"

npm-package-arg@6.1.0:
  version "6.1.0"
  resolved "http://npm-registry.persgroep.digital/npm-package-arg/-/npm-package-arg-6.1.0.tgz#15ae1e2758a5027efb4c250554b85a737db7fcc1"
  dependencies:
    hosted-git-info "^2.6.0"
    osenv "^0.1.5"
    semver "^5.5.0"
    validate-npm-package-name "^3.0.0"

npm-package-arg@^6.0.0, npm-package-arg@^6.1.0:
  version "6.1.1"
  resolved "http://npm-registry.persgroep.digital/npm-package-arg/-/npm-package-arg-6.1.1.tgz#02168cb0a49a2b75bf988a28698de7b529df5cb7"
  dependencies:
    hosted-git-info "^2.7.1"
    osenv "^0.1.5"
    semver "^5.6.0"
    validate-npm-package-name "^3.0.0"

npm-packlist@^1.1.12:
  version "1.4.8"
  resolved "http://npm-registry.persgroep.digital/npm-packlist/-/npm-packlist-1.4.8.tgz#56ee6cc135b9f98ad3d51c1c95da22bbb9b2ef3e"
  dependencies:
    ignore-walk "^3.0.1"
    npm-bundled "^1.0.1"
    npm-normalize-package-bin "^1.0.1"

npm-pick-manifest@^2.2.3:
  version "2.2.3"
  resolved "http://npm-registry.persgroep.digital/npm-pick-manifest/-/npm-pick-manifest-2.2.3.tgz#32111d2a9562638bb2c8f2bf27f7f3092c8fae40"
  dependencies:
    figgy-pudding "^3.5.1"
    npm-package-arg "^6.0.0"
    semver "^5.4.1"

npm-registry-fetch@^3.8.0:
  version "3.9.1"
  resolved "http://npm-registry.persgroep.digital/npm-registry-fetch/-/npm-registry-fetch-3.9.1.tgz#00ff6e4e35d3f75a172b332440b53e93f4cb67de"
  dependencies:
    JSONStream "^1.3.4"
    bluebird "^3.5.1"
    figgy-pudding "^3.4.1"
    lru-cache "^5.1.1"
    make-fetch-happen "^4.0.2"
    npm-package-arg "^6.1.0"

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "http://npm-registry.persgroep.digital/npm-run-path/-/npm-run-path-2.0.2.tgz#35a9232dfa35d7067b4cb2ddf2357b1871536c5f"
  dependencies:
    path-key "^2.0.0"

"npmlog@0 || 1 || 2 || 3 || 4", npmlog@^4.0.0:
  version "4.1.2"
  resolved "http://npm-registry.persgroep.digital/npmlog/-/npmlog-4.1.2.tgz#08a7f2a8bf734604779a9efa4ad5cc717abb954b"
  dependencies:
    are-we-there-yet "~1.1.2"
    console-control-strings "~1.1.0"
    gauge "~2.7.3"
    set-blocking "~2.0.0"

null-check@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/null-check/-/null-check-1.0.0.tgz#977dffd7176012b9ec30d2a39db5cf72a0439edd"

num2fraction@^1.2.2:
  version "1.2.2"
  resolved "http://npm-registry.persgroep.digital/num2fraction/-/num2fraction-1.2.2.tgz#6f682b6a027a4e9ddfa4564cd2589d1d4e669ede"

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/number-is-nan/-/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "http://npm-registry.persgroep.digital/oauth-sign/-/oauth-sign-0.9.0.tgz#47a7b016baa68b5fa0ecf3dee08a85c679ac6455"

object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "http://npm-registry.persgroep.digital/object-assign/-/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"

object-component@0.0.3:
  version "0.0.3"
  resolved "http://npm-registry.persgroep.digital/object-component/-/object-component-0.0.3.tgz#f0c69aa50efc95b866c186f400a33769cb2f1291"

object-copy@^0.1.0:
  version "0.1.0"
  resolved "http://npm-registry.persgroep.digital/object-copy/-/object-copy-0.1.0.tgz#7e7d858b781bd7c991a41ba975ed3812754e998c"
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-inspect@^1.9.0:
  version "1.12.3"
  resolved "http://npm-registry.persgroep.digital/object-inspect/-/object-inspect-1.12.3.tgz#ba62dffd67ee256c8c086dfae69e016cd1f198b9"

object-is@^1.0.1:
  version "1.1.5"
  resolved "http://npm-registry.persgroep.digital/object-is/-/object-is-1.1.5.tgz#b9deeaa5fc7f1846a0faecdceec138e5778f53ac"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

object-keys@^1.1.1:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/object-keys/-/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"

object-visit@^1.0.0:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/object-visit/-/object-visit-1.0.1.tgz#f79c4493af0c5377b59fe39d395e41042dd045bb"
  dependencies:
    isobject "^3.0.0"

object.assign@^4.1.0:
  version "4.1.4"
  resolved "http://npm-registry.persgroep.digital/object.assign/-/object.assign-4.1.4.tgz#9673c7c7c351ab8c4d0b516f4343ebf4dfb7799f"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    has-symbols "^1.0.3"
    object-keys "^1.1.1"

object.omit@^2.0.0:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/object.omit/-/object.omit-2.0.1.tgz#1a9c744829f39dbb858c76ca3579ae2a54ebd1fa"
  dependencies:
    for-own "^0.1.4"
    is-extendable "^0.1.1"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "http://npm-registry.persgroep.digital/object.pick/-/object.pick-1.3.0.tgz#87a10ac4c1694bd2e1cbf53591a66141fb5dd747"
  dependencies:
    isobject "^3.0.1"

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "http://npm-registry.persgroep.digital/obuf/-/obuf-1.1.2.tgz#09bea3343d41859ebd446292d11c9d4db619084e"

on-finished@2.4.1:
  version "2.4.1"
  resolved "http://npm-registry.persgroep.digital/on-finished/-/on-finished-2.4.1.tgz#58c8c44116e54845ad57f14ab10b03533184ac3f"
  dependencies:
    ee-first "1.1.1"

on-finished@~2.3.0:
  version "2.3.0"
  resolved "http://npm-registry.persgroep.digital/on-finished/-/on-finished-2.3.0.tgz#20f1336481b083cd75337992a16971aa2d906947"
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/on-headers/-/on-headers-1.0.2.tgz#772b0ae6aaa525c399e489adfad90c403eb3c28f"

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "http://npm-registry.persgroep.digital/once/-/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  dependencies:
    wrappy "1"

onetime@^1.0.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/onetime/-/onetime-1.1.0.tgz#a1f7838f8314c516f05ecefcbc4ccfe04b4ed789"

onetime@^2.0.0:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/onetime/-/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
  dependencies:
    mimic-fn "^1.0.0"

ono@^6.0.0:
  version "6.0.1"
  resolved "http://npm-registry.persgroep.digital/ono/-/ono-6.0.1.tgz#1bc14ffb8af1e5db3f7397f75b88e4a2d64bbd71"

open@6.0.0:
  version "6.0.0"
  resolved "http://npm-registry.persgroep.digital/open/-/open-6.0.0.tgz#cae5e2c1a3a1bfaee0d0acc8c4b7609374750346"
  dependencies:
    is-wsl "^1.1.0"

openapi-schemas@^1.0.2:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/openapi-schemas/-/openapi-schemas-1.0.3.tgz#0fa2f19e44ce8a1cdab9c9f616df4babe1aa026b"

openapi-types@^1.3.5:
  version "1.3.5"
  resolved "http://npm-registry.persgroep.digital/openapi-types/-/openapi-types-1.3.5.tgz#6718cfbc857fe6c6f1471f65b32bdebb9c10ce40"

opn@^5.1.0:
  version "5.5.0"
  resolved "http://npm-registry.persgroep.digital/opn/-/opn-5.5.0.tgz#fc7164fab56d235904c51c3b27da6758ca3b9bfc"
  dependencies:
    is-wsl "^1.1.0"

optimist@^0.6.1:
  version "0.6.1"
  resolved "http://npm-registry.persgroep.digital/optimist/-/optimist-0.6.1.tgz#da3ea74686fa21a19a111c326e90eb15a0196686"
  dependencies:
    minimist "~0.0.1"
    wordwrap "~0.0.2"

ora@^0.2.3:
  version "0.2.3"
  resolved "http://npm-registry.persgroep.digital/ora/-/ora-0.2.3.tgz#37527d220adcd53c39b73571d754156d5db657a4"
  dependencies:
    chalk "^1.1.1"
    cli-cursor "^1.0.2"
    cli-spinners "^0.1.2"
    object-assign "^4.0.1"

os-browserify@^0.3.0, os-browserify@~0.3.0:
  version "0.3.0"
  resolved "http://npm-registry.persgroep.digital/os-browserify/-/os-browserify-0.3.0.tgz#854373c7f5c2315914fc9bfc6bd8238fdda1ec27"

os-homedir@^1.0.0, os-homedir@^1.0.1:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/os-homedir/-/os-homedir-1.0.2.tgz#ffbc4988336e0e833de0c168c7ef152121aa7fb3"

os-locale@^1.4.0:
  version "1.4.0"
  resolved "http://npm-registry.persgroep.digital/os-locale/-/os-locale-1.4.0.tgz#20f9f17ae29ed345e8bde583b13d2009803c14d9"
  dependencies:
    lcid "^1.0.0"

os-locale@^3.0.0, os-locale@^3.1.0:
  version "3.1.0"
  resolved "http://npm-registry.persgroep.digital/os-locale/-/os-locale-3.1.0.tgz#a802a6ee17f24c10483ab9935719cef4ed16bf1a"
  dependencies:
    execa "^1.0.0"
    lcid "^2.0.0"
    mem "^4.0.0"

os-tmpdir@^1.0.0, os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/os-tmpdir/-/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"

osenv@0, osenv@^0.1.5:
  version "0.1.5"
  resolved "http://npm-registry.persgroep.digital/osenv/-/osenv-0.1.5.tgz#85cdfafaeb28e8677f416e287592b5f3f49ea410"
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.0"

outpipe@^1.1.0:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/outpipe/-/outpipe-1.1.1.tgz#50cf8616365e87e031e29a5ec9339a3da4725fa2"
  dependencies:
    shell-quote "^1.4.2"

p-defer@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/p-defer/-/p-defer-1.0.0.tgz#9f6eb182f6c9aa8cd743004a7d4f96b196b0fb0c"

p-finally@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/p-finally/-/p-finally-1.0.0.tgz#3fbcfb15b899a44123b34b6dcc18b724336a2cae"

p-is-promise@^2.0.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/p-is-promise/-/p-is-promise-2.1.0.tgz#918cebaea248a62cf7ffab8e3bca8c5f882fc42e"

p-limit@^1.1.0:
  version "1.3.0"
  resolved "http://npm-registry.persgroep.digital/p-limit/-/p-limit-1.3.0.tgz#b86bd5f0c25690911c7590fcbfc2010d54b3ccb8"
  dependencies:
    p-try "^1.0.0"

p-limit@^2.0.0, p-limit@^2.2.1:
  version "2.3.0"
  resolved "http://npm-registry.persgroep.digital/p-limit/-/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  dependencies:
    p-try "^2.0.0"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/p-locate/-/p-locate-2.0.0.tgz#20a0103b222a70c8fd39cc2e580680f3dde5ec43"
  dependencies:
    p-limit "^1.1.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/p-locate/-/p-locate-3.0.0.tgz#322d69a05c0264b25997d9f40cd8a891ab0064a4"
  dependencies:
    p-limit "^2.0.0"

p-map@^1.1.1:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/p-map/-/p-map-1.2.0.tgz#e4e94f311eabbc8633a1e79908165fca26241b6b"

p-try@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/p-try/-/p-try-1.0.0.tgz#cbc79cdbaf8fd4228e13f621f2b1a237c1b207b3"

p-try@^2.0.0:
  version "2.2.0"
  resolved "http://npm-registry.persgroep.digital/p-try/-/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"

pacote@9.4.0:
  version "9.4.0"
  resolved "http://npm-registry.persgroep.digital/pacote/-/pacote-9.4.0.tgz#af979abdeb175cd347c3e33be3241af1ed254807"
  dependencies:
    bluebird "^3.5.3"
    cacache "^11.3.2"
    figgy-pudding "^3.5.1"
    get-stream "^4.1.0"
    glob "^7.1.3"
    lru-cache "^5.1.1"
    make-fetch-happen "^4.0.1"
    minimatch "^3.0.4"
    minipass "^2.3.5"
    mississippi "^3.0.0"
    mkdirp "^0.5.1"
    normalize-package-data "^2.4.0"
    npm-package-arg "^6.1.0"
    npm-packlist "^1.1.12"
    npm-pick-manifest "^2.2.3"
    npm-registry-fetch "^3.8.0"
    osenv "^0.1.5"
    promise-inflight "^1.0.1"
    promise-retry "^1.1.1"
    protoduck "^5.0.1"
    rimraf "^2.6.2"
    safe-buffer "^5.1.2"
    semver "^5.6.0"
    ssri "^6.0.1"
    tar "^4.4.8"
    unique-filename "^1.1.1"
    which "^1.3.1"

pad-right@^0.2.2:
  version "0.2.2"
  resolved "http://npm-registry.persgroep.digital/pad-right/-/pad-right-0.2.2.tgz#6fbc924045d244f2a2a244503060d3bfc6009774"
  dependencies:
    repeat-string "^1.5.2"

pako@~1.0.5:
  version "1.0.11"
  resolved "http://npm-registry.persgroep.digital/pako/-/pako-1.0.11.tgz#6c9599d340d54dfd3946380252a35705a6b992bf"

parallel-transform@^1.1.0:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/parallel-transform/-/parallel-transform-1.2.0.tgz#9049ca37d6cb2182c3b1d2c720be94d14a5814fc"
  dependencies:
    cyclist "^1.0.1"
    inherits "^2.0.3"
    readable-stream "^2.1.5"

parents@^1.0.0, parents@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/parents/-/parents-1.0.1.tgz#fedd4d2bf193a77745fe71e371d73c3307d9c751"
  dependencies:
    path-platform "~0.11.15"

parse-asn1@^5.0.0, parse-asn1@^5.1.5:
  version "5.1.6"
  resolved "http://npm-registry.persgroep.digital/parse-asn1/-/parse-asn1-5.1.6.tgz#385080a3ec13cb62a62d39409cb3e88844cdaed4"
  dependencies:
    asn1.js "^5.2.0"
    browserify-aes "^1.0.0"
    evp_bytestokey "^1.0.0"
    pbkdf2 "^3.0.3"
    safe-buffer "^5.1.1"

parse-entities@^1.0.2, parse-entities@^1.1.0:
  version "1.2.2"
  resolved "http://npm-registry.persgroep.digital/parse-entities/-/parse-entities-1.2.2.tgz#c31bf0f653b6661354f8973559cb86dd1d5edf50"
  dependencies:
    character-entities "^1.0.0"
    character-entities-legacy "^1.0.0"
    character-reference-invalid "^1.0.0"
    is-alphanumerical "^1.0.0"
    is-decimal "^1.0.0"
    is-hexadecimal "^1.0.0"

parse-glob@^3.0.4:
  version "3.0.4"
  resolved "http://npm-registry.persgroep.digital/parse-glob/-/parse-glob-3.0.4.tgz#b2c376cfb11f35513badd173ef0bb6e3a388391c"
  dependencies:
    glob-base "^0.3.0"
    is-dotfile "^1.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.0"

parse-json@^2.2.0:
  version "2.2.0"
  resolved "http://npm-registry.persgroep.digital/parse-json/-/parse-json-2.2.0.tgz#f480f40434ef80741f8469099f8dea18f55a4dc9"
  dependencies:
    error-ex "^1.2.0"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "http://npm-registry.persgroep.digital/parse-json/-/parse-json-4.0.0.tgz#be35f5425be1f7f6c747184f98a788cb99477ee0"
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse5-html-rewriting-stream@5.1.0:
  version "5.1.0"
  resolved "http://npm-registry.persgroep.digital/parse5-html-rewriting-stream/-/parse5-html-rewriting-stream-5.1.0.tgz#63f92c958764aea8cbe3aa68f2e5895c32068ab8"
  dependencies:
    parse5 "^5.1.0"
    parse5-sax-parser "^5.1.0"

parse5-sax-parser@^5.1.0:
  version "5.1.1"
  resolved "http://npm-registry.persgroep.digital/parse5-sax-parser/-/parse5-sax-parser-5.1.1.tgz#02834a9d08b23ea2d99584841c38be09d5247a15"
  dependencies:
    parse5 "^5.1.1"

parse5@4.0.0:
  version "4.0.0"
  resolved "http://npm-registry.persgroep.digital/parse5/-/parse5-4.0.0.tgz#6d78656e3da8d78b4ec0b906f7c08ef1dfe3f608"

parse5@^5.1.0, parse5@^5.1.1:
  version "5.1.1"
  resolved "http://npm-registry.persgroep.digital/parse5/-/parse5-5.1.1.tgz#f68e4e5ba1852ac2cadc00f4555fff6c2abb6178"

parseqs@0.0.5:
  version "0.0.5"
  resolved "http://npm-registry.persgroep.digital/parseqs/-/parseqs-0.0.5.tgz#d5208a3738e46766e291ba2ea173684921a8b89d"
  dependencies:
    better-assert "~1.0.0"

parseuri@0.0.5:
  version "0.0.5"
  resolved "http://npm-registry.persgroep.digital/parseuri/-/parseuri-0.0.5.tgz#80204a50d4dbb779bfdc6ebe2778d90e4bce320a"
  dependencies:
    better-assert "~1.0.0"

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "http://npm-registry.persgroep.digital/parseurl/-/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "http://npm-registry.persgroep.digital/pascalcase/-/pascalcase-0.1.1.tgz#b363e55e8006ca6fe21784d2db22bd15d7917f14"

path-browserify@0.0.1, path-browserify@~0.0.0:
  version "0.0.1"
  resolved "http://npm-registry.persgroep.digital/path-browserify/-/path-browserify-0.0.1.tgz#e6c4ddd7ed3aa27c68a20cc4e50e1a4ee83bbc4a"

path-dirname@^1.0.0:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/path-dirname/-/path-dirname-1.0.2.tgz#cc33d24d525e099a5388c0336c6e32b9160609e0"

path-exists@^2.0.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/path-exists/-/path-exists-2.1.0.tgz#0feb6c64f0fc518d9a754dd5efb62c7022761f4b"
  dependencies:
    pinkie-promise "^2.0.0"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/path-exists/-/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"

path-is-absolute@^1.0.0, path-is-absolute@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/path-is-absolute/-/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"

path-is-inside@^1.0.1:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/path-is-inside/-/path-is-inside-1.0.2.tgz#365417dede44430d1c11af61027facf074bdfc53"

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/path-key/-/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"

path-parse@^1.0.7:
  version "1.0.7"
  resolved "http://npm-registry.persgroep.digital/path-parse/-/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"

path-platform@~0.11.15:
  version "0.11.15"
  resolved "http://npm-registry.persgroep.digital/path-platform/-/path-platform-0.11.15.tgz#e864217f74c36850f0852b78dc7bf7d4a5721bf2"

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "http://npm-registry.persgroep.digital/path-to-regexp/-/path-to-regexp-0.1.7.tgz#df604178005f522f15eb4490e7247a1bfaa67f8c"

path-type@^1.0.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/path-type/-/path-type-1.1.0.tgz#59c44f7ee491da704da415da5a4070ba4f8fe441"
  dependencies:
    graceful-fs "^4.1.2"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

path-type@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/path-type/-/path-type-3.0.0.tgz#cef31dc8e0a1a3bb0d105c0cd97cf3bf47f4e36f"
  dependencies:
    pify "^3.0.0"

pathval@^1.1.1:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/pathval/-/pathval-1.1.1.tgz#8534e77a77ce7ac5a2512ea21e0fdb8fcf6c3d8d"

pbkdf2@^3.0.3:
  version "3.1.2"
  resolved "http://npm-registry.persgroep.digital/pbkdf2/-/pbkdf2-3.1.2.tgz#dd822aa0887580e52f1a039dc3eda108efae3075"
  dependencies:
    create-hash "^1.1.2"
    create-hmac "^1.1.4"
    ripemd160 "^2.0.1"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

pend@~1.2.0:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/pend/-/pend-1.2.0.tgz#7a57eb550a6783f9115331fcf4663d5c8e007a50"

performance-now@^2.1.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/performance-now/-/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"

phantomjs-prebuilt@^2.1.16, phantomjs-prebuilt@^2.1.7:
  version "2.1.16"
  resolved "http://npm-registry.persgroep.digital/phantomjs-prebuilt/-/phantomjs-prebuilt-2.1.16.tgz#efd212a4a3966d3647684ea8ba788549be2aefef"
  dependencies:
    es6-promise "^4.0.3"
    extract-zip "^1.6.5"
    fs-extra "^1.0.0"
    hasha "^2.2.0"
    kew "^0.7.0"
    progress "^1.1.8"
    request "^2.81.0"
    request-progress "^2.0.1"
    which "^1.2.10"

picocolors@^0.2.1:
  version "0.2.1"
  resolved "http://npm-registry.persgroep.digital/picocolors/-/picocolors-0.2.1.tgz#570670f793646851d1ba135996962abad587859f"

picocolors@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/picocolors/-/picocolors-1.0.0.tgz#cb5bdc74ff3f51892236eaf79d68bc44564ab81c"

picomatch@^2.0.4, picomatch@^2.2.1:
  version "2.3.1"
  resolved "http://npm-registry.persgroep.digital/picomatch/-/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"

pify@^2.0.0, pify@^2.2.0, pify@^2.3.0:
  version "2.3.0"
  resolved "http://npm-registry.persgroep.digital/pify/-/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"

pify@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/pify/-/pify-3.0.0.tgz#e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176"

pify@^4.0.0, pify@^4.0.1:
  version "4.0.1"
  resolved "http://npm-registry.persgroep.digital/pify/-/pify-4.0.1.tgz#4b2cd25c50d598735c50292224fd8c6df41e3231"

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/pinkie-promise/-/pinkie-promise-2.0.1.tgz#2135d6dfa7a358c069ac9b178776288228450ffa"
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "http://npm-registry.persgroep.digital/pinkie/-/pinkie-2.0.4.tgz#72556b80cfa0d48a974e80e77248e80ed4f7f870"

pkg-dir@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/pkg-dir/-/pkg-dir-3.0.0.tgz#2749020f239ed990881b1f71210d51eb6523bea3"
  dependencies:
    find-up "^3.0.0"

portfinder@^1.0.9:
  version "1.0.32"
  resolved "http://npm-registry.persgroep.digital/portfinder/-/portfinder-1.0.32.tgz#2fe1b9e58389712429dc2bea5beb2146146c7f81"
  dependencies:
    async "^2.6.4"
    debug "^3.2.7"
    mkdirp "^0.5.6"

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "http://npm-registry.persgroep.digital/posix-character-classes/-/posix-character-classes-0.1.1.tgz#01eac0fe3b5af71a2a6c02feabb8c1fef7e00eab"

postcss-html@^0.36.0:
  version "0.36.0"
  resolved "http://npm-registry.persgroep.digital/postcss-html/-/postcss-html-0.36.0.tgz#b40913f94eaacc2453fd30a1327ad6ee1f88b204"
  dependencies:
    htmlparser2 "^3.10.0"

postcss-import@12.0.1:
  version "12.0.1"
  resolved "http://npm-registry.persgroep.digital/postcss-import/-/postcss-import-12.0.1.tgz#cf8c7ab0b5ccab5649024536e565f841928b7153"
  dependencies:
    postcss "^7.0.1"
    postcss-value-parser "^3.2.3"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-jsx@^0.36.0:
  version "0.36.4"
  resolved "http://npm-registry.persgroep.digital/postcss-jsx/-/postcss-jsx-0.36.4.tgz#37a68f300a39e5748d547f19a747b3257240bd50"
  dependencies:
    "@babel/core" ">=7.2.2"

postcss-less@^3.1.0:
  version "3.1.4"
  resolved "http://npm-registry.persgroep.digital/postcss-less/-/postcss-less-3.1.4.tgz#369f58642b5928ef898ffbc1a6e93c958304c5ad"
  dependencies:
    postcss "^7.0.14"

postcss-load-config@^2.0.0:
  version "2.1.2"
  resolved "http://npm-registry.persgroep.digital/postcss-load-config/-/postcss-load-config-2.1.2.tgz#c5ea504f2c4aef33c7359a34de3573772ad7502a"
  dependencies:
    cosmiconfig "^5.0.0"
    import-cwd "^2.0.0"

postcss-loader@3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/postcss-loader/-/postcss-loader-3.0.0.tgz#6b97943e47c72d845fa9e03f273773d4e8dd6c2d"
  dependencies:
    loader-utils "^1.1.0"
    postcss "^7.0.0"
    postcss-load-config "^2.0.0"
    schema-utils "^1.0.0"

postcss-markdown@^0.36.0:
  version "0.36.0"
  resolved "http://npm-registry.persgroep.digital/postcss-markdown/-/postcss-markdown-0.36.0.tgz#7f22849ae0e3db18820b7b0d5e7833f13a447560"
  dependencies:
    remark "^10.0.1"
    unist-util-find-all-after "^1.0.2"

postcss-media-query-parser@^0.2.3:
  version "0.2.3"
  resolved "http://npm-registry.persgroep.digital/postcss-media-query-parser/-/postcss-media-query-parser-0.2.3.tgz#27b39c6f4d94f81b1a73b8f76351c609e5cef244"

postcss-reporter@^6.0.0:
  version "6.0.1"
  resolved "http://npm-registry.persgroep.digital/postcss-reporter/-/postcss-reporter-6.0.1.tgz#7c055120060a97c8837b4e48215661aafb74245f"
  dependencies:
    chalk "^2.4.1"
    lodash "^4.17.11"
    log-symbols "^2.2.0"
    postcss "^7.0.7"

postcss-resolve-nested-selector@^0.1.1:
  version "0.1.1"
  resolved "http://npm-registry.persgroep.digital/postcss-resolve-nested-selector/-/postcss-resolve-nested-selector-0.1.1.tgz#29ccbc7c37dedfac304e9fff0bf1596b3f6a0e4e"

postcss-safe-parser@^4.0.0:
  version "4.0.2"
  resolved "http://npm-registry.persgroep.digital/postcss-safe-parser/-/postcss-safe-parser-4.0.2.tgz#a6d4e48f0f37d9f7c11b2a581bf00f8ba4870b96"
  dependencies:
    postcss "^7.0.26"

postcss-sass@^0.3.5:
  version "0.3.5"
  resolved "http://npm-registry.persgroep.digital/postcss-sass/-/postcss-sass-0.3.5.tgz#6d3e39f101a53d2efa091f953493116d32beb68c"
  dependencies:
    gonzales-pe "^4.2.3"
    postcss "^7.0.1"

postcss-scss@^2.0.0:
  version "2.1.1"
  resolved "http://npm-registry.persgroep.digital/postcss-scss/-/postcss-scss-2.1.1.tgz#ec3a75fa29a55e016b90bf3269026c53c1d2b383"
  dependencies:
    postcss "^7.0.6"

postcss-selector-parser@^3.1.0:
  version "3.1.2"
  resolved "http://npm-registry.persgroep.digital/postcss-selector-parser/-/postcss-selector-parser-3.1.2.tgz#b310f5c4c0fdaf76f94902bbaa30db6aa84f5270"
  dependencies:
    dot-prop "^5.2.0"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-sorting@^4.1.0:
  version "4.1.0"
  resolved "http://npm-registry.persgroep.digital/postcss-sorting/-/postcss-sorting-4.1.0.tgz#a107f0bf3852977fa64e4442bc340c88d5aacdb3"
  dependencies:
    lodash "^4.17.4"
    postcss "^7.0.0"

postcss-syntax@^0.36.2:
  version "0.36.2"
  resolved "http://npm-registry.persgroep.digital/postcss-syntax/-/postcss-syntax-0.36.2.tgz#f08578c7d95834574e5593a82dfbfa8afae3b51c"

postcss-value-parser@^3.2.3, postcss-value-parser@^3.3.0, postcss-value-parser@^3.3.1:
  version "3.3.1"
  resolved "http://npm-registry.persgroep.digital/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz#9ff822547e2893213cf1c30efa51ac5fd1ba8281"

postcss-value-parser@^4.1.0:
  version "4.2.0"
  resolved "http://npm-registry.persgroep.digital/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz#723c09920836ba6d3e5af019f92bc0971c02e514"

postcss@7.0.14:
  version "7.0.14"
  resolved "http://npm-registry.persgroep.digital/postcss/-/postcss-7.0.14.tgz#4527ed6b1ca0d82c53ce5ec1a2041c2346bbd6e5"
  dependencies:
    chalk "^2.4.2"
    source-map "^0.6.1"
    supports-color "^6.1.0"

postcss@^7.0.0, postcss@^7.0.1, postcss@^7.0.13, postcss@^7.0.14, postcss@^7.0.2, postcss@^7.0.26, postcss@^7.0.32, postcss@^7.0.6, postcss@^7.0.7:
  version "7.0.39"
  resolved "http://npm-registry.persgroep.digital/postcss/-/postcss-7.0.39.tgz#9624375d965630e2e1f2c02a935c82a59cb48309"
  dependencies:
    picocolors "^0.2.1"
    source-map "^0.6.1"

preserve@^0.2.0:
  version "0.2.0"
  resolved "http://npm-registry.persgroep.digital/preserve/-/preserve-0.2.0.tgz#815ed1f6ebc65926f865b310c0713bcb3315ce4b"

prettier@^1.16.4:
  version "1.19.1"
  resolved "http://npm-registry.persgroep.digital/prettier/-/prettier-1.19.1.tgz#f7d7f5ff8a9cd872a7be4ca142095956a60797cb"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/process-nextick-args/-/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"

process@^0.11.10, process@~0.11.0:
  version "0.11.10"
  resolved "http://npm-registry.persgroep.digital/process/-/process-0.11.10.tgz#7332300e840161bda3e69a1d1d91a7d4bc16f182"

progress@^1.1.8:
  version "1.1.8"
  resolved "http://npm-registry.persgroep.digital/progress/-/progress-1.1.8.tgz#e260c78f6161cdd9b0e56cc3e0a85de17c7a57be"

progress@^2.0.0:
  version "2.0.3"
  resolved "http://npm-registry.persgroep.digital/progress/-/progress-2.0.3.tgz#7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8"

promise-inflight@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/promise-inflight/-/promise-inflight-1.0.1.tgz#98472870bf228132fcbdd868129bad12c3c029e3"

promise-retry@^1.1.1:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/promise-retry/-/promise-retry-1.1.1.tgz#6739e968e3051da20ce6497fb2b50f6911df3d6d"
  dependencies:
    err-code "^1.0.0"
    retry "^0.10.0"

promise@^7.1.1:
  version "7.3.1"
  resolved "http://npm-registry.persgroep.digital/promise/-/promise-7.3.1.tgz#064b72602b18f90f29192b8b1bc418ffd1ebd3bf"
  dependencies:
    asap "~2.0.3"

protoduck@^5.0.1:
  version "5.0.1"
  resolved "http://npm-registry.persgroep.digital/protoduck/-/protoduck-5.0.1.tgz#03c3659ca18007b69a50fd82a7ebcc516261151f"
  dependencies:
    genfun "^5.0.0"

proxy-addr@~2.0.7:
  version "2.0.7"
  resolved "http://npm-registry.persgroep.digital/proxy-addr/-/proxy-addr-2.0.7.tgz#f19fe69ceab311eeb94b42e70e8c2070f9ba1025"
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

prr@~1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/prr/-/prr-1.0.1.tgz#d3fc114ba06995a45ec6893f484ceb1d78f5f476"

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/pseudomap/-/pseudomap-1.0.2.tgz#f052a28da70e618917ef0a8ac34c1ae5a68286b3"

psl@^1.1.24, psl@^1.1.28:
  version "1.9.0"
  resolved "http://npm-registry.persgroep.digital/psl/-/psl-1.9.0.tgz#d0df2a137f00794565fcaf3b2c00cd09f8d5a5a7"

public-encrypt@^4.0.0:
  version "4.0.3"
  resolved "http://npm-registry.persgroep.digital/public-encrypt/-/public-encrypt-4.0.3.tgz#4fcc9d77a07e48ba7527e7cbe0de33d0701331e0"
  dependencies:
    bn.js "^4.1.0"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    parse-asn1 "^5.0.0"
    randombytes "^2.0.1"
    safe-buffer "^5.1.2"

pump@^2.0.0:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/pump/-/pump-2.0.1.tgz#12399add6e4cf7526d973cbc8b5ce2e2908b3909"
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pump@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/pump/-/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64"
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pumpify@^1.3.3:
  version "1.5.1"
  resolved "http://npm-registry.persgroep.digital/pumpify/-/pumpify-1.5.1.tgz#36513be246ab27570b1a374a5ce278bfd74370ce"
  dependencies:
    duplexify "^3.6.0"
    inherits "^2.0.3"
    pump "^2.0.0"

punycode@1.3.2:
  version "1.3.2"
  resolved "http://npm-registry.persgroep.digital/punycode/-/punycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"

punycode@^1.2.4, punycode@^1.3.2, punycode@^1.4.1:
  version "1.4.1"
  resolved "http://npm-registry.persgroep.digital/punycode/-/punycode-1.4.1.tgz#c0d5a63b2718800ad8e1eb0fa5269c84dd41845e"

punycode@^2.1.0, punycode@^2.1.1:
  version "2.3.0"
  resolved "http://npm-registry.persgroep.digital/punycode/-/punycode-2.3.0.tgz#f67fa67c94da8f4d0cfff981aee4118064199b8f"

qjobs@^1.1.4:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/qjobs/-/qjobs-1.2.0.tgz#c45e9c61800bd087ef88d7e256423bdd49e5d071"

qs@6.11.0:
  version "6.11.0"
  resolved "http://npm-registry.persgroep.digital/qs/-/qs-6.11.0.tgz#fd0d963446f7a65e1367e01abd85429453f0c37a"
  dependencies:
    side-channel "^1.0.4"

qs@~1.2.2:
  version "1.2.2"
  resolved "http://npm-registry.persgroep.digital/qs/-/qs-1.2.2.tgz#19b57ff24dc2a99ce1f8bdf6afcda59f8ef61f88"

qs@~6.5.2:
  version "6.5.3"
  resolved "http://npm-registry.persgroep.digital/qs/-/qs-6.5.3.tgz#3aeeffc91967ef6e35c0e488ef46fb296ab76aad"

querystring-es3@^0.2.0, querystring-es3@~0.2.0:
  version "0.2.1"
  resolved "http://npm-registry.persgroep.digital/querystring-es3/-/querystring-es3-0.2.1.tgz#9ec61f79049875707d69414596fd907a4d711e73"

querystring@0.2.0:
  version "0.2.0"
  resolved "http://npm-registry.persgroep.digital/querystring/-/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"

querystringify@^2.1.1:
  version "2.2.0"
  resolved "http://npm-registry.persgroep.digital/querystringify/-/querystringify-2.2.0.tgz#3345941b4153cb9d082d8eee4cda2016a9aef7f6"

quick-lru@^1.0.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/quick-lru/-/quick-lru-1.1.0.tgz#4360b17c61136ad38078397ff11416e186dcfbb8"

raf@^3.4.1:
  version "3.4.1"
  resolved "http://npm-registry.persgroep.digital/raf/-/raf-3.4.1.tgz#0742e99a4a6552f445d73e3ee0328af0ff1ede39"
  dependencies:
    performance-now "^2.1.0"

ramda@0.24.1:
  version "0.24.1"
  resolved "http://npm-registry.persgroep.digital/ramda/-/ramda-0.24.1.tgz#c3b7755197f35b8dc3502228262c4c91ddb6b857"

randomatic@^3.0.0:
  version "3.1.1"
  resolved "http://npm-registry.persgroep.digital/randomatic/-/randomatic-3.1.1.tgz#b776efc59375984e36c537b2f51a1f0aff0da1ed"
  dependencies:
    is-number "^4.0.0"
    kind-of "^6.0.0"
    math-random "^1.0.1"

randombytes@^2.0.0, randombytes@^2.0.1, randombytes@^2.0.5, randombytes@^2.1.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/randombytes/-/randombytes-2.1.0.tgz#df6f84372f0270dc65cdf6291349ab7a473d4f2a"
  dependencies:
    safe-buffer "^5.1.0"

randomfill@^1.0.3:
  version "1.0.4"
  resolved "http://npm-registry.persgroep.digital/randomfill/-/randomfill-1.0.4.tgz#c92196fc86ab42be983f1bf31778224931d61458"
  dependencies:
    randombytes "^2.0.5"
    safe-buffer "^5.1.0"

range-parser@^1.0.3, range-parser@^1.2.0, range-parser@~1.2.1:
  version "1.2.1"
  resolved "http://npm-registry.persgroep.digital/range-parser/-/range-parser-1.2.1.tgz#3cf37023d199e1c24d1a55b84800c2f3e6468031"

raven-js@^3.27.0:
  version "3.27.2"
  resolved "http://npm-registry.persgroep.digital/raven-js/-/raven-js-3.27.2.tgz#6c33df952026cd73820aa999122b7b7737a66775"

raw-body@2.5.1:
  version "2.5.1"
  resolved "http://npm-registry.persgroep.digital/raw-body/-/raw-body-2.5.1.tgz#fe1b1628b181b700215e5fd42389f98b71392857"
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

raw-body@2.5.2:
  version "2.5.2"
  resolved "http://npm-registry.persgroep.digital/raw-body/-/raw-body-2.5.2.tgz#99febd83b90e08975087e8f1f9419a149366b68a"
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

raw-loader@1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/raw-loader/-/raw-loader-1.0.0.tgz#3f9889e73dadbda9a424bce79809b4133ad46405"
  dependencies:
    loader-utils "^1.1.0"
    schema-utils "^1.0.0"

read-cache@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/read-cache/-/read-cache-1.0.0.tgz#e664ef31161166c9751cdbe8dbcf86b5fb58f774"
  dependencies:
    pify "^2.3.0"

read-only-stream@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/read-only-stream/-/read-only-stream-2.0.0.tgz#2724fd6a8113d73764ac288d4386270c1dbf17f0"
  dependencies:
    readable-stream "^2.0.2"

read-pkg-up@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/read-pkg-up/-/read-pkg-up-1.0.1.tgz#9d63c13276c065918d57f002a57f40a1b643fb02"
  dependencies:
    find-up "^1.0.0"
    read-pkg "^1.0.0"

read-pkg-up@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/read-pkg-up/-/read-pkg-up-3.0.0.tgz#3ed496685dba0f8fe118d0691dc51f4a1ff96f07"
  dependencies:
    find-up "^2.0.0"
    read-pkg "^3.0.0"

read-pkg@^1.0.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/read-pkg/-/read-pkg-1.1.0.tgz#f5ffaa5ecd29cb31c0474bca7d756b6bb29e3f28"
  dependencies:
    load-json-file "^1.0.0"
    normalize-package-data "^2.3.2"
    path-type "^1.0.0"

read-pkg@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/read-pkg/-/read-pkg-3.0.0.tgz#9cbc686978fee65d16c00e2b19c237fcf6e38389"
  dependencies:
    load-json-file "^4.0.0"
    normalize-package-data "^2.3.2"
    path-type "^3.0.0"

"readable-stream@1 || 2", readable-stream@^2.0.0, readable-stream@^2.0.1, readable-stream@^2.0.2, readable-stream@^2.0.6, readable-stream@^2.1.5, readable-stream@^2.2.2, readable-stream@^2.3.3, readable-stream@^2.3.6, readable-stream@~2.3.6:
  version "2.3.8"
  resolved "http://npm-registry.persgroep.digital/readable-stream/-/readable-stream-2.3.8.tgz#91125e8042bba1b9887f49345f6277027ce8be9b"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@1.1.x:
  version "1.1.14"
  resolved "http://npm-registry.persgroep.digital/readable-stream/-/readable-stream-1.1.14.tgz#7cf4c54ef648e3813084c636dd2079e166c081d9"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readable-stream@^3.0.6, readable-stream@^3.1.1, readable-stream@^3.6.0:
  version "3.6.2"
  resolved "http://npm-registry.persgroep.digital/readable-stream/-/readable-stream-3.6.2.tgz#56a9b36ea965c00c5a93ef31eb111a0f11056967"
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@^2.0.0, readdirp@^2.2.1:
  version "2.2.1"
  resolved "http://npm-registry.persgroep.digital/readdirp/-/readdirp-2.2.1.tgz#0e87622a3325aa33e892285caf8b4e846529a525"
  dependencies:
    graceful-fs "^4.1.11"
    micromatch "^3.1.10"
    readable-stream "^2.0.2"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "http://npm-registry.persgroep.digital/readdirp/-/readdirp-3.6.0.tgz#74a370bd857116e245b29cc97340cd431a02a6c7"
  dependencies:
    picomatch "^2.2.1"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "http://npm-registry.persgroep.digital/rechoir/-/rechoir-0.6.2.tgz#85204b54dba82d5742e28c96756ef43af50e3384"
  dependencies:
    resolve "^1.1.6"

redent@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/redent/-/redent-1.0.0.tgz#cf916ab1fd5f1f16dfb20822dd6ec7f730c2afde"
  dependencies:
    indent-string "^2.1.0"
    strip-indent "^1.0.1"

redent@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/redent/-/redent-2.0.0.tgz#c1b2007b42d57eb1389079b3c8333639d5e1ccaa"
  dependencies:
    indent-string "^3.0.0"
    strip-indent "^2.0.0"

reflect-metadata@^0.1.2:
  version "0.1.13"
  resolved "http://npm-registry.persgroep.digital/reflect-metadata/-/reflect-metadata-0.1.13.tgz#67ae3ca57c972a2aa1642b10fe363fe32d49dc08"

regenerate-unicode-properties@^10.1.0:
  version "10.1.0"
  resolved "http://npm-registry.persgroep.digital/regenerate-unicode-properties/-/regenerate-unicode-properties-10.1.0.tgz#7c3192cab6dd24e21cb4461e5ddd7dd24fa8374c"
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "http://npm-registry.persgroep.digital/regenerate/-/regenerate-1.4.2.tgz#b9346d8827e8f5a32f7ba29637d398b69014848a"

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "http://npm-registry.persgroep.digital/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz#be05ad7f9bf7d22e056f9726cee5017fbf19e2e9"

regenerator-runtime@^0.12.0:
  version "0.12.1"
  resolved "http://npm-registry.persgroep.digital/regenerator-runtime/-/regenerator-runtime-0.12.1.tgz#fa1a71544764c036f8c49b13a08b2594c9f8a0de"

regenerator-runtime@^0.13.11, regenerator-runtime@^0.13.4, regenerator-runtime@^0.13.7:
  version "0.13.11"
  resolved "http://npm-registry.persgroep.digital/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz#f6dca3e7ceec20590d07ada785636a90cdca17f9"

regenerator-transform@^0.15.1:
  version "0.15.1"
  resolved "http://npm-registry.persgroep.digital/regenerator-transform/-/regenerator-transform-0.15.1.tgz#f6c4e99fc1b4591f780db2586328e4d9a9d8dc56"
  dependencies:
    "@babel/runtime" "^7.8.4"

regex-cache@^0.4.2:
  version "0.4.4"
  resolved "http://npm-registry.persgroep.digital/regex-cache/-/regex-cache-0.4.4.tgz#75bdc58a2a1496cec48a12835bc54c8d562336dd"
  dependencies:
    is-equal-shallow "^0.1.3"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/regex-not/-/regex-not-1.0.2.tgz#1f4ece27e00b0b65e0247a6810e6a85d83a5752c"
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexp.prototype.flags@^1.2.0:
  version "1.5.0"
  resolved "http://npm-registry.persgroep.digital/regexp.prototype.flags/-/regexp.prototype.flags-1.5.0.tgz#fe7ce25e7e4cca8db37b6634c8a2c7009199b9cb"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    functions-have-names "^1.2.3"

regexpu-core@^5.3.1:
  version "5.3.2"
  resolved "http://npm-registry.persgroep.digital/regexpu-core/-/regexpu-core-5.3.2.tgz#11a2b06884f3527aec3e93dbbf4a3b958a95546b"
  dependencies:
    "@babel/regjsgen" "^0.8.0"
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.1.0"
    regjsparser "^0.9.1"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.1.0"

regjsparser@^0.9.1:
  version "0.9.1"
  resolved "http://npm-registry.persgroep.digital/regjsparser/-/regjsparser-0.9.1.tgz#272d05aa10c7c1f67095b1ff0addae8442fc5709"
  dependencies:
    jsesc "~0.5.0"

remark-parse@^6.0.0:
  version "6.0.3"
  resolved "http://npm-registry.persgroep.digital/remark-parse/-/remark-parse-6.0.3.tgz#c99131052809da482108413f87b0ee7f52180a3a"
  dependencies:
    collapse-white-space "^1.0.2"
    is-alphabetical "^1.0.0"
    is-decimal "^1.0.0"
    is-whitespace-character "^1.0.0"
    is-word-character "^1.0.0"
    markdown-escapes "^1.0.0"
    parse-entities "^1.1.0"
    repeat-string "^1.5.4"
    state-toggle "^1.0.0"
    trim "0.0.1"
    trim-trailing-lines "^1.0.0"
    unherit "^1.0.4"
    unist-util-remove-position "^1.0.0"
    vfile-location "^2.0.0"
    xtend "^4.0.1"

remark-stringify@^6.0.0:
  version "6.0.4"
  resolved "http://npm-registry.persgroep.digital/remark-stringify/-/remark-stringify-6.0.4.tgz#16ac229d4d1593249018663c7bddf28aafc4e088"
  dependencies:
    ccount "^1.0.0"
    is-alphanumeric "^1.0.0"
    is-decimal "^1.0.0"
    is-whitespace-character "^1.0.0"
    longest-streak "^2.0.1"
    markdown-escapes "^1.0.0"
    markdown-table "^1.1.0"
    mdast-util-compact "^1.0.0"
    parse-entities "^1.0.2"
    repeat-string "^1.5.4"
    state-toggle "^1.0.0"
    stringify-entities "^1.0.1"
    unherit "^1.0.4"
    xtend "^4.0.1"

remark@^10.0.1:
  version "10.0.1"
  resolved "http://npm-registry.persgroep.digital/remark/-/remark-10.0.1.tgz#3058076dc41781bf505d8978c291485fe47667df"
  dependencies:
    remark-parse "^6.0.0"
    remark-stringify "^6.0.0"
    unified "^7.0.0"

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz#c24bce2a283adad5bc3f58e0d48249b92379d8ef"

repeat-element@^1.1.2:
  version "1.1.4"
  resolved "http://npm-registry.persgroep.digital/repeat-element/-/repeat-element-1.1.4.tgz#be681520847ab58c7568ac75fbfad28ed42d39e9"

repeat-string@^1.5.2, repeat-string@^1.5.4, repeat-string@^1.6.1:
  version "1.6.1"
  resolved "http://npm-registry.persgroep.digital/repeat-string/-/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"

repeating@^2.0.0:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/repeating/-/repeating-2.0.1.tgz#5214c53a926d3552707527fbab415dbc08d06dda"
  dependencies:
    is-finite "^1.0.0"

replace-ext@1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/replace-ext/-/replace-ext-1.0.0.tgz#de63128373fcbf7c3ccfa4de5a480c45a67958eb"

request-progress@0.4.0:
  version "0.4.0"
  resolved "http://npm-registry.persgroep.digital/request-progress/-/request-progress-0.4.0.tgz#c1954e39086aa85269c5660bcee0142a6a70d7e7"
  dependencies:
    node-eta "^0.1.1"
    throttleit "^0.0.2"

request-progress@^2.0.1:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/request-progress/-/request-progress-2.0.1.tgz#5d36bb57961c673aa5b788dbc8141fdf23b44e08"
  dependencies:
    throttleit "^1.0.0"

request@2.88.0:
  version "2.88.0"
  resolved "http://npm-registry.persgroep.digital/request/-/request-2.88.0.tgz#9c2fca4f7d35b592efe57c7f0a55e81052124fef"
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.0"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.4.3"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

request@^2.81.0, request@^2.83.0, request@^2.87.0, request@^2.88.0:
  version "2.88.2"
  resolved "http://npm-registry.persgroep.digital/request/-/request-2.88.2.tgz#d73c918731cb5a87da047e207234146f664d12b3"
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "http://npm-registry.persgroep.digital/require-directory/-/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"

require-from-string@^2.0.1:
  version "2.0.2"
  resolved "http://npm-registry.persgroep.digital/require-from-string/-/require-from-string-2.0.2.tgz#89a7fdd938261267318eafe14f9c32e598c36909"

require-main-filename@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/require-main-filename/-/require-main-filename-1.0.1.tgz#97f717b69d48784f5f526a6c5aa8ffdda055a4d1"

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/require-main-filename/-/require-main-filename-2.0.0.tgz#d0b329ecc7cc0f61649f62215be69af54aa8989b"

requires-port@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/requires-port/-/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"

resolve-cwd@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/resolve-cwd/-/resolve-cwd-2.0.0.tgz#00a9f7387556e27038eae232caa372a6a59b665a"
  dependencies:
    resolve-from "^3.0.0"

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/resolve-from/-/resolve-from-3.0.0.tgz#b22c7af7d9d6881bc8b6e653335eebcb0a188748"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://npm-registry.persgroep.digital/resolve-from/-/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "http://npm-registry.persgroep.digital/resolve-url/-/resolve-url-0.2.1.tgz#2c637fe77c893afd2a663fe21aa9080068e2052a"

resolve@1.1.7:
  version "1.1.7"
  resolved "http://npm-registry.persgroep.digital/resolve/-/resolve-1.1.7.tgz#203114d82ad2c5ed9e8e0411b3932875e889e97b"

resolve@^1.1.4, resolve@^1.1.6, resolve@^1.1.7, resolve@^1.10.0, resolve@^1.17.0, resolve@^1.3.2, resolve@^1.3.3, resolve@^1.4.0, resolve@^1.8.1:
  version "1.22.2"
  resolved "http://npm-registry.persgroep.digital/resolve/-/resolve-1.22.2.tgz#0ed0943d4e301867955766c9f3e1ae6d01c6845f"
  dependencies:
    is-core-module "^2.11.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/restore-cursor/-/restore-cursor-1.0.1.tgz#34661f46886327fed2991479152252df92daa541"
  dependencies:
    exit-hook "^1.0.0"
    onetime "^1.0.0"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/restore-cursor/-/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "http://npm-registry.persgroep.digital/ret/-/ret-0.1.15.tgz#b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc"

retry@^0.10.0:
  version "0.10.1"
  resolved "http://npm-registry.persgroep.digital/retry/-/retry-0.10.1.tgz#e76388d217992c252750241d3d3956fed98d8ff4"

rfdc@^1.1.4:
  version "1.3.0"
  resolved "http://npm-registry.persgroep.digital/rfdc/-/rfdc-1.3.0.tgz#d0b7c441ab2720d05dc4cf26e01c89631d9da08b"

rgbcolor@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/rgbcolor/-/rgbcolor-1.0.1.tgz#d6505ecdb304a6595da26fa4b43307306775945d"

rimraf@2, rimraf@^2.2.8, rimraf@^2.5.4, rimraf@^2.6.0, rimraf@^2.6.2, rimraf@^2.6.3:
  version "2.7.1"
  resolved "http://npm-registry.persgroep.digital/rimraf/-/rimraf-2.7.1.tgz#35797f13a7fdadc566142c29d4f07ccad483e3ec"
  dependencies:
    glob "^7.1.3"

rimraf@2.6.3:
  version "2.6.3"
  resolved "http://npm-registry.persgroep.digital/rimraf/-/rimraf-2.6.3.tgz#b2d104fe0d8fb27cf9e0a1cda8262dd3833c6cab"
  dependencies:
    glob "^7.1.3"

ripemd160@^2.0.0, ripemd160@^2.0.1:
  version "2.0.2"
  resolved "http://npm-registry.persgroep.digital/ripemd160/-/ripemd160-2.0.2.tgz#a1c1a6f624751577ba5d07914cbc92850585890c"
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"

run-async@^2.2.0:
  version "2.4.1"
  resolved "http://npm-registry.persgroep.digital/run-async/-/run-async-2.4.1.tgz#8440eccf99ea3e70bd409d49aab88e10c189a455"

run-queue@^1.0.0, run-queue@^1.0.3:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/run-queue/-/run-queue-1.0.3.tgz#e848396f057d223f24386924618e25694161ec47"
  dependencies:
    aproba "^1.1.1"

rxjs@6.3.3:
  version "6.3.3"
  resolved "http://npm-registry.persgroep.digital/rxjs/-/rxjs-6.3.3.tgz#3c6a7fa420e844a81390fb1158a9ec614f4bad55"
  dependencies:
    tslib "^1.9.0"

rxjs@^5.0.0-beta.11:
  version "5.5.12"
  resolved "http://npm-registry.persgroep.digital/rxjs/-/rxjs-5.5.12.tgz#6fa61b8a77c3d793dbaf270bee2f43f652d741cc"
  dependencies:
    symbol-observable "1.0.1"

rxjs@^6.1.0, rxjs@^6.4.0:
  version "6.6.7"
  resolved "http://npm-registry.persgroep.digital/rxjs/-/rxjs-6.6.7.tgz#90ac018acabf491bf65044235d5863c4dab804c9"
  dependencies:
    tslib "^1.9.0"

safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://npm-registry.persgroep.digital/safe-buffer/-/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"

safe-buffer@5.2.1, safe-buffer@>=5.1.0, safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@^5.1.2, safe-buffer@^5.2.0, safe-buffer@^5.2.1, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "http://npm-registry.persgroep.digital/safe-buffer/-/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/safe-regex/-/safe-regex-1.1.0.tgz#40a3669f3b077d1e943d44629e157dd48023bf2e"
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "http://npm-registry.persgroep.digital/safer-buffer/-/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"

sass-graph@^2.2.4:
  version "2.2.6"
  resolved "http://npm-registry.persgroep.digital/sass-graph/-/sass-graph-2.2.6.tgz#09fda0e4287480e3e4967b72a2d133ba09b8d827"
  dependencies:
    glob "^7.0.0"
    lodash "^4.0.0"
    scss-tokenizer "^0.2.3"
    yargs "^7.0.0"

sass-loader@7.1.0:
  version "7.1.0"
  resolved "http://npm-registry.persgroep.digital/sass-loader/-/sass-loader-7.1.0.tgz#16fd5138cb8b424bf8a759528a1972d72aad069d"
  dependencies:
    clone-deep "^2.0.1"
    loader-utils "^1.0.1"
    lodash.tail "^4.1.1"
    neo-async "^2.5.0"
    pify "^3.0.0"
    semver "^5.5.0"

sax@0.5.x:
  version "0.5.8"
  resolved "http://npm-registry.persgroep.digital/sax/-/sax-0.5.8.tgz#d472db228eb331c2506b0e8c15524adb939d12c1"

schema-utils@^0.3.0:
  version "0.3.0"
  resolved "http://npm-registry.persgroep.digital/schema-utils/-/schema-utils-0.3.0.tgz#f5877222ce3e931edae039f17eb3716e7137f8cf"
  dependencies:
    ajv "^5.0.0"

schema-utils@^0.4.4:
  version "0.4.7"
  resolved "http://npm-registry.persgroep.digital/schema-utils/-/schema-utils-0.4.7.tgz#ba74f597d2be2ea880131746ee17d0a093c68187"
  dependencies:
    ajv "^6.1.0"
    ajv-keywords "^3.1.0"

schema-utils@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/schema-utils/-/schema-utils-1.0.0.tgz#0b79a93204d7b600d4b2850d1f66c2a34951c770"
  dependencies:
    ajv "^6.1.0"
    ajv-errors "^1.0.0"
    ajv-keywords "^3.1.0"

scss-tokenizer@^0.2.3:
  version "0.2.3"
  resolved "http://npm-registry.persgroep.digital/scss-tokenizer/-/scss-tokenizer-0.2.3.tgz#8eb06db9a9723333824d3f5530641149847ce5d1"
  dependencies:
    js-base64 "^2.1.8"
    source-map "^0.4.2"

seed-random@~2.2.0:
  version "2.2.0"
  resolved "http://npm-registry.persgroep.digital/seed-random/-/seed-random-2.2.0.tgz#2a9b19e250a817099231a5b99a4daf80b7fbed54"

select-hose@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/select-hose/-/select-hose-2.0.0.tgz#625d8658f865af43ec962bfc376a37359a4994ca"

selfsigned@^1.9.1:
  version "1.10.14"
  resolved "http://npm-registry.persgroep.digital/selfsigned/-/selfsigned-1.10.14.tgz#ee51d84d9dcecc61e07e4aba34f229ab525c1574"
  dependencies:
    node-forge "^0.10.0"

semver-dsl@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/semver-dsl/-/semver-dsl-1.0.1.tgz#d3678de5555e8a61f629eed025366ae5f27340a0"
  dependencies:
    semver "^5.3.0"

semver-intersect@1.4.0:
  version "1.4.0"
  resolved "http://npm-registry.persgroep.digital/semver-intersect/-/semver-intersect-1.4.0.tgz#bdd9c06bedcdd2fedb8cd352c3c43ee8c61321f3"
  dependencies:
    semver "^5.0.0"

"semver@2 || 3 || 4 || 5", semver@^5.0.0, semver@^5.3.0, semver@^5.4.1, semver@^5.5.0, semver@^5.5.1, semver@^5.6.0:
  version "5.7.1"
  resolved "http://npm-registry.persgroep.digital/semver/-/semver-5.7.1.tgz#a954f931aeba508d307bbf069eff0c01c96116f7"

semver@5.6.0:
  version "5.6.0"
  resolved "http://npm-registry.persgroep.digital/semver/-/semver-5.6.0.tgz#7e74256fbaa49c75aa7c7a205cc22799cac80004"

semver@^6.0.0, semver@^6.3.0:
  version "6.3.0"
  resolved "http://npm-registry.persgroep.digital/semver/-/semver-6.3.0.tgz#ee0a64c8af5e8ceea67687b133761e1becbd1d3d"

semver@~5.3.0:
  version "5.3.0"
  resolved "http://npm-registry.persgroep.digital/semver/-/semver-5.3.0.tgz#9b2ce5d3de02d17c6012ad326aa6b4d0cf54f94f"

send@0.18.0:
  version "0.18.0"
  resolved "http://npm-registry.persgroep.digital/send/-/send-0.18.0.tgz#670167cc654b05f5aa4a767f9113bb371bc706be"
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

serialize-error@^2.1.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/serialize-error/-/serialize-error-2.1.0.tgz#50b679d5635cdf84667bdc8e59af4e5b81d5f60a"

serialize-error@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/serialize-error/-/serialize-error-3.0.0.tgz#80100282b09be33c611536f50033481cb9cc87cf"

serialize-javascript@^2.1.2:
  version "2.1.2"
  resolved "http://npm-registry.persgroep.digital/serialize-javascript/-/serialize-javascript-2.1.2.tgz#ecec53b0e0317bdc95ef76ab7074b7384785fa61"

serialize-javascript@^4.0.0:
  version "4.0.0"
  resolved "http://npm-registry.persgroep.digital/serialize-javascript/-/serialize-javascript-4.0.0.tgz#b525e1238489a5ecfc42afacc3fe99e666f4b1aa"
  dependencies:
    randombytes "^2.1.0"

serve-index@^1.7.2:
  version "1.9.1"
  resolved "http://npm-registry.persgroep.digital/serve-index/-/serve-index-1.9.1.tgz#d3768d69b1e7d82e5ce050fff5b453bea12a9239"
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.15.0:
  version "1.15.0"
  resolved "http://npm-registry.persgroep.digital/serve-static/-/serve-static-1.15.0.tgz#faaef08cffe0a1a62f60cad0c4e513cff0ac9540"
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.18.0"

set-blocking@^2.0.0, set-blocking@~2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/set-blocking/-/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/set-value/-/set-value-2.0.1.tgz#a18d40530e6f07de4228c7defe4227af8cad005b"
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setimmediate@^1.0.4:
  version "1.0.5"
  resolved "http://npm-registry.persgroep.digital/setimmediate/-/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285"

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/setprototypeof/-/setprototypeof-1.1.0.tgz#d0bd85536887b6fe7c0d818cb962d9d91c54e656"

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/setprototypeof/-/setprototypeof-1.2.0.tgz#66c9a24a73f9fc28cbe66b09fed3d33dcaf1b424"

sha.js@^2.4.0, sha.js@^2.4.8, sha.js@~2.4.4:
  version "2.4.11"
  resolved "http://npm-registry.persgroep.digital/sha.js/-/sha.js-2.4.11.tgz#37a5cf0b81ecbc6943de109ba2960d1b26584ae7"
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shallow-clone@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/shallow-clone/-/shallow-clone-1.0.0.tgz#4480cd06e882ef68b2ad88a3ea54832e2c48b571"
  dependencies:
    is-extendable "^0.1.1"
    kind-of "^5.0.0"
    mixin-object "^2.0.1"

shasum-object@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/shasum-object/-/shasum-object-1.0.0.tgz#0b7b74ff5b66ecf9035475522fa05090ac47e29e"
  dependencies:
    fast-safe-stringify "^2.0.7"

shasum@^1.0.0:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/shasum/-/shasum-1.0.2.tgz#e7012310d8f417f4deb5712150e5678b87ae565f"
  dependencies:
    json-stable-stringify "~0.0.0"
    sha.js "~2.4.4"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/shebang-command/-/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
  dependencies:
    shebang-regex "^1.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/shebang-regex/-/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"

shell-quote@^1.4.2, shell-quote@^1.6.1:
  version "1.8.1"
  resolved "http://npm-registry.persgroep.digital/shell-quote/-/shell-quote-1.8.1.tgz#6dbf4db75515ad5bac63b4f1894c3a154c766680"

shelljs@^0.8.1:
  version "0.8.5"
  resolved "http://npm-registry.persgroep.digital/shelljs/-/shelljs-0.8.5.tgz#de055408d8361bed66c669d2f000538ced8ee20c"
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

side-channel@^1.0.4:
  version "1.0.4"
  resolved "http://npm-registry.persgroep.digital/side-channel/-/side-channel-1.0.4.tgz#efce5c8fdc104ee751b25c58d4290011fa5ea2cf"
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.7"
  resolved "http://npm-registry.persgroep.digital/signal-exit/-/signal-exit-3.0.7.tgz#a9a1767f8af84155114eaabd73f99273c8f59ad9"

simple-concat@^1.0.0:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/simple-concat/-/simple-concat-1.0.1.tgz#f46976082ba35c2263f1c8ab5edfe26c41c9552f"

slash@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/slash/-/slash-1.0.0.tgz#c41f2f6c39fc16d1cd17ad4b5d896114ae470d55"

slash@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/slash/-/slash-2.0.0.tgz#de552851a1759df3a8f206535442f5ec4ddeab44"

slice-ansi@0.0.4:
  version "0.0.4"
  resolved "http://npm-registry.persgroep.digital/slice-ansi/-/slice-ansi-0.0.4.tgz#edbf8903f66f7ce2f8eafd6ceed65e264c831b35"

slice-ansi@^2.1.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/slice-ansi/-/slice-ansi-2.1.0.tgz#cacd7693461a637a5788d92a7dd4fba068e81636"
  dependencies:
    ansi-styles "^3.2.0"
    astral-regex "^1.0.0"
    is-fullwidth-code-point "^2.0.0"

smart-buffer@^4.1.0:
  version "4.2.0"
  resolved "http://npm-registry.persgroep.digital/smart-buffer/-/smart-buffer-4.2.0.tgz#6e1d71fa4f18c05f7d0ff216dd16a481d0e8d9ae"

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "http://npm-registry.persgroep.digital/snapdragon-node/-/snapdragon-node-2.1.1.tgz#6c175f86ff14bdb0724563e8f3c1b021a286853b"
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "http://npm-registry.persgroep.digital/snapdragon-util/-/snapdragon-util-3.0.1.tgz#f956479486f2acd79700693f6f7b805e45ab56e2"
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "http://npm-registry.persgroep.digital/snapdragon/-/snapdragon-0.8.2.tgz#64922e7c565b0e14204ba1aa7d6964278d25182d"
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

socket.io-adapter@~1.1.0:
  version "1.1.2"
  resolved "http://npm-registry.persgroep.digital/socket.io-adapter/-/socket.io-adapter-1.1.2.tgz#ab3f0d6f66b8fc7fca3959ab5991f82221789be9"

socket.io-client@2.1.1:
  version "2.1.1"
  resolved "http://npm-registry.persgroep.digital/socket.io-client/-/socket.io-client-2.1.1.tgz#dcb38103436ab4578ddb026638ae2f21b623671f"
  dependencies:
    backo2 "1.0.2"
    base64-arraybuffer "0.1.5"
    component-bind "1.0.0"
    component-emitter "1.2.1"
    debug "~3.1.0"
    engine.io-client "~3.2.0"
    has-binary2 "~1.0.2"
    has-cors "1.1.0"
    indexof "0.0.1"
    object-component "0.0.3"
    parseqs "0.0.5"
    parseuri "0.0.5"
    socket.io-parser "~3.2.0"
    to-array "0.1.4"

socket.io-parser@~3.2.0:
  version "3.2.0"
  resolved "http://npm-registry.persgroep.digital/socket.io-parser/-/socket.io-parser-3.2.0.tgz#e7c6228b6aa1f814e6148aea325b51aa9499e077"
  dependencies:
    component-emitter "1.2.1"
    debug "~3.1.0"
    isarray "2.0.1"

socket.io@2.1.1:
  version "2.1.1"
  resolved "http://npm-registry.persgroep.digital/socket.io/-/socket.io-2.1.1.tgz#a069c5feabee3e6b214a75b40ce0652e1cfb9980"
  dependencies:
    debug "~3.1.0"
    engine.io "~3.2.0"
    has-binary2 "~1.0.2"
    socket.io-adapter "~1.1.0"
    socket.io-client "2.1.1"
    socket.io-parser "~3.2.0"

sockjs-client@1.3.0:
  version "1.3.0"
  resolved "http://npm-registry.persgroep.digital/sockjs-client/-/sockjs-client-1.3.0.tgz#12fc9d6cb663da5739d3dc5fb6e8687da95cb177"
  dependencies:
    debug "^3.2.5"
    eventsource "^1.0.7"
    faye-websocket "~0.11.1"
    inherits "^2.0.3"
    json3 "^3.3.2"
    url-parse "^1.4.3"

sockjs@0.3.19:
  version "0.3.19"
  resolved "http://npm-registry.persgroep.digital/sockjs/-/sockjs-0.3.19.tgz#d976bbe800af7bd20ae08598d582393508993c0d"
  dependencies:
    faye-websocket "^0.10.0"
    uuid "^3.0.1"

socks-proxy-agent@^4.0.0:
  version "4.0.2"
  resolved "http://npm-registry.persgroep.digital/socks-proxy-agent/-/socks-proxy-agent-4.0.2.tgz#3c8991f3145b2799e70e11bd5fbc8b1963116386"
  dependencies:
    agent-base "~4.2.1"
    socks "~2.3.2"

socks@~2.3.2:
  version "2.3.3"
  resolved "http://npm-registry.persgroep.digital/socks/-/socks-2.3.3.tgz#01129f0a5d534d2b897712ed8aceab7ee65d78e3"
  dependencies:
    ip "1.1.5"
    smart-buffer "^4.1.0"

source-list-map@^2.0.0:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/source-list-map/-/source-list-map-2.0.1.tgz#3993bd873bfc48479cca9ea3a547835c7c154b34"

source-list-map@~0.1.7:
  version "0.1.8"
  resolved "http://npm-registry.persgroep.digital/source-list-map/-/source-list-map-0.1.8.tgz#c550b2ab5427f6b3f21f5afead88c4f5587b2106"

source-map-loader@0.2.4:
  version "0.2.4"
  resolved "http://npm-registry.persgroep.digital/source-map-loader/-/source-map-loader-0.2.4.tgz#c18b0dc6e23bf66f6792437557c569a11e072271"
  dependencies:
    async "^2.5.0"
    loader-utils "^1.1.0"

source-map-resolve@^0.5.0:
  version "0.5.3"
  resolved "http://npm-registry.persgroep.digital/source-map-resolve/-/source-map-resolve-0.5.3.tgz#190866bece7553e1f8f267a2ee82c606b5509a1a"
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@0.5.10:
  version "0.5.10"
  resolved "http://npm-registry.persgroep.digital/source-map-support/-/source-map-support-0.5.10.tgz#2214080bc9d51832511ee2bab96e3c2f9353120c"
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-support@^0.5.17, source-map-support@^0.5.5, source-map-support@~0.5.12:
  version "0.5.21"
  resolved "http://npm-registry.persgroep.digital/source-map-support/-/source-map-support-0.5.21.tgz#04fe7c7f9e1ed2d662233c28cb2b35b9f63f6e4f"
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.1"
  resolved "http://npm-registry.persgroep.digital/source-map-url/-/source-map-url-0.4.1.tgz#0af66605a745a5a2f91cf1bbf8a7afbc283dec56"

source-map@0.1.x:
  version "0.1.43"
  resolved "http://npm-registry.persgroep.digital/source-map/-/source-map-0.1.43.tgz#c24bc146ca517c1471f5dacbe2571b2b7f9e3346"
  dependencies:
    amdefine ">=0.0.4"

source-map@0.5.6:
  version "0.5.6"
  resolved "http://npm-registry.persgroep.digital/source-map/-/source-map-0.5.6.tgz#75ce38f52bf0733c5a7f0c118d81334a2bb5f412"

source-map@0.7.3:
  version "0.7.3"
  resolved "http://npm-registry.persgroep.digital/source-map/-/source-map-0.7.3.tgz#5302f8169031735226544092e64981f751750383"

source-map@^0.4.2, source-map@~0.4.1:
  version "0.4.4"
  resolved "http://npm-registry.persgroep.digital/source-map/-/source-map-0.4.4.tgz#eba4f5da9c0dc999de68032d8b4f76173652036b"
  dependencies:
    amdefine ">=0.0.4"

source-map@^0.5.0, source-map@^0.5.6, source-map@^0.5.7, source-map@~0.5.3:
  version "0.5.7"
  resolved "http://npm-registry.persgroep.digital/source-map/-/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.0, source-map@~0.6.1:
  version "0.6.1"
  resolved "http://npm-registry.persgroep.digital/source-map/-/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"

sourcemap-codec@^1.4.8:
  version "1.4.8"
  resolved "http://npm-registry.persgroep.digital/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz#ea804bd94857402e6992d05a38ef1ae35a9ab4c4"

spdx-correct@^3.0.0:
  version "3.2.0"
  resolved "http://npm-registry.persgroep.digital/spdx-correct/-/spdx-correct-3.2.0.tgz#4f5ab0668f0059e34f9c00dce331784a12de4e9c"
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "http://npm-registry.persgroep.digital/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz#3f28ce1a77a00372683eade4a433183527a2163d"

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "http://npm-registry.persgroep.digital/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz#cf70f50482eefdc98e3ce0a6833e4a53ceeba679"
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.13"
  resolved "http://npm-registry.persgroep.digital/spdx-license-ids/-/spdx-license-ids-3.0.13.tgz#7189a474c46f8d47c7b0da4b987bb45e908bd2d5"

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/spdy-transport/-/spdy-transport-3.0.0.tgz#00d4863a6400ad75df93361a1608605e5dcdcf31"
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.0:
  version "4.0.2"
  resolved "http://npm-registry.persgroep.digital/spdy/-/spdy-4.0.2.tgz#b74f466203a3eda452c02492b91fb9e84a27677b"
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

specificity@^0.4.1:
  version "0.4.1"
  resolved "http://npm-registry.persgroep.digital/specificity/-/specificity-0.4.1.tgz#aab5e645012db08ba182e151165738d00887b019"

speed-measure-webpack-plugin@1.3.1:
  version "1.3.1"
  resolved "http://npm-registry.persgroep.digital/speed-measure-webpack-plugin/-/speed-measure-webpack-plugin-1.3.1.tgz#69840a5cdc08b4638697dac7db037f595d7f36a0"
  dependencies:
    chalk "^2.0.1"

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "http://npm-registry.persgroep.digital/split-string/-/split-string-3.1.0.tgz#7cb09dda3a86585705c64b39a6466038682e8fe2"
  dependencies:
    extend-shallow "^3.0.0"

sprintf-js@^1.1.2:
  version "1.1.2"
  resolved "http://npm-registry.persgroep.digital/sprintf-js/-/sprintf-js-1.1.2.tgz#da1765262bf8c0f571749f2ad6c26300207ae673"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/sprintf-js/-/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"

sshpk@^1.7.0:
  version "1.17.0"
  resolved "http://npm-registry.persgroep.digital/sshpk/-/sshpk-1.17.0.tgz#578082d92d4fe612b13007496e543fa0fbcbe4c5"
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

ssri@^6.0.0, ssri@^6.0.1:
  version "6.0.2"
  resolved "http://npm-registry.persgroep.digital/ssri/-/ssri-6.0.2.tgz#157939134f20464e7301ddba3e90ffa8f7728ac5"
  dependencies:
    figgy-pudding "^3.5.1"

stack-chain@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/stack-chain/-/stack-chain-2.0.0.tgz#d73d1172af89565f07438b5bcc086831b6689b2d"

stack-generator@^2.0.5:
  version "2.0.10"
  resolved "http://npm-registry.persgroep.digital/stack-generator/-/stack-generator-2.0.10.tgz#8ae171e985ed62287d4f1ed55a1633b3fb53bb4d"
  dependencies:
    stackframe "^1.3.4"

stackblur-canvas@^2.0.0:
  version "2.5.0"
  resolved "http://npm-registry.persgroep.digital/stackblur-canvas/-/stackblur-canvas-2.5.0.tgz#aa87bbed1560fdcd3138fff344fc6a1c413ebac4"

stackframe@^1.3.4:
  version "1.3.4"
  resolved "http://npm-registry.persgroep.digital/stackframe/-/stackframe-1.3.4.tgz#b881a004c8c149a5e8efef37d51b16e412943310"

stacktrace-gps@^3.0.4:
  version "3.1.2"
  resolved "http://npm-registry.persgroep.digital/stacktrace-gps/-/stacktrace-gps-3.1.2.tgz#0c40b24a9b119b20da4525c398795338966a2fb0"
  dependencies:
    source-map "0.5.6"
    stackframe "^1.3.4"

stacktrace-js@^2.0.0:
  version "2.0.2"
  resolved "http://npm-registry.persgroep.digital/stacktrace-js/-/stacktrace-js-2.0.2.tgz#4ca93ea9f494752d55709a081d400fdaebee897b"
  dependencies:
    error-stack-parser "^2.0.6"
    stack-generator "^2.0.5"
    stacktrace-gps "^3.0.4"

state-toggle@^1.0.0:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/state-toggle/-/state-toggle-1.0.3.tgz#e123b16a88e143139b09c6852221bc9815917dfe"

static-extend@^0.1.1:
  version "0.1.2"
  resolved "http://npm-registry.persgroep.digital/static-extend/-/static-extend-0.1.2.tgz#60809c39cbff55337226fd5e0b520f341f1fb5c6"
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

stats-webpack-plugin@0.7.0:
  version "0.7.0"
  resolved "http://npm-registry.persgroep.digital/stats-webpack-plugin/-/stats-webpack-plugin-0.7.0.tgz#ccffe9b745de8bbb155571e063f8263fc0e2bc06"
  dependencies:
    lodash "^4.17.4"

statuses@2.0.1:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/statuses/-/statuses-2.0.1.tgz#55cb000ccf1d48728bd23c685a063998cf1a1b63"

"statuses@>= 1.4.0 < 2", statuses@~1.5.0:
  version "1.5.0"
  resolved "http://npm-registry.persgroep.digital/statuses/-/statuses-1.5.0.tgz#161c7dac177659fd9811f43771fa99381478628c"

stdout-stream@^1.4.0:
  version "1.4.1"
  resolved "http://npm-registry.persgroep.digital/stdout-stream/-/stdout-stream-1.4.1.tgz#5ac174cdd5cd726104aa0c0b2bd83815d8d535de"
  dependencies:
    readable-stream "^2.0.1"

stream-browserify@^2.0.0, stream-browserify@^2.0.1:
  version "2.0.2"
  resolved "http://npm-registry.persgroep.digital/stream-browserify/-/stream-browserify-2.0.2.tgz#87521d38a44aa7ee91ce1cd2a47df0cb49dd660b"
  dependencies:
    inherits "~2.0.1"
    readable-stream "^2.0.2"

stream-combiner2@^1.1.1:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/stream-combiner2/-/stream-combiner2-1.1.1.tgz#fb4d8a1420ea362764e21ad4780397bebcb41cbe"
  dependencies:
    duplexer2 "~0.1.0"
    readable-stream "^2.0.2"

stream-each@^1.1.0:
  version "1.2.3"
  resolved "http://npm-registry.persgroep.digital/stream-each/-/stream-each-1.2.3.tgz#ebe27a0c389b04fbcc233642952e10731afa9bae"
  dependencies:
    end-of-stream "^1.1.0"
    stream-shift "^1.0.0"

stream-http@^2.0.0, stream-http@^2.7.2:
  version "2.8.3"
  resolved "http://npm-registry.persgroep.digital/stream-http/-/stream-http-2.8.3.tgz#b2d242469288a5a27ec4fe8933acf623de6514fc"
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.1"
    readable-stream "^2.3.6"
    to-arraybuffer "^1.0.0"
    xtend "^4.0.0"

stream-http@^3.0.0:
  version "3.2.0"
  resolved "http://npm-registry.persgroep.digital/stream-http/-/stream-http-3.2.0.tgz#1872dfcf24cb15752677e40e5c3f9cc1926028b5"
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.4"
    readable-stream "^3.6.0"
    xtend "^4.0.2"

stream-shift@^1.0.0:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/stream-shift/-/stream-shift-1.0.1.tgz#d7088281559ab2778424279b0877da3c392d5a3d"

stream-splicer@^2.0.0:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/stream-splicer/-/stream-splicer-2.0.1.tgz#0b13b7ee2b5ac7e0609a7463d83899589a363fcd"
  dependencies:
    inherits "^2.0.1"
    readable-stream "^2.0.2"

stream-to-observable@^0.1.0:
  version "0.1.0"
  resolved "http://npm-registry.persgroep.digital/stream-to-observable/-/stream-to-observable-0.1.0.tgz#45bf1d9f2d7dc09bed81f1c307c430e68b84cffe"

streamroller@^1.0.6:
  version "1.0.6"
  resolved "http://npm-registry.persgroep.digital/streamroller/-/streamroller-1.0.6.tgz#8167d8496ed9f19f05ee4b158d9611321b8cacd9"
  dependencies:
    async "^2.6.2"
    date-format "^2.0.0"
    debug "^3.2.6"
    fs-extra "^7.0.1"
    lodash "^4.17.14"

streamsearch@0.1.2:
  version "0.1.2"
  resolved "http://npm-registry.persgroep.digital/streamsearch/-/streamsearch-0.1.2.tgz#808b9d0e56fc273d809ba57338e929919a1a9f1a"

string-argv@0.0.2:
  version "0.0.2"
  resolved "http://npm-registry.persgroep.digital/string-argv/-/string-argv-0.0.2.tgz#dac30408690c21f3c3630a3ff3a05877bdcbd736"

string-argv@0.1.1:
  version "0.1.1"
  resolved "http://npm-registry.persgroep.digital/string-argv/-/string-argv-0.1.1.tgz#66bd5ae3823708eaa1916fa5412703150d4ddfaf"

string-width@^1.0.1, string-width@^1.0.2:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/string-width/-/string-width-1.0.2.tgz#118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3"
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

"string-width@^1.0.2 || 2 || 3 || 4":
  version "4.2.3"
  resolved "http://npm-registry.persgroep.digital/string-width/-/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^2.0.0, string-width@^2.1.0, string-width@^2.1.1:
  version "2.1.1"
  resolved "http://npm-registry.persgroep.digital/string-width/-/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^3.0.0:
  version "3.1.0"
  resolved "http://npm-registry.persgroep.digital/string-width/-/string-width-3.1.0.tgz#22767be21b62af1081574306f69ac51b62203961"
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string_decoder@^1.0.0, string_decoder@^1.1.1:
  version "1.3.0"
  resolved "http://npm-registry.persgroep.digital/string_decoder/-/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e"
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~0.10.x:
  version "0.10.31"
  resolved "http://npm-registry.persgroep.digital/string_decoder/-/string_decoder-0.10.31.tgz#62e203bc41766c6c28c9fc84301dab1c5310fa94"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/string_decoder/-/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  dependencies:
    safe-buffer "~5.1.0"

stringify-entities@^1.0.1:
  version "1.3.2"
  resolved "http://npm-registry.persgroep.digital/stringify-entities/-/stringify-entities-1.3.2.tgz#a98417e5471fd227b3e45d3db1861c11caf668f7"
  dependencies:
    character-entities-html4 "^1.0.0"
    character-entities-legacy "^1.0.0"
    is-alphanumerical "^1.0.0"
    is-hexadecimal "^1.0.0"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "http://npm-registry.persgroep.digital/strip-ansi/-/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://npm-registry.persgroep.digital/strip-ansi/-/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^5.0.0, strip-ansi@^5.1.0:
  version "5.2.0"
  resolved "http://npm-registry.persgroep.digital/strip-ansi/-/strip-ansi-5.2.0.tgz#8c9a536feb6afc962bdfa5b104a5091c1ad9c0ae"
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://npm-registry.persgroep.digital/strip-ansi/-/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  dependencies:
    ansi-regex "^5.0.1"

strip-bom@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/strip-bom/-/strip-bom-2.0.0.tgz#6219a85616520491f35788bdbf1447a99c7e6b0e"
  dependencies:
    is-utf8 "^0.2.0"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/strip-bom/-/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/strip-eof/-/strip-eof-1.0.0.tgz#bb43ff5598a6eb05d89b59fcd129c983313606bf"

strip-indent@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/strip-indent/-/strip-indent-1.0.1.tgz#0c7962a6adefa7bbd4ac366460a638552ae1a0a2"
  dependencies:
    get-stdin "^4.0.1"

strip-indent@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/strip-indent/-/strip-indent-2.0.0.tgz#5ef8db295d01e6ed6cbf7aab96998d7822527b68"

style-loader@0.23.1:
  version "0.23.1"
  resolved "http://npm-registry.persgroep.digital/style-loader/-/style-loader-0.23.1.tgz#cb9154606f3e771ab6c4ab637026a1049174d925"
  dependencies:
    loader-utils "^1.1.0"
    schema-utils "^1.0.0"

style-search@^0.1.0:
  version "0.1.0"
  resolved "http://npm-registry.persgroep.digital/style-search/-/style-search-0.1.0.tgz#7958c793e47e32e07d2b5cafe5c0bf8e12e77902"

"styleguide@https://github.com/Vnumedia/styleguide#fb63f4fa6b51c8eecb32ff3589fb0f2c3929edb3":
  version "0.0.0"
  resolved "https://github.com/Vnumedia/styleguide#fb63f4fa6b51c8eecb32ff3589fb0f2c3929edb3"

stylelint-order@^2.0.0:
  version "2.2.1"
  resolved "http://npm-registry.persgroep.digital/stylelint-order/-/stylelint-order-2.2.1.tgz#cd2d4a0d81d91c705f1d275a58487e5ad5aa5828"
  dependencies:
    lodash "^4.17.10"
    postcss "^7.0.2"
    postcss-sorting "^4.1.0"

stylelint@^9.10.1:
  version "9.10.1"
  resolved "http://npm-registry.persgroep.digital/stylelint/-/stylelint-9.10.1.tgz#5f0ee3701461dff1d68284e1386efe8f0677a75d"
  dependencies:
    autoprefixer "^9.0.0"
    balanced-match "^1.0.0"
    chalk "^2.4.1"
    cosmiconfig "^5.0.0"
    debug "^4.0.0"
    execall "^1.0.0"
    file-entry-cache "^4.0.0"
    get-stdin "^6.0.0"
    global-modules "^2.0.0"
    globby "^9.0.0"
    globjoin "^0.1.4"
    html-tags "^2.0.0"
    ignore "^5.0.4"
    import-lazy "^3.1.0"
    imurmurhash "^0.1.4"
    known-css-properties "^0.11.0"
    leven "^2.1.0"
    lodash "^4.17.4"
    log-symbols "^2.0.0"
    mathml-tag-names "^2.0.1"
    meow "^5.0.0"
    micromatch "^3.1.10"
    normalize-selector "^0.2.0"
    pify "^4.0.0"
    postcss "^7.0.13"
    postcss-html "^0.36.0"
    postcss-jsx "^0.36.0"
    postcss-less "^3.1.0"
    postcss-markdown "^0.36.0"
    postcss-media-query-parser "^0.2.3"
    postcss-reporter "^6.0.0"
    postcss-resolve-nested-selector "^0.1.1"
    postcss-safe-parser "^4.0.0"
    postcss-sass "^0.3.5"
    postcss-scss "^2.0.0"
    postcss-selector-parser "^3.1.0"
    postcss-syntax "^0.36.2"
    postcss-value-parser "^3.3.0"
    resolve-from "^4.0.0"
    signal-exit "^3.0.2"
    slash "^2.0.0"
    specificity "^0.4.1"
    string-width "^3.0.0"
    style-search "^0.1.0"
    sugarss "^2.0.0"
    svg-tags "^1.0.0"
    table "^5.0.0"

stylus-loader@3.0.2:
  version "3.0.2"
  resolved "http://npm-registry.persgroep.digital/stylus-loader/-/stylus-loader-3.0.2.tgz#27a706420b05a38e038e7cacb153578d450513c6"
  dependencies:
    loader-utils "^1.0.2"
    lodash.clonedeep "^4.5.0"
    when "~3.6.x"

stylus@0.54.5:
  version "0.54.5"
  resolved "http://npm-registry.persgroep.digital/stylus/-/stylus-0.54.5.tgz#42b9560931ca7090ce8515a798ba9e6aa3d6dc79"
  dependencies:
    css-parse "1.7.x"
    debug "*"
    glob "7.0.x"
    mkdirp "0.5.x"
    sax "0.5.x"
    source-map "0.1.x"

subarg@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/subarg/-/subarg-1.0.0.tgz#f62cf17581e996b48fc965699f54c06ae268b8d2"
  dependencies:
    minimist "^1.1.0"

sugarss@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/sugarss/-/sugarss-2.0.0.tgz#ddd76e0124b297d40bf3cca31c8b22ecb43bc61d"
  dependencies:
    postcss "^7.0.2"

supports-color@5.5.0, supports-color@^5.1.0, supports-color@^5.3.0:
  version "5.5.0"
  resolved "http://npm-registry.persgroep.digital/supports-color/-/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  dependencies:
    has-flag "^3.0.0"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/supports-color/-/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"

supports-color@^6.1.0:
  version "6.1.0"
  resolved "http://npm-registry.persgroep.digital/supports-color/-/supports-color-6.1.0.tgz#0764abc69c63d5ac842dd4867e8d025e880df8f3"
  dependencies:
    has-flag "^3.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"

svg-pathdata@^6.0.3:
  version "6.0.3"
  resolved "http://npm-registry.persgroep.digital/svg-pathdata/-/svg-pathdata-6.0.3.tgz#80b0e0283b652ccbafb69ad4f8f73e8d3fbf2cac"

svg-tags@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/svg-tags/-/svg-tags-1.0.0.tgz#58f71cee3bd519b59d4b2a843b6c7de64ac04764"

swagger-express-middleware@^2.0.1:
  version "2.0.5"
  resolved "http://npm-registry.persgroep.digital/swagger-express-middleware/-/swagger-express-middleware-2.0.5.tgz#45b35a1dc8c55cb7827d050f076c9988938d0249"
  dependencies:
    body-parser "^1.19.0"
    cookie-parser "^1.4.4"
    debug "^4.1.1"
    lodash "^4.17.15"
    mkdirp "^0.5.1"
    multer "^0.1.8"
    ono "^6.0.0"
    swagger-methods "^2.0.1"
    swagger-parser "^8.0.4"
    tmp "^0.1.0"
    tv4 "^1.2.5"
    type-is "^1.6.18"

swagger-methods@^2.0.1:
  version "2.0.2"
  resolved "http://npm-registry.persgroep.digital/swagger-methods/-/swagger-methods-2.0.2.tgz#5891d5536e54d5ba8e7ae1007acc9170f41c9590"

swagger-parser@^8.0.4:
  version "8.0.4"
  resolved "http://npm-registry.persgroep.digital/swagger-parser/-/swagger-parser-8.0.4.tgz#ddec68723d13ee3748dd08fd5b7ba579327595da"
  dependencies:
    call-me-maybe "^1.0.1"
    json-schema-ref-parser "^7.1.3"
    ono "^6.0.0"
    openapi-schemas "^1.0.2"
    openapi-types "^1.3.5"
    swagger-methods "^2.0.1"
    z-schema "^4.2.2"

symbol-observable@1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/symbol-observable/-/symbol-observable-1.0.1.tgz#8340fc4702c3122df5d22288f88283f513d3fdd4"

symbol-observable@1.2.0:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/symbol-observable/-/symbol-observable-1.2.0.tgz#c22688aed4eab3cdc2dfeacbb561660560a00804"

syntax-error@^1.1.1:
  version "1.4.0"
  resolved "http://npm-registry.persgroep.digital/syntax-error/-/syntax-error-1.4.0.tgz#2d9d4ff5c064acb711594a3e3b95054ad51d907c"
  dependencies:
    acorn-node "^1.2.0"

table@^5.0.0:
  version "5.4.6"
  resolved "http://npm-registry.persgroep.digital/table/-/table-5.4.6.tgz#1292d19500ce3f86053b05f0e8e7e4a3bb21079e"
  dependencies:
    ajv "^6.10.2"
    lodash "^4.17.14"
    slice-ansi "^2.1.0"
    string-width "^3.0.0"

tapable@^1.0.0, tapable@^1.1.0:
  version "1.1.3"
  resolved "http://npm-registry.persgroep.digital/tapable/-/tapable-1.1.3.tgz#a1fccc06b58db61fd7a45da2da44f5f3a3e67ba2"

tar@^2.0.0:
  version "2.2.2"
  resolved "http://npm-registry.persgroep.digital/tar/-/tar-2.2.2.tgz#0ca8848562c7299b8b446ff6a4d60cdbb23edc40"
  dependencies:
    block-stream "*"
    fstream "^1.0.12"
    inherits "2"

tar@^4.4.8:
  version "4.4.19"
  resolved "http://npm-registry.persgroep.digital/tar/-/tar-4.4.19.tgz#2e4d7263df26f2b914dee10c825ab132123742f3"
  dependencies:
    chownr "^1.1.4"
    fs-minipass "^1.2.7"
    minipass "^2.9.0"
    minizlib "^1.3.3"
    mkdirp "^0.5.5"
    safe-buffer "^5.2.1"
    yallist "^3.1.1"

terser-webpack-plugin@1.4.3:
  version "1.4.3"
  resolved "http://npm-registry.persgroep.digital/terser-webpack-plugin/-/terser-webpack-plugin-1.4.3.tgz#5ecaf2dbdc5fb99745fd06791f46fc9ddb1c9a7c"
  dependencies:
    cacache "^12.0.2"
    find-cache-dir "^2.1.0"
    is-wsl "^1.1.0"
    schema-utils "^1.0.0"
    serialize-javascript "^2.1.2"
    source-map "^0.6.1"
    terser "^4.1.2"
    webpack-sources "^1.4.0"
    worker-farm "^1.7.0"

terser-webpack-plugin@^1.1.0:
  version "1.4.5"
  resolved "http://npm-registry.persgroep.digital/terser-webpack-plugin/-/terser-webpack-plugin-1.4.5.tgz#a217aefaea330e734ffacb6120ec1fa312d6040b"
  dependencies:
    cacache "^12.0.2"
    find-cache-dir "^2.1.0"
    is-wsl "^1.1.0"
    schema-utils "^1.0.0"
    serialize-javascript "^4.0.0"
    source-map "^0.6.1"
    terser "^4.1.2"
    webpack-sources "^1.4.0"
    worker-farm "^1.7.0"

terser@^4.1.2:
  version "4.8.1"
  resolved "http://npm-registry.persgroep.digital/terser/-/terser-4.8.1.tgz#a00e5634562de2239fd404c649051bf6fc21144f"
  dependencies:
    commander "^2.20.0"
    source-map "~0.6.1"
    source-map-support "~0.5.12"

text-segmentation@^1.0.3:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/text-segmentation/-/text-segmentation-1.0.3.tgz#52a388159efffe746b24a63ba311b6ac9f2d7943"
  dependencies:
    utrie "^1.0.2"

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "http://npm-registry.persgroep.digital/thenify-all/-/thenify-all-1.6.0.tgz#1a1918d402d8fc3f98fbf234db0bcc8cc10e9726"
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "http://npm-registry.persgroep.digital/thenify/-/thenify-3.3.1.tgz#8932e686a4066038a016dd9e2ca46add9838a95f"
  dependencies:
    any-promise "^1.0.0"

throttleit@^0.0.2:
  version "0.0.2"
  resolved "http://npm-registry.persgroep.digital/throttleit/-/throttleit-0.0.2.tgz#cfedf88e60c00dd9697b61fdd2a8343a9b680eaf"

throttleit@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/throttleit/-/throttleit-1.0.0.tgz#9e785836daf46743145a5984b6268d828528ac6c"

through2@^2.0.0:
  version "2.0.5"
  resolved "http://npm-registry.persgroep.digital/through2/-/through2-2.0.5.tgz#01c1e39eb31d07cb7d03a96a70823260b23132cd"
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

"through@>=2.2.7 <3", through@X.X.X, through@^2.3.6, through@^2.3.8:
  version "2.3.8"
  resolved "http://npm-registry.persgroep.digital/through/-/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"

thunky@^1.0.2:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/thunky/-/thunky-1.1.0.tgz#5abaf714a9405db0504732bbccd2cedd9ef9537d"

timers-browserify@^1.0.1:
  version "1.4.2"
  resolved "http://npm-registry.persgroep.digital/timers-browserify/-/timers-browserify-1.4.2.tgz#c9c58b575be8407375cb5e2462dacee74359f41d"
  dependencies:
    process "~0.11.0"

timers-browserify@^2.0.4:
  version "2.0.12"
  resolved "http://npm-registry.persgroep.digital/timers-browserify/-/timers-browserify-2.0.12.tgz#44a45c11fbf407f34f97bccd1577c652361b00ee"
  dependencies:
    setimmediate "^1.0.4"

title-case@^2.1.1:
  version "2.1.1"
  resolved "http://npm-registry.persgroep.digital/title-case/-/title-case-2.1.1.tgz#3e127216da58d2bc5becf137ab91dae3a7cd8faa"
  dependencies:
    no-case "^2.2.0"
    upper-case "^1.0.3"

tmp@0.0.33, tmp@0.0.x, tmp@^0.0.33:
  version "0.0.33"
  resolved "http://npm-registry.persgroep.digital/tmp/-/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
  dependencies:
    os-tmpdir "~1.0.2"

tmp@^0.1.0:
  version "0.1.0"
  resolved "http://npm-registry.persgroep.digital/tmp/-/tmp-0.1.0.tgz#ee434a4e22543082e294ba6201dcc6eafefa2877"
  dependencies:
    rimraf "^2.6.3"

to-array@0.1.4:
  version "0.1.4"
  resolved "http://npm-registry.persgroep.digital/to-array/-/to-array-0.1.4.tgz#17e6c11f73dd4f3d74cda7a4ff3238e9ad9bf890"

to-arraybuffer@^1.0.0:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz#7d229b1fcc637e466ca081180836a7aabff83f43"

to-fast-properties@^1.0.3:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/to-fast-properties/-/to-fast-properties-1.0.3.tgz#b83571fa4d8c25b82e231b06e3a3055de4ca1a47"

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/to-fast-properties/-/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "http://npm-registry.persgroep.digital/to-object-path/-/to-object-path-0.3.0.tgz#297588b7b0e7e0ac08e04e672f85c1f4999e17af"
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "http://npm-registry.persgroep.digital/to-regex-range/-/to-regex-range-2.1.1.tgz#7c80c17b9dfebe599e27367e0d4dd5590141db38"
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://npm-registry.persgroep.digital/to-regex-range/-/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "http://npm-registry.persgroep.digital/to-regex/-/to-regex-3.0.2.tgz#13cfdd9b336552f30b51f33a8ae1b42a7a7599ce"
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/toidentifier/-/toidentifier-1.0.1.tgz#3be34321a88a820ed1bd80dfaa33e479fbb8dd35"

tough-cookie@~2.4.3:
  version "2.4.3"
  resolved "http://npm-registry.persgroep.digital/tough-cookie/-/tough-cookie-2.4.3.tgz#53f36da3f47783b0925afa06ff9f3b165280f781"
  dependencies:
    psl "^1.1.24"
    punycode "^1.4.1"

tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "http://npm-registry.persgroep.digital/tough-cookie/-/tough-cookie-2.5.0.tgz#cd9fb2a0aa1d5a12b473bd9fb96fa3dcff65ade2"
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

tree-kill@1.2.2:
  version "1.2.2"
  resolved "http://npm-registry.persgroep.digital/tree-kill/-/tree-kill-1.2.2.tgz#4ca09a9092c88b73a7cdc5e8a01b507b0790a0cc"

trim-newlines@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/trim-newlines/-/trim-newlines-1.0.0.tgz#5887966bb582a4503a41eb524f7d35011815a613"

trim-newlines@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/trim-newlines/-/trim-newlines-2.0.0.tgz#b403d0b91be50c331dfc4b82eeceb22c3de16d20"

trim-right@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/trim-right/-/trim-right-1.0.1.tgz#cb2e1203067e0c8de1f614094b9fe45704ea6003"

trim-trailing-lines@^1.0.0:
  version "1.1.4"
  resolved "http://npm-registry.persgroep.digital/trim-trailing-lines/-/trim-trailing-lines-1.1.4.tgz#bd4abbec7cc880462f10b2c8b5ce1d8d1ec7c2c0"

trim@0.0.1:
  version "0.0.1"
  resolved "http://npm-registry.persgroep.digital/trim/-/trim-0.0.1.tgz#5858547f6b290757ee95cccc666fb50084c460dd"

trough@^1.0.0:
  version "1.0.5"
  resolved "http://npm-registry.persgroep.digital/trough/-/trough-1.0.5.tgz#b8b639cefad7d0bb2abd37d433ff8293efa5f406"

"true-case-path@^1.0.2":
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/true-case-path/-/true-case-path-1.0.3.tgz#f813b5a8c86b40da59606722b144e3225799f47d"
  dependencies:
    glob "^7.1.2"

ts-node@^8.0.2:
  version "8.10.2"
  resolved "http://npm-registry.persgroep.digital/ts-node/-/ts-node-8.10.2.tgz#eee03764633b1234ddd37f8db9ec10b75ec7fb8d"
  dependencies:
    arg "^4.1.0"
    diff "^4.0.1"
    make-error "^1.1.1"
    source-map-support "^0.5.17"
    yn "3.1.1"

tslib@^1.8.0, tslib@^1.8.1, tslib@^1.9.0:
  version "1.14.1"
  resolved "http://npm-registry.persgroep.digital/tslib/-/tslib-1.14.1.tgz#cf2d38bdc34a134bcaf1091c41f6619e2f672d00"

tslint@~5.12.1:
  version "5.12.1"
  resolved "http://npm-registry.persgroep.digital/tslint/-/tslint-5.12.1.tgz#8cec9d454cf8a1de9b0a26d7bdbad6de362e52c1"
  dependencies:
    babel-code-frame "^6.22.0"
    builtin-modules "^1.1.1"
    chalk "^2.3.0"
    commander "^2.12.1"
    diff "^3.2.0"
    glob "^7.1.1"
    js-yaml "^3.7.0"
    minimatch "^3.0.4"
    resolve "^1.3.2"
    semver "^5.3.0"
    tslib "^1.8.0"
    tsutils "^2.27.2"

tsutils@^2.27.2:
  version "2.29.0"
  resolved "http://npm-registry.persgroep.digital/tsutils/-/tsutils-2.29.0.tgz#32b488501467acbedd4b85498673a0812aca0b99"
  dependencies:
    tslib "^1.8.1"

tty-browserify@0.0.0:
  version "0.0.0"
  resolved "http://npm-registry.persgroep.digital/tty-browserify/-/tty-browserify-0.0.0.tgz#a157ba402da24e9bf957f9aa69d524eed42901a6"

tty-browserify@0.0.1:
  version "0.0.1"
  resolved "http://npm-registry.persgroep.digital/tty-browserify/-/tty-browserify-0.0.1.tgz#3f05251ee17904dfd0677546670db9651682b811"

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "http://npm-registry.persgroep.digital/tunnel-agent/-/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  dependencies:
    safe-buffer "^5.0.1"

tv4@^1.2.5:
  version "1.3.0"
  resolved "http://npm-registry.persgroep.digital/tv4/-/tv4-1.3.0.tgz#d020c846fadd50c855abb25ebaecc68fc10f7963"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "http://npm-registry.persgroep.digital/tweetnacl/-/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"

type-detect@^4.0.0, type-detect@^4.0.5:
  version "4.0.8"
  resolved "http://npm-registry.persgroep.digital/type-detect/-/type-detect-4.0.8.tgz#7646fb5f18871cfbb7749e69bd39a6388eb7450c"

type-is@^1.6.18, type-is@~1.6.18:
  version "1.6.18"
  resolved "http://npm-registry.persgroep.digital/type-is/-/type-is-1.6.18.tgz#4e552cd05df09467dcbc4ef739de89f2cf37c131"
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

type-is@~1.5.2:
  version "1.5.7"
  resolved "http://npm-registry.persgroep.digital/type-is/-/type-is-1.5.7.tgz#b9368a593cc6ef7d0645e78b2f4c64cbecd05e90"
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.0.9"

type@^1.0.1:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/type/-/type-1.2.0.tgz#848dd7698dafa3e54a6c479e759c4bc3f18847a0"

type@^2.7.2:
  version "2.7.2"
  resolved "http://npm-registry.persgroep.digital/type/-/type-2.7.2.tgz#2376a15a3a28b1efa0f5350dcf72d24df6ef98d0"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "http://npm-registry.persgroep.digital/typedarray/-/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"

typescript@3.2.4:
  version "3.2.4"
  resolved "http://npm-registry.persgroep.digital/typescript/-/typescript-3.2.4.tgz#c585cb952912263d915b462726ce244ba510ef3d"

"typescript@>=3.1.1 <3.2.0":
  version "3.1.8"
  resolved "http://npm-registry.persgroep.digital/typescript/-/typescript-3.1.8.tgz#20703f388d9fccc562ca4a049d8a7567c8e95ebd"

uc.micro@^1.0.1, uc.micro@^1.0.5:
  version "1.0.6"
  resolved "http://npm-registry.persgroep.digital/uc.micro/-/uc.micro-1.0.6.tgz#9c411a802a409a91fc6cf74081baba34b24499ac"

ultron@~1.1.0:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/ultron/-/ultron-1.1.1.tgz#9fe1536a10a664a65266a1e3ccf85fd36302bc9c"

umd@^3.0.0:
  version "3.0.3"
  resolved "http://npm-registry.persgroep.digital/umd/-/umd-3.0.3.tgz#aa9fe653c42b9097678489c01000acb69f0b26cf"

undeclared-identifiers@^1.1.2:
  version "1.1.3"
  resolved "http://npm-registry.persgroep.digital/undeclared-identifiers/-/undeclared-identifiers-1.1.3.tgz#9254c1d37bdac0ac2b52de4b6722792d2a91e30f"
  dependencies:
    acorn-node "^1.3.0"
    dash-ast "^1.0.0"
    get-assigned-identifiers "^1.2.0"
    simple-concat "^1.0.0"
    xtend "^4.0.1"

unherit@^1.0.4:
  version "1.1.3"
  resolved "http://npm-registry.persgroep.digital/unherit/-/unherit-1.1.3.tgz#6c9b503f2b41b262330c80e91c8614abdaa69c22"
  dependencies:
    inherits "^2.0.0"
    xtend "^4.0.0"

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz#301acdc525631670d39f6146e0e77ff6bbdebddc"

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz#54fd16e0ecb167cf04cf1f756bdcc92eba7976c3"
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.1.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.1.0.tgz#cb5fffdcd16a05124f5a4b0bf7c3770208acbbe0"

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz#43d41e3be698bd493ef911077c9b131f827e8ccd"

unified@^7.0.0:
  version "7.1.0"
  resolved "http://npm-registry.persgroep.digital/unified/-/unified-7.1.0.tgz#5032f1c1ee3364bd09da12e27fdd4a7553c7be13"
  dependencies:
    "@types/unist" "^2.0.0"
    "@types/vfile" "^3.0.0"
    bail "^1.0.0"
    extend "^3.0.0"
    is-plain-obj "^1.1.0"
    trough "^1.0.0"
    vfile "^3.0.0"
    x-is-string "^0.1.0"

union-value@^1.0.0:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/union-value/-/union-value-1.0.1.tgz#0b6fe7b835aecda61c6ea4d4f02c14221e109847"
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

uniq@^1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/uniq/-/uniq-1.0.1.tgz#b31c5ae8254844a3a8281541ce2b04b865a734ff"

unique-filename@^1.1.1:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/unique-filename/-/unique-filename-1.1.1.tgz#1d69769369ada0583103a1e6ae87681b56573230"
  dependencies:
    unique-slug "^2.0.0"

unique-slug@^2.0.0:
  version "2.0.2"
  resolved "http://npm-registry.persgroep.digital/unique-slug/-/unique-slug-2.0.2.tgz#baabce91083fc64e945b0f3ad613e264f7cd4e6c"
  dependencies:
    imurmurhash "^0.1.4"

unist-util-find-all-after@^1.0.2:
  version "1.0.5"
  resolved "http://npm-registry.persgroep.digital/unist-util-find-all-after/-/unist-util-find-all-after-1.0.5.tgz#5751a8608834f41d117ad9c577770c5f2f1b2899"
  dependencies:
    unist-util-is "^3.0.0"

unist-util-is@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/unist-util-is/-/unist-util-is-3.0.0.tgz#d9e84381c2468e82629e4a5be9d7d05a2dd324cd"

unist-util-remove-position@^1.0.0:
  version "1.1.4"
  resolved "http://npm-registry.persgroep.digital/unist-util-remove-position/-/unist-util-remove-position-1.1.4.tgz#ec037348b6102c897703eee6d0294ca4755a2020"
  dependencies:
    unist-util-visit "^1.1.0"

unist-util-stringify-position@^1.0.0, unist-util-stringify-position@^1.1.1:
  version "1.1.2"
  resolved "http://npm-registry.persgroep.digital/unist-util-stringify-position/-/unist-util-stringify-position-1.1.2.tgz#3f37fcf351279dcbca7480ab5889bb8a832ee1c6"

unist-util-stringify-position@^3.0.0:
  version "3.0.3"
  resolved "http://npm-registry.persgroep.digital/unist-util-stringify-position/-/unist-util-stringify-position-3.0.3.tgz#03ad3348210c2d930772d64b489580c13a7db39d"
  dependencies:
    "@types/unist" "^2.0.0"

unist-util-visit-parents@^2.0.0:
  version "2.1.2"
  resolved "http://npm-registry.persgroep.digital/unist-util-visit-parents/-/unist-util-visit-parents-2.1.2.tgz#25e43e55312166f3348cae6743588781d112c1e9"
  dependencies:
    unist-util-is "^3.0.0"

unist-util-visit@^1.1.0:
  version "1.4.1"
  resolved "http://npm-registry.persgroep.digital/unist-util-visit/-/unist-util-visit-1.4.1.tgz#4724aaa8486e6ee6e26d7ff3c8685960d560b1e3"
  dependencies:
    unist-util-visit-parents "^2.0.0"

universalify@^0.1.0:
  version "0.1.2"
  resolved "http://npm-registry.persgroep.digital/universalify/-/universalify-0.1.2.tgz#b646f69be3942dabcecc9d6639c80dc105efaa66"

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/unpipe/-/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"

unset-value@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/unset-value/-/unset-value-1.0.0.tgz#8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559"
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

upath@^1.0.5, upath@^1.1.1:
  version "1.2.0"
  resolved "http://npm-registry.persgroep.digital/upath/-/upath-1.2.0.tgz#8f66dbcd55a883acdae4408af8b035a5044c1894"

update-browserslist-db@^1.0.10:
  version "1.0.11"
  resolved "http://npm-registry.persgroep.digital/update-browserslist-db/-/update-browserslist-db-1.0.11.tgz#9a2a641ad2907ae7b3616506f4b977851db5b940"
  dependencies:
    escalade "^3.1.1"
    picocolors "^1.0.0"

upper-case@^1.0.3:
  version "1.1.3"
  resolved "http://npm-registry.persgroep.digital/upper-case/-/upper-case-1.1.3.tgz#f6b4501c2ec4cdd26ba78be7222961de77621598"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "http://npm-registry.persgroep.digital/uri-js/-/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "http://npm-registry.persgroep.digital/urix/-/urix-0.1.0.tgz#da937f7a62e21fec1fd18d49b35c2935067a6c72"

url-parse@^1.4.3:
  version "1.5.10"
  resolved "http://npm-registry.persgroep.digital/url-parse/-/url-parse-1.5.10.tgz#9d3c2f736c1d75dd3bd2be507dcc111f1e2ea9c1"
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

url@0.11.0, url@^0.11.0, url@~0.11.0:
  version "0.11.0"
  resolved "http://npm-registry.persgroep.digital/url/-/url-0.11.0.tgz#3838e97cfc60521eb73c525a8e55bfdd9e2e28f1"
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

use@^3.1.0:
  version "3.1.1"
  resolved "http://npm-registry.persgroep.digital/use/-/use-3.1.1.tgz#d50c8cac79a19fbc20f2911f56eb973f4e10070f"

useragent@2.3.0:
  version "2.3.0"
  resolved "http://npm-registry.persgroep.digital/useragent/-/useragent-2.3.0.tgz#217f943ad540cb2128658ab23fc960f6a88c9972"
  dependencies:
    lru-cache "4.1.x"
    tmp "0.0.x"

util-arity@^1.0.2:
  version "1.1.0"
  resolved "http://npm-registry.persgroep.digital/util-arity/-/util-arity-1.1.0.tgz#59d01af1fdb3fede0ac4e632b0ab5f6ce97c9330"

util-deprecate@^1.0.1, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/util-deprecate/-/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"

util@0.10.3:
  version "0.10.3"
  resolved "http://npm-registry.persgroep.digital/util/-/util-0.10.3.tgz#7afb1afe50805246489e3db7fe0ed379336ac0f9"
  dependencies:
    inherits "2.0.1"

util@^0.11.0:
  version "0.11.1"
  resolved "http://npm-registry.persgroep.digital/util/-/util-0.11.1.tgz#3236733720ec64bb27f6e26f421aaa2e1b588d61"
  dependencies:
    inherits "2.0.3"

util@~0.10.1:
  version "0.10.4"
  resolved "http://npm-registry.persgroep.digital/util/-/util-0.10.4.tgz#3aa0125bfe668a4672de58857d3ace27ecb76901"
  dependencies:
    inherits "2.0.3"

utils-merge@1.0.1:
  version "1.0.1"
  resolved "http://npm-registry.persgroep.digital/utils-merge/-/utils-merge-1.0.1.tgz#9f95710f50a267947b2ccc124741c1028427e713"

utrie@^1.0.2:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/utrie/-/utrie-1.0.2.tgz#d42fe44de9bc0119c25de7f564a6ed1b2c87a645"
  dependencies:
    base64-arraybuffer "^1.0.2"

uuid@^3.0.1, uuid@^3.3.2:
  version "3.4.0"
  resolved "http://npm-registry.persgroep.digital/uuid/-/uuid-3.4.0.tgz#b23e4358afa8a202fe7a100af1f5f883f02007ee"

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "http://npm-registry.persgroep.digital/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

validate-npm-package-name@^3.0.0:
  version "3.0.0"
  resolved "http://npm-registry.persgroep.digital/validate-npm-package-name/-/validate-npm-package-name-3.0.0.tgz#5fa912d81eb7d0c74afc140de7317f0ca7df437e"
  dependencies:
    builtins "^1.0.3"

validator@^13.6.0:
  version "13.9.0"
  resolved "http://npm-registry.persgroep.digital/validator/-/validator-13.9.0.tgz#33e7b85b604f3bbce9bb1a05d5c3e22e1c2ff855"

vary@~1.1.2:
  version "1.1.2"
  resolved "http://npm-registry.persgroep.digital/vary/-/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"

verror@1.10.0:
  version "1.10.0"
  resolved "http://npm-registry.persgroep.digital/verror/-/verror-1.10.0.tgz#3a105ca17053af55d6e270c1f8288682e18da400"
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

verror@^1.9.0:
  version "1.10.1"
  resolved "http://npm-registry.persgroep.digital/verror/-/verror-1.10.1.tgz#4bf09eeccf4563b109ed4b3d458380c972b0cdeb"
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

vfile-location@^2.0.0:
  version "2.0.6"
  resolved "http://npm-registry.persgroep.digital/vfile-location/-/vfile-location-2.0.6.tgz#8a274f39411b8719ea5728802e10d9e0dff1519e"

vfile-message@*:
  version "3.1.4"
  resolved "http://npm-registry.persgroep.digital/vfile-message/-/vfile-message-3.1.4.tgz#15a50816ae7d7c2d1fa87090a7f9f96612b59dea"
  dependencies:
    "@types/unist" "^2.0.0"
    unist-util-stringify-position "^3.0.0"

vfile-message@^1.0.0:
  version "1.1.1"
  resolved "http://npm-registry.persgroep.digital/vfile-message/-/vfile-message-1.1.1.tgz#5833ae078a1dfa2d96e9647886cd32993ab313e1"
  dependencies:
    unist-util-stringify-position "^1.1.1"

vfile@^3.0.0:
  version "3.0.1"
  resolved "http://npm-registry.persgroep.digital/vfile/-/vfile-3.0.1.tgz#47331d2abe3282424f4a4bb6acd20a44c4121803"
  dependencies:
    is-buffer "^2.0.0"
    replace-ext "1.0.0"
    unist-util-stringify-position "^1.0.0"
    vfile-message "^1.0.0"

vm-browserify@^1.0.0, vm-browserify@^1.0.1:
  version "1.1.2"
  resolved "http://npm-registry.persgroep.digital/vm-browserify/-/vm-browserify-1.1.2.tgz#78641c488b8e6ca91a75f511e7a3b32a86e5dda0"

void-elements@^2.0.0:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/void-elements/-/void-elements-2.0.1.tgz#c066afb582bb1cb4128d60ea92392e94d5e9dbec"

watchify@3.11.0:
  version "3.11.0"
  resolved "http://npm-registry.persgroep.digital/watchify/-/watchify-3.11.0.tgz#03f1355c643955e7ab8dcbf399f624644221330f"
  dependencies:
    anymatch "^1.3.0"
    browserify "^16.1.0"
    chokidar "^1.0.0"
    defined "^1.0.0"
    outpipe "^1.1.0"
    through2 "^2.0.0"
    xtend "^4.0.0"

watchpack-chokidar2@^2.0.1:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/watchpack-chokidar2/-/watchpack-chokidar2-2.0.1.tgz#38500072ee6ece66f3769936950ea1771be1c957"
  dependencies:
    chokidar "^2.1.8"

watchpack@^1.5.0:
  version "1.7.5"
  resolved "http://npm-registry.persgroep.digital/watchpack/-/watchpack-1.7.5.tgz#1267e6c55e0b9b5be44c2023aed5437a2c26c453"
  dependencies:
    graceful-fs "^4.1.2"
    neo-async "^2.5.0"
  optionalDependencies:
    chokidar "^3.4.1"
    watchpack-chokidar2 "^2.0.1"

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "http://npm-registry.persgroep.digital/wbuf/-/wbuf-1.7.3.tgz#c1d8d149316d3ea852848895cb6a0bfe887b87df"
  dependencies:
    minimalistic-assert "^1.0.0"

webpack-core@^0.6.8:
  version "0.6.9"
  resolved "http://npm-registry.persgroep.digital/webpack-core/-/webpack-core-0.6.9.tgz#fc571588c8558da77be9efb6debdc5a3b172bdc2"
  dependencies:
    source-list-map "~0.1.7"
    source-map "~0.4.1"

webpack-dev-middleware@3.4.0:
  version "3.4.0"
  resolved "http://npm-registry.persgroep.digital/webpack-dev-middleware/-/webpack-dev-middleware-3.4.0.tgz#1132fecc9026fd90f0ecedac5cbff75d1fb45890"
  dependencies:
    memory-fs "~0.4.1"
    mime "^2.3.1"
    range-parser "^1.0.3"
    webpack-log "^2.0.0"

webpack-dev-middleware@3.5.1:
  version "3.5.1"
  resolved "http://npm-registry.persgroep.digital/webpack-dev-middleware/-/webpack-dev-middleware-3.5.1.tgz#9265b7742ef50f54f54c1d9af022fc17c1be9b88"
  dependencies:
    memory-fs "~0.4.1"
    mime "^2.3.1"
    range-parser "^1.0.3"
    webpack-log "^2.0.0"

webpack-dev-server@3.1.14:
  version "3.1.14"
  resolved "http://npm-registry.persgroep.digital/webpack-dev-server/-/webpack-dev-server-3.1.14.tgz#60fb229b997fc5a0a1fc6237421030180959d469"
  dependencies:
    ansi-html "0.0.7"
    bonjour "^3.5.0"
    chokidar "^2.0.0"
    compression "^1.5.2"
    connect-history-api-fallback "^1.3.0"
    debug "^3.1.0"
    del "^3.0.0"
    express "^4.16.2"
    html-entities "^1.2.0"
    http-proxy-middleware "~0.18.0"
    import-local "^2.0.0"
    internal-ip "^3.0.1"
    ip "^1.1.5"
    killable "^1.0.0"
    loglevel "^1.4.1"
    opn "^5.1.0"
    portfinder "^1.0.9"
    schema-utils "^1.0.0"
    selfsigned "^1.9.1"
    semver "^5.6.0"
    serve-index "^1.7.2"
    sockjs "0.3.19"
    sockjs-client "1.3.0"
    spdy "^4.0.0"
    strip-ansi "^3.0.0"
    supports-color "^5.1.0"
    url "^0.11.0"
    webpack-dev-middleware "3.4.0"
    webpack-log "^2.0.0"
    yargs "12.0.2"

webpack-log@^2.0.0:
  version "2.0.0"
  resolved "http://npm-registry.persgroep.digital/webpack-log/-/webpack-log-2.0.0.tgz#5b7928e0637593f119d32f6227c1e0ac31e1b47f"
  dependencies:
    ansi-colors "^3.0.0"
    uuid "^3.3.2"

webpack-merge@4.2.1:
  version "4.2.1"
  resolved "http://npm-registry.persgroep.digital/webpack-merge/-/webpack-merge-4.2.1.tgz#5e923cf802ea2ace4fd5af1d3247368a633489b4"
  dependencies:
    lodash "^4.17.5"

webpack-sources@1.3.0:
  version "1.3.0"
  resolved "http://npm-registry.persgroep.digital/webpack-sources/-/webpack-sources-1.3.0.tgz#2a28dcb9f1f45fe960d8f1493252b5ee6530fa85"
  dependencies:
    source-list-map "^2.0.0"
    source-map "~0.6.1"

webpack-sources@^1.1.0, webpack-sources@^1.2.0, webpack-sources@^1.3.0, webpack-sources@^1.4.0:
  version "1.4.3"
  resolved "http://npm-registry.persgroep.digital/webpack-sources/-/webpack-sources-1.4.3.tgz#eedd8ec0b928fbf1cbfe994e22d2d890f330a933"
  dependencies:
    source-list-map "^2.0.0"
    source-map "~0.6.1"

webpack-subresource-integrity@1.1.0-rc.6:
  version "1.1.0-rc.6"
  resolved "http://npm-registry.persgroep.digital/webpack-subresource-integrity/-/webpack-subresource-integrity-1.1.0-rc.6.tgz#37f6f1264e1eb378e41465a98da80fad76ab8886"
  dependencies:
    webpack-core "^0.6.8"

webpack@4.29.0:
  version "4.29.0"
  resolved "http://npm-registry.persgroep.digital/webpack/-/webpack-4.29.0.tgz#f2cfef83f7ae404ba889ff5d43efd285ca26e750"
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/helper-module-context" "1.7.11"
    "@webassemblyjs/wasm-edit" "1.7.11"
    "@webassemblyjs/wasm-parser" "1.7.11"
    acorn "^6.0.5"
    acorn-dynamic-import "^4.0.0"
    ajv "^6.1.0"
    ajv-keywords "^3.1.0"
    chrome-trace-event "^1.0.0"
    enhanced-resolve "^4.1.0"
    eslint-scope "^4.0.0"
    json-parse-better-errors "^1.0.2"
    loader-runner "^2.3.0"
    loader-utils "^1.1.0"
    memory-fs "~0.4.1"
    micromatch "^3.1.8"
    mkdirp "~0.5.0"
    neo-async "^2.5.0"
    node-libs-browser "^2.0.0"
    schema-utils "^0.4.4"
    tapable "^1.1.0"
    terser-webpack-plugin "^1.1.0"
    watchpack "^1.5.0"
    webpack-sources "^1.3.0"

websocket-driver@>=0.5.1:
  version "0.7.4"
  resolved "http://npm-registry.persgroep.digital/websocket-driver/-/websocket-driver-0.7.4.tgz#89ad5295bbf64b480abcba31e4953aca706f5760"
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "http://npm-registry.persgroep.digital/websocket-extensions/-/websocket-extensions-0.1.4.tgz#7f8473bc839dfd87608adb95d7eb075211578a42"

when@~3.6.x:
  version "3.6.4"
  resolved "http://npm-registry.persgroep.digital/when/-/when-3.6.4.tgz#473b517ec159e2b85005497a13983f095412e34e"

which-module@^1.0.0:
  version "1.0.0"
  resolved "http://npm-registry.persgroep.digital/which-module/-/which-module-1.0.0.tgz#bba63ca861948994ff307736089e3b96026c2a4f"

which-module@^2.0.0:
  version "2.0.1"
  resolved "http://npm-registry.persgroep.digital/which-module/-/which-module-2.0.1.tgz#776b1fe35d90aebe99e8ac15eb24093389a4a409"

which@1, which@^1.2.1, which@^1.2.10, which@^1.2.9, which@^1.3.1:
  version "1.3.1"
  resolved "http://npm-registry.persgroep.digital/which/-/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.0:
  version "1.1.5"
  resolved "http://npm-registry.persgroep.digital/wide-align/-/wide-align-1.1.5.tgz#df1d4c206854369ecf3c9a4898f1b23fbd9d15d3"
  dependencies:
    string-width "^1.0.2 || 2 || 3 || 4"

wordwrap@~0.0.2:
  version "0.0.3"
  resolved "http://npm-registry.persgroep.digital/wordwrap/-/wordwrap-0.0.3.tgz#a3d5da6cd5c0bc0008d37234bbaf1bed63059107"

worker-farm@^1.7.0:
  version "1.7.0"
  resolved "http://npm-registry.persgroep.digital/worker-farm/-/worker-farm-1.7.0.tgz#26a94c5391bbca926152002f69b84a4bf772e5a8"
  dependencies:
    errno "~0.1.7"

wrap-ansi@^2.0.0:
  version "2.1.0"
  resolved "http://npm-registry.persgroep.digital/wrap-ansi/-/wrap-ansi-2.1.0.tgz#d8fc3d284dd05794fe84973caecdd1cf824fdd85"
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"

wrappy@1:
  version "1.0.2"
  resolved "http://npm-registry.persgroep.digital/wrappy/-/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"

write@1.0.3:
  version "1.0.3"
  resolved "http://npm-registry.persgroep.digital/write/-/write-1.0.3.tgz#0800e14523b923a387e415123c865616aae0f5c3"
  dependencies:
    mkdirp "^0.5.1"

ws@~3.3.1:
  version "3.3.3"
  resolved "http://npm-registry.persgroep.digital/ws/-/ws-3.3.3.tgz#f1cf84fe2d5e901ebce94efaece785f187a228f2"
  dependencies:
    async-limiter "~1.0.0"
    safe-buffer "~5.1.0"
    ultron "~1.1.0"

x-is-string@^0.1.0:
  version "0.1.0"
  resolved "http://npm-registry.persgroep.digital/x-is-string/-/x-is-string-0.1.0.tgz#474b50865af3a49a9c4657f05acd145458f77d82"

xmlhttprequest-ssl@~1.5.4:
  version "1.5.5"
  resolved "http://npm-registry.persgroep.digital/xmlhttprequest-ssl/-/xmlhttprequest-ssl-1.5.5.tgz#c2876b06168aadc40e57d97e81191ac8f4398b3e"

xregexp@4.0.0:
  version "4.0.0"
  resolved "http://npm-registry.persgroep.digital/xregexp/-/xregexp-4.0.0.tgz#e698189de49dd2a18cc5687b05e17c8e43943020"

xtend@^4.0.0, xtend@^4.0.1, xtend@^4.0.2, xtend@~4.0.1:
  version "4.0.2"
  resolved "http://npm-registry.persgroep.digital/xtend/-/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"

y18n@^3.2.1:
  version "3.2.2"
  resolved "http://npm-registry.persgroep.digital/y18n/-/y18n-3.2.2.tgz#85c901bd6470ce71fc4bb723ad209b70f7f28696"

"y18n@^3.2.1 || ^4.0.0", y18n@^4.0.0:
  version "4.0.3"
  resolved "http://npm-registry.persgroep.digital/y18n/-/y18n-4.0.3.tgz#b5f259c82cd6e336921efd7bfd8bf560de9eeedf"

yallist@^2.1.2:
  version "2.1.2"
  resolved "http://npm-registry.persgroep.digital/yallist/-/yallist-2.1.2.tgz#1c11f9218f076089a47dd512f93c6699a6a81d52"

yallist@^3.0.0, yallist@^3.0.2, yallist@^3.1.1:
  version "3.1.1"
  resolved "http://npm-registry.persgroep.digital/yallist/-/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"

yargs-parser@^10.0.0, yargs-parser@^10.1.0:
  version "10.1.0"
  resolved "http://npm-registry.persgroep.digital/yargs-parser/-/yargs-parser-10.1.0.tgz#7202265b89f7e9e9f2e5765e0fe735a905edbaa8"
  dependencies:
    camelcase "^4.1.0"

yargs-parser@^13.0.0:
  version "13.1.2"
  resolved "http://npm-registry.persgroep.digital/yargs-parser/-/yargs-parser-13.1.2.tgz#130f09702ebaeef2650d54ce6e3e5706f7a4fb38"
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^5.0.1:
  version "5.0.1"
  resolved "http://npm-registry.persgroep.digital/yargs-parser/-/yargs-parser-5.0.1.tgz#7ede329c1d8cdbbe209bd25cdb990e9b1ebbb394"
  dependencies:
    camelcase "^3.0.0"
    object.assign "^4.1.0"

yargs@12.0.2:
  version "12.0.2"
  resolved "http://npm-registry.persgroep.digital/yargs/-/yargs-12.0.2.tgz#fe58234369392af33ecbef53819171eff0f5aadc"
  dependencies:
    cliui "^4.0.0"
    decamelize "^2.0.0"
    find-up "^3.0.0"
    get-caller-file "^1.0.1"
    os-locale "^3.0.0"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^2.0.0"
    which-module "^2.0.0"
    y18n "^3.2.1 || ^4.0.0"
    yargs-parser "^10.1.0"

yargs@13.1.0:
  version "13.1.0"
  resolved "http://npm-registry.persgroep.digital/yargs/-/yargs-13.1.0.tgz#b2729ce4bfc0c584939719514099d8a916ad2301"
  dependencies:
    cliui "^4.0.0"
    find-up "^3.0.0"
    get-caller-file "^2.0.1"
    os-locale "^3.1.0"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^3.0.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^13.0.0"

yargs@^7.0.0:
  version "7.1.2"
  resolved "http://npm-registry.persgroep.digital/yargs/-/yargs-7.1.2.tgz#63a0a5d42143879fdbb30370741374e0641d55db"
  dependencies:
    camelcase "^3.0.0"
    cliui "^3.2.0"
    decamelize "^1.1.1"
    get-caller-file "^1.0.1"
    os-locale "^1.4.0"
    read-pkg-up "^1.0.1"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^1.0.2"
    which-module "^1.0.0"
    y18n "^3.2.1"
    yargs-parser "^5.0.1"

yauzl@2.10.0, yauzl@^2.10.0:
  version "2.10.0"
  resolved "http://npm-registry.persgroep.digital/yauzl/-/yauzl-2.10.0.tgz#c7eb17c93e112cb1086fa6d8e51fb0667b79a5f9"
  dependencies:
    buffer-crc32 "~0.2.3"
    fd-slicer "~1.1.0"

yauzl@2.4.1:
  version "2.4.1"
  resolved "http://npm-registry.persgroep.digital/yauzl/-/yauzl-2.4.1.tgz#9528f442dab1b2284e58b4379bb194e22e0c4005"
  dependencies:
    fd-slicer "~1.0.1"

yeast@0.1.2:
  version "0.1.2"
  resolved "http://npm-registry.persgroep.digital/yeast/-/yeast-0.1.2.tgz#008e06d8094320c372dbc2f8ed76a0ca6c8ac419"

yn@3.1.1:
  version "3.1.1"
  resolved "http://npm-registry.persgroep.digital/yn/-/yn-3.1.1.tgz#1e87401a09d767c1d5eab26a6e4c185182d2eb50"

z-schema@^4.2.2:
  version "4.2.4"
  resolved "http://npm-registry.persgroep.digital/z-schema/-/z-schema-4.2.4.tgz#73102a49512179b12a8ec50b1daa676b984da6e4"
  dependencies:
    lodash.get "^4.4.2"
    lodash.isequal "^4.5.0"
    validator "^13.6.0"
  optionalDependencies:
    commander "^2.7.1"

zone.js@^0.8.29:
  version "0.8.29"
  resolved "http://npm-registry.persgroep.digital/zone.js/-/zone.js-0.8.29.tgz#8dce92aa0dd553b50bc5bfbb90af9986ad845a12"
