{"name": "recruiter-frontend", "version": "0.0.1", "license": "MIT", "scripts": {"ng": "ng", "postinstall": "node node_modules/phantomjs-prebuilt/install.js && npm rebuild node-sass", "start-nvb": "ng run nvb:serve:dev", "start-b2b": "ng run b2b:serve:dev", "start-iol": "ng run iol:serve:dev", "start-e2e": "ng run nvb:serve:e2e", "start-nvb-pro": "ng run nvb:serve:dev-pro", "build-b2b-acc": "ng run b2b:build:acc", "build-b2b-pro": "ng run b2b:build:pro", "build-nvb-acc": "ng run nvb:build:acc", "build-nvb-pro": "ng run nvb:build:pro", "build-iol-acc": "ng run iol:build:acc", "build-iol-pro": "ng run iol:build:pro", "test": "yarn install && ng test --watch=false --code-coverage --progress=false", "test-watch": "ng test --watch=true --code-coverage", "lint": "ng run nvb:lint && node node_modules/stylelint/bin/stylelint.js '**/*.scss'", "format": "prettier --config ./.prettierrc.yml --write './**/*.ts'", "cypress-run": "cypress run --record --key e1533d07-ad91-4159-805b-add250732b95 --spec 'cypress/tests/features/*'", "cypress-debug": "cypress open", "mocks": "node swagger-mocks/generate-json-mocks.js && node swagger-mocks/server/index.js"}, "private": true, "dependencies": {"@angular/common": "^7.2.6", "@angular/compiler": "^7.2.6", "@angular/core": "^7.2.6", "@angular/forms": "^7.2.6", "@angular/http": "^7.2.6", "@angular/platform-browser": "^7.2.6", "@angular/platform-browser-dynamic": "^7.2.6", "@angular/pwa": "^0.13.3", "@angular/router": "^7.2.6", "@angular/service-worker": "^7.2.6", "@ngrx/core": "^1.2.0", "@ngrx/effects": "^7.2.0", "@ngrx/store": "^7.2.0", "bootstrap": "^4.3.1", "browserlist": "^1.0.1", "caniuse-lite": "^1.0.30001031", "core-js": "^2.6.5", "ngrx-store-logger": "^0.2.2", "raven-js": "^3.27.0", "rxjs": "^6.4.0", "styleguide": "https://github.com/Vnumedia/styleguide#fb63f4fa6b51c8eecb32ff3589fb0f2c3929edb3", "zone.js": "^0.8.29", "@dpgr/pdf-forger": "1.0.8"}, "devDependencies": {"@angular-devkit/build-angular": "~0.13.3", "@angular/cli": "^7.3.3", "@angular/compiler-cli": "^7.2.6", "@ngrx/store-devtools": "^7.2.0", "@types/chai": "^4.1.7", "@types/chai-as-promised": "7.1.0", "@types/cucumber": "^4.0.5", "@types/jasmine": "3.3.9", "@types/jasminewd2": "^2.0.6", "@types/node": "~11.9.4", "@types/selenium-webdriver": "^3.0.15", "body-parser": "^1.18.3", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "codelyzer": ">=5.0.0-rc.0 <6.0.0||>=2.3.1 <3.0.0||>=4.0.0-beta <5.0.0", "cucumber": "^5.1.0", "cypress": "3.2.0", "cypress-cucumber-preprocessor": "1.11.0", "express": "^4.16.4", "faker": "^4.1.0", "jasmine-core": "~3.3.0", "jasmine-spec-reporter": "^4.2.1", "karma": "~4.0.0", "karma-cli": "~2.0.0", "karma-coverage-istanbul-reporter": "^2.0.5", "karma-jasmine": "~2.0.1", "karma-jasmine-html-reporter": "^1.4.0", "karma-chrome-launcher": "^2.2.0", "karma-phantomjs-launcher": "^1.0.4", "phantomjs-prebuilt": "^2.1.16", "prettier": "^1.16.4", "stylelint": "^9.10.1", "stylelint-order": "^2.0.0", "swagger-express-middleware": "^2.0.1", "ts-node": "^8.0.2", "tslint": "~5.12.1", "typescript": ">=3.1.1 <3.2.0"}, "cypress-cucumber-preprocessor": {"step_definitions": "cypress/tests/step_definitions/"}}