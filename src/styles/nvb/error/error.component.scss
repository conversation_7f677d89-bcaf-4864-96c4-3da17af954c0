@import "components";

.error-component {
  header .header-body,
  footer .footer-body,
  main {
    @include make-container();
    @include make-container-max-widths();
  }

  header {
    h1 {
      margin-top: 8%;
    }

    .header-body {
      padding-bottom: 200px;
    }

    @include media-breakpoint-down(sm) {
      margin: 0 1rem;

      .header-body {
        padding-bottom: 150px;
      }
    }
  }

  main {
    position: relative;

    .block {
      background-image: url("#{$project-images-folder}balloon.svg");
      background-repeat: no-repeat;
      background-size: cover;
      height: 190px;
      position: absolute;
      right: 0;
      top: -160px;
      width: 170px;

      @include media-breakpoint-up(md) {
        height: 376px;
        top: -314px;
        width: 336px;
      }
    }
  }
}
