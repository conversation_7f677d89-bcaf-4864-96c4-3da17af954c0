.btn {
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-family: inherit;
  font-size: 15px;
  font-weight: 600;
  line-height: 40px;
  text-transform: lowercase;
  transition: 0.2s background ease;
  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  &-block {
    display: block;
    text-align: center;
  }

  &-transparent {
    background: $dpes-white;
    border: 1px solid $dpes-dull-orange;
    color: $dpes-dull-orange;
    line-height: 40px;
    &:hover {
      background: $dpes-dull-orange;
      border: 1px solid $dpes-dull-orange;
      color: $dpes-white;
    }
  }

  &-primary {
    background: $dpes-dull-orange;
    border: 1px solid $dpes-dull-orange;
    color: $dpes-white;
    display: inline-block;
    padding: 0 30px;
    &:hover {
      background: darken($dpes-dull-orange, 10%);
      border: 1px solid $dpes-dull-orange;
      color: $dpes-white;
    }

    &.disabled {
      color: $dpes-white;
    }
  }
}

.btn-block + .btn-block {
  @include media-breakpoint-down(md) {
    margin-top: 0;
  }
}
