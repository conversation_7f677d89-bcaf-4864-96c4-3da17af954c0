@import "components";

.modal {
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  bottom: 0;
  box-sizing: border-box;
  color: $generic-text-color;
  display: flex;
  height: 100vh;
  left: 0;
  padding: 0 15px;
  position: fixed;
  right: 0;
  top: 0;
  width: 100vw;
  z-index: 2000;

  @include media-breakpoint-up(md) {
    padding: 0;
  }

  &-box {
    background: $generic-background-color;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.15);
    margin: 0 auto;
    max-width: 500px;
    padding: 30px 30px 30px 30px;
    position: relative;
    width: 500px;

    &--scrollable {
      max-height: 100vh;
      overflow-y: scroll;
    }

    &--larger {
      max-width: 800px;
      width: 800px;
    }
  }

  &-contents {
    color: $generic-text-color;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 22px;
  }

  &-actions {
    display: flex;
    flex-direction: column;
    text-align: center;
    @include media-breakpoint-up(md) {
      display: block;
      text-align: right;
    }
  }

  .close-icon {
    background-image: url("#{$project-icons-folder}close.svg");
    background-repeat: no-repeat;
    background-size: 100%;
    cursor: pointer;
    display: inline-block;
    height: 16px;
    margin-left: 4px;
    position: absolute;
    right: 15px;
    top: 15px;
    vertical-align: middle;
    width: 16px;
  }

  h3 {
    color: $generic-text-color;
    font-size: 18px;
    letter-spacing: 0;
    line-height: 22px;
    margin: 0 0 30px;
    @include media-breakpoint-up(md) {
      font-size: 20px;
    }
  }

  h4 {
    color: $generic-text-color;
    font-size: 16px;
  }

  p {
    margin: 12px 0;
  }

  .confirm {
    margin-bottom: 30px;
    margin-top: 20px;
    order: 1;
    text-decoration: none;

    @include media-breakpoint-up(md) {
      margin-bottom: 0;
      margin-left: 20px;
      margin-top: 0;
    }
  }
}
