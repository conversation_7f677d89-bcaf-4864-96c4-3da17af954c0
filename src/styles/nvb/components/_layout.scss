main {
  .row {
    @include outer-container();
  }

  .page {
    background-color: $dpes-white;
    margin: 1px 0 -1em 0;
    min-height: 100%;
    padding: 15px 10px 33px;
    @include media-breakpoint-up(md) {
      margin-left: auto;
      margin-right: auto;
      margin-top: 12px;
      padding: 20px 15px 50px;
    }
  }

  ul {
    list-style: none;
    padding: 0;

    li {
      line-height: 24px;
      padding-left: 24px;
      position: relative;
      &::before {
        background-color: $dpes-black;
        border-radius: 50%;
        content: "";
        display: block;
        height: 5px;
        left: 5px;
        position: absolute;
        top: 10px;
        width: 5px;
      }
    }
  }
}
