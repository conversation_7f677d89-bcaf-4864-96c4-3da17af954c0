@import "components";

$height: 44px;

:host {
  background: $dpes-white;
  display: block;
  position: relative;
  z-index: 98;

  &::after {
    clear: both;
    content: "";
    display: table;
  }

  @include media-breakpoint-up(md) {
    border-bottom-left-radius: 5px;
    border-top-left-radius: 5px;
  }

  .search-input {
    border: none;
    border-radius: 0;
    color: #666;
    font-family: $font-family-default;
    font-size: $search-bar-font-size-mobile;
    font-weight: 400;
    height: auto;
    line-height: 44px;
    margin: 0;
    max-width: 100%;
    outline: 0;
    overflow: hidden;
    padding: 0 20px;
    resize: none;
    width: calc(60% - 25px);
    @include media-breakpoint-up(md) {
      border-bottom-left-radius: 5px;
      border-top-left-radius: 5px;
      float: left;
      font-size: $search-bar-font-size-desktop;
      line-height: $height;
      min-height: $height;
    }
    &::placeholder {
      color: $search-placeholder-color;
    }
    &.focus {
      overflow: auto;
    }
  }

  .icon-delete {
    color: #9f9ca5;
    cursor: pointer;
    display: block;
    float: right;
    margin-top: 15px;
    padding: 15px 20px 15px 10px;
    position: relative;
    @include media-breakpoint-up(md) {
      float: left;
      height: 15px;
      padding: 0;
      width: 15px;
    }
    &::before, &::after {
      background-color: #9f9ca5;
      border: 1px solid #9f9ca5;
      content: "";
      height: 1px;
      left: 0;
      position: absolute;
      top: 7px;
      width: 15px;
    }
    &::before {
      -ms-transform: rotate(-45deg);
      -moz-transform: rotate(-45deg);
      -o-transform: rotate(-45deg);
      -webkit-transform: rotate(-45deg);
      transform: rotate(-45deg);
    }
    &::after {
      -ms-transform: rotate(-135deg);
      -moz-transform: rotate(-135deg);
      -o-transform: rotate(-135deg);
      -webkit-transform: rotate(-135deg);
      transform: rotate(-135deg);
    }
  }

  .field-search {
    position: relative;
    @include media-breakpoint-up(md) {
      float: right;
      width: 40%;
    }
    .field-search-in {
      color: #666;
      float: left;
      font-size: $search-bar-font-size-mobile;
      font-weight: 400;
      line-height: 44px;
      padding-left: 20px;
      width: 40px;
      @include media-breakpoint-up(md) {
        font-size: $search-bar-font-size-desktop;
        line-height: $height;
        padding-left: 0;
        width: 22px;
      }
    }
    .form-select {
      float: right;
      width: calc(100% - 40px);
      @include media-breakpoint-up(md) {
        width: calc(100% - 22px);
      }
      select {
        border: none;
        color: #666;
        font-size: $search-bar-font-size-mobile;
        font-weight: 400;
        height: 44px;
        line-height: 44px;
        margin-bottom: 0;
        @include media-breakpoint-up(md) {
          font-size: $search-bar-font-size-desktop;
          height: $height;
          line-height: $height;
        }
      }
    }
  }
}
