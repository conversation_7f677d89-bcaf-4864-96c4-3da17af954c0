@import "../../../node_modules/bootstrap/scss/mixins";
@import "variables";

body {
  font-size: 15px;
  @include media-breakpoint-up(md) {
    font-size: 17px;
    line-height: 1.47;
  }
}

h1, h2, h3, h4, h5 {
  color: $dpes-dusty-blue;
  font-family: $font-family-default;
  font-weight: 300;
}

h1, .h1 {
  font-size: 2rem;
  font-weight: 300;
  line-height: 36px;

  @include media-breakpoint-up(md) {
    font-size: 2.5rem;
    line-height: 1.1;
  }
}
h2, .h2 {
  line-height: 1.3077;
  @include media-breakpoint-up(md) {
    font-size: 1.76rem;
    line-height: 1.2;
  }
}
h3, .h3 {
  line-height: 1.2727;
  @include media-breakpoint-up(md) {
    font-size: 1.41rem;
    line-height: 1.25;
  }
}
h4, .h4 {
  font-weight: 600;
  line-height: 1.3333;
  @include media-breakpoint-up(md) {
    font-size: 1.176rem;
    line-height: 1.3;
  }
}
h5, .h5 {
  font-weight: 400;
  line-height: 1.3333;
  @include media-breakpoint-up(md) {
    font-size: 1.176rem;
    line-height: 1.3;
  }
}
h6, .h6 {
  line-height: 1.375;
  @include media-breakpoint-up(md) {
    font-size: 1rem;
    line-height: 1.76;
  }
}

p {
  color: $dpes-dark-grey;
}
