/*
*   From styleguide
*/

// Primary brand colour
$dpes-dusty-blue:     #5ba0b3;
$dpes-seafoam-blue:   #69b6cc;
$dpes-dull-orange:    #df8d2f;

// Other colours
$dpes-brownish-orange:      #cd7e23;
$dpes-brownish-orange-two:  #bd7421;
$dpes-light-khaki:          #f2d6b6;
$dpes-mango:                #ff9320;

// Greyshade / typography colours
$dpes-dark-grey:      #2e3233;
$dpes-gunmetal: #5c6466;
$dpes-cool-grey: #969c9e;
$dpes-silver: #babebf;
$dpes-silver-two: #d9ddde;
$dpes-pale-grey: #edeff0;
/*
*   Other colours
*/

// Colors needed for bootstrap
$dpes-white:  #fff;
$dpes-black:  #000;
$dpes-black-light: #030401;
$dpes-red:    #d0011b;
$dpes-yellow: #fbe05d;
$dpes-green:  #1abc9c;
$dpes-teal:   #5bc0de;
$dpes-pink:   #ff5b77;
$dpes-purple: #7c348b;
$dpes-gray-dark: #292b2c;
$dpes-gray: #464a4c;
$dpes-grey-soft: #ced4da;
$dpes-gray-light: #636c72;
$dpes-gray-light2: #9b9b9b;
$dpes-gray-lighter: #eceeef;
$dpes-gray-lightest: #f7f7f7;

// Fonts
$text-color: $dpes-dark-grey;
$font-family: Palanquin, Arial, Helvetica, sans-serif;
$font-family-default: $font-family;
$primary-font: ("Palanquin", Helvetica, Arial, sans-serif);
$medium-font: ("Palanquin", Helvetica, Arial, sans-serif);
$notification-font: ("Roboto Condensed", Helvetica, Arial, sans-serif);

// Paths
$project-images-folder: "/assets/b2b/images/";
$project-icons-folder: "/assets/b2b/icons/";
$project-licenses-folder: "/assets/b2b/licenses/";
$project-operators-folder: "/assets/b2b/operators/";
