@import "variables";
/*
*   BOOTSTRAP 4
*/

//== Container sizes
//
//## Define the maximum width of `.container` for different screen sizes.

// Medium screen / desktop

// Large screen / wide desktop

// General variable structure
//
// Variable format should follow the `$component-modifier-state-property` order.

$dpes-white: $dpes-white;
$black: $dpes-black;

$blue:    $dpes-seafoam-blue;
$indigo:  #6610f2;
$purple:  $dpes-purple;
$pink:    $dpes-pink;
$red:     $dpes-red;
$orange:  $dpes-dull-orange;
$yellow:  $dpes-yellow;
$green: $dpes-green;
$teal: $dpes-teal;
$cyan:    #17a2b8;

$primary: $dpes-dull-orange;
$secondary: $dpes-gray-light;
$success:  $dpes-green;
$info: $dpes-seafoam-blue;
$warning: $dpes-yellow;
$danger: $dpes-red;
$light: $dpes-gray-lighter;
$dark: $dpes-gray-lightest;

// Colors
$gray-dark: $dpes-gray-dark;
$gray: $dpes-gray;
$gray-light: $dpes-gray-light;
$gray-lighter: $dpes-gray-lighter;
$gray-lightest: $dpes-gray-lightest;

$brand-primary-darker: $dpes-brownish-orange;
$brand-primary-darkest: $dpes-brownish-orange-two;

// Options
//
// Quickly modify global styling by enabling or disabling optional features.

$enable-rounded:            true !default;
$enable-shadows:            false !default;
$enable-gradients:          false !default;
$enable-transitions:        true !default;
$enable-hover-media-query:  false !default;
$enable-grid-classes:       true !default;
$enable-print-styles:       true !default;

// Spacing
//
// Control the default styling of most Bootstrap elements by modifying these
// variables. Mostly focused on spacing.
// You can add more entries to the $spacers map, should you need more variation.
// stylelint-disable
$spacer: 1rem;
$spacers: () !default;
$spacers: map-merge((
  0: 0,
  1: ($spacer * .25),
  2: ($spacer * .5),
  3: $spacer,
  4: ($spacer * 1.5),
  5: ($spacer * 2.5)
), $spacers);

$border-width: 1px !default;

// This variable affects the `.h-*` and `.w-*` classes.
$sizes: (
  25: 25%,
  50: 50%,
  75: 75%,
  100: 100%
) !default;

// Body
//
// Settings for the `<body>` element.

$body-bg:       $dpes-white;
$body-color:    $text-color;
$inverse-bg:    $gray-dark !default;
$inverse-color: $gray-lighter !default;

// Links
//
// Style anchor elements.

$link-color:            $primary !default;
$link-decoration:       none !default;
$link-hover-color:      darken($link-color, 15%) !default;
$link-hover-decoration: underline !default;

// Grid breakpoints
//
// Define the minimum dimensions at which your layout will change,
// adapting to different screen sizes, for use in media queries.

$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px
) !default;

// Grid containers
//
// Define the maximum width of `.container` for different screen sizes.

$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1170px
) !default;

// Grid columns
//
// Set the number of columns and specify the width of the gutters.

$grid-columns:               12 !default;
$grid-gutter-width:          30px !default;

// Fonts
//
// Font, line-height, and color for body text, headings, and more.

$font-family-sans-serif: -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !default;
$font-family-serif:      Georgia, "Times New Roman", Times, serif !default;
$font-family-monospace:  Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace !default;
$font-family-base:      $primary-font;

$font-size-base: 1rem !default; // Assumes the browser default, typically `16px`
$font-size-lg:   1.25rem !default;
$font-size-sm:   0.875rem !default;
$font-size-xs:   0.75rem !default;

$font-weight-normal: normal !default;
$font-weight-bold: bold !default;

$font-weight-base: $font-weight-normal !default;
$line-height-base: 1.53 !default;

$font-size-h1: 2.13rem !default;
$font-size-h2: 1.73rem !default;
$font-size-h3: 1.467rem !default;
$font-size-h4: 1.2rem !default;
$font-size-h5: 1.2rem !default;
$font-size-h6: 1.067rem !default;

$headings-margin-bottom: ($spacer / 2) !default;
$headings-font-family:   inherit !default;
$headings-font-weight:   600 !default;
$headings-line-height:   1.25 !default;
$headings-color:         $dpes-seafoam-blue !default;

$display1-size: 6rem !default;
$display2-size: 5.5rem !default;
$display3-size: 4.5rem !default;
$display4-size: 3.5rem !default;

$display1-weight:     300 !default;
$display2-weight:     300 !default;
$display3-weight:     300 !default;
$display4-weight:     300 !default;
$display-line-height: $headings-line-height !default;

$lead-font-size:   1.33rem !default;
$lead-font-weight: 300 !default;

$small-font-size: 80% !default;

$text-muted: $gray-light !default;

$abbr-border-color: $gray-light !default;

$blockquote-small-color:  $gray-light !default;
$blockquote-font-size:    ($font-size-base * 1.25) !default;
$blockquote-border-color: $gray-lighter !default;
$blockquote-border-width: 0.25rem !default;

$hr-border-color: rgba($black, 0.1) !default;
$hr-border-width: $border-width !default;

$mark-padding: 0.2em !default;

$dt-font-weight: $font-weight-bold !default;

$kbd-box-shadow:         inset 0 - 0.1rem 0 rgba($black, 0.25) !default;
$nested-kbd-font-weight: $font-weight-bold !default;

$list-inline-padding: 5px !default;

// Components
//
// Define common padding and border radius sizes and more.

$line-height-lg:         (4 / 3) !default;
$line-height-sm:         1.5 !default;

$border-radius:          0.25rem !default;
$border-radius-lg:       0.3rem !default;
$border-radius-sm:       0.2rem !default;

$component-active-color: $dpes-white;
$component-active-bg:    $dpes-seafoam-blue;

$caret-width:            0.3em !default;

$transition-base:        all 0.2s ease-in-out !default;
$transition-fade:        opacity 0.15s linear !default;
$transition-collapse:    height 0.35s ease !default;

// Buttons
//
// For each of Bootstrap's buttons, define text, background and border color.

$btn-padding-x:                  20px !default;
$btn-padding-y:                  0 !default;
$btn-line-height:                43px !default;
$btn-font-weight:                600;
$btn-box-shadow:                 inset 0 1px 0 rgba($dpes-white, 0.15), 0 1px 1px rgba($black, 0.075) !default;
$btn-focus-box-shadow:           0 0 0 2px rgba($primary, 0.25) !default;
$btn-active-box-shadow:          inset 0 3px 5px rgba($black, 0.125) !default;

$btn-primary-color:              $dpes-white !default;
$btn-primary-bg:                 $primary !default;
$btn-primary-border:             $btn-primary-bg !default;

$btn-primary-hover-color:        $dpes-white !default;
$btn-primary-hover-bg:           $brand-primary-darker !default;
$btn-primary-hover-border:       $brand-primary-darker !default;

$btn-primary-pressed-color:      $dpes-white !default;
$btn-primary-pressed-bg:         $brand-primary-darkest !default;
$btn-primary-pressed-border:     $brand-primary-darkest !default;

$btn-secondary-color:            $primary !default;
$btn-secondary-bg:               $dpes-white !default;
$btn-secondary-border:           $primary !default;

$btn-secondary-hover-color:      $brand-primary-darker !default;
$btn-secondary-hover-bg:         $dpes-white !default;
$btn-secondary-hover-border:     $brand-primary-darker !default;

$btn-secondary-pressed-color:    $brand-primary-darkest !default;
$btn-secondary-pressed-bg:       $dpes-white !default;
$btn-secondary-pressed-border:   $brand-primary-darkest !default;

$btn-info-color:                 $dpes-white !default;
$btn-info-bg:                    $info !default;
$btn-info-border:                $btn-info-bg !default;

$btn-success-color:              $dpes-white !default;
$btn-success-bg:                 $success !default;
$btn-success-border:             $btn-success-bg !default;

$btn-warning-color:              $dpes-white !default;
$btn-warning-bg:                 $warning !default;
$btn-warning-border:             $btn-warning-bg !default;

$btn-danger-color:               $dpes-white !default;
$btn-danger-bg:                  $danger !default;
$btn-danger-border:              $btn-danger-bg !default;

$btn-link-disabled-color:        $gray-light !default;

$btn-padding-x-sm:               1.75rem !default;
$btn-padding-y-sm:               0.25rem !default;

$btn-padding-x-lg:               2rem !default;
$btn-padding-y-lg:               0.5rem !default;

$btn-block-spacing-y:            0.5rem !default;
$btn-toolbar-margin:             0.5rem !default;

// Allows for customizing button radius independently from global border radius
$btn-border-radius:              2px;
$btn-border-radius-lg:           2px;
$btn-border-radius-sm:           2px;

$btn-transition:                 all 0.2s ease-in-out !default;

// Pagination

$pagination-padding-x:                0.75rem !default;
$pagination-padding-y:                0.5rem !default;
$pagination-padding-x-sm:             0.5rem !default;
$pagination-padding-y-sm:             0.25rem !default;
$pagination-padding-x-lg:             1.5rem !default;
$pagination-padding-y-lg:             0.75rem !default;
$pagination-line-height:              1.25 !default;

$pagination-color:                     $link-color !default;
$pagination-bg:                        $dpes-white !default;
$pagination-border-width:              $border-width !default;
$pagination-border-color:              #ddd !default;

$pagination-hover-color:               $link-hover-color !default;
$pagination-hover-bg:                  $gray-lighter !default;
$pagination-hover-border:              #ddd !default;

$pagination-active-color:              $dpes-white !default;
$pagination-active-bg:                 $primary !default;
$pagination-active-border:             $primary !default;

$pagination-disabled-color:            $gray-light !default;
$pagination-disabled-bg:               $dpes-white !default;
$pagination-disabled-border:           #ddd !default;

// MODAL
$zindex-modal:                         10500 !default;

// Cards
$card-spacer-y:                     .75rem !default;
$card-spacer-x:                     1.25rem !default;
$card-border-width:                 0;
$card-border-radius:                $border-radius !default;
$card-border-color:                 rgba($black, .125) !default;
$card-inner-border-radius:          calc(#{$card-border-radius} - #{$card-border-width}) !default;
$card-cap-bg:                       rgba($black, .03) !default;
$card-bg:                           $dpes-white !default;

$card-img-overlay-padding:          1.25rem !default;

$card-group-margin:                 ($grid-gutter-width / 2) !default;
$card-deck-margin:                  $card-group-margin !default;

$card-columns-count:                3 !default;
$card-columns-gap:                  1.25rem !default;
$card-columns-margin:               $card-spacer-y !default;
