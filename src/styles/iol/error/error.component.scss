@import "components";

.error-component {
  padding: 1rem;

  @include media-breakpoint-up(lg) {
    padding: 3rem 0;
  }

  .error-component-body {
    @include make-container();
    @include make-container-max-widths();

    border-radius: 5px;
    box-shadow: 0 8px 8px 0 $steel-grey;
    padding: 2rem;

    @include media-breakpoint-up(md) {
      padding: 7rem;
    }
  }

  main {
    margin-top: 2rem;

    .block {
      animation-duration: 4s;
      animation-iteration-count: infinite;
      animation-name: fly;
      background-image: url("#{$project-images-folder}yoga_man.svg");
      background-repeat: no-repeat;
      bottom: 80px;
      height: 225px;
      position: absolute;
      right: 55px;
      width: 131px;
      z-index: 5;

      @include media-breakpoint-up(xl) {
        bottom: 130px;
        right: 300px;
      }
    }

    &:after {
      animation-duration: 4s;
      animation-iteration-count: infinite;
      animation-name: shadow;
      background-image: url("#{$project-images-folder}yoga_man_shadow.svg");
      background-size: cover;
      bottom: 43px;
      content: "";
      height: 70px;
      position: absolute;
      right: 67px;
      width: 119px;

      @include media-breakpoint-up(xl) {
        bottom: 100px;
        right: 310px;
      }
    }
  }

  footer {
    padding-bottom: 10rem;

    span {
      display: block;
      font-size: 0.9rem;

      @include media-breakpoint-down(md) {
        display: none;
      }
    }
  }
}

@keyframes fly {
  50% {
    transform: translateY(-30px);
  }
}

@keyframes shadow {
  50% {
    transform: scale(0.8);
  }
}
