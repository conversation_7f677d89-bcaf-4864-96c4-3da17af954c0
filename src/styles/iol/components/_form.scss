@import "components";

.value {
  background-color: white;
  border-color: $border-color;
  font-size: 16px;
  height: 38px;
  margin-bottom: 15px;
  padding: 8px;
  position: relative;
  width: 100%;
  &:last-child {
    margin-bottom: 25px;
  }
}

.form-select {
  position: relative;
  &::after {
    border: 3px solid $blue;
    border-right: none;
    border-top: none;
    color: #000;
    content: "";
    font-size: 18px;
    height: 12px;
    padding: 0 0 2px;
    pointer-events: none;
    position: absolute;
    right: 14px;
    top: 15px;
    -moz-transform: rotate(-45deg) skew(10deg, 10deg);
    -ms-transform: rotate(-45deg) skew(10deg, 10deg);
    -o-transform: rotate(-45deg) skew(10deg, 10deg);
    -webkit-transform: rotate(-45deg) skew(10deg, 10deg);
    transform: rotate(-45deg) skew(10deg, 10deg);
    width: 12px;
  }
  &::before {
    content: "";
    display: block;
    height: 20px;
    pointer-events: none;
    position: absolute;
    right: 6px;
    top: 0;
    width: 20px;
  }

  select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: inline-block;
    font-family: $font-family-default;
    font-size: $filters-label-fontsize;
    height: 44px;
    line-height: 42px;
    outline: none;
    padding: 0 24px 0 8px;
    &::-ms-expand {
      display: none;
    }
  }
  &.form-select-border select {
    border: 1px solid $iol-grey-soft;
  }

  @media screen and (-webkit-min-device-pixel-ratio: 0) {
    select {
      padding-right: 18px;
    }
  }
}

.input {
  border-color: $iol-grey-soft;
  border-radius: 4px;
  border-style: solid;
  border-width: 1px;
  font-family: $font-family-default;
  font-size: 14px;
  height: 44px;
  line-height: 44px;
  outline: none;
  padding: 0 12px;
  transition: border-color 0.5s ease;
  width: 100%;

  &:focus {
    border-color: $border-color-focus;
  }
  &.error {
    border-color: $iol-error;
  }
}
