@import "components";

.btn {
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-family: inherit;
  font-size: 15px;
  font-weight: 600;
  line-height: 40px;
  text-transform: lowercase;
  transition: 0.2s background ease;
  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  &-block {
    display: block;
    text-align: center;
  }

  &-transparent {
    background-color: $iol_white;
    border: 1px solid $iol-grey-dark;
    color: $iol-grey-dark;
    line-height: 40px;
    &:hover {
      background-color: $iol_white;
      border: 1px solid darken($iol-grey-dark, 10%);
      color: darken($iol-grey-dark, 10%);
    }
  }

  &-primary {
    background: $iol-yellow;
    border: 1px solid $iol-yellow;
    color: $iol_blue;
    display: inline-block;
    padding: 0 30px;
    &:hover {
      background: darken($iol-yellow, 10%);
      border: 1px solid darken($iol-yellow, 10%);
      color: $iol_blue;
    }

    &.disabled {
      color: $iol_white;
    }
  }
}

.btn-block + .btn-block {
  @include media-breakpoint-down(md) {
    margin-top: 0;
  }
}
