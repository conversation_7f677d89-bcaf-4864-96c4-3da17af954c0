import { Component } from '@angular/core';
import { NavigationEnd, NavigationStart, Router, ActivatedRoute, ActivatedRouteSnapshot } from '@angular/router';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { environment } from '../environments/environment';
import { Title } from '@angular/platform-browser';
import { AppState } from './types/app-state';
import { AnalyticsService } from './shared/services';
import { ProfileAPIService } from './shared/api';

@Component({
  selector: 'app-recruiter-frontend',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent {
  public filter: Observable<boolean>;
  public site = environment.site;
  public isLoading = false;
  public user: Observable<any>;
  public locationAssignFn = window.location.assign;

  constructor(
    private router: Router,
    public store: Store<AppState>,
    private titleService: Title,
    private activatedRoute: ActivatedRoute,
    private analyticsService: AnalyticsService,
    private profileApiService: ProfileAPIService
  ) {
    this.handleRouterEvents();
    this.handleFilter();
    this.handleSpinner();
    this.handleLoggedInRecruiter();
  }

  private handleRouterEvents(): void {
    this.router.events.subscribe(event => {
      if (event instanceof NavigationStart && event.url.substr(0, 3) === '/#/') {
        this.locationAssignFn(event.url.substr(3));
      } else if (event instanceof NavigationEnd) {
        const firstRoute = this.getFirstActivatedRoute(this.activatedRoute);

        if (firstRoute.outlet === 'primary') {
          this.setPageTitle(firstRoute.snapshot.data.title);
        }

        if (this.isDataLayerChangeUrlEvent(firstRoute.snapshot, event.url)) {
          this.analyticsService.pushPageViewToDataLayer(firstRoute.snapshot.data.trackingEvent);
        }
      }
    });

    this.activatedRoute.queryParams.subscribe(() => {
      const firstRoute = this.getFirstActivatedRoute(this.activatedRoute);
      if (firstRoute.snapshot && firstRoute.snapshot.data.trackingEvent) {
        this.analyticsService.pushPageViewToDataLayer(firstRoute.snapshot.data.trackingEvent);
      }
    });
  }

  private handleFilter(): void {
    this.filter = this.store.select(s => s && s.filter && s.filter.open);
  }

  private handleSpinner(): void {
    this.store.subscribe(state => {
      this.isLoading = state.spinner.mainSpinnerShown;
    });
  }

  private getFirstActivatedRoute(route: ActivatedRoute): ActivatedRoute {
    while (route.firstChild) {
      route = route.firstChild;
    }

    return route;
  }

  private setPageTitle(title: string | undefined): void {
    this.titleService.setTitle(title !== undefined && title !== '' ? title : environment.titles.default);
  }

  private isDataLayerChangeUrlEvent(routeSnapshot: ActivatedRouteSnapshot, currentUrl: string): boolean {
    if (!routeSnapshot || !routeSnapshot.data.trackingEvent) {
      return false;
    }

    if (routeSnapshot.data.skipBaseUrlTrackingEvent && currentUrl.indexOf('?') < 0) {
      return false;
    }

    return true;
  }

  private handleLoggedInRecruiter() {
    this.user = this.profileApiService.getRecruiter();
  }
}
