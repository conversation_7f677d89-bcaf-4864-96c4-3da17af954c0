import { Filter } from '../store/models/filter.modal';
import { Modals } from '../store/models/modals.model';
import { Search } from '../store/models/search.model';
import { Spinner } from '../store/models/spinner.modal';

export interface AppState {
  filter: { open: boolean; filters: Filter[] };
  search: Search;
  modals: Modals;
  spinner: Spinner;
}

export interface Account {
  emailAddress: string;
  firstName: string;
  lastName: string;
  hasSubscription: boolean;
  hasDeductibleSubscription: boolean;
  hasValidProduct: boolean;
  amountOfCredits: number;
  companyVerified: boolean;
}
