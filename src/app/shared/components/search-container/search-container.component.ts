import { Component, Output, EventEmitter, ChangeDetectionStrategy, ViewChild, OnInit, OnDestroy, ChangeDetectorRef } from '@angular/core';
import { Subject, Observable } from 'rxjs';
import { Store } from '@ngrx/store';
import { AppState } from 'app/types/app-state';
import { takeUntil } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import * as SearchActions from '../../../store/actions/search/search.actions';
import { SearchService } from '../../services';
import { SearchBarComponent } from './search-bar/search-bar.component';
import { Search } from '../../../store/models/search.model';

@Component({
  selector: 'app-search-container',
  templateUrl: './search-container.component.html',
  styleUrls: ['./search-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SearchContainerComponent implements OnInit, On<PERSON><PERSON>roy {
  @ViewChild(SearchBarComponent)
  searchBar: SearchBarComponent;
  @Output()
  locationQueryChange = new EventEmitter();
  public locationError: boolean;
  public queryError: boolean;
  public searchQuery: { [key: string]: string[] };
  public locationQuery: string;
  public search$: Observable<Search>;
  public site: string = environment.site;
  public keyUp$ = new Subject<KeyboardEvent>();
  public blur$ = new Subject<Event>();
  public focus$ = new Subject<Event>();
  public keyDown$ = new Subject<KeyboardEvent>();
  private componentDestroyed: Subject<any> = new Subject();

  constructor(private store: Store<AppState>, private searchService: SearchService, private ref: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.search$ = this.store.select(this.getStoreSearchData);

    this.search$.pipe(takeUntil(this.componentDestroyed)).subscribe(search => {
      this.searchQuery = search.searchQuery;
      this.locationQuery = search.locationQuery;
      this.ref.markForCheck();
    });

    this.searchService.search$.pipe(takeUntil(this.componentDestroyed)).subscribe({
      next: (data: any) => {
        this.queryError = data.invalid_query;
        this.locationError = data.location_not_found;
      }
    });
  }

  ngOnDestroy(): void {
    this.componentDestroyed.next();
    this.componentDestroyed.unsubscribe();
  }

  private getStoreSearchData(store: AppState): Search {
    return store.search;
  }

  changeLocation(location: string): void {
    this.locationQuery = location;
    this.updateSearch();
  }

  changeSearch(search: { field: string; value: string }): void {
    this.searchQuery = {};
    this.searchQuery[search.field] = [search.value];
    this.updateSearch();
  }

  updateSearchFromSearchBar(): void {
    this.searchBar.emitChangeSearch();
  }

  getSearchQueryFields(searchQuery: any): string[] {
    const fields = [];

    for (const query in searchQuery) {
      if (searchQuery.hasOwnProperty(query)) {
        fields.push(query);
      }
    }

    return fields;
  }

  private updateSearch(): void {
    const payloadUpdateSearch = {
      searchQuery: this.searchQuery,
      locationQuery: this.locationQuery
    };
    this.store.dispatch(new SearchActions.UpdateSearch(payloadUpdateSearch));
  }
}
