import { TestBed, ComponentFixture } from '@angular/core/testing';
import { Subject } from 'rxjs';
import { NO_ERRORS_SCHEMA, ChangeDetectorRef } from '@angular/core';
import { By } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import * as SearchActions from '../../../store/actions/search/search.actions';
import { AppState } from 'app/types/app-state';
import { SearchService } from '../../services';
import { SearchContainerComponent } from './search-container.component';

describe('SearchContainerComponent', () => {
  let stub: any = {};
  let componentFixture: ComponentFixture<SearchContainerComponent>, component: SearchContainerComponent;

  beforeEach(() => {
    stub = {
      Store: jasmine.createSpyObj('Store', ['dispatch', 'select']),
      ChangeDetectorRef: jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']),
      SearchService: {
        search$: new Subject()
      },
      search: {
        searchQuery: 'searchQuery',
        locationQuery: 'locationQuery',
        sort: 'default',
        pageSize: 15,
        currentPage: 1
      },
      storeSubject: new Subject()
    };

    stub.Store.select.and.returnValue(stub.storeSubject);

    TestBed.configureTestingModule({
      providers: [
        { provide: Store, useValue: stub.Store },
        { provide: SearchService, useValue: stub.SearchService },
        { provide: ChangeDetectorRef, useValue: stub.ChangeDetectorRef }
      ],
      declarations: [SearchContainerComponent],
      schemas: [NO_ERRORS_SCHEMA]
    });

    TestBed.compileComponents();

    componentFixture = TestBed.createComponent(SearchContainerComponent);
    componentFixture.detectChanges();
    component = componentFixture.componentInstance;
  });

  describe('#constructor', () => {
    it('should render one element', () => {
      const element = componentFixture.debugElement.queryAll(By.css('.search-container'));
      expect(element.length).toBe(1);
    });
  });

  describe('#ngOnInit', () => {
    it('should select the search store', () => {
      stub.Store.select.calls.reset();

      component.ngOnInit();

      expect(stub.Store.select).toHaveBeenCalledTimes(1);
      expect(stub.Store.select).toHaveBeenCalledWith(component['getStoreSearchData']);
    });

    it('should update search and location', () => {
      expect(component.searchQuery).toBeUndefined();
      expect(component.locationQuery).toBeUndefined();

      stub.storeSubject.next(stub.search);

      expect(component.searchQuery).toBe(stub.search.searchQuery);
      expect(component.locationQuery).toBe(stub.search.locationQuery);
    });

    it('should update query and location error', () => {
      expect(component.queryError).toBeUndefined();
      expect(component.locationError).toBeUndefined();

      const response = {
        invalid_query: true,
        location_not_found: true
      };

      stub.SearchService.search$.next(response);

      expect(component.queryError).toBeTruthy();
      expect(component.locationError).toBeTruthy();
    });
  });

  describe('#ngOnDestroy', () => {
    it('should unsubscribe to the component promises', () => {
      component['componentDestroyed'] = jasmine.createSpyObj('componentDestroyed', ['next', 'unsubscribe']);

      component.ngOnDestroy();

      expect(component['componentDestroyed'].next).toHaveBeenCalledTimes(1);
      expect(component['componentDestroyed'].next).toHaveBeenCalledWith();
      expect(component['componentDestroyed'].unsubscribe).toHaveBeenCalledTimes(1);
      expect(component['componentDestroyed'].unsubscribe).toHaveBeenCalledWith();
    });
  });

  describe('#getStoreSearchData', () => {
    it('should return the search store', () => {
      const store = <AppState>{ search: stub.search };

      const storeSearch = component['getStoreSearchData'](store);

      expect(storeSearch).toEqual(stub.search);
    });
  });

  describe('#changeLocation', () => {
    it('should set location on update', () => {
      const location = 'Amsterdam';
      component.searchQuery = { all: ['query'] };
      const payloadUpdateSearch = {
        searchQuery: component.searchQuery,
        locationQuery: location
      };

      component.changeLocation(location);

      expect(component.locationQuery).toBe(location);
      expect(stub.Store.dispatch).toHaveBeenCalledWith(new SearchActions.UpdateSearch(payloadUpdateSearch));
    });
  });

  describe('#changeSearch', () => {
    beforeEach(() => {
      component['searchQuery'] = {};
      component['locationQuery'] = '';
    });

    it('should update search parameters', () => {
      const searchField = 'locationQuery';
      const searchValue = 'Ede';

      component.changeSearch({ field: searchField, value: searchValue });

      expect(component['searchQuery'][searchField]).toEqual([searchValue]);
    });

    it('should manage the call for update search', () => {
      component['updateSearch'] = jasmine.createSpy('updateSearch');

      component.changeSearch({ field: '', value: '' });

      expect(component['updateSearch']).toHaveBeenCalledTimes(1);
      expect(component['updateSearch']).toHaveBeenCalledWith();
    });
  });

  describe('#updateSearchFromSearchBar', () => {
    it('should call method to emit change search event on the search bar component', () => {
      component.searchBar = <any>{
        emitChangeSearch: jasmine.createSpy('emitChangeSearch')
      };

      component.updateSearchFromSearchBar();

      expect(component.searchBar.emitChangeSearch).toHaveBeenCalledTimes(1);
      expect(component.searchBar.emitChangeSearch).toHaveBeenCalledWith();
    });
  });

  describe('#getSearchQueryFields', () => {
    it('should return object keys', () => {
      const object = { one: 1, two: 2 };

      expect(component.getSearchQueryFields(object)).toEqual(Object.keys(object));
    });

    it('should skip not own properties', () => {
      class SearchQuery {
        one = 1;
      }

      SearchQuery.prototype = <any>{ two: 2 };

      const searchQuery = new SearchQuery();

      expect(component.getSearchQueryFields(searchQuery)).toEqual(['one']);
    });
  });

  describe('#updateSearch', () => {
    it('should dispatch action', () => {
      component.locationQuery = 'location';
      const payloadUpdateSearch = {
        searchQuery: { all: ['query'] },
        locationQuery: component.locationQuery
      };

      component.changeSearch({ field: 'all', value: 'query' });

      expect(stub.Store.dispatch).toHaveBeenCalledWith(new SearchActions.UpdateSearch(payloadUpdateSearch));
    });
  });
});
