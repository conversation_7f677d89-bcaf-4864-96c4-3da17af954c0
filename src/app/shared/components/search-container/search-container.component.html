<form class="search-bar"
      [ngClass]="{ error: locationError }">
    <div class="search-container">
        <a class="back-btn" href="/dashboard" *ngIf="site === 'b2b'"></a>
        <div class="search-wrap">
            <div class="boolean-search">
                <div *ngFor="let searchField of getSearchQueryFields(searchQuery)">
                    <div *ngFor="let searchString of searchQuery[searchField]; let i = index">
                        <app-search-bar [field]="searchField"
                                        [value]="searchString"
                                        (changeSearch)="changeSearch($event)"
                                        [ngClass]="{error: queryError}">
                        </app-search-bar>
                    </div>
                </div>
                <span *ngIf="queryError" class="location-error-message">
                    De ingegeven zoekopdracht is niet correct
                </span>
            </div>
            <div class="location-and-button">
                <div class="table">
                    <div class="table-row">
                        <div class="search-bar-location">
                            <input [ngClass]="{ error: locationError }"
                                   type="text"
                                   [(ngModel)]="locationQuery"
                                   (ngModelChange)="locationQueryChange.emit($event)"
                                   name="locationQuery"
                                   (keydown)="keyDown$.next($event)"
                                   (keyup)="keyUp$.next($event)"
                                   (blur)="blur$.next()"
                                   (focus)="focus$.next()"
                                   autocomplete="off"
                                   placeholder="locatie opdracht"
                                   tabindex="4">
                            <app-auto-complete [input]="locationQueryChange"
                                               [keyUp]="keyUp$"
                                               [keyDown]="keyDown$"
                                               [blur]="blur$"
                                               [focus]="focus$"
                                               action="FETCH_LOCATIONS"
                                               resultAction="FETCHED_LOCATIONS" (autocompleteValueSelect)="changeLocation($event)"></app-auto-complete>
                            <span *ngIf="locationError" class="location-error-message">Voer een bestaande postcode of plaatsnaam in</span>
                        </div>

                        <button class="search-submit" (click)="updateSearchFromSearchBar()" tabindex="5" data-gtm="search-bar-submit">
                            Zoeken
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
