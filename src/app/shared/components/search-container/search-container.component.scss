@import "components";

:host {
  app-search-bar {
    &.error {
      border: 2px solid #e00;
    }
  }
  .search-bar {
    background-color: $search-bar-background-color;
    margin: 0;
    max-width: 100%;

    .search-container {
      @include outer-container();
      padding: 10px $grid-gutter-width/2;

      @include media-breakpoint-up(md) {
        padding: 16px $grid-gutter-width;
      }
    }

    .search-wrap {
      @include media-breakpoint-up(md) {
        border-radius: 5px;
        box-shadow: 2px 2px 5px 0 rgba(0, 0, 0, 0.2);
        height: 60px;
      }

      .boolean-search {
        margin-bottom: 8px;
        @include media-breakpoint-up(md) {
          display: inline-block;
          margin-bottom: 0;
          vertical-align: top;
          width: 62%;
        }
      }
    }

    @include media-breakpoint-up(md) {
      &.error {
        padding-bottom: 0;
      }
    }

    .location-and-button {
      @include media-breakpoint-up(md) {
        display: inline-block;
        width: 38%;
      }
    }

    .search-bar-location {
      margin-bottom: 16px;
      position: relative;

      input {
        color: #666;
        font-family: $font-family-default;
        font-size: $search-bar-font-size-mobile;
        font-weight: 400;
        line-height: 44px;
        outline: 0;

        @include media-breakpoint-up(md) {
          font-size: $search-bar-font-size-desktop;
          line-height: 60px;
        }
      }
      .error {
        border: 2px solid #e00;
      }
      @include media-breakpoint-up(md) {
        display: table-cell;
        margin-bottom: 0;
        vertical-align: top;
        width: $search-bar-location-width;
      }
    }

    .search-submit {
      background-color: $search-submit-background-color;
      border: none;
      color: $search-submit-color;
      cursor: pointer;
      display: block;
      font-family: $font-family-default;
      font-size: 17px;
      font-weight: 600;
      height: 44px;
      line-height: 44px;
      padding: 0 18px;
      text-align: center;
      text-transform: lowercase;
      transition: 0.2s background ease;
      width: 100%;
      @include media-breakpoint-up(md) {
        border-bottom-right-radius: 5px;
        border-top-right-radius: 5px;
        font-size: 20px;
        height: 60px;
        line-height: 60px;
      }
      @include media-breakpoint-up(lg) {
        display: table-cell;
        max-width: 115px;
        width: 115px;
      }

      &:hover {
        background-color: darken($search-submit-background-color, 10%);
      }
    }
    input[type="text"] {
      border: none;
      border-radius: 0;
      font-family: $font-family-default;
      font-size: 15px;
      font-weight: 600;
      height: 44px;
      padding: 0 20px;
      width: 100%;
      @include media-breakpoint-up(md) {
        font-size: 20px;
        height: 60px;
      }
      &::placeholder {
        color: $search-placeholder-color;
      }
    }
  }

  .table {
    display: table;
    width: 100%;
    .table-row {
      display: table-row;
    }
  }

  .location-error-message {
    background-color: #e00;
    color: white;
    font-size: 14px;
    letter-spacing: 0;
    z-index: 10;
  }
}
