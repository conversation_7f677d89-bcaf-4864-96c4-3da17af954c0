<textarea class="search-input"
          [(ngModel)]="value"
          rows="1"
          #textArea
          (ngModelChange)="changeValue($event)"
          (focus)="focus()"
          (blur)="blur()"
          (keydown)="keydown($event)"
          (keyup)="keyUp$.next($event)"
          [ngClass]="{focus: focussed}"
          placeholder="funct<PERSON><PERSON><PERSON>, vaar<PERSON><PERSON><PERSON><PERSON> of bedrijf"
          tabindex="1"
          autofocus>
</textarea>
<i class="icon-delete"
   *ngIf="value?.length > 0"
   tabindex="2"
   (keydown.enter)="clearQuery()"
   (click)="clearQuery()"
   data-gtm="search-bar-clear">
</i>
<div class="field-search">
    <span class="field-search-in">in:</span>
    <div class="form-select">
        <select class="value"
                [(ngModel)]="field"
                (change)="onFieldChange()"
                tabindex="3"
                data-gtm="search-bar-in-field"
                [attr.data-slug]="field">
            <option *ngFor="let searchField of searchFields" [value]="searchField.value">
                {{searchField.text}}
            </option>
        </select>
    </div>
</div>
<app-auto-complete [input]="valueChange"
                   [keyUp]="keyUp$"
                   [keyDown]="keyDown$"
                   [blur]="blur$"
                   [focus]="focus$"
                   [openWithFirstSelected]="false"
                   (autocompleteSelectedValueChange)="updateAutoCompleteOpened($event)"
                   class="auto-complete-search-bar"
                   action="FETCH_JOB_TITLES"
                   resultAction="FETCHED_JOB_TITLES"
                   (autocompleteValueSelect)="onAutoCompleteSelect($event)"></app-auto-complete>
