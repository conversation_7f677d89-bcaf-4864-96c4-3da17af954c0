import { Input, Output, EventEmitter, Component, ViewChild, ElementRef } from '@angular/core';
import { Subject } from 'rxjs';
import { searchFields } from './search-fields';

@Component({
  selector: 'app-search-bar',
  templateUrl: './search-bar.component.html',
  styleUrls: ['./search-bar.component.scss']
})
export class SearchBarComponent {
  @ViewChild('textArea')
  textArea: ElementRef;
  @Input()
  public field: string;
  @Input()
  public value: string;
  @Output()
  public valueChange = new EventEmitter<string>();
  @Output()
  public changeSearch = new EventEmitter<object>();
  public focussed: boolean;
  public keyUp$ = new Subject<KeyboardEvent>();
  public blur$ = new Subject<Event>();
  public focus$ = new Subject<Event>();
  public keyDown$ = new Subject<KeyboardEvent>();
  public autoCompleteOpened = false;
  public searchFields = searchFields;
  private lineHeight = 44;
  private padding = 0;

  constructor() {}

  focus() {
    if (this.textArea) {
      this.focussed = true;
      this.textArea.nativeElement.rows = 2;
      this.textArea.nativeElement.rows = this.calcRows();
      this.focus$.next();
    }
  }

  blur() {
    if (this.textArea) {
      this.focussed = false;
      this.textArea.nativeElement.rows = 1;
      this.blur$.next();
    }
  }

  changeValue(value: string) {
    if (this.textArea) {
      this.valueChange.emit(value);
      this.textArea.nativeElement.rows = 1;
      this.textArea.nativeElement.rows = this.calcRows();
    }
  }

  calcRows(): number {
    return Math.floor((this.textArea.nativeElement.scrollHeight - this.padding) / this.lineHeight);
  }

  updateAutoCompleteOpened(visible: true) {
    this.autoCompleteOpened = visible;
  }

  emitChangeSearch() {
    this.changeSearch.emit({
      field: this.field,
      value: this.value
    });
  }

  keydown(event: KeyboardEvent) {
    if (event.keyCode === 13) {
      event.preventDefault();
      if (this.autoCompleteOpened === false) {
        this.emitChangeSearch();
      }
    }
    this.keyDown$.next(event);
  }

  onFieldChange() {
    this.emitChangeSearch();
  }

  clearQuery() {
    this.value = '';
    this.emitChangeSearch();
  }

  onAutoCompleteSelect(value: string) {
    this.value = value;
    this.emitChangeSearch();
  }
}
