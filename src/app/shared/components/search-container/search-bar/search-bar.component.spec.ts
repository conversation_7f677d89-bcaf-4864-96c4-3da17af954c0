import { TestBed, ComponentFixture } from '@angular/core/testing';
import { SearchBarComponent } from './search-bar.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('SearchBarComponent', () => {
  let componentFixture: ComponentFixture<SearchBarComponent>, component: SearchBarComponent;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [],
      declarations: [SearchBarComponent],
      schemas: [NO_ERRORS_SCHEMA]
    });

    TestBed.compileComponents();
    componentFixture = TestBed.createComponent(SearchBarComponent);
    componentFixture.detectChanges();
    component = componentFixture.componentInstance;
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('#focus', () => {
    it('should set focussed to true', () => {
      component.focussed = false;
      component.focus();
      expect(component.focussed).toBe(true);
    });

    it('should set the height of the textArea by setting the row to the correct amount', () => {
      component.focus();

      expect(component.textArea.nativeElement.rows).toEqual(2);
    });

    it('should not do anything', () => {
      delete component.textArea;
      component.focussed = false;

      component.focus();

      expect(component.focussed).toBe(false);
    });
  });

  describe('#blur', () => {
    it('should set focussed to true', () => {
      component.focussed = true;
      component.blur();
      expect(component.focussed).toBe(false);
    });

    it('should fold the search bar by setting the rows on the textArea to 1', () => {
      component.blur();

      expect(component.textArea.nativeElement.rows).toEqual(1);
    });

    it('should not do anything', () => {
      delete component.textArea;
      component.focussed = false;

      component.blur();

      expect(component.focussed).toBe(false);
    });
  });

  describe('#changeValue', () => {
    beforeEach(() => {
      spyOn(component.valueChange, 'emit');
      spyOn(component, 'calcRows').and.returnValue(2);
    });

    it('should emit a change to propagate the change to parent component', () => {
      component.changeValue('value');

      expect(component.valueChange.emit).toHaveBeenCalledWith('value');
    });

    it('should set the height of the textArea by setting the row to the correct amount', () => {
      component.changeValue('value');

      expect(component.textArea.nativeElement.rows).toEqual(2);
    });

    it('should not do anything', () => {
      delete component.textArea;

      component.changeValue('value');

      expect(component.valueChange.emit).not.toHaveBeenCalled();
    });
  });

  describe('#calcRows', () => {
    it('should return the amount of rows the textArea should be to fit the text', () => {
      component.textArea = { nativeElement: { scrollHeight: 100 } };
      expect(component.calcRows()).toBe(2);
    });
  });

  describe('#keydown', () => {
    let c: any;
    beforeEach(() => {
      c = component;
      spyOn(c.changeSearch, 'emit');
    });

    it('should prevent default event action when enter is pressed', () => {
      const keyBoardEvent = <any>{
        keyCode: 13,
        preventDefault: jasmine.createSpy('spy')
      };
      component.keydown(keyBoardEvent);
      expect(keyBoardEvent.preventDefault).toHaveBeenCalled();
    });

    it('should emit a submit when enter is pressed', () => {
      const keyBoardEvent = <any>{
        keyCode: 13,
        preventDefault: jasmine.createSpy('spy')
      };
      component.keydown(keyBoardEvent);
      expect(c.changeSearch.emit).toHaveBeenCalled();
    });

    it('should not emit when other key is pressed', () => {
      component.keydown(<KeyboardEvent>{});
      expect(c.changeSearch.emit).not.toHaveBeenCalled();
    });

    it('should pass keydown event to autocomplete component', () => {
      spyOn(component.keyDown$, 'next');
      component.keydown(<KeyboardEvent>{});
      expect(component.keyDown$.next).toHaveBeenCalled();
    });

    it('should not emit a changeSearch when enter is pressed while the autocomplete is open', () => {
      const keyBoardEvent = <any>{
        keyCode: 13,
        preventDefault: jasmine.createSpy('spy')
      };
      component.autoCompleteOpened = true;
      component.keydown(keyBoardEvent);
      expect(c.changeSearch.emit).not.toHaveBeenCalled();
    });
  });

  describe('#updateAutoCompleteOpened', () => {
    it('should set autoCompleteOpened to new state', () => {
      component.updateAutoCompleteOpened(true);
      expect(component.autoCompleteOpened).toBe(true);
    });
  });

  describe('#onFieldChange', () => {
    beforeEach(() => {
      spyOn(component.changeSearch, 'emit');
    });

    it('should emit change search event when field is changed', () => {
      component.field = 'all';
      component.value = 'query';

      component.onFieldChange();

      expect(component.changeSearch.emit).toHaveBeenCalledTimes(1);
      expect(component.changeSearch.emit).toHaveBeenCalledWith({
        field: component.field,
        value: component.value
      });
    });
  });

  describe('#clearQuery', () => {
    beforeEach(() => {
      spyOn(component.changeSearch, 'emit');
    });

    it('should clear search query and emit change search event', () => {
      component.field = 'all';
      component.value = 'query';

      component.clearQuery();

      expect(component.value).toBe('');
      expect(component.changeSearch.emit).toHaveBeenCalledTimes(1);
      expect(component.changeSearch.emit).toHaveBeenCalledWith({
        field: component.field,
        value: ''
      });
    });
  });

  describe('#onAutoCompleteSelect', () => {
    beforeEach(() => {
      spyOn(component.changeSearch, 'emit');
    });

    it('should set search query and emit change search event', () => {
      const queryFromAutoComplete = 'from auto complete';

      component.field = 'all';
      component.value = 'query';

      component.onAutoCompleteSelect(queryFromAutoComplete);

      expect(component.value).toBe(queryFromAutoComplete);
      expect(component.changeSearch.emit).toHaveBeenCalledTimes(1);
      expect(component.changeSearch.emit).toHaveBeenCalledWith({
        field: component.field,
        value: queryFromAutoComplete
      });
    });
  });
});
