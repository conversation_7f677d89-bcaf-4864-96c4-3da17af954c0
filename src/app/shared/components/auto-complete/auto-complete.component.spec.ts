import { TestBed, ComponentFixture } from '@angular/core/testing';
import { AutoCompleteComponent } from './auto-complete.component';
import { NO_ERRORS_SCHEMA, ChangeDetectorRef } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject, EMPTY, from } from 'rxjs';
import { AutoCompleteEffects } from '../../services';

describe('AutoCompleteComponent', () => {
  const stub: any = {};
  let componentFixture: ComponentFixture<AutoCompleteComponent>, component: AutoCompleteComponent;

  beforeEach(() => {
    stub.ChangeDetectorRef = jasmine.createSpyObj('ChangeDetectorRef', ['detectChanges']);
    stub.Store = jasmine.createSpyObj('Store', ['dispatch', 'select']);
    stub.AutoCompleteEffects = jasmine.createSpyObj('AutoCompleteEffects', ['getAutoCompleteEffects']);
    stub.AutoCompleteObservable = new Subject();
    stub.AutoCompleteEffects.getAutoCompleteEffects.and.returnValue(stub.AutoCompleteObservable.asObservable());
    TestBed.configureTestingModule({
      providers: [
        { provide: ChangeDetectorRef, useValue: stub.ChangeDetectorRef },
        { provide: Store, useValue: stub.Store },
        { provide: AutoCompleteEffects, useValue: stub.AutoCompleteEffects }
      ],
      declarations: [AutoCompleteComponent],
      schemas: [NO_ERRORS_SCHEMA]
    });

    TestBed.compileComponents();
    componentFixture = TestBed.createComponent(AutoCompleteComponent);
    componentFixture.detectChanges();
    component = componentFixture.componentInstance;
  });

  describe('#constructor', () => {
    beforeEach(() => {
      component.resultAction = 'ACTION';
    });

    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should load results from action of type {resultAction} if waitingForResults is true', () => {
      component.waitingForResults = true;
      stub.AutoCompleteObservable.next({
        type: 'ACTION',
        payload: { results: ['test'], searchValue: 'te' }
      });
      expect(component.results).toEqual([
        {
          label: '<b>te</b>st',
          value: 'test'
        }
      ]);
    });

    it('should load results from action of type {resultAction} if waitingForResults is false', () => {
      stub.AutoCompleteObservable.next({
        type: 'ACTION',
        payload: { results: ['test'], searchValue: 'te' }
      });
      expect(component.results).toEqual([]);
    });

    it('should not load results from action of other types than {resultAction}', () => {
      stub.AutoCompleteObservable.next({
        type: 'ACTION_2',
        payload: { results: ['test'], searchValue: 'te' }
      });
      expect(component.results).toEqual([]);
    });
  });

  describe('#ngOnChanges', () => {
    it('should subscribe to keyUp events when set', () => {
      const changes = {
        keyUp: { currentValue: jasmine.createSpyObj('keyUp', ['subscribe']) }
      };
      component.ngOnChanges(<any>changes);
      expect(changes.keyUp.currentValue.subscribe).toHaveBeenCalled();
    });

    it('should subscribe to keyDown events when set', () => {
      const changes = {
        keyDown: {
          currentValue: jasmine.createSpyObj('keyDown', ['subscribe'])
        }
      };
      component.ngOnChanges(<any>changes);
      expect(changes.keyDown.currentValue.subscribe).toHaveBeenCalled();
    });

    it('should subscribe to focus events when set', () => {
      const changes = {
        focus: { currentValue: jasmine.createSpyObj('focus', ['subscribe']) }
      };
      component.ngOnChanges(<any>changes);
      expect(changes.focus.currentValue.subscribe).toHaveBeenCalled();
    });

    it('should subscribe to blur events when set', () => {
      const changes = {
        blur: { currentValue: jasmine.createSpyObj('blur', ['subscribe']) }
      };
      component.ngOnChanges(<any>changes);
      expect(changes.blur.currentValue.subscribe).toHaveBeenCalled();
    });

    it('should subscribe to newResults events when set', () => {
      const changes = {
        newResults: {
          currentValue: jasmine.createSpyObj('newResults', ['subscribe'])
        }
      };
      component.ngOnChanges(<any>changes);
      expect(changes.newResults.currentValue.subscribe).toHaveBeenCalled();
    });

    it('should subscribe to inputChange events when set', () => {
      const changes = {
        input: {
          currentValue: jasmine.createSpyObj('input', ['pipe'])
        }
      };
      changes.input.currentValue.pipe.and.returnValue(EMPTY);
      component.ngOnChanges(<any>changes);
      expect(changes.input.currentValue.pipe).toHaveBeenCalled();
    });

    it('should set waitingForResults to true when input changes', () => {
      const changes = {
        input: {
          currentValue: from('abcde')
        }
      };
      component.ngOnChanges(<any>changes);

      expect(component.waitingForResults).toBe(true);
    });
  });

  describe('#ngOnDestroy', () => {
    it('should unsubscribe to the component promises', () => {
      component['componentDestroyed'] = jasmine.createSpyObj('componentDestroyed', ['next', 'unsubscribe']);

      component.ngOnDestroy();

      expect(component['componentDestroyed'].next).toHaveBeenCalledTimes(1);
      expect(component['componentDestroyed'].next).toHaveBeenCalledWith();
      expect(component['componentDestroyed'].unsubscribe).toHaveBeenCalledTimes(1);
      expect(component['componentDestroyed'].unsubscribe).toHaveBeenCalledWith();
    });
  });

  describe('#loadNewResults', () => {
    it('should update results, having not yet any pre selected filters', () => {
      spyOn(component, 'updateResults');
      component.waitingForResults = true;
      component.selectedFilters = [];

      const autocompleteResponse = {
        results: ['AAAAAA', 'bbbbbb', '33333'],
        searchValue: 'bbb'
      };
      const expectedResultsToLoad = [
        {
          label: 'aaaaaa',
          value: 'AAAAAA'
        },
        {
          label: '<b>bbb</b>bbb',
          value: 'bbbbbb'
        },
        {
          label: '33333',
          value: '33333'
        }
      ];

      component['loadNewResults'](autocompleteResponse);

      expect(component['updateResults']).toHaveBeenCalledTimes(1);
      expect(component['updateResults']).toHaveBeenCalledWith(expectedResultsToLoad);
    });

    it('should update results, excluding pre selected filters', () => {
      spyOn(component, 'updateResults');
      component.waitingForResults = true;
      component.selectedFilters = [{ name: '33333' }, { name: '44444444' }];

      const autocompleteResponse = {
        results: ['AAAAAA', 'bbbbbb', '33333'],
        searchValue: 'bbb'
      };
      const expectedResultsToLoad = [
        {
          label: 'aaaaaa',
          value: 'AAAAAA'
        },
        {
          label: '<b>bbb</b>bbb',
          value: 'bbbbbb'
        }
      ];

      component['loadNewResults'](autocompleteResponse);

      expect(component['updateResults']).toHaveBeenCalledTimes(1);
      expect(component['updateResults']).toHaveBeenCalledWith(expectedResultsToLoad);
    });

    it('should do nothing', () => {
      spyOn(component, 'updateResults');
      component.waitingForResults = false;
      const autocompleteResponse = { results: ['bbbbbb'], searchValue: 'bbb' };

      component['loadNewResults'](autocompleteResponse);

      expect(component['updateResults']).not.toHaveBeenCalled();
    });
  });

  describe('#handleFocus', () => {
    it('should set focussed to true', () => {
      component.focussed = false;
      component.handleFocus();
      expect(component.focussed).toBe(true);
    });
  });

  describe('#handleBlur', () => {
    it('should set focussed to false', () => {
      component.focussed = true;
      component.handleBlur();
      expect(component.focussed).toBe(false);
    });

    it('should close the auto complete', () => {
      spyOn(component, 'close');
      component.handleBlur();
      expect(component.close).toHaveBeenCalled();
    });
  });

  describe('#handleKeyDown', () => {
    it('should select a result on enter', () => {
      const event = { keyCode: 13 };
      spyOn(component, 'selectResult');
      component.handleKeyDown(<KeyboardEvent>event);
      expect(component.selectResult).toHaveBeenCalled();
    });

    it('should select a result on tab', () => {
      const event = { keyCode: 9 };
      spyOn(component, 'selectResult');
      component.handleKeyDown(<any>event);
      expect(component.selectResult).toHaveBeenCalled();
    });

    it('should stop up arrow key to change cursor position', () => {
      const event = {
        key: 'ArrowUp',
        preventDefault: jasmine.createSpy('preventDefault')
      };
      component.handleKeyDown(<any>event);
      expect(event.preventDefault).toHaveBeenCalled();
    });

    it('should stop down arrow key to change cursor position', () => {
      const event = {
        key: 'ArrowDown',
        preventDefault: jasmine.createSpy('preventDefault')
      };
      component.handleKeyDown(<any>event);
      expect(event.preventDefault).toHaveBeenCalled();
    });
  });

  describe('#handleKeyUp', () => {
    it('should prevent enter from submitting form', () => {
      const event = {
        key: 'Enter',
        preventDefault: jasmine.createSpy('preventDefault')
      };
      component.handleKeyUp(<any>event);
      expect(event.preventDefault).toHaveBeenCalled();
    });

    it('up arrow key should select the value above the currently selected value', () => {
      const event = { key: 'ArrowUp' };
      spyOn(component, 'selectedIndexUp');
      component.handleKeyUp(<any>event);
      expect(component.selectedIndexUp).toHaveBeenCalled();
    });

    it('down arrow key should select the value beneath the currently selected value', () => {
      const event = { key: 'ArrowDown' };
      spyOn(component, 'selectedIndexDown');
      component.handleKeyUp(<any>event);
      expect(component.selectedIndexDown).toHaveBeenCalled();
    });

    it('escape should close the auto complete', () => {
      const event = { key: 'Escape' };
      spyOn(component, 'close');
      component.handleKeyUp(<any>event);
      expect(component.close).toHaveBeenCalled();
    });
  });

  describe('#handleInputValueChange', () => {
    beforeEach(() => {
      spyOn(component, 'updateResults');
    });

    it('should update results with an empty array if the input value does not start wit two characters', () => {
      component.handleInputValueChange('1011 BG');
      expect(component.updateResults).toHaveBeenCalledWith([]);
    });

    it('should update results with an empty array if the input value equals the currentlySelectedValue', () => {
      component.currentSelectedValue = 'Amsterdam';
      component.handleInputValueChange('Amsterdam');
      expect(component.updateResults).toHaveBeenCalledWith([]);
    });

    it('should dispatch action if two or more characters are entered and action is set', () => {
      component.action = 'ACTION';
      component.handleInputValueChange('Amsterdam');
      expect(stub.Store.dispatch).toHaveBeenCalledWith({
        type: 'ACTION',
        payload: 'Amsterdam'
      });
    });

    it('should not dispatch action if no action is set', () => {
      component.handleInputValueChange('Amsterdam');
      expect(stub.Store.dispatch).not.toHaveBeenCalled();
    });
  });

  describe('#updateResults', () => {
    it('should hide the auto complete if there are no results', () => {
      component.visible = true;
      component.updateResults([]);
      expect(component.visible).toBe(false);
    });

    it('should hide the auto complete if the search field is no longer focussed', () => {
      component.visible = true;
      component.focussed = false;
      component.updateResults([{ value: 'Amsterdam', label: 'Amsterdam' }]);
      expect(component.visible).toBe(false);
    });

    it('should set the results', () => {
      component.updateResults([]);
      expect(component.results).toEqual([]);
    });

    it('should notify parent value is selected when openWithFirstSelected is true', () => {
      component.openWithFirstSelected = true;
      component.autocompleteSelectedValueChange.subscribe((bool: boolean) => {
        expect(bool).toBe(true);
      });
      component.updateResults([{ label: 'test', value: 'test' }]);
    });

    it('should set currentSelectedIndex to 0 if openWithFirstSelected is true', () => {
      component.openWithFirstSelected = true;
      component.updateResults([{ label: 'test', value: 'test' }]);
      expect(component.currentSelectedIndex).toBe(0);
    });

    it('should notify parent not value is selected when openWithFirstSelected is false', () => {
      component.openWithFirstSelected = false;
      component.autocompleteSelectedValueChange.subscribe((bool: boolean) => {
        expect(bool).toBe(false);
      });
      component.updateResults([{ label: 'test', value: 'test' }]);
    });

    it('should reset currentSelectedIndex if openWithFirstSelected is false', () => {
      component.openWithFirstSelected = false;
      component.updateResults([{ label: 'test', value: 'test' }]);
      expect(component.currentSelectedIndex).toBeUndefined();
    });
  });

  describe('#selectedIndexUp', () => {
    beforeEach(() => {
      component.results = [{ value: 'Amsterdam', label: 'Amsterdam' }, { value: 'Amstelveen', label: 'Amstelveen' }];
    });
    it('should decrease the currentSelectedIndex', () => {
      component.currentSelectedIndex = 2;
      component.selectedIndexUp();
      expect(component.currentSelectedIndex).toBe(1);
    });

    it('should go to bottom option when current index is on the top option', () => {
      component.currentSelectedIndex = 0;
      component.selectedIndexUp();
      expect(component.currentSelectedIndex).toBe(1);
    });

    it('should start at the bottom when no value is currently selected', () => {
      component.currentSelectedIndex = undefined;
      component.selectedIndexUp();
      expect(component.currentSelectedIndex).toBe(0);
    });

    it('should notify parent that value is highlighted', () => {
      spyOn(component.autocompleteSelectedValueChange, 'emit');
      component.currentSelectedIndex = 0;
      component.selectedIndexUp();
      expect(component.autocompleteSelectedValueChange.emit).toHaveBeenCalledWith(true);
    });
  });

  describe('#selectedIndexDown', () => {
    beforeEach(() => {
      component.results = [{ value: 'Amsterdam', label: 'Amsterdam' }, { value: 'Amstelveen', label: 'Amstelveen' }];
    });

    it('should decrease the currentSelectedIndex', () => {
      component.currentSelectedIndex = 0;
      component.selectedIndexDown();
      expect(component.currentSelectedIndex).toBe(1);
    });

    it('should go to bottom option when current index is on the top option', () => {
      component.currentSelectedIndex = 1;
      component.selectedIndexDown();
      expect(component.currentSelectedIndex).toBe(0);
    });

    it('should start at the bottom when no value is currently selected', () => {
      component.currentSelectedIndex = undefined;
      component.selectedIndexDown();
      expect(component.currentSelectedIndex).toBe(0);
    });

    it('should notify parent that value is highlighted', () => {
      spyOn(component.autocompleteSelectedValueChange, 'emit');
      component.currentSelectedIndex = 0;
      component.selectedIndexDown();
      expect(component.autocompleteSelectedValueChange.emit).toHaveBeenCalledWith(true);
    });
  });

  describe('#selectResult', () => {
    beforeEach(() => {
      spyOn(component, 'close');
      component.visible = true;
      component.results = [{ value: 'Amsterdam', label: 'Amsterdam' }, { value: 'Amstelveen', label: 'Amstelveen' }];
    });

    it('should close the auto complete', () => {
      component.visible = false;
      component.selectResult(0);
      expect(component.close).toHaveBeenCalled();
    });

    it('should close when enter is pressed', () => {
      component.selectResult(0);
      expect(component.close).toHaveBeenCalled();
    });

    it('should emit only when value is selected and auto complete is visible', () => {
      component.visible = true;
      spyOn(component.autocompleteValueSelect, 'emit');
      component.selectResult(0);
      expect(component.autocompleteValueSelect.emit).toHaveBeenCalled();
    });
  });

  describe('#close', () => {
    it('should set visible to false', () => {
      component.visible = true;
      component.close();
      expect(component.visible).toBe(false);
    });

    it('should clear results', () => {
      component.results = [{ value: 'Amsterdam', label: 'Amsterdam' }, { value: 'Amstelveen', label: 'Amstelveen' }];
      component.close();
      expect(component.results).toEqual([]);
    });
  });
});
