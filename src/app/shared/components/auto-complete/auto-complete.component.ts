import { Component, Input, Output, EventEmitter, OnChanges, ChangeDetectorRef, OnDestroy, SimpleChanges } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable, Subject } from 'rxjs';
import { map, filter, tap, debounceTime, takeUntil } from 'rxjs/operators';
import { AppState } from '../../../types/app-state';
import { AutoCompleteEffects } from '../../services';

@Component({
  selector: 'app-auto-complete',
  templateUrl: './auto-complete.component.html',
  styleUrls: ['./auto-complete.component.scss']
})
export class AutoCompleteComponent implements OnChanges, OnDestroy {
  @Input()
  input: Observable<string>;
  @Input()
  blur: Observable<Event>;
  @Input()
  focus: Observable<Event>;
  @Input()
  keyUp: Observable<KeyboardEvent>;
  @Input()
  keyDown: Observable<KeyboardEvent>;
  @Input()
  position = 'absolute';
  @Input()
  openWithFirstSelected = true;
  @Input()
  action: string;
  @Input()
  name: string;
  @Input()
  resultAction: string;
  @Input()
  selectedFilters: any[];

  @Output()
  autocompleteValueSelect = new EventEmitter<string>();
  @Output()
  autocompleteSelectedValueChange = new EventEmitter<Boolean>();

  public currentSelectedIndex: number;
  public currentSelectedValue: string;
  public results: { label: string; value: string }[] = [];
  public waitingForResults = false;
  public visible = false;
  public focussed = false;
  private componentDestroyed: Subject<any> = new Subject();

  constructor(private ref: ChangeDetectorRef, private store: Store<AppState>, private autoCompleteEffects: AutoCompleteEffects) {
    this.handleKeyUp = this.handleKeyUp.bind(this);
    this.handleKeyDown = this.handleKeyDown.bind(this);
    this.handleBlur = this.handleBlur.bind(this);
    this.handleFocus = this.handleFocus.bind(this);
    this.handleInputValueChange = this.handleInputValueChange.bind(this);
    this.loadNewResults = this.loadNewResults.bind(this);

    this.autoCompleteEffects
      .getAutoCompleteEffects()
      .pipe(
        filter(({ type }) => type === this.resultAction),
        map(({ payload }) => payload),
        takeUntil(this.componentDestroyed)
      )
      .subscribe(this.loadNewResults);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.keyUp && changes.keyUp.currentValue) {
      changes.keyUp.currentValue.subscribe(this.handleKeyUp);
    }

    if (changes.keyDown && changes.keyDown.currentValue) {
      changes.keyDown.currentValue.subscribe(this.handleKeyDown);
    }

    if (changes.blur && changes.blur.currentValue) {
      changes.blur.currentValue.subscribe(this.handleBlur);
    }

    if (changes.focus && changes.focus.currentValue) {
      changes.focus.currentValue.subscribe(this.handleFocus);
    }

    if (changes.input && changes.input.currentValue) {
      changes.input.currentValue
        .pipe(
          tap(() => {
            this.waitingForResults = true;
          }),
          debounceTime(300)
        )
        .subscribe(this.handleInputValueChange);
    }

    if (changes.newResults && changes.newResults.currentValue) {
      changes.newResults.currentValue.subscribe(this.loadNewResults);
    }
  }

  ngOnDestroy(): void {
    this.componentDestroyed.next();
    this.componentDestroyed.unsubscribe();
  }

  private loadNewResults(response: { results: string[]; searchValue: string }): void {
    const selectedFilters: string[] = [];
    if (this.selectedFilters) {
      for (const selectedFilter of this.selectedFilters) {
        selectedFilters.push(selectedFilter.name.toLowerCase());
      }
    }

    if (this.waitingForResults) {
      const results = response.results
        .map(result => {
          if (selectedFilters.indexOf(result.toLowerCase()) < 0) {
            return {
              label: result.toLowerCase().replace(response.searchValue.toLowerCase(), `<b>${response.searchValue}</b>`),
              value: result
            };
          }
        })
        .filter(result => result !== undefined);

      this.updateResults(results);
    }
  }

  handleFocus(): void {
    this.focussed = true;
  }

  handleBlur(): void {
    this.focussed = false;
    this.close();
  }

  handleKeyDown(event: KeyboardEvent): void {
    if (event.keyCode === 13 || event.keyCode === 9) {
      this.selectResult(this.currentSelectedIndex);
    }
    if (event.key === 'ArrowUp') {
      event.preventDefault();
    }
    if (event.key === 'ArrowDown') {
      event.preventDefault();
    }
  }

  handleKeyUp(event: KeyboardEvent): void {
    switch (event.key) {
      case 'Enter':
        event.preventDefault();
        break;
      case 'Escape':
        this.close();
        break;
      case 'ArrowUp':
        this.selectedIndexUp();
        break;
      case 'ArrowDown':
        this.selectedIndexDown();
        break;
    }
  }

  handleInputValueChange(value: string): void {
    if (value.match(/^[a-zA-Z']{2}.*/) && value !== this.currentSelectedValue) {
      if (this.action) {
        this.store.dispatch({ type: this.action, payload: value });
      }
    } else {
      this.updateResults([]);
    }
  }

  updateResults(results: { label: string; value: string }[]): void {
    this.visible = results.length > 0 && this.focussed;
    this.results = results.splice(0, 9);

    if (this.openWithFirstSelected) {
      this.currentSelectedIndex = 0;
      this.autocompleteSelectedValueChange.emit(this.results.length > 0);
    } else {
      this.currentSelectedIndex = undefined;
      this.autocompleteSelectedValueChange.emit(false);
    }
    this.ref.detectChanges();
  }

  selectedIndexUp(): void {
    this.autocompleteSelectedValueChange.emit(this.results.length > 0);

    this.currentSelectedIndex = (Number.isInteger(this.currentSelectedIndex) ? this.currentSelectedIndex : 1) - 1;
    if (this.currentSelectedIndex < 0) {
      this.currentSelectedIndex = this.results.length - 1;
    }
  }

  selectedIndexDown(): void {
    this.autocompleteSelectedValueChange.emit(this.results.length > 0);

    this.currentSelectedIndex = (Number.isInteger(this.currentSelectedIndex) ? this.currentSelectedIndex : -1) + 1;
    if (this.currentSelectedIndex >= this.results.length) {
      this.currentSelectedIndex = 0;
    }
  }

  selectResult(index: number): void {
    if (this.visible && Number.isInteger(index)) {
      this.currentSelectedValue = this.results[index].value;
      this.autocompleteValueSelect.emit(this.results[index].value);
    }

    this.close();
  }

  close(): void {
    this.waitingForResults = false;
    this.visible = false;
    this.results = [];
    this.autocompleteSelectedValueChange.emit(false);
  }
}
