<div class="container">
    <ul [ngClass]="{ closed: !visible }"
        [style.position]="position"
        class="auto-complete">
        <li [ngClass]="{ selected: currentSelectedIndex === index}"
            class="item"
            *ngFor="let result of results; let index = index;"
            (mousedown)="selectResult(index)">
            <a [innerHTML]="result.label"
               href="javascript:"
               [attr.data-gtm]="'filter-' + name?.toLowerCase()"
               [attr.data-slug]="name?.toLowerCase() + '-add-' + result.value.toLowerCase()"></a>
        </li>
    </ul>
</div>
