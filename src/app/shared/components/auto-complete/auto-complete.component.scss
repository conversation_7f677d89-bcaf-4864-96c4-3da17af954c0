@import "components";

:host {
  margin-top: 5px;

  .closed {
    display: none;
  }

  .auto-complete {
    background-color: white;
    border: 1px solid #9f9ca5;
    box-shadow: 1px 1px 4px 0 rgba(0, 0, 0, 0.15);
    list-style-type: none;
    margin-top: 0;
    max-width: 100%;
    min-width: 100%;
    padding: 12px 27px;
    position: absolute;
    white-space: nowrap;
    z-index: 1;

    .item {
      color: $autocomplete-text-color;
      cursor: pointer;
      font-size: 14px;
      height: 38px;
      letter-spacing: 0;
      line-height: 38px;
      margin-left: -27px;
      margin-right: -27px;
      overflow-x: hidden;
      position: relative;
      text-overflow: ellipsis;
      text-transform: capitalize;

      a {
        bottom: 0;
        color: $autocomplete-text-color;
        left: 0;
        padding-left: 15px;
        position: absolute;
        right: 0;
        top: 0;

        &:hover {
          color: $autocomplete-text-color;
          text-decoration: none;
        }
      }

      &:before {
        display: none;
      }

      &.selected {
        background-color: #dce7ef;
      }
    }
  }
  &.auto-complete-search-bar .auto-complete {
    margin-top: 60px;
  }

  .container {
    min-width: 100%;
    position: relative;
  }
}
