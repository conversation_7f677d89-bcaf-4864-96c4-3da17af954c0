import { Component, Input, OnInit } from '@angular/core';
import * as ModalsActions from '../../../../store/actions/modals/modals.actions';
import { Store } from '@ngrx/store';
import { AppState } from '../../../../types/app-state';
import { FEATURES, HttpService, isFeatureEnabled } from '../../../services';
import { environment } from '../../../../../environments/environment';

@Component({
  selector: 'app-share-profile-modal',
  templateUrl: './share-profile-modal.component.html',
  styleUrls: ['share-profile-modal.component.scss']
})
export class ShareProfileModalComponent implements OnInit {
  @Input()
  jobseekerId: string;
  @Input()
  firstName: string;
  @Input()
  lastName: string;
  public isSuccess = false;
  public isError = false;
  public recipientSubject = '';
  public recipientEmailAddress = '';
  public motivation = '';
  public cvAttached = true;
  public isLoading = false;
  public displayShareProfileModal = false;

  constructor(private store: Store<AppState>, private http: HttpService) {
    this.store
      .select(s => s.modals)
      .subscribe(modals => {
        this.displayShareProfileModal = modals.displayShareProfileModal;
      });

    this.onSharerSuccess = this.onSharerSuccess.bind(this);
    this.onSharerFailure = this.onSharerFailure.bind(this);
  }

  ngOnInit(): void {
    this.setDefaultSubject();
  }

  private setDefaultSubject(): void {
    this.recipientSubject =
      `Kandidaat via ${environment.siteHeader.charAt(0).toUpperCase()}${environment.siteHeader.substring(1)}` +
      `: ${this.firstName} ${this.lastName}`;
  }

  closeSharerModal(event: Event) {
    if (event.type === 'keydown') {
      event.preventDefault();
      event.stopPropagation();
    }

    this.isLoading = false;
    this.isSuccess = false;
    this.isError = false;

    this.setDefaultSubject();
    this.recipientEmailAddress = '';
    this.motivation = '';
    this.cvAttached = true;

    this.store.dispatch(new ModalsActions.CloseModals());
  }

  toggleSetCvAttached(): void {
    this.cvAttached = !this.cvAttached;
  }

  shareProfile() {
    if (this.isLoading) {
      return;
    }

    this.isLoading = true;

    this.http
      .post(`/dashboard/api/recruiter/job-seeker/${this.jobseekerId}/share`, {
        jobSeekerProfileUrl: window.location.href,
        subject: this.recipientSubject,
        recipientEmailAddress: this.recipientEmailAddress,
        motivation: this.motivation,
        isCvAttached: this.cvAttached
      })
      .subscribe(this.onSharerSuccess, this.onSharerFailure);
  }

  private onSharerSuccess() {
    this.isLoading = false;
    this.isSuccess = true;
    this.isError = false;
  }

  private onSharerFailure() {
    this.isLoading = false;
    this.isError = true;
  }
}
