<div class="modal">
    <div class="modal-box">
        <div class="modal-contents">
            <i class="close-icon"
               (click)="closeSharerModal($event)"
               data-gtm="pro-share-cancel"></i>

            <h3><PERSON><PERSON> delen met:</h3>
            <div class="error-message" *ngIf="isError">Er is iets misgegaan</div>
            <ng-template #successfullySharedMessageTemplate>
                <h4>{{recipientEmailAddress}}</h4>
                <div>Gedeeld!</div>
                <div class="modal-actions">
                    <button class="btn btn-transparent cancel"
                            (click)="closeSharerModal($event)"
                            data-gtm="pro-share-close">
                        sluiten
                    </button>
                </div>
            </ng-template>
            <div *ngIf="isSuccess === false; else successfullySharedMessageTemplate">
                <form>
                    <div class="form-group">
                        <label for="emailAddress">E-mailadres</label>
                        <input #emailAddress
                               type="email"
                               name="emailAddress"
                               class="input"
                               [ngClass]="{'valid-data': emailAddress.checkValidity(), 'invalid-data': !emailAddress.checkValidity()}"
                               id="emailAddress"
                               placeholder="E-mailadres"
                               [(ngModel)]="recipientEmailAddress"
                               minlength="2"
                               tabindex="2"
                               (keydown.enter)="$event.preventDefault()"
                               required
                               autofocus
                               data-gtm="pro-share-email"
                               maxlength="500">
                    </div>
                    <div class="form-group">
                        <label for="subject">Onderwerp</label>
                        <input #subject
                               type="text"
                               name="subject"
                               class="input"
                               [ngClass]="{'valid-data':  recipientSubject !== '', 'invalid-data':  recipientSubject === ''}"
                               id="subject"
                               placeholder="Onderwerp"
                               [(ngModel)]="recipientSubject"
                               tabindex="1"
                               (keydown.enter)="$event.preventDefault()"
                               required
                               autofocus
                               data-gtm="pro-share-subject"
                               maxlength="78">
                    </div>
                    <div class="form-group">
                        <label for="motivation">Motivatie</label>
                        <textarea name="motivation"
                                  id="motivation"
                                  placeholder="Motivatie"
                                  [(ngModel)]="motivation"
                                  class="form-control"
                                  rows="3"
                                  tabindex="3"
                                  data-gtm="pro-share-motivation">
                        </textarea>
                    </div>
                    <div class="form-check">
                        <label for="cvAttached">
                            <input type="checkbox"
                                   name="cvAttached"
                                   id="cvAttached"
                                   [checked]="cvAttached"
                                   (click)="toggleSetCvAttached()"
                                   tabindex="4"
                                   (keydown.enter)="$event.preventDefault()"
                                   data-gtm="pro-share-attachment">
                            Verstuur profiel als bijlage
                        </label>
                    </div>
                    <div class="modal-actions">
                        <button class="btn btn-transparent cancel"
                                (click)="closeSharerModal($event)"
                                (keydown.enter)="closeSharerModal($event)"
                                tabindex="5"
                                data-gtm="pro-share-cancel">
                            annuleren
                        </button>
                        <button [ngClass]="{'confirm btn btn-primary': true, 'disabled': !emailAddress.checkValidity() || recipientSubject === ''}"
                                (click)="shareProfile()"
                                (keydown.enter)="shareProfile()"
                                tabindex="6"
                                [disabled]="!emailAddress.checkValidity() || recipientSubject === ''"
                                data-gtm="pro-share-save">
                            delen
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
