import { TestBed, ComponentFixture } from '@angular/core/testing';
import { ShareProfileModalComponent } from './share-profile-modal.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { throwError, of, Subject } from 'rxjs';
import * as ModalsActions from '../../../../store/actions/modals/modals.actions';
import { Store } from '@ngrx/store';
import { HttpService } from '../../../services';
import { environment } from '../../../../../environments/environment';

describe('ShareProfileModalComponent', () => {
  let componentFixture: ComponentFixture<ShareProfileModalComponent>, component: ShareProfileModalComponent;
  const stub: any = {};

  beforeEach(() => {
    stub.StoreModalsSubject = new Subject();
    stub.Store = {
      dispatch: jasmine.createSpy('dispatch'),
      modals: stub.StoreModalsSubject
    };
    stub.Store.select = (storeSelect: Function) => {
      return storeSelect(stub.Store);
    };

    stub.HttpService = jasmine.createSpyObj('HttpService', ['post']);

    TestBed.configureTestingModule({
      declarations: [ShareProfileModalComponent],
      providers: [{ provide: Store, useValue: stub.Store }, { provide: HttpService, useValue: stub.HttpService }],
      schemas: [NO_ERRORS_SCHEMA]
    });

    TestBed.compileComponents();

    componentFixture = TestBed.createComponent(ShareProfileModalComponent);

    componentFixture.detectChanges();
    component = componentFixture.componentInstance;
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should subscribe to modals store', () => {
      stub.StoreModalsSubject.next({ displayShareProfileModal: true });

      expect(component.displayShareProfileModal).toBe(true);
    });
  });

  describe('#ngOnInit', () => {
    it('should set default subject for modal', () => {
      component['setDefaultSubject'] = jasmine.createSpy('setDefaultSubject');

      component.ngOnInit();

      expect(component['setDefaultSubject']).toHaveBeenCalledTimes(1);
      expect(component['setDefaultSubject']).toHaveBeenCalledWith();
    });
  });

  describe('#setDefaultSubject', () => {
    it('should set default subject for modal', () => {
      component.firstName = 'Alessandro';
      component.lastName = 'Rabitti';
      const expectedSubject =
        `Kandidaat via ${environment.siteHeader.charAt(0).toUpperCase()}${environment.siteHeader.substring(1)}` +
        `: ${component.firstName} ${component.lastName}`;

      component['setDefaultSubject']();

      expect(component.recipientSubject).toEqual(expectedSubject);
    });
  });

  describe('#closeSharerModal', () => {
    it('should dispatch an CLOSE_MODALS action', () => {
      stub.Store.dispatch.calls.reset();

      component.isLoading = true;
      component.isSuccess = true;
      component.isError = true;
      component['setDefaultSubject'] = jasmine.createSpy('setDefaultSubject');

      component.recipientEmailAddress = '<EMAIL>';
      component.motivation = 'Motivation';
      component.cvAttached = true;

      component.closeSharerModal(new Event('event'));

      expect(stub.Store.dispatch).toHaveBeenCalledTimes(1);
      expect(stub.Store.dispatch).toHaveBeenCalledWith(new ModalsActions.CloseModals());
      expect(component.isLoading).toBe(false);
      expect(component.isSuccess).toBe(false);
      expect(component.isError).toBe(false);

      expect(component['setDefaultSubject']).toHaveBeenCalledTimes(1);
      expect(component['setDefaultSubject']).toHaveBeenCalledWith();
      expect(component.recipientEmailAddress).toBe('');
      expect(component.motivation).toBe('');
      expect(component.cvAttached).toBe(true);
    });

    it('should prevent event propagation when modal is closed with keyboard event - esc', () => {
      const keyboardEvent = new Event('keydown');
      keyboardEvent.preventDefault = jasmine.createSpy('preventDefault');
      keyboardEvent.stopPropagation = jasmine.createSpy('stopPropagation');

      component.closeSharerModal(keyboardEvent);

      expect(keyboardEvent.preventDefault).toHaveBeenCalledTimes(1);
      expect(keyboardEvent.preventDefault).toHaveBeenCalledWith();

      expect(keyboardEvent.stopPropagation).toHaveBeenCalledTimes(1);
      expect(keyboardEvent.stopPropagation).toHaveBeenCalledWith();
    });
  });

  describe('#toggleSetCvAttached', () => {
    it('should set true if cv as attachment is disabled', () => {
      component.cvAttached = false;

      component.toggleSetCvAttached();

      expect(component.cvAttached).toEqual(true);
    });

    it('should set false if cv as attachment is enabled', () => {
      component.cvAttached = true;

      component.toggleSetCvAttached();

      expect(component.cvAttached).toEqual(false);
    });
  });

  describe('#shareProfile', () => {
    it('should make HTTP call and do successful clean up', () => {
      component.jobseekerId = '123456';
      component.isLoading = false;
      component.isSuccess = false;
      component.isError = true;

      component.recipientSubject = 'This kandidaat is a good boy';
      component.recipientEmailAddress = '<EMAIL>';
      component.motivation = 'Motivation';
      component.cvAttached = true;

      stub.HttpService.post.and.returnValue(of(1));

      component.shareProfile();

      expect(stub.HttpService.post).toHaveBeenCalledTimes(1);
      expect(stub.HttpService.post).toHaveBeenCalledWith(`/dashboard/api/recruiter/job-seeker/${component.jobseekerId}/share`, {
        jobSeekerProfileUrl: window.location.href,
        subject: component.recipientSubject,
        recipientEmailAddress: component.recipientEmailAddress,
        motivation: component.motivation,
        isCvAttached: component.cvAttached
      });
      expect(component.isLoading).toBe(false);
      expect(component.isSuccess).toBe(true);
      expect(component.isError).toBe(false);
    });

    it('should make HTTP call and do successful clean up', () => {
      component.jobseekerId = '123456';
      component.isLoading = false;
      component.isError = false;

      component.recipientSubject = 'This kandidaat is a good boy';
      component.recipientEmailAddress = '<EMAIL>';
      component.motivation = 'Motivation';
      component.cvAttached = true;

      stub.HttpService.post.and.returnValue(throwError(new Error()));

      component.shareProfile();

      expect(stub.HttpService.post).toHaveBeenCalledTimes(1);
      expect(stub.HttpService.post).toHaveBeenCalledWith(`/dashboard/api/recruiter/job-seeker/${component.jobseekerId}/share`, {
        jobSeekerProfileUrl: window.location.href,
        subject: component.recipientSubject,
        recipientEmailAddress: component.recipientEmailAddress,
        motivation: component.motivation,
        isCvAttached: component.cvAttached
      });
      expect(component.isLoading).toBe(false);
      expect(component.isError).toBe(true);
    });

    it('should not make a new HTTP call if already sending data', () => {
      component.jobseekerId = '123456';
      component.isLoading = true;
      component.isError = false;

      component.recipientSubject = 'This kandidaat is a good boy';
      component.recipientEmailAddress = '<EMAIL>';
      component.motivation = 'Motivation';
      component.cvAttached = true;

      stub.HttpService.post.and.returnValue(throwError(new Error()));

      component.shareProfile();

      expect(stub.HttpService.post).not.toHaveBeenCalled();
      expect(component.isLoading).toBe(true);
    });
  });

  describe('#onSharerSuccess', () => {
    it('should set parameters if sharing is successful', () => {
      component['onSharerSuccess']();

      expect(component.isLoading).toBe(false);
      expect(component.isSuccess).toBe(true);
      expect(component.isError).toBe(false);
    });
  });

  describe('#onSharerFailure', () => {
    it('should set parameters if sharing is not successful', () => {
      component['onSharerFailure']();

      expect(component.isLoading).toBe(false);
      expect(component.isError).toBe(true);
    });
  });
});
