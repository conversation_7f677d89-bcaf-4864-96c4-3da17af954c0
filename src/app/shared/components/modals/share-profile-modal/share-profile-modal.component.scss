@import "components";

:host {
  .valid-data {
    border-left: 5px solid $success;
  }
  .invalid-data {
    border-left: 5px solid $danger;
  }

  textarea {
    border: 1px solid #d8d8d8;
    font-family: "Arial", sans-serif;
    font-size: 14px;
    line-height: 20px;
    min-height: 74px;
    outline: none;
    padding: 12px;
    resize: vertical;
    width: 100%;
  }

  @include media-breakpoint-up(md) {
    .form-group {
      display: flex;
      margin-bottom: 10px;
      &::after {
        clear: both;
        content: "";
        display: table;
      }
      label {
        float: left;
        line-height: 36px;
        width: 35%;
      }
      input, textarea {
        float: left;
        width: 65%;
      }
    }
    .form-check {
      display: block;
      margin: 0.5rem 0;
      padding-left: 0;
      position: relative;
    }
  }

  .error-message {
    background: lighten($danger, 55);
    border: 1px solid $danger;
    margin: 10px 0;
    padding: 10px;
  }
}
