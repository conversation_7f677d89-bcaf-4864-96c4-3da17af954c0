<div class="modal">
    <div class="modal-box">
        <div class="modal-contents">
            <h3>Zoekopdracht opgeslagen</h3>

            <ng-template #successfullySavedMessageTemplate>
                <i class="close-icon"
                   (click)="closeModal()"
                   data-gtm="modal-saved-search"
                   data-slug="close"></i>

                <div *ngIf="isSuccess === true">Zoekopdracht is succesvol opgeslagen.</div>
                <div *ngIf="isError === true">Er is iets misgegaan. Probeer het opnieuw.</div>
            </ng-template>

            <div *ngIf="isSuccess === false; else successfullySavedMessageTemplate">
                <i class="close-icon"
                   (click)="closeModal()"
                   data-gtm="modal-saved-search"
                   data-slug="cancel"></i>

                <div class="name-container">
                    <label for="save-search-name">Bewaren als...</label>
                    <input #name
                           name="name"
                           class="input input-gray"
                           id="save-search-name"
                           (ngModel)="name"
                           [ngClass]="{'input': true, 'valid-data': name.checkValidity(), 'invalid-data': !name.checkValidity()}"
                           required>
                </div>
                <div class="frequency-container">
                    <label for="save-search-frequency">Email updates</label>
                    <div class="form-select form-select-border">
                        <select #frequency
                                name="frequency"
                                class="input input-gray"
                                id="save-search-frequency"
                                (ngModel)="frequency"
                                required
                                data-gtm="modal-saved-search"
                                [attr.data-slug]="frequency.value">
                            <option value="Nooit" [selected]="true">Geen updates</option>
                            <option value="Dagelijks">Dagelijkse updates</option>
                            <option value="Wekelijks">Wekelijkse updates</option>
                        </select>
                    </div>
                </div>
                <div class="modal-actions">
                    <button class="btn btn-transparent cancel"
                            (click)="closeModal()"
                            data-gtm="modal-saved-search"
                            data-slug="cancel">
                        annuleren
                    </button>
                    <button class="confirm btn btn-primary"
                            [ngClass]="{'confirm btn btn-primary': true, 'disabled': !(name.checkValidity())}"
                            (click)="saveSearch(name.value, frequency.value)"
                            data-gtm="modal-saved-search"
                            data-slug="submit">
                        bewaren
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
