import { Component, Input } from '@angular/core';
import { Store } from '@ngrx/store';
import { AppState } from '../../../../types/app-state';
import { Filter, FilterOption, FilterTypes } from '../../../../store/models/filter.modal';
import * as ModalsActions from '../../../../store/actions/modals/modals.actions';
import { SearchParameters } from '../../../../classes';
import { SavedSearchService } from '../../../services';

@Component({
  selector: 'app-save-search-modal',
  templateUrl: './save-search-modal.component.html',
  styleUrls: ['save-search-modal.component.scss']
})
export class SaveSearchModalComponent {
  @Input()
  searchQuery: { [key: string]: string[] };
  @Input()
  locationQuery: string;
  @Input()
  filters: Array<Filter>;
  public name: string;
  public isSuccess = false;
  public isError = false;

  constructor(public store: Store<AppState>, private savedSearchService: SavedSearchService) {
    this.onSaveSearchSuccess = this.onSaveSearchSuccess.bind(this);
    this.onSaveSearchFailure = this.onSaveSearchFailure.bind(this);
  }

  closeModal() {
    this.store.dispatch(new ModalsActions.CloseModals());
    this.isSuccess = false;
    this.isError = true;
  }

  saveSearch(name: string, mailingFrequency: string) {
    const searchParameters = new SearchParameters();

    searchParameters.updatedDate = 'Afgelopen 6 maanden';
    searchParameters.term = this.searchQuery ? this.searchQuery : null;
    searchParameters.location = this.locationQuery ? this.locationQuery : null;

    this.filters.forEach((filter: Filter) => {
      filter.filterOptions.forEach((filterOption: FilterOption) => {
        if (filterOption.selected) {
          switch (filter.type) {
            case FilterTypes.Dropdown:
              searchParameters[filter.name] = filterOption.name;
              break;
            case FilterTypes.CheckBox:
            case FilterTypes.AutoComplete:
              searchParameters[filter.name].push(filterOption.name);
              break;
          }
        }
      });
    });

    this.savedSearchService
      .post({
        name: name,
        frequency: mailingFrequency,
        searchParameters: searchParameters
      })
      .subscribe(this.onSaveSearchSuccess, this.onSaveSearchFailure);
  }

  private onSaveSearchSuccess() {
    this.isSuccess = true;
    this.isError = false;
  }

  private onSaveSearchFailure() {
    this.isError = true;
    this.isSuccess = false;
  }
}
