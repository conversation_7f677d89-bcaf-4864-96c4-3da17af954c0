import { TestBed, ComponentFixture } from '@angular/core/testing';
import { SaveSearchModalComponent } from './save-search-modal.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject, of } from 'rxjs';
import { SavedSearchService } from '../../../services';
import { initialFiltersState } from '../../../../store/reducers/filter/filter.config';
import { Filter, FilterOption, FilterTypes } from '../../../../store/models/filter.modal';
import * as ModalsActions from '../../../../store/actions/modals/modals.actions';
import { SearchParameters } from '../../../../classes';

describe('SaveSearchModalComponent', () => {
  let componentFixture: ComponentFixture<SaveSearchModalComponent>,
    component: SaveSearchModalComponent,
    stub: any,
    filters: Array<Filter>,
    name: string,
    searchQuery: { [key: string]: string[] },
    locationQuery: string,
    frequency: string;

  beforeEach(() => {
    filters = initialFiltersState.filters.map((filter: Filter) => {
      filter.filterOptions = filter.filterOptions.map((filterOption: FilterOption, index: number) => {
        filterOption.selected = index === 0;

        return filterOption;
      });

      return filter;
    });

    name = 'Foo';
    searchQuery = { all: [''] };
    locationQuery = 'Bar';
    frequency = 'Dagelijks';

    stub = {
      Store: jasmine.createSpyObj('Store', ['dispatch', 'select']),
      SavedSearchService: jasmine.createSpyObj('SavedSearchService', ['post', 'get', 'del'])
    };

    stub.SavedSearchService.post.and.returnValue(of(1));

    stub.StoreObservable = new Subject();
    stub.Store.select.and.callFake((f: Function) => stub.StoreObservable.asObservable().map(f));

    TestBed.configureTestingModule({
      declarations: [SaveSearchModalComponent],
      providers: [{ provide: Store, useValue: stub.Store }, { provide: SavedSearchService, useValue: stub.SavedSearchService }],
      schemas: [NO_ERRORS_SCHEMA]
    });

    TestBed.compileComponents();

    componentFixture = TestBed.createComponent(SaveSearchModalComponent);

    componentFixture.detectChanges();
    component = componentFixture.componentInstance;
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('#closeModal', () => {
    it('dispatch a close modal action when sluiten is clicked', () => {
      component['isSuccess'] = false;
      component['isError'] = false;

      component.closeModal();

      expect(stub.Store.dispatch).toHaveBeenCalledWith(new ModalsActions.CloseModals());
      expect(component['isSuccess']).toBe(false);
      expect(component['isError']).toBe(true);
    });
  });

  describe('#saveSearch', () => {
    it('should send all selected filters', () => {
      component.filters = filters;
      component.searchQuery = searchQuery;
      component.locationQuery = locationQuery;

      component.saveSearch(name, frequency);

      const expectedSavedSearchData = new SearchParameters();

      expectedSavedSearchData.updatedDate = 'Afgelopen 6 maanden';
      expectedSavedSearchData.term = searchQuery;
      expectedSavedSearchData.location = locationQuery;

      filters.forEach((filter: Filter) => {
        filter.filterOptions.forEach((filterOption: FilterOption) => {
          if (filterOption.selected) {
            switch (filter.type) {
              case FilterTypes.Dropdown:
                expectedSavedSearchData[filter.name] = filterOption.name;
                break;
              case FilterTypes.CheckBox:
              case FilterTypes.AutoComplete:
                if (typeof expectedSavedSearchData[filter.name] === 'undefined') {
                  expectedSavedSearchData[filter.name] = [];
                }

                expectedSavedSearchData[filter.name].push(filterOption.name);
                break;
            }
          }
        });
      });

      expect(stub.SavedSearchService.post).toHaveBeenCalledWith({
        name: name,
        frequency: frequency,
        searchParameters: expectedSavedSearchData
      });
    });

    it('should send null as search query if not present', () => {
      component.filters = filters;
      component.searchQuery = undefined;
      component.locationQuery = locationQuery;

      component.saveSearch(name, frequency);

      const expectedSavedSearchData = new SearchParameters();

      expectedSavedSearchData.updatedDate = 'Afgelopen 6 maanden';
      expectedSavedSearchData.term = null;
      expectedSavedSearchData.location = locationQuery;

      filters.forEach((filter: Filter) => {
        filter.filterOptions.forEach((filterOption: FilterOption) => {
          if (filterOption.selected) {
            switch (filter.type) {
              case FilterTypes.Dropdown:
                expectedSavedSearchData[filter.name] = filterOption.name;
                break;
              case FilterTypes.CheckBox:
              case FilterTypes.AutoComplete:
                if (typeof expectedSavedSearchData[filter.name] === 'undefined') {
                  expectedSavedSearchData[filter.name] = [];
                }

                expectedSavedSearchData[filter.name].push(filterOption.name);
                break;
            }
          }
        });
      });

      expect(stub.SavedSearchService.post).toHaveBeenCalledWith({
        name: name,
        frequency: frequency,
        searchParameters: expectedSavedSearchData
      });
    });

    it('should send null as location query if not present', () => {
      component.filters = filters;
      component.searchQuery = searchQuery;
      component.locationQuery = undefined;

      component.saveSearch(name, frequency);

      const expectedSavedSearchData = new SearchParameters();

      expectedSavedSearchData.updatedDate = 'Afgelopen 6 maanden';
      expectedSavedSearchData.term = searchQuery;
      expectedSavedSearchData.location = null;

      filters.forEach((filter: Filter) => {
        filter.filterOptions.forEach((filterOption: FilterOption) => {
          if (filterOption.selected) {
            switch (filter.type) {
              case FilterTypes.Dropdown:
                expectedSavedSearchData[filter.name] = filterOption.name;
                break;
              case FilterTypes.CheckBox:
              case FilterTypes.AutoComplete:
                if (typeof expectedSavedSearchData[filter.name] === 'undefined') {
                  expectedSavedSearchData[filter.name] = [];
                }

                expectedSavedSearchData[filter.name].push(filterOption.name);
                break;
            }
          }
        });
      });

      expect(stub.SavedSearchService.post).toHaveBeenCalledWith({
        name: name,
        frequency: frequency,
        searchParameters: expectedSavedSearchData
      });
    });
  });

  describe('#onSaveSearchSuccess', () => {
    it('should update flags when search saved successfully', () => {
      component['isSuccess'] = false;
      component['isError'] = false;

      component['onSaveSearchSuccess']();

      expect(component['isSuccess']).toBe(true);
      expect(component['isError']).toBe(false);
    });
  });

  describe('#onSaveSearchFailure', () => {
    it('should update flags when search saved successfully', () => {
      component['isSuccess'] = false;
      component['isError'] = false;

      component['onSaveSearchFailure']();

      expect(component['isSuccess']).toBe(false);
      expect(component['isError']).toBe(true);
    });
  });
});
