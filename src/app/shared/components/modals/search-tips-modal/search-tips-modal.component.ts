import { Component } from '@angular/core';
import * as ModalsActions from '../../../../store/actions/modals/modals.actions';
import { Store } from '@ngrx/store';
import { AppState } from '../../../../types/app-state';

@Component({
  selector: 'app-search-tips-modal',
  templateUrl: './search-tips-modal.component.html',
  styleUrls: ['search-tips-modal.component.scss']
})
export class SearchTipsModalComponent {
  constructor(private store: Store<AppState>) {}

  closeModal() {
    this.store.dispatch(new ModalsActions.CloseModals());
  }
}
