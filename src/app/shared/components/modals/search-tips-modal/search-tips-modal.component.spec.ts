import { TestBed, ComponentFixture } from '@angular/core/testing';
import { SearchTipsModalComponent } from './search-tips-modal.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import * as ModalsActions from '../../../../store/actions/modals/modals.actions';
import { Store } from '@ngrx/store';

describe('SearchTipsModalComponent', () => {
  let componentFixture: ComponentFixture<SearchTipsModalComponent>, component: SearchTipsModalComponent;
  let stub: any;

  beforeEach(() => {
    stub = {
      Store: jasmine.createSpyObj('Store', ['dispatch'])
    };

    TestBed.configureTestingModule({
      declarations: [SearchTipsModalComponent],
      providers: [{ provide: Store, useValue: stub.Store }],
      schemas: [NO_ERRORS_SCHEMA]
    });

    TestBed.compileComponents();
    componentFixture = TestBed.createComponent(SearchTipsModalComponent);
    componentFixture.detectChanges();
    component = componentFixture.componentInstance;
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('#closeModal', () => {
    it('dispatch a close modal action when close action is invoked', () => {
      component.closeModal();

      expect(stub.Store.dispatch).toHaveBeenCalledWith(new ModalsActions.CloseModals());
    });
  });
});
