@import "components";

:host {

  h4 {
    span {
      text-transform: uppercase;
    }
  }

  span {
    font-weight: 700;

    &.emphasis {
      color: $generic-highlight-color;
    }
  }

  .figure {
    text-align: center;
    width: 100%;

    div {
      &.operator {
        background: {
          position: center center;
          repeat: no-repeat;
          size: contain;
        }
        height: 200px;
        margin: 20px 0;
        width: 100%;

        &__and {
          background-image: url("#{$project-operators-folder}and.svg");
        }
        &__not {
          background-image: url("#{$project-operators-folder}not.svg");
        }
        &__not-parenthesis {
          background-image: url("#{$project-operators-folder}not-parenthesis.svg");
          height: 300px;
        }
        &__or {
          background-image: url("#{$project-operators-folder}or.svg");
        }
        &__parenthesis {
          background-image: url("#{$project-operators-folder}parenthesis.svg");
          height: 300px;
        }
        &__quotes {
          background-image: url("#{$project-operators-folder}quotes.svg");
        }
        &__wildcard {
          background-image: url("#{$project-operators-folder}wildcard.svg");
          height: 280px;
        }
      }
    }
  }
}
