<div class="modal">
    <div class="modal-box modal-box--scrollable modal-box--larger">
        <div class="modal-contents">
            <i class="close-icon"
               (click)="closeModal()"
               data-gtm="modal-search-tips"
               data-slug="cancel"></i>

            <h3>Hoe vind je de beste kandidaten?</h3>

            <p>Duizenden mensen staan open voor een nieuwe baan, maar hoe vind je precies die kandidaten die passen bij je vacature(s)?
                Door gebruik te maken van boolean search. Probeer eens een van onderstaande combinaties en vind de meest geschikte
                kandidaten. Succes!</p>

            <h4><PERSON><PERSON> zoekopdracht</h4>

            <p>Als er tussen twee woorden een spatie staat, dan vullen we dit altijd in als EN. Dus juridisch medewerker wordt juridisch EN
                medewerker.</p>


            <h4><PERSON><PERSON><PERSON>dra<PERSON> met boolean operators</h4>

            <p>Je kunt gerichter zoeken met behulp van boolean operators. Dit zijn woorden of leestekens die je gebruikt om aan de
                zoekopdracht specifieke eisen of kenmerken mee te geven. Je kunt hiermee bijvoorbeeld bepalen dat een zoekwoord
                verplicht moet voorkomen, of juist niet, of dat ze in een bepaalde combinatie moeten voorkomen. Hieronder behandelen we
                de boolean operators en geven we daar voorbeelden bij.</p>

            <hr/>

            <h4><span class="emphasis">EN</span></h4>

            <p>Zoeken naar kandidaten die <span>beide</span> zoektermen ergens in hun profiel hebben staan.</p>

            <div class="figure">
                <div class="operator operator__and"></div>
                <div>developer <span class="emphasis">EN</span> front-end</div>
            </div>

            <hr/>

            <h4><span class="emphasis">OF</span></h4>

            <p>Zoeken naar kandidaten <span>in ieder geval één van de zoektermen</span> ergens in hun profiel hebben staan. Beide kan ook, maar is
                niet verplicht.</p>

            <div class="figure">
                <div class="operator operator__or"></div>
                <div>juridisch <span class="emphasis">OF</span> medewerker</div>
            </div>

            <hr/>

            <h4><span class="emphasis">NIET</span></h4>

            <p>Resultaten met een bepaalde zoekterm <span>uitsluiten</span> van de zoekresultaten.</p>

            <div class="figure">
                <div class="operator operator__not"></div>
                <div>juridisch <span class="emphasis">NIET</span> assistent</div>
            </div>

            <hr/>

            <h4>Aanhalingstekens <span class="emphasis">"</span>...<span class="emphasis">"</span></h4>

            <p>Zoeken naar kandidaten die de <span>exacte combinatie</span> van zoektermen in hun profiel hebben staan. Je kunt alles tussen de
                aanhalingstekens beschouwen als één woord.</p>

            <div class="figure">
                <div class="operator operator__quotes"></div>
                <div><span class="emphasis">"</span>juridisch medewerker<span class="emphasis">"</span></div>
            </div>

            <hr/>

            <h4>Asterisk/wildcard <span class="emphasis">*</span></h4>

            <p>Een asterisk of wildcard gebruik je om zoekopdrachten <span>flexibel te deﬁniëren</span>. Op de plek van de * mag alles staan. Deze
                operator gebruik je om bijvoorbeeld meerdere spellingsvormen van een woord toe te laten zonder ze allemaal apart in de
                zoekopdracht te zetten.</p>

            <p>Voorbeeld: je zoekt specifiek naar een <span>Juridisch medewerker</span>, maar je wil kandidaten die Juridisch<span>e</span> ingevuld hebben daarbij
                niet uitsluiten. In dat geval vul je "Juridisch<span class="emphasis">*</span> medewerker" in. Hierbij mag achter <span>juridisch</span> van alles staan zolang het maar
                meteen aan het woord juridisch vastzit. In dit geval worden zowel <span>juridisch</span> als <span>juridische</span> gevonden.</p>

            <div class="figure">
                <div class="operator operator__wildcard"></div>
                <div><span class="emphasis">"</span>juridisch<span class="emphasis">*</span> medewerker<span class="emphasis">"</span></div>
            </div>

            <hr/>

            <h4>Haakjes <span class="emphasis">(...)</span></h4>

            <p>Haakjes <span>groeperen woorden</span> in een zoekopdracht. Alles wat buiten de haakjes staat is van toepassing op de hele groep, niet
                op de individuele zoektermen.</p>

            <p>Voorbeeld 1: je zoekt naar kandidaten die <span>juridisch medewerker</span>, <span>juridisch adviseur</span> of <span>juridisch secretaresse</span> in hun
                profiel hebben staan. Nu kun je alle drie deze termen apart invullen, maar het kan ook korter.</p>

            <div class="figure">
                <div class="operator operator__parenthesis"></div>
                <div>juridisch <span class="emphasis">EN (</span>medewerker <span class="emphasis">OF</span> adviseur <span class="emphasis">OF</span> secretaresse<span class="emphasis">)</span></div>
            </div>

            <p>Voorbeeld 2: je zoekt naar kandidaten die zowel <span>juridisch</span> als <span>medewerker</span>, maar niet <span>arbeidsrecht</span> en <span>strafrecht</span> in hun
                profiel hebben staan.</p>

            <p>Zonder de haakjes zou <span>strafrecht</span> wel in het profiel moeten staan.</p>


            <div class="figure">
                <div class="operator operator__not-parenthesis"></div>
                <div>juridisch <span class="emphasis">EN</span> medewerker <span class="emphasis">NIET (</span>arbeidsrecht <span class="emphasis">OF</span> strafrecht<span class="emphasis">)</span></div>
            </div>

            <hr/>

            <h4>Engels</h4>

            <p>Alle boolean operators kunnen ook in het engels gebruikt worden. Dus AND, OR, NOT.
                <br/>+ - A ! en | kunnen ook gebruikt worden.</p>

            <h4>Aandachtspunten</h4>

            <p>* Let op met het gebruik van — en & en andere niet-letters.<br/>
                Als in een zoekopdracht bijvoorbeeld HBO-V staat, dan wordt de - als NIET gezien. Wil je HBO-V als zoekterm gebruiken, vul
                dan in: "HBO-V".</p>

            <p>* Sluit de zoekopdracht altijd af.<br/>
                Als je geen sluitend haakje of aanhalingsteken gebruikt aan het einde van je zoekopdracht in het zoekveld, worden de
                resultaten onvoorspelbaar. Het aantal gevonden kandidaten kan uitkomen op 0 of juist groter worden dan bedoeld.
            </p>
        </div>
        <div class="modal-actions">
            <button class="btn btn-primary"
                    (click)="closeModal()"
                    data-gtm="modal-search-tips"
                    data-slug="close">
                sluiten
            </button>
        </div>
    </div>
</div>
