@import "components";

:host {
  align-items: center;
  display: flex;
  justify-content: center;

  @include media-breakpoint-up(md) {
    display: inline-block;
    float: left;
  }
  .pagination-container {
    clear: both;
    display: flex;
  }

  .pagination {
    align-items: center;
    display: flex;
    justify-content: center;
    padding-bottom: 25px;

    > span {
      color: $dpes-seafoam-blue;
      cursor: pointer;
      display: none;
      font-size: 15px;
      line-height: 2;
      padding: 12px;
      position: relative;
      text-align: center;
      @include media-breakpoint-up(md) {
        display: inline-block;
        min-width: 38px;
        padding: 8px;
      }
      &.active {
        background: transparent;
        color: $text-color;
        display: inline-block;
        font-size: 17px;
        line-height: 1.76;
        order: 3;
        padding-right: 0;
        @include media-breakpoint-up(md) {
          order: 0;
          padding-right: 8px;
        }
      }
      &.prev, &.next,
      &.prev-first, &.next-last {
        color: transparent;
        font-size: 0;
        height: 100%;
        line-height: 10px;
        @include media-breakpoint-up(md) {
          height: 38px;
          min-width: 38px;
        }
        &.gray {
          pointer-events: none;
        }
      }
      &.prev, &.next {
        display: inline-block;
        width: 44px;
      }
      &.prev::before, &.next::before,
      &.prev-first::before, &.next-last::before,
      &.prev-first::after, &.next-last::after {
        border: 2px solid $dpes-seafoam-blue;
        border-bottom: none;
        border-right: none;
        content: "";
        height: 10px;
        position: absolute;
        top: 24px;
        width: 10px;
        @include media-breakpoint-up(md) {
          height: 10px;
          top: 15px;
          width: 10px;
        }
      }
      &.prev, &.prev-first {
        order: 0;

        &::before, &::after {
          left: 50%;
          margin-left: -5px;
          -ms-transform: rotate(-45deg) skew(-15deg, -15deg);
          -moz-transform: rotate(-45deg) skew(-15deg, -15deg);
          -o-transform: rotate(-45deg) skew(-15deg, -15deg);
          -webkit-transform: rotate(-45deg) skew(-15deg, -15deg);
          transform: rotate(-45deg) skew(-15deg, -15deg);
        }
        &::after {
          margin-left: 0;
        }
      }
      &.next, &.next-last {
        &::before, &::after {
          margin-right: -5px;
          right: 50%;
          -ms-transform: rotate(135deg) skew(-15deg, -15deg);
          -moz-transform: rotate(135deg) skew(-15deg, -15deg);
          -o-transform: rotate(135deg) skew(-15deg, -15deg);
          -webkit-transform: rotate(135deg) skew(-15deg, -15deg);
          transform: rotate(135deg) skew(-15deg, -15deg);
        }
        &::after {
          margin-right: 0;
        }
        order: 10;
      }
      &.prev-first, &.next-last {
        display: inline-block;
        min-width: 44px;
        @include media-breakpoint-up(md) {
          display: none;
        }
      }
      &.gray::before, &.gray::after {
        border-color: #efeef0;
      }
      &.total-pages {
        color: #9f9ca5;
        display: inline-block;
        order: 4;
        padding-left: 7px;

        &.last-page-in-pages {
          @include media-breakpoint-up(md) {
            display: none;
          }
        }

        &::before {
          content: "/";
        }

        @include media-breakpoint-up(md) {
          color: $dpes-seafoam-blue;
          order: 0;
          padding-left: 10px;

          &::before {
            content: "";
            display: none;
          }
        }
        @include media-breakpoint-up(lg) {
          padding-left: 15px;
        }
      }
      &.dots {
        color: #9f9ca5;
        pointer-events: none;
      }
    }
  }
}
