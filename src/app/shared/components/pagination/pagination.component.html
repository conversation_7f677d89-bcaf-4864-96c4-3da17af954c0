<div class="pagination-container">
    <div class="pagination" data-gtm="pagination">
        <span [attr.data-gtm]="dataGtm"
              data-slug="first"
              *ngIf="pages?.currentPage > 1"
              (click)="changePage(1)"
              class="prev-first">
            &laquo;
        </span>
        <span [attr.data-gtm]="dataGtm"
              data-slug="previous"
              (click)="changePage(pages?.currentPage - 1)"
              [ngClass]="{'gray': pages?.currentPage == 1}"
              class="prev">
            &lsaquo;
        </span>
        <span [attr.data-gtm]="dataGtm"
              data-slug="1"
              *ngIf="pages?.currentPage > 5 && pages?.totalPages > 5"
              (click)="changePage(1)"
              class="prev-first-number">
            1
        </span>
        <span *ngIf="pages?.currentPage > 5 && pages?.totalPages > 5"
              class="dots">
            ...
        </span>
        <span [attr.data-gtm]="dataGtm"
              [attr.data-slug]="page"
              *ngFor="let page of pages?.pages;"
              (click)="changePage(page)"
              [ngClass]="{ 'active': page === pages?.currentPage }">
            {{ page }}
        </span>

        <span *ngIf="pages?.currentPage < (pages?.totalPages - 5) && pages?.totalPages > 5"
              class="dots">
            ...
        </span>

        <span [attr.data-gtm]="dataGtm"
              [attr.data-slug]="pages?.totalPages"
              *ngIf="pages?.totalItems < maxAmountOfResults"
              [ngClass]="{ 'last-page-in-pages': pages?.totalPages <= pages?.endPage}"
              (click)="changePage(pages?.totalPages)"
              class="total-pages">
            {{ pages?.totalPages }}
        </span>
        <span [attr.data-gtm]="dataGtm"
              data-slug="next"
              (click)="changePage(pages?.currentPage + 1)"
              [ngClass]="{ 'gray': pages?.currentPage == pages?.endPage}"
              class="next">
            &rsaquo;
        </span>
        <span [attr.data-gtm]="dataGtm"
              data-slug="last"
              *ngIf="pages?.currentPage < pages?.endPage && pages?.totalItems < maxAmountOfResults"
              (click)="changePage(pages?.totalPages)"
              class="next-last">
            &raquo;
        </span>
    </div>
</div>
