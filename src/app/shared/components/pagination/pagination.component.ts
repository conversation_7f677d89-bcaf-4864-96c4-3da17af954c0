import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges, OnInit } from '@angular/core';
import { Pages, PaginationService } from '../../services';

@Component({
  selector: 'app-pagination',
  templateUrl: './pagination.component.html',
  styleUrls: ['./pagination.component.scss']
})
export class PaginationComponent implements OnChanges, OnInit {
  public pages: Pages;
  public maxAmountOfResults = 10000;
  public dataGtm: string;

  @Input()
  public currentPage: number;
  @Input()
  public resultCount: number;
  @Input()
  public pageSize = 10;
  @Input()
  public type: string;
  @Output()
  public pageChange = new EventEmitter<number>();

  constructor(private pagination: PaginationService) {}

  ngOnInit(): void {
    this.dataGtm = `${this.type}-pagination`;
  }

  ngOnChanges(changes: SimpleChanges) {
    let currentPage = this.currentPage;
    let resultCount = this.resultCount;
    let pageSize = this.pageSize;

    if (changes.currentPage && changes.currentPage.currentValue) {
      currentPage = changes.currentPage.currentValue;
    }

    if (changes.resultCount && changes.resultCount.currentValue) {
      resultCount = changes.resultCount.currentValue;
    }

    if (changes.pageSize && changes.pageSize.currentValue) {
      pageSize = changes.pageSize.currentValue;
    }

    this.pages = this.getPages(resultCount, currentPage, pageSize);
  }

  private getPages(resultCount: number, currentPage: number, pageSize: number) {
    return this.pagination.getPages(resultCount, currentPage, pageSize);
  }

  public changePage(pageNumber: number) {
    this.pageChange.emit(pageNumber);
  }
}
