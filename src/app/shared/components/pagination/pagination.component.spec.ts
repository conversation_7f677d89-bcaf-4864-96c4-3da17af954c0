import { TestBed, ComponentFixture } from '@angular/core/testing';
import { PaginationComponent } from './pagination.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { PaginationService } from '../../services';

describe('PaginationComponent', () => {
  const stub: any = {};
  let componentFixture: ComponentFixture<PaginationComponent>, component: PaginationComponent;

  beforeEach(() => {
    stub.PaginationService = jasmine.createSpyObj('PaginationService', ['getPages']);
    TestBed.configureTestingModule({
      providers: [{ provide: PaginationService, useValue: stub.PaginationService }],
      declarations: [PaginationComponent],
      schemas: [NO_ERRORS_SCHEMA]
    });

    TestBed.compileComponents();
    componentFixture = TestBed.createComponent(PaginationComponent);
    componentFixture.detectChanges();
    component = componentFixture.componentInstance;
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('#ngOnInit', () => {
    it('should set type of pagination', () => {
      component.type = 'favorites';

      component.ngOnInit();

      expect(component.dataGtm).toEqual(`${component.type}-pagination`);
    });
  });

  describe('#ngOnChanges', () => {
    let c: any;
    beforeEach(() => {
      c = component;
      spyOn(c, 'getPages');
    });

    it('should get new pages with new currentPage, resultCount and pageSize', () => {
      component.ngOnChanges(<any>{
        resultCount: { currentValue: 200 },
        currentPage: { currentValue: 2 },
        pageSize: { currentValue: 25 }
      });

      expect(c.getPages).toHaveBeenCalledWith(200, 2, 25);
    });

    it('should get new pages with old currentPage, resultCount and pageSize with no changes', () => {
      component.resultCount = 100;
      component.currentPage = 1;
      component.pageSize = 10;

      component.ngOnChanges({});

      expect(c.getPages).toHaveBeenCalledWith(100, 1, 10);
    });
  });

  describe('#changePage', () => {
    it('should emit a pageChange event', () => {
      spyOn(component.pageChange, 'emit');
      component.changePage(1);

      expect(component.pageChange.emit).toHaveBeenCalledWith(1);
    });
  });

  describe('#getPages', () => {
    let c: any;
    it('should getPages from pagination service', () => {
      stub.PaginationService.getPages.and.returnValue('pages');
      c = component;
      expect(c.getPages()).toBe('pages');
    });
  });
});
