@import "components";

.read-more {
  &__content {
    &--collapsed {
      max-height: 100px;
      overflow-y: hidden;
      position: relative;

      @include media-breakpoint-down(sm) {
        max-height: 70px;
      }

      &::after {
        content: " ";
        height: 70px;
        left: 0;
        position: absolute;
        top: 30px;
        width: 100%;
        @include gradient-y($readmore-collapsed-fadein-color, $readmore-collapsed-fadeout-color);

        @include media-breakpoint-down(sm) {
          height: 53px;
          top: 17px;
        }
      }
    }
  }

  a.read-more__actions {
    color: $readmore-color;
    cursor: pointer;
    display: inline-block;
    font-size: 15px;
    text-decoration: underline;

    &:hover {
      text-decoration: underline;
    }
  }
}
