import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-read-more',
  templateUrl: './read-more.component.html',
  styleUrls: ['./read-more.component.scss']
})
export class ReadMoreComponent implements OnInit {
  @Input()
  fullText: string;
  @Input()
  slug: string;
  private limit = 200;
  public readMore = false;
  public expanded = false;
  public dataGtmToExpand: string;
  public dataGtmToCollapse: string;

  ngOnInit(): void {
    this.readMore = this.fullText !== undefined && this.fullText.length > this.limit;
    this.slug = this.slug.replace(/\s/g, '');
    this.dataGtmToExpand = `acc-${this.slug}-description-show`;
    this.dataGtmToCollapse = `acc-${this.slug}-description-hide`;
  }

  expand() {
    this.expanded = true;
  }

  collapse() {
    this.expanded = false;
  }
}
