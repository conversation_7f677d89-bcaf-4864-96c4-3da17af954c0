import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { By } from '@angular/platform-browser';
import { ReadMoreComponent } from './read-more.component';
import { SearchHighlightPipe } from '../../pipes';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs/index';

const longText =
  'Test with very long text that needs to be concatenated if it is more than 200 characters long ' +
  'and we shall see what actually happens in this very nicely written test. What? It is still not 200 ' +
  'characters? What else can I say?';

const shortText = 'Test with short text';

@Component({
  selector: 'app-read-more-wrapper',
  template: '<app-read-more>' + longText + '</app-read-more><app-read-more>' + shortText + '</app-read-more>'
})
class ReadMoreWrapperComponent {}

describe('ReadMoreComponent', () => {
  let componentLong: ReadMoreComponent;
  let componentShort: ReadMoreComponent;
  let fixture: ComponentFixture<ReadMoreWrapperComponent>;
  const stub: any = {};

  beforeEach(async(() => {
    stub.Store = new Subject();

    TestBed.configureTestingModule({
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [{ provide: Store, useValue: stub.Store }],
      declarations: [ReadMoreComponent, ReadMoreWrapperComponent, SearchHighlightPipe]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ReadMoreWrapperComponent);

    componentLong = fixture.debugElement.query(By.directive(ReadMoreComponent)).componentInstance;
    componentLong.fullText = longText;
    componentLong.slug = 'training';

    componentShort = fixture.debugElement.queryAll(By.directive(ReadMoreComponent))[1].componentInstance;
    componentShort.fullText = shortText;
    componentShort.slug = 'experience';

    fixture.detectChanges();
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(componentLong).toBeTruthy();
    });
  });

  describe('#ngOnInit', () => {
    it('should display the read more element', () => {
      componentLong.ngOnInit();

      expect(componentLong.readMore).toEqual(true);
    });

    it('should not display the read more element', () => {
      componentShort.ngOnInit();

      expect(componentShort.readMore).toEqual(false);
    });

    it('should initialize data gtm attributes', () => {
      componentShort.ngOnInit();

      expect(componentShort.dataGtmToExpand).toEqual(`acc-${componentShort.slug}-description-show`);
      expect(componentShort.dataGtmToCollapse).toEqual(`acc-${componentShort.slug}-description-hide`);

      componentLong.ngOnInit();

      expect(componentLong.dataGtmToExpand).toEqual(`acc-${componentLong.slug}-description-show`);
      expect(componentLong.dataGtmToCollapse).toEqual(`acc-${componentLong.slug}-description-hide`);
    });

    it('should remove spaces from slug for data gtm attributes', () => {
      componentShort.slug = 'wrong slug';

      componentShort.ngOnInit();

      expect(componentShort.slug).toEqual('wrongslug');
    });
  });

  describe('#expand', () => {
    it('should expand full text', () => {
      componentLong.fullText = shortText;
      componentLong.expand();

      expect(componentLong.expanded).toEqual(true);
    });
  });

  describe('#collapse', () => {
    it('should collapse full text', () => {
      componentLong.fullText = longText;
      componentLong.collapse();

      expect(componentLong.expanded).toEqual(false);
    });
  });
});
