<div class="read-more">
    <div [innerHTML]="fullText | searchHighlight"
         class="read-more__content"
         [ngClass]="{'read-more__content--collapsed': readMore && !expanded}"></div>

    <a *ngIf="readMore && !expanded"
       (click)="expand()"
       class="read-more__actions"
       [attr.data-gtm]="dataGtmToExpand"
       href="javascript:">
        Toon meer
    </a>

    <a *ngIf="readMore && expanded"
       (click)="collapse()"
       class="read-more__actions"
       [attr.data-gtm]="dataGtmToCollapse"
       href="javascript:">
        Toon minder
    </a>
</div>
