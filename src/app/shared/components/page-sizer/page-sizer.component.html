<div class="page-size" data-gtm="page-size">
    <label class="label">Aantal per pagina:</label>
    <div class="form-select" data-gtm="page-size-dropdown">
        <select class="value"
                [(ngModel)]="pageSize"
                (ngModelChange)="onSelectNumberOfItems($event)"
                [attr.data-gtm]="dataGtm"
                [attr.data-slug]="pageSize">
            <option *ngFor="let numberOfItems of itemsPerPageList"
                    [value]="numberOfItems">
                {{numberOfItems}}
            </option>
        </select>
    </div>
</div>
