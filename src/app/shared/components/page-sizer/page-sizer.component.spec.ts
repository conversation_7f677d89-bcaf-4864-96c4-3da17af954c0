import { TestBed, ComponentFixture } from '@angular/core/testing';
import { PageSizerComponent } from './page-sizer.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('PageSizerComponent', () => {
  let componentFixture: ComponentFixture<PageSizerComponent>, component: PageSizerComponent;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [],
      declarations: [PageSizerComponent],
      schemas: [NO_ERRORS_SCHEMA]
    });

    TestBed.compileComponents();
    componentFixture = TestBed.createComponent(PageSizerComponent);
    componentFixture.detectChanges();
    component = componentFixture.componentInstance;
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('#ngOnInit', () => {
    it('should set type of page sizer for gtm', () => {
      component.type = 'bottom';

      component.ngOnInit();

      expect(component.dataGtm).toEqual(`jobseeker-results-change-${component.type}`);
    });
  });

  describe('#onSelectNumberOfItems', () => {
    it('should emit a pageChange event', () => {
      spyOn(component.updateItemsPerPage, 'emit');
      const numberItemsPerPage = 25;
      component.onSelectNumberOfItems(numberItemsPerPage);

      expect(component.updateItemsPerPage.emit).toHaveBeenCalledWith(numberItemsPerPage);
      expect(component.updateItemsPerPage.emit).toHaveBeenCalledTimes(1);
    });
  });
});
