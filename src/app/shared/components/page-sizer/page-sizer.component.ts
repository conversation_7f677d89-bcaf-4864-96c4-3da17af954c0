import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-page-sizer',
  templateUrl: './page-sizer.component.html',
  styleUrls: ['./page-sizer.component.scss']
})
export class PageSizerComponent implements OnInit {
  public itemsPerPageList = [15, 25, 50, 100];
  public dataGtm: string;

  @Output()
  public updateItemsPerPage = new EventEmitter<number>();
  @Input()
  public pageSize: number;
  @Input()
  public type: string;

  constructor() {}

  ngOnInit(): void {
    this.dataGtm = `jobseeker-results-change-${this.type}`;
  }

  public onSelectNumberOfItems(numberOfItems: number) {
    this.updateItemsPerPage.emit(numberOfItems);
  }
}
