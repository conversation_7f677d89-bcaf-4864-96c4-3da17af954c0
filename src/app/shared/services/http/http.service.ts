import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpHeaders, HttpParams } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { environment, getLoginUrl } from '../../../../environments/environment';

const httpHeaders = new HttpHeaders({
  'Content-Type': 'application/json',
  'X-Site': environment.siteHeader
});

@Injectable()
export class HttpService {
  public locationReplaceFn = window.location.replace;

  constructor(private httpClient: HttpClient) {
    this.handleError = this.handleError.bind(this);
  }

  get(url: string, params?: HttpParams) {
    return this.request('get', url, {}, params);
  }

  post(url: string, data: Object) {
    return this.request('post', url, data);
  }

  put(url: string, data: Object) {
    return this.request('put', url, data);
  }

  patch(url: string, data: Object) {
    return this.request('patch', url, data);
  }

  del(url: string) {
    return this.request('delete', url);
  }

  private getRequestDomain(): string {
    return environment.profileApi.mainUrl;
  }

  private request(method: string, url: string, data?: Object, params?: HttpParams) {
    const httpOptions = { headers: httpHeaders, body: data, params: params };
    return this.httpClient.request(method, `${this.getRequestDomain()}${url}`, httpOptions).pipe(catchError(this.handleError));
  }

  private handleError(errorResponse: HttpErrorResponse) {
    const error = errorResponse.error;

    if (errorResponse.status && errorResponse.status === 401) {
      this.locationReplaceFn(getLoginUrl(window.location.href));
    }

    if (
      (errorResponse.status === 403 && error.message === 'not_migrated_recruiter') ||
      errorResponse.message === 'not_migrated_recruiter'
    ) {
      this.locationReplaceFn(environment.url.legacyPlatform);
    }

    return throwError(error);
  }
}
