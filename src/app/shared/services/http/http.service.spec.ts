import { TestBed } from '@angular/core/testing';
import { Observable, throwError } from 'rxjs';
import { HttpService } from './http.service';
import { environment } from '../../../../environments/environment';
import { HttpClient, HttpClientModule, HttpErrorResponse, HttpHeaders, HttpParams } from '@angular/common/http';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';

class MockHttp {
  get() {}
  post() {}
  put() {}
  patch() {}
  del() {}
  request() {}
}

describe('HttpService', () => {
  const stub: any = {};
  let service: HttpService;

  beforeEach(() => {
    stub.Http = new MockHttp();
    stub.document = { location: { href: '' } };
    stub.response = jasmine.createSpyObj('response', ['json']);
    stub.HttpObservable = Observable.create((obs: any) => {
      obs.next(stub.response);
    });

    service = new HttpService(<any>stub.Http);

    spyOn(stub.Http, 'request').and.returnValue(stub.HttpObservable);

    TestBed.configureTestingModule({
      imports: [HttpClientModule, HttpClientTestingModule],
      providers: [HttpService]
    });
  });

  describe('#get', () => {
    it('should call request with correct get parameters', () => {
      const s: any = service;
      spyOn(s, 'request');
      service.get('/test');
      expect(s.request).toHaveBeenCalledWith('get', '/test', {}, undefined);
    });
  });

  describe('#post', () => {
    it('should call request with correct post parameters', () => {
      const s: any = service;
      spyOn(s, 'request');
      const data = { test: 'test' };
      service.post('/test', data);
      expect(s.request).toHaveBeenCalledWith('post', '/test', data);
    });
  });

  describe('#put', () => {
    it('should call request with correct put parameters', () => {
      const s: any = service;
      spyOn(s, 'request');
      const data = { test: 'test' };
      service.put('/test', data);
      expect(s.request).toHaveBeenCalledWith('put', '/test', data);
    });
  });

  describe('#patch', () => {
    it('should call request with correct patch parameters', () => {
      const s: any = service;
      spyOn(s, 'request');
      const data = { test: 'test' };
      service.patch('/test', data);
      expect(s.request).toHaveBeenCalledWith('patch', '/test', data);
    });
  });

  describe('#del', () => {
    it('should call request with correct del parameters', () => {
      const s: any = service;
      spyOn(s, 'request');
      service.del('/test');
      expect(s.request).toHaveBeenCalledWith('delete', '/test');
    });
  });

  describe('#request', () => {
    it('should call request, with get parameters', () => {
      const s: any = service;
      const httpParams = new HttpParams({ fromString: 'a=1&b=2' });
      spyOn(s, 'request');

      service['request']('get', '/test', {}, httpParams);
      expect(s.request).toHaveBeenCalledWith('get', '/test', {}, httpParams);
    });
  });

  describe('#getRequestDomain', () => {
    it('return the main request domain', () => {
      const expectedDomain = service['getRequestDomain']();

      expect(expectedDomain).toEqual(environment.profileApi.mainUrl);
    });
  });

  describe('#handleError', () => {
    it('should navigate to login when handling 401 error', () => {
      const s: any = service;
      const httpError = <HttpErrorResponse>{
        status: 401,
        error: {
          status: 401
        }
      };

      s.locationReplaceFn = jasmine.createSpy('locationReplaceFn');

      s.handleError(httpError);

      expect(s.locationReplaceFn).toHaveBeenCalledTimes(1);
      expect(s.locationReplaceFn).toHaveBeenCalledWith('http://login-url.here');
    });

    it('should navigate to San Diego home page when handling 403 error with "not_migrated_recruiter" message', () => {
      const s: any = service;
      const httpError = <HttpErrorResponse>{
        status: 403,
        error: {
          status: 403,
          message: 'not_migrated_recruiter'
        }
      };

      s.locationReplaceFn = jasmine.createSpy('locationReplaceFn');

      s.handleError(httpError);

      expect(s.locationReplaceFn).toHaveBeenCalledTimes(1);
      expect(s.locationReplaceFn).toHaveBeenCalledWith(environment.url.legacyPlatform);
    });

    it('should call handleError on Http error', () => {
      const typeError = 'error';
      stub.Http.request.and.returnValue(throwError({ error: typeError }));

      service.get('/test').subscribe(
        res => {},
        err => {
          expect(err).toEqual(typeError);
        }
      );
    });
  });
});

describe('HttpService', () => {
  const stub: any = {};
  let service: HttpService;
  let httpClient: HttpClient;
  let httpTestingController: HttpTestingController;
  let defaultUrl: string;
  const defaultBody = 'test';
  const defaultHttpHeaders = {
    'Content-Type': 'application/json',
    'X-Site': environment.siteHeader
  };

  beforeEach(() => {
    stub.Http = new MockHttp();
    service = new HttpService(<any>stub.Http);

    TestBed.configureTestingModule({
      imports: [HttpClientModule, HttpClientTestingModule],
      providers: [HttpService]
    });

    httpClient = TestBed.get(HttpClient);
    httpTestingController = TestBed.get(HttpTestingController);

    defaultUrl = `${service['getRequestDomain']()}/test`;
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  describe('#request', () => {
    it('should set base http request', () => {
      const requestHttpOptions = {
        body: defaultBody,
        headers: new HttpHeaders(defaultHttpHeaders),
        params: <HttpParams>undefined
      };

      httpClient.request('get', defaultUrl, requestHttpOptions).subscribe(data => expect(data).toEqual(defaultBody));

      const req = httpTestingController.expectOne(defaultUrl);

      expect(req.request.method).toEqual('GET');
      expect(req.request.headers).toEqual(requestHttpOptions.headers);
      expect(req.request.headers.get('Content-Type')).toEqual(defaultHttpHeaders['Content-Type']);
      expect(req.request.headers.get('X-Site')).toEqual(defaultHttpHeaders['X-Site']);

      req.flush(defaultBody);

      httpTestingController.verify();
    });

    it('should add params to POST request when provided', () => {
      const expectedParamUser = 'axel';
      const expectedParamFood = 'lasagna';
      const extraParams = new HttpParams().set('user', expectedParamUser).set('food', expectedParamFood);
      const postHttpOptions = {
        body: defaultBody,
        headers: new HttpHeaders(),
        params: extraParams
      };

      httpClient.request('get', defaultUrl, postHttpOptions).subscribe();

      const req = httpTestingController.expectOne(`${defaultUrl}?user=${expectedParamUser}&food=${expectedParamFood}`);

      expect(req.request.method).toEqual('GET');
      expect(req.request.params.get('user')).toEqual(expectedParamUser);
      expect(req.request.params.get('food')).toEqual(expectedParamFood);

      req.flush(defaultBody);

      httpTestingController.verify();
    });

    it('should manage an http error', () => {
      const errorStatus = 404;
      const errorMessage = 'Err 404';

      const requestHttpOptions = {
        body: defaultBody,
        headers: new HttpHeaders(defaultHttpHeaders),
        params: <HttpParams>undefined
      };

      httpClient.request('get', defaultUrl, requestHttpOptions).subscribe(
        () => fail(errorMessage),
        (error: HttpErrorResponse) => {
          expect(error.status).toEqual(errorStatus, 'status');
          expect(error.error).toEqual(errorMessage, 'message');
        }
      );

      const req = httpTestingController.expectOne(defaultUrl);

      req.flush(errorMessage, { status: errorStatus, statusText: '/' });
    });
  });
});
