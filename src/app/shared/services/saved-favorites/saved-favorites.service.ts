import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpParams } from '@angular/common/http';
import { HttpService } from '../http/http.service';

export interface Favorite {
  jobSeekerId: string;
  firstName: string;
  lastName: string;
  jobTitle?: string;
  photo?: string;
}

export interface FavoritesResponse {
  items: Favorite[];
  total: number;
}

@Injectable()
export class SavedFavoritesService {
  constructor(private http: HttpService) {}

  fetch(page: number, limit: number): Observable<any> {
    const urlSearchParametersList: string[] = [];
    if (page) {
      urlSearchParametersList.push(`page=${page}`);
    }
    if (limit) {
      urlSearchParametersList.push(`limit=${limit}`);
    }

    return this.http.get(
      '/api/recruiter/favorites/list',
      urlSearchParametersList.length > 0 ? new HttpParams({ fromString: urlSearchParametersList.join('&') }) : null
    );
  }

  del(jobSeekerId: string): Observable<any> {
    return this.http.del('/api/recruiter/favorites/' + jobSeekerId);
  }

  post(jobSeekerId: string): Observable<any> {
    return this.http.post('/api/recruiter/favorites/' + jobSeekerId, {});
  }
}
