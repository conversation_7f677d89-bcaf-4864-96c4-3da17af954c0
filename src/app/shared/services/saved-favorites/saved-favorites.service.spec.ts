import { from, empty } from 'rxjs';
import { SavedFavoritesService } from './saved-favorites.service';
import { HttpParams } from '@angular/common/http';

describe('SavedFavoritesService', () => {
  const stub: any = {};
  let service: SavedFavoritesService;

  beforeEach(() => {
    stub.HttpService = jasmine.createSpyObj('HttpService', ['get', 'post', 'del']);

    stub.HttpService.get.and.returnValue(from([{ items: [], total: 0 }]));
    stub.HttpService.del.and.returnValue(empty());
    stub.HttpService.post.and.returnValue(empty());

    service = new SavedFavoritesService(<any>stub.HttpService);
  });

  describe('#fetch', () => {
    it('should get saved candidates with pagination, both page and limit parameters', () => {
      const observable = service.fetch(2, 25);

      observable.subscribe(() => {
        expect(stub.HttpService.get).toHaveBeenCalledWith(
          '/api/recruiter/favorites/list',
          new HttpParams({ fromString: 'page=2&limit=25' })
        );
      });
    });

    it('should get saved candidates with pagination, with page parameter', () => {
      const observable = service.fetch(2, null);

      observable.subscribe(() => {
        expect(stub.HttpService.get).toHaveBeenCalledWith('/api/recruiter/favorites/list', new HttpParams({ fromString: 'page=2' }));
      });
    });

    it('should get saved candidates with pagination, with no parameters', () => {
      const observable = service.fetch(null, null);

      observable.subscribe(() => {
        expect(stub.HttpService.get).toHaveBeenCalledWith('/api/recruiter/favorites/list', null);
      });
    });
  });

  describe('#post', () => {
    it('should create a saved candidate', () => {
      service.post('1234');
      expect(stub.HttpService.post).toHaveBeenCalledWith('/api/recruiter/favorites/1234', {});
    });
  });

  describe('#del', () => {
    it('should delete a saved candidate', () => {
      service.del('1234');
      expect(stub.HttpService.del).toHaveBeenCalledWith('/api/recruiter/favorites/1234');
    });
  });
});
