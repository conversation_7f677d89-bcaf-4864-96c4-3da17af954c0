import { JobTitlesService } from './job-titles.service';

describe('JobTitlesService', () => {
  const stub: any = {};
  let service: JobTitlesService;

  beforeEach(() => {
    stub.ProfileAPIService = jasmine.createSpyObj('ProfileAPIService', ['getAutocompleteJobTitles']);
    service = new JobTitlesService(<any>stub.ProfileAPIService);
  });

  describe('get', () => {
    it('should get suggestions for jobtitle', () => {
      const input = 'te';
      service.get(input);
      expect(stub.ProfileAPIService.getAutocompleteJobTitles).toHaveBeenCalledWith(input);
    });
  });
});
