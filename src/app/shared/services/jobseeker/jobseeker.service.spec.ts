import { JobSeekerService } from './jobseeker.service';
import { Education, Experience, JobSeeker } from '../../../classes';

describe('JobSeekerService', () => {
  const stub: any = {};
  let service: JobSeekerService;

  const introText =
    'I am <PERSON> from **Reggio Emilia** -Italy- and I live in Amsterdam -The Netherlands- since 2013.' +
    'I am a frontend developer with growing experience and a big passion for web technologies since around 2005.';

  const introTextHtml =
    'I am <PERSON> from <b>Reggio Emilia</b> -Italy- and I live in Amsterdam -The Netherlands- since 2013.' +
    'I am a frontend developer with growing experience and a big passion for web technologies since around 2005.';

  beforeEach(() => {
    stub.data = {
      commute: {
        city: 'city'
      },
      education: [Education.empty()],
      experiences: [Experience.empty()],
      minWorkingHours: 2,
      maxWorkingHours: 40,
      emailAddress: 'emailAddress',
      firstName: 'first_name',
      lastName: 'last_name',
      phoneNumber: 'phone_number',
      id: 'id',
      workLevels: 'work_level',
      hours: 'hours',
      photo: 'photo.jpeg',
      languages: [],
      driverLicenses: [],
      attachments: [],
      introductionText: introText,
      introductionTextHtml: introTextHtml
    };
    stub.ProfileAPIService = jasmine.createSpyObj('ProfileAPIService', ['getCv']);

    service = new JobSeekerService(stub.ProfileAPIService);
  });

  describe('#fromJson', () => {
    let jobSeeker: JobSeeker;
    beforeEach(() => {
      jobSeeker = service.fromJson(stub.data);
    });

    it('should create Education fromJson', () => {
      expect(jobSeeker.education.length).toBe(1);
    });

    it('should create Experience fromJson', () => {
      expect(jobSeeker.experience.length).toBe(1);
    });

    it('should create JobCriteria from data', () => {
      expect(jobSeeker.jobCriteria.workLevels).toBe('work_level');
      expect(jobSeeker.jobCriteria.minWorkingHours).toBe(2);
      expect(jobSeeker.jobCriteria.maxWorkingHours).toBe(40);
    });

    it('should create a new JobSeeker', () => {
      expect(jobSeeker.personalInfo.email).toBe('emailAddress');
      expect(jobSeeker.personalInfo.firstName).toBe('first_name');
      expect(jobSeeker.personalInfo.lastName).toBe('last_name');
      expect(jobSeeker.personalInfo.phoneNumber).toBe('phone_number');
      expect(jobSeeker.introductionText).toBe(introText);
      expect(jobSeeker.introductionTextHtml).toBe(introTextHtml);
      expect(jobSeeker.id).toBe('id');
    });

    it('should default experience to empty array', () => {
      stub.data.experiences = undefined;
      jobSeeker = service.fromJson(stub.data);
      expect(jobSeeker.experience.length).toBe(0);
    });

    it('should default education to empty array', () => {
      stub.data.education = undefined;
      jobSeeker = service.fromJson(stub.data);
      expect(jobSeeker.education.length).toBe(0);
    });
  });

  describe('#getCv', () => {
    it('should call profile api service to retrieve CV', () => {
      const jobSeeker = service.fromJson(stub.data);
      service.getCv(jobSeeker);
      expect(stub.ProfileAPIService.getCv).toHaveBeenCalledTimes(1);
      expect(stub.ProfileAPIService.getCv).toHaveBeenCalledWith(jobSeeker.id);
    });
  });
});
