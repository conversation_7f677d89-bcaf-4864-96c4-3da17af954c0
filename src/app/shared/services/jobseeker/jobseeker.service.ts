import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ProfileAPIService } from '../../api';
import {
  JobSeeker,
  Education,
  Experience,
  Training,
  JobCriteria,
  PersonalInfo,
  Language,
  DriversLicense,
  Attachment
} from '../../../classes';

@Injectable()
export class JobSeekerService {
  constructor(private profileAPIService: ProfileAPIService) {}

  getCv(jobSeeker: JobSeeker): Observable<any> {
    return this.profileAPIService.getCv(jobSeeker.id);
  }

  from<PERSON>son(data: any) {
    const education = Education.fromJson(data.education || [], data.unapprovedEducationIds);
    const experience = Experience.fromJson(data.experiences || [], data.unapprovedExperienceIds);
    const training = Training.fromJson(data.training || []);
    const jobCriteria = JobCriteria.fromJson(data);
    const personalInfo = PersonalInfo.fromJson(data);
    const languages = Language.fromJson(data.languages);
    const driversLicenses = DriversLicense.fromJson(data.driverLicenses);
    const attachments = Attachment.fromJson(data.attachments);

    return new <PERSON>Seeker(
      data.id,
      data.updatedDate,
      data.noWorkExperience,
      education,
      experience,
      training,
      jobCriteria,
      personalInfo,
      data.photo,
      data.fileCvString,
      data.site,
      data.extraInfo,
      languages,
      driversLicenses,
      attachments,
      data.isFavorite,
      data.migrationStatus,
      data.introductionText,
      data.introductionTextHtml,
      data.viewedDate,
      data
    );
  }
}
