import { Injectable } from '@angular/core';

export interface RestoreScrollPosition {
  jobSeekerId: string;
  scrollY: number;
}

@Injectable()
export class RestoreScrollPositionService {
  private lastJobSeekerViewed: RestoreScrollPosition;

  setLastJobSeekerViewed(jobSeekerId: string, scrollY: number) {
    this.lastJobSeekerViewed = { jobSeekerId, scrollY };
  }

  getLastJobSeekerViewed(): RestoreScrollPosition {
    return this.lastJobSeekerViewed;
  }

  resetReplay() {
    this.lastJobSeekerViewed = null;
  }
}
