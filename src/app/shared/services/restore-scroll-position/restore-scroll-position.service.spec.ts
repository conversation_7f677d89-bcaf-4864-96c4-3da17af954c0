import { RestoreScrollPosition, RestoreScrollPositionService } from './restore-scroll-position.service';

describe('RestoreScrollPositionService', () => {
  let service: RestoreScrollPositionService;

  const jobseekerId = '23';
  const yPosition = 415;

  beforeEach(() => {
    service = new RestoreScrollPositionService();
  });

  describe('#setLastJobSeekerViewed', () => {
    it('should store jobseeker and scroll data', () => {
      service.setLastJobSeekerViewed(jobseekerId, yPosition);
      const scrollPosition: RestoreScrollPosition = {
        jobSeekerId: jobseekerId,
        scrollY: yPosition
      };

      expect(service['lastJobSeekerViewed']).toEqual(scrollPosition);
    });
  });

  describe('#getLastJobSeekerViewed', () => {
    it('should store jobseeker and scroll data', () => {
      const restoreScrollPosition = service.getLastJobSeekerViewed();

      expect(restoreScrollPosition).toEqual(service['lastJobSeekerViewed']);
    });
  });

  describe('#resetReplay', () => {
    it('should store jobseeker and scroll data', () => {
      service.resetReplay();

      expect(service['lastJobSeekerViewed']).toEqual(null);
    });
  });
});
