import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { languages } from '../../../classes/languages';

@Injectable()
export class LanguagesService {
  getLanguages(language: string): Observable<string[]> {
    return new Observable(obs => {
      obs.next(
        languages.filter(l => {
          return l.toLowerCase().match(language.toLowerCase());
        })
      );
    });
  }
}
