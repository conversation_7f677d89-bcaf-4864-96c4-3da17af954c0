import { LanguagesService } from './languages.service';

describe('LanguagesService', () => {
  let service: LanguagesService;

  beforeEach(() => {
    service = new LanguagesService();
  });

  describe('getLanguages', () => {
    it('should return a list of languages matching a string', () => {
      const languages = service.getLanguages('neder');
      languages.subscribe(list => {
        expect(list).toEqual(['Nederlands']);
      });
    });
  });
});
