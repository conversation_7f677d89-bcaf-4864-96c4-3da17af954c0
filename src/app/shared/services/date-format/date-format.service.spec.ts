import { DateFormatService } from './date-format.service';

describe('DateFormatService', () => {
  let service: DateFormatService;

  beforeEach(() => {
    service = new DateFormatService();
  });

  describe('formatDate', () => {
    it('should return "heden" if date is not given', () => {
      expect(service.formatDate(undefined)).toBe('heden');
    });

    it('should return the date in the format "Maart 2016" for a date in march 2016', () => {
      expect(service.formatDate('2016-03-01')).toBe('Maart 2016');
    });
  });

  describe('formatShortDate', () => {
    it('should return the date in the format "Maa 2016" for a date in march 2016', () => {
      expect(service.formatShortDate('2016-03-01')).toBe('Mrt 2016');
    });
  });

  describe('parseTimeDiff', () => {
    it('should return formatted string for of time difference between two dates', () => {
      const formattedDate = service.parseTimeDiff(new Date('2014-06-01'), new Date('2016-01-01'));
      expect(formattedDate).toBe('1 jaar, 7 maanden');
    });

    it('should not add months if amount is 0', () => {
      const formattedDate = service.parseTimeDiff(new Date('2014-06-01'), new Date('2016-06-01'));
      expect(formattedDate).toBe('2 jaar');
    });

    it('should not pluralize when amount of months is 1', () => {
      const formattedDate = service.parseTimeDiff(new Date('2014-06-01'), new Date('2016-07-01'));
      expect(formattedDate).toBe('2 jaar, 1 maand');
    });

    it('should not show comma if only year present', () => {
      const formattedDate = service.parseTimeDiff(new Date('2014-05-05'), new Date('2015-05-05'));
      expect(formattedDate).toBe('1 jaar');
    });

    it('should not show comma if only month present', () => {
      const formattedDate = service.parseTimeDiff(new Date('2015-05-05'), new Date('2015-06-05'));
      expect(formattedDate).toBe('1 maand');
    });

    it('should default toDates that are undefined to a the current date', () => {
      expect(service.parseTimeDiff.bind(null, '2014-01-01', undefined)).not.toThrow();
    });
  });
});
