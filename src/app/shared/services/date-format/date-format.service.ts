import { Injectable } from '@angular/core';

@Injectable()
export class DateFormatService {
  private months = [
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    'April',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>',
    'September',
    '<PERSON><PERSON><PERSON>',
    'November',
    'December'
  ];

  private shortMonths = ['Jan', 'Feb', 'Mrt', 'Apr', 'Mei', 'Jun', 'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'Dec'];

  constructor() {}

  formatDate(dateString: string) {
    if (dateString) {
      const date = new Date(dateString);
      return `${this.months[date.getMonth()]} ${date.getFullYear()}`;
    } else {
      return 'heden';
    }
  }

  formatShortDate(d: string) {
    const date = new Date(d);
    const month = date.getMonth();
    const year = date.getFullYear();
    return `${this.shortMonths[month]} ${year}`;
  }

  parseTimeDiff(d1: Date, d2: Date) {
    const date1 = new Date(d1);
    d2 = d2 || new Date();
    const date2 = new Date(d2);

    let months;
    months = (date2.getFullYear() - date1.getFullYear()) * 12;
    months -= date1.getMonth();
    months += date2.getMonth();

    const years = Math.floor(months / 12),
      remainderMonths = Math.floor(months % 12);

    let formattedDate = years > 0 ? `${years} jaar` : '';

    if (years > 0 && remainderMonths > 0) {
      formattedDate += ', ';
    }

    if (remainderMonths === 1) {
      formattedDate += `${remainderMonths} maand`;
    }

    if (remainderMonths > 1) {
      formattedDate += `${remainderMonths} maanden`;
    }

    return formattedDate;
  }
}
