import { take, filter } from 'rxjs/operators';
import { Injectable, NgZone } from '@angular/core';
import { interval, asyncScheduler } from 'rxjs';
import { Subscription } from 'rxjs';
import { Router, Event, NavigationEnd } from '@angular/router';
import { Title } from '@angular/platform-browser';

declare const ga: any;
declare const dataLayer: any;

export const BASE_PATH = '/old-recruiter';

@Injectable()
export class AnalyticsService {
  private queue: Array<string[]> = [];
  private source: Subscription;

  constructor(private router: Router, public ngZone: NgZone, private titleService: Title) {
    this.setSourceForGA = this.setSourceForGA.bind(this);

    this.router.events.pipe(filter((event: Event) => event instanceof NavigationEnd)).subscribe((event: NavigationEnd) => {
      if (event.url === event.urlAfterRedirects) {
        this.trackPage(event.url);
      }
    });

    this.ngZone.runOutsideAngular(this.setSourceForGA);
  }

  private setSourceForGA(): void {
    this.source = this.listenForGA(asyncScheduler);
  }

  private listenForGA(scheduler: any) {
    return interval(1000, scheduler)
      .pipe(take(10))
      .subscribe(() => {
        if (this.isGADefined()) {
          this.source.unsubscribe();
          this.clearQueue();
        }
      });
  }

  private clearQueue() {
    this.queue.map(event => {
      this.sendEvent(event);
    });
  }

  public trackPage(url: string) {
    this.sendEventOrQueue(['send', 'pageview', `${BASE_PATH}${url}`]);
  }

  public trackEvent(eventCategory: string, eventAction: string, eventLabel?: string) {
    this.sendEventOrQueue(['send', 'event', eventCategory, eventAction, eventLabel]);
  }

  private isGADefined(): boolean {
    return typeof ga !== 'undefined';
  }

  private sendEventOrQueue(event: string[]) {
    if (this.isGADefined()) {
      this.sendEvent(event);
    } else {
      this.queue.push(event);
    }
  }

  private sendEvent(event: string[]) {
    ga.apply(undefined, event);
  }

  private getCurrentTimestamp(): number {
    return +new Date();
  }

  pushPageViewToDataLayer(pageType: string) {
    if (typeof dataLayer !== 'undefined') {
      const pageViewDataLayer = {
        event: 'request',
        request: {
          pageTitle: this.titleService.getTitle(),
          pageType: pageType,
          pageAudience: 'b2b',
          urlHostname: window.location.origin,
          urlPath: this.router.url,
          timestamp: this.getCurrentTimestamp()
        }
      };

      dataLayer.push(pageViewDataLayer);
    }
  }
}
