import { AnalyticsService, BASE_PATH } from './analytics.service';
import { interval, asyncScheduler, Subject } from 'rxjs';
import { NavigationEnd } from '@angular/router';
import { Observable } from 'rxjs';
import { fakeAsync, tick, discardPeriodicTasks } from '@angular/core/testing';

describe('AnalyticsService', () => {
  const stub: any = {};
  let service: AnalyticsService;
  const currentPageTitle = 'Favoriete kandidaten | NationaleVacaturebank.nl';
  const currentTimestamp = *********;
  const currentUrl = '/kandidaten';

  beforeEach(() => {
    stub.mockNgZone = jasmine.createSpyObj('mockNgZone', ['runOutsideAngular']);
    stub.router = {
      events: Observable.create((object: any) => {
        stub.sendEventToObservable = object;
      }),
      url: currentUrl
    };
    stub.Title = jasmine.createSpyObj('Title', ['getTitle']);
    stub.Title.getTitle.and.returnValue(currentPageTitle);

    service = new AnalyticsService(stub.router, stub.mockNgZone, stub.Title);

    const s: any = service;
    spyOn(s, 'sendEvent');
  });

  describe('#constructor', () => {
    it('should track page if the NavigationEnd router event is triggered and there was no redirection', () => {
      const s: any = service;
      spyOn(s, 'sendEventOrQueue');

      const url = '/some-url';
      const urlAfterRedirects = '/some-url';
      const event = new NavigationEnd(123456, url, urlAfterRedirects);

      stub.sendEventToObservable.next(event);

      expect(s.sendEventOrQueue).toHaveBeenCalledWith(['send', 'pageview', `${BASE_PATH}${url}`]);
    });

    it('should ignore other than NavigationEnd router events', () => {
      const s: any = service;
      spyOn(s, 'sendEventOrQueue');

      stub.sendEventToObservable.next('event');

      expect(s.sendEventOrQueue).not.toHaveBeenCalled();
    });

    it('should not track page if route was redirected', () => {
      const s: any = service;
      spyOn(s, 'sendEventOrQueue');

      const event = new NavigationEnd(123456, '/some-url', '/another-url');

      stub.sendEventToObservable.next(event);

      expect(s.sendEventOrQueue).not.toHaveBeenCalled();
    });

    it('should run observable outside of angular', () => {
      expect(stub.mockNgZone.runOutsideAngular).toHaveBeenCalled();
    });
  });

  describe('#setSourceForGA', () => {
    it('should set the source with GA listener', () => {
      service['listenForGA'] = jasmine.createSpy('listenForGA');

      service['setSourceForGA']();

      expect(service['listenForGA']).toHaveBeenCalledTimes(1);
      expect(service['listenForGA']).toHaveBeenCalledWith(asyncScheduler);
    });
  });

  describe('#listenForGA', () => {
    let w: any;
    let source: any;
    let isGADefinedSpy: any;
    let clearQueueSpy: any;
    let intervalSpy: any;

    beforeEach(() => {
      w = window;
      w.ga = jasmine.createSpy('ga');
      source = new Subject();

      isGADefinedSpy = jasmine.createSpy('isGADefined');
      isGADefinedSpy.and.returnValue(true);
      clearQueueSpy = jasmine.createSpy('clearQueue');
      intervalSpy = jasmine.createSpy('interval');
      intervalSpy.and.returnValue(source);

      window['interval'] = intervalSpy;
      service['source'] = jasmine.createSpyObj('source', ['unsubscribe']);
    });

    it('should do nothing before GA is enabled', () => {
      service['isGADefined'] = isGADefinedSpy;
      service['clearQueue'] = clearQueueSpy;

      service['listenForGA'](asyncScheduler);

      window['interval'](1000);

      isGADefinedSpy.calls.reset();
      clearQueueSpy.calls.reset();

      expect(service['isGADefined']).not.toHaveBeenCalled();
      expect(service['clearQueue']).not.toHaveBeenCalled();
      expect(w.ga).not.toHaveBeenCalled();
    });

    it('should unsubscribe listener after GA is enabled', fakeAsync(() => {
      service['isGADefined'] = isGADefinedSpy;
      service['listenForGA'](asyncScheduler);

      interval(1000, asyncScheduler);

      tick(1000);

      expect(service['source'].unsubscribe).toHaveBeenCalledTimes(1);
      expect(service['source'].unsubscribe).toHaveBeenCalledWith();

      discardPeriodicTasks();
    }));

    it('should clear the queue', fakeAsync(() => {
      service['isGADefined'] = isGADefinedSpy;
      service['clearQueue'] = clearQueueSpy;
      service['listenForGA'](asyncScheduler);

      interval(1000, asyncScheduler);

      clearQueueSpy.calls.reset();

      tick(1000);

      expect(service['clearQueue']).toHaveBeenCalledWith();
      expect(service['clearQueue']).toHaveBeenCalledTimes(1);

      discardPeriodicTasks();
    }));

    it('should do nothing when isGADefined is not enabled', fakeAsync(() => {
      isGADefinedSpy.and.returnValue(false);
      service['isGADefined'] = isGADefinedSpy;
      service['clearQueue'] = clearQueueSpy;
      service['listenForGA'](asyncScheduler);

      interval(1000, asyncScheduler);

      clearQueueSpy.calls.reset();

      tick(1000);

      expect(service['clearQueue']).not.toHaveBeenCalled();
      expect(service['source'].unsubscribe).not.toHaveBeenCalled();

      discardPeriodicTasks();
    }));
  });

  describe('#clearQueue', () => {
    it('should send every event in the queue', () => {
      const s: any = service;
      s.queue = [['event']];
      s.clearQueue();
      expect(s.sendEvent).toHaveBeenCalledWith(['event']);
    });
  });

  describe('#trackPage', () => {
    it('should send or queue a page track event prefixed with recruiter namespace', () => {
      const s: any = service;
      spyOn(s, 'sendEventOrQueue');

      const url = '/search';

      service.trackPage(url);

      expect(s.sendEventOrQueue).toHaveBeenCalledWith(['send', 'pageview', `${BASE_PATH}${url}`]);
    });
  });

  describe('#trackEvent', () => {
    it('should send or queue an event to track', () => {
      const s: any = service;
      spyOn(s, 'sendEventOrQueue');

      service.trackEvent('eventCategory', 'eventAction', 'eventLabel');

      expect(s.sendEventOrQueue).toHaveBeenCalledWith(['send', 'event', 'eventCategory', 'eventAction', 'eventLabel']);
    });
  });

  describe('#isGADefined', () => {
    it('should should return true if ga is defined', () => {
      const w: any = window;
      w.ga = true;
      const s: any = service;
      expect(s.isGADefined()).toBe(true);
    });

    it('should should return false if ga is undefined', () => {
      const w: any = window;
      w.ga = undefined;
      const s: any = service;
      expect(s.isGADefined()).toBe(false);
    });
  });

  describe('#sendEventOrQueue', () => {
    let s: any;
    beforeEach(() => {
      s = service;
      spyOn(s, 'isGADefined').and.returnValue(false);
    });

    it('should add event to queue if GA is not ready yet', () => {
      s.sendEventOrQueue(['event']);
      expect(s.queue.length).toBe(1);
    });

    it('should fire event if GA is ready', () => {
      s.isGADefined.and.returnValue(true);
      s.sendEventOrQueue(['event']);
      expect(s.sendEvent).toHaveBeenCalledWith(['event']);
    });
  });

  describe('#sendEvent', () => {
    it('should push event to google analytics', () => {
      const w: any = window;
      const s: any = service;
      w.ga = jasmine.createSpy('ga');

      s.sendEvent.and.callThrough();
      s.sendEvent(['event', 'argument 2']);
      expect(w.ga).toHaveBeenCalledWith('event', 'argument 2');
    });
  });

  describe('#getCurrentTimestamp', () => {
    it('should return a valid timestamp', () => {
      const s: any = service;

      const timestamp = s.getCurrentTimestamp();

      const expectedAtLeastTimestamp = +new Date();
      expect(timestamp).toBeTruthy();
      expect(timestamp).toBeGreaterThanOrEqual(expectedAtLeastTimestamp);
    });
  });

  describe('#pushPageViewToDataLayer', () => {
    it('should push data to data layer', () => {
      const w: any = window;
      const s: any = service;
      w.dataLayer = jasmine.createSpyObj('dataLayer', ['push']);
      const getCurrentTimestampSpy = jasmine.createSpy('getCurrentTimestamp');
      getCurrentTimestampSpy.and.returnValue(currentTimestamp);
      s.getCurrentTimestamp = getCurrentTimestampSpy;

      const pageType = 'registration';
      s.pushPageViewToDataLayer(pageType);

      const expectedData = {
        event: 'request',
        request: {
          pageTitle: currentPageTitle,
          pageType: pageType,
          pageAudience: 'b2b',
          urlHostname: window.location.origin,
          urlPath: stub.router.url,
          timestamp: currentTimestamp
        }
      };
      expect(w.dataLayer.push).toHaveBeenCalledTimes(1);
      expect(w.dataLayer.push).toHaveBeenCalledWith(expectedData);
    });

    it('should not push data to data layer if it is not defined', () => {
      const w: any = window;
      const s: any = service;
      w.dataLayer = undefined;

      const notRelevant = 'blah';

      s.pushPageViewToDataLayer(notRelevant);

      expect(w.dataLayer).toBeUndefined();
    });
  });
});
