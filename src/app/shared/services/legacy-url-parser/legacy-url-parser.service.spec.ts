import { LegacyUrlParser } from './legacy-url-parser.service';
import { of } from 'rxjs';
import { HttpParams } from '@angular/common/http';

describe('LegacyUrlParser', () => {
  const stub: any = {};
  let service: LegacyUrlParser;

  beforeEach(() => {
    stub.HttpService = jasmine.createSpyObj('HttpService', ['get']);
    stub.HttpService.get.and.returnValue(of({}));
    service = new LegacyUrlParser(stub.HttpService);
  });

  describe('searchUrl', () => {
    it('should call the correct endpoint', () => {
      service.searchUrl('http://example.com').subscribe(() => {
        let search = new HttpParams();
        search = search.set('url', 'http://example.com');

        expect(stub.HttpService.get).toHaveBeenCalledWith('/api/recruiter/url-parser/search', search);
      });
    });

    it('should return the json value as an object', () => {
      service.searchUrl('http://example.com').subscribe(returnValue => {
        expect(returnValue).toEqual({});
      });
    });
  });

  describe('jobSeekerUrl', () => {
    it('should call the correct endpoint', () => {
      service.jobSeekerUrl('http://example.com').subscribe(() => {
        const search = new HttpParams();
        search.set('url', 'http://example.com');

        expect(stub.HttpService.get).toHaveBeenCalledTimes(1);
        expect(typeof stub.HttpService.get['calls'].argsFor(0)[0]).toBe('string');
        expect(stub.HttpService.get['calls'].argsFor(0)[0].toString()).toEqual('/api/recruiter/url-parser/job-seeker-details');
        expect(stub.HttpService.get['calls'].argsFor(0)[1].toString()).toEqual('url=http://example.com');
      });
    });

    it('should return the json value as an object', () => {
      service.jobSeekerUrl('http://example.com').subscribe(returnValue => {
        expect(returnValue).toEqual({});
      });
    });
  });
});
