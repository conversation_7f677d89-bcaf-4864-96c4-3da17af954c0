import { Injectable } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { HttpService } from '../http/http.service';

@Injectable()
export class LegacyUrlParser {
  constructor(private http: HttpService) {}

  public searchUrl(url: string): Observable<any> {
    let searchParams = new HttpParams();
    searchParams = searchParams.set('url', url);

    return this.http.get('/api/recruiter/url-parser/search', searchParams);
  }

  public jobSeekerUrl(url: string): Observable<any> {
    let searchParams = new HttpParams();
    searchParams = searchParams.append('url', url);

    return this.http.get('/api/recruiter/url-parser/job-seeker-details', searchParams);
  }
}
