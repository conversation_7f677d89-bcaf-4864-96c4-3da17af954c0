import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { NavigationExtras } from '@angular/router';
import * as SavedSearchTypes from '../../../shared/components/modals/save-search-modal/save-search-modal.types';
import { SavedSearch, SearchParameters } from '../../../classes';
import { HttpService } from '../http/http.service';

@Injectable()
export class SavedSearchService {
  constructor(private http: HttpService) {}

  get(): Observable<any> {
    return this.http.get('/api/recruiter/saved-searches').pipe(map((rs: any) => rs.items));
  }

  del(savedSearchId: string): Observable<any> {
    return this.http.del('/api/recruiter/saved-searches/' + savedSearchId);
  }

  post(savedSearch: SavedSearchTypes.SavedSearch): Observable<any> {
    return this.http.post('/api/recruiter/saved-searches', savedSearch);
  }

  getOne(recruiterId: string, savedSearchId: string): Observable<any> {
    return this.http.get('/api/recruiter/saved-searches/' + recruiterId + '/' + savedSearchId).pipe(map((rs: any) => rs));
  }

  put(savedSearchId: string, data: SavedSearch): Observable<any> {
    const json = JSON.stringify(data);

    return this.http.put('/api/recruiter/saved-searches/' + savedSearchId, json);
  }

  getParamsFromSearch(savedSearch: SearchParameters): NavigationExtras {
    const navigationExtras: NavigationExtras = {
      queryParams: {
        locationQuery: savedSearch.location,
        'filters[updatedDate][0]': savedSearch.updatedDate,
        'filters[radius][0]': savedSearch.radius
      }
    };

    const addParams = (obj: any, property: string, label: string) => {
      if (obj.hasOwnProperty(property) && obj[property].length > 0) {
        obj[property].forEach((value: any, key: any) => {
          navigationExtras.queryParams[`${label}[${property}][${key}]`] = value;
        });
      }
    };

    for (const name of Object.keys(savedSearch.term)) {
      addParams(savedSearch.term, name, 'search');
    }

    addParams(savedSearch, 'workingHours', 'filters');
    addParams(savedSearch, 'workLevels', 'filters');
    addParams(savedSearch, 'availability', 'filters');
    addParams(savedSearch, 'driverLicenses', 'filters');
    addParams(savedSearch, 'languages', 'filters');
    addParams(savedSearch, 'careerLevel', 'filters');
    addParams(savedSearch, 'functionGroups', 'filters');
    addParams(savedSearch, 'requestedSalary', 'filters');
    addParams(savedSearch, 'provinces', 'filters');

    return navigationExtras;
  }
}
