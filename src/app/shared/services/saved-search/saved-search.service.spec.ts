import { from, empty } from 'rxjs';
import { SavedSearchService } from './saved-search.service';
import { SavedSearch, SearchParameters } from '../../../classes';

describe('SavedSearchService', () => {
  const stub: any = {};
  let service: SavedSearchService;

  beforeEach(() => {
    stub.HttpService = jasmine.createSpyObj('HttpService', ['get', 'del', 'post', 'put']);

    stub.HttpService.get.and.returnValue(from([{ items: [] }]));
    stub.HttpService.del.and.returnValue(empty());
    stub.HttpService.post.and.returnValue(empty());
    stub.HttpService.put.and.returnValue(empty());
    service = new SavedSearchService(<any>stub.HttpService);
  });

  describe('get', () => {
    it('should get saved searches', () => {
      const observable = service.get();

      observable.subscribe(() => {
        expect(stub.HttpService.get).toHaveBeenCalledWith('/api/recruiter/saved-searches');
      });
    });
  });

  describe('del', () => {
    it('should delete a saved search', () => {
      service.del('1234');
      expect(stub.HttpService.del).toHaveBeenCalledWith('/api/recruiter/saved-searches/1234');
    });
  });

  describe('getOne', () => {
    it('should get one saved search', () => {
      const observable = service.getOne('1234', '2');

      observable.subscribe(() => {
        expect(stub.HttpService.get).toHaveBeenCalledWith('/api/recruiter/saved-searches/1234/2');
      });
    });
  });

  describe('post', () => {
    it('should create a saved search', () => {
      const savedSearch = {
        name: 'Foo',
        frequency: 'Wekelijks',
        searchParameters: new SearchParameters()
      };

      service.post(savedSearch);

      expect(stub.HttpService.post).toHaveBeenCalledWith('/api/recruiter/saved-searches', savedSearch);
    });
  });

  describe('put', () => {
    it('should update a saved search', () => {
      const obj = new SavedSearch('1', '1', '1', new SearchParameters());
      service.put('1234', obj);
      expect(stub.HttpService.put).toHaveBeenCalledWith('/api/recruiter/saved-searches/1234', JSON.stringify(obj));
    });
  });

  describe('getParamsFromSearch', () => {
    it('should map search params to correct filters', () => {
      const navigationExtras = {
        queryParams: {
          'search[courses][0]': 'php',
          locationQuery: 'Almere',
          'filters[workingHours][0]': '16 tot 24 uur',
          'filters[workLevels][0]': 'MBO',
          'filters[radius][0]': '*-5.0',
          'filters[availability][0]': 'In overleg',
          'filters[driverLicenses][0]': 'B - personenauto',
          'filters[languages][0]': 'Afrikaans',
          'filters[careerLevel][0]': 'Directie',
          'filters[functionGroups][0]': 'Administratief/Secretarieel',
          'filters[updatedDate][0]': 'Afgelopen 3 maanden',
          'filters[requestedSalary][0]': '< 1.750',
          'filters[provinces][0]': 'Zuid-Holland'
        }
      };

      const searchParameters = {
        term: {
          courses: ['php']
        },
        location: 'Almere',
        updatedDate: 'Afgelopen 3 maanden',
        radius: '*-5.0',
        workLevels: ['MBO'],
        workingHours: ['16 tot 24 uur'],
        availability: ['In overleg'],
        driverLicenses: ['B - personenauto'],
        languages: ['Afrikaans'],
        careerLevel: ['Directie'],
        functionGroups: ['Administratief/Secretarieel'],
        requestedSalary: ['< 1.750'],
        provinces: ['Zuid-Holland']
      };

      const result = service.getParamsFromSearch(searchParameters);

      expect(result).toEqual(navigationExtras);
    });

    it('should skip fields without queries', () => {
      const navigationExtras = {
        queryParams: {
          locationQuery: 'Almere',
          'filters[workingHours][0]': '16 tot 24 uur',
          'filters[workLevels][0]': 'MBO',
          'filters[radius][0]': '*-5.0',
          'filters[availability][0]': 'In overleg',
          'filters[driverLicenses][0]': 'B - personenauto',
          'filters[languages][0]': 'Afrikaans',
          'filters[careerLevel][0]': 'Directie',
          'filters[functionGroups][0]': 'Administratief/Secretarieel',
          'filters[updatedDate][0]': 'Afgelopen 3 maanden',
          'filters[requestedSalary][0]': '< 1.750',
          'filters[provinces][0]': 'Zuid-Holland'
        }
      };

      const searchParameters = {
        term: {},
        location: 'Almere',
        updatedDate: 'Afgelopen 3 maanden',
        radius: '*-5.0',
        workLevels: ['MBO'],
        workingHours: ['16 tot 24 uur'],
        availability: ['In overleg'],
        driverLicenses: ['B - personenauto'],
        languages: ['Afrikaans'],
        careerLevel: ['Directie'],
        functionGroups: ['Administratief/Secretarieel'],
        requestedSalary: ['< 1.750'],
        provinces: ['Zuid-Holland']
      };

      const result = service.getParamsFromSearch(searchParameters);

      expect(result).toEqual(navigationExtras);
    });
  });
});
