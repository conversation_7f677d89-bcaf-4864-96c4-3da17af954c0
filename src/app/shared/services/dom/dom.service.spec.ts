import { DomService } from './dom.service';

describe('DomService', () => {
  let service: DomService;

  const elementHeight = 10;
  let element: any;

  beforeEach(() => {
    service = new DomService();

    function Element() {
      this.offsetHeight = elementHeight;
    }
    element = new (Element as any)();
    element.innerHTML = 'text';
  });

  describe('#getMainHeaderHeight', () => {
    it('should return the main header element height', () => {
      const getMainHeaderElementSpy = jasmine.createSpy('getMainHeaderElement');
      getMainHeaderElementSpy.and.returnValue(<Element>element);

      service['getMainHeaderElement'] = getMainHeaderElementSpy;

      const mainHeaderHeight = service.getMainHeaderHeight();
      expect(mainHeaderHeight).toEqual(elementHeight);
    });

    it('should return a null height if element not defined', () => {
      const getMainHeaderElementSpy = jasmine.createSpy('getMainHeaderElement');
      getMainHeaderElementSpy.and.returnValue(null);

      service['getMainHeaderElement'] = getMainHeaderElementSpy;

      const mainHeaderHeight = service.getMainHeaderHeight();
      expect(mainHeaderHeight).toEqual(0);
    });
  });

  describe('#getMainHeaderElement', () => {
    it('should return the main header element of the DOM document', () => {
      const mainHeader = service['getMainHeaderElement']();

      expect(service['getMainHeaderElement']()).toEqual(mainHeader);
    });
  });
});
