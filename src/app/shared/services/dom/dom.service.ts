import { Injectable } from '@angular/core';

@Injectable()
export class DomService {
  getMainHeaderHeight(): number {
    const header = <HTMLElement>this.getMainHeaderElement();
    return header && header.offsetHeight ? header.offsetHeight : 0;
  }

  private getMainHeaderElement(): Element {
    const listHeaders = document.querySelectorAll('nvb-header-b2b, header-b2b, top-bar');
    return listHeaders[0];
  }
}
