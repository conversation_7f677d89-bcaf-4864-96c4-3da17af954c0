import { Observable, of as observableOf, Subject } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { Actions, Effect, ofType } from '@ngrx/effects';
import * as FilterActions from '../../../store/actions/filter/filter.actions';
import { JobTitlesService } from '../job-titles/job-titles.service';
import { LocationsService } from '../locations/locations.service';
import { LanguagesService } from '../languages/languages.service';
import { ActionWithPayload } from '../../../types/app-actions';

/* istanbul ignore next */
@Injectable()
export class AutoCompleteEffects {
  public autoComplete$ = new Subject<{
    type: string;
    payload: { results: string[]; searchValue: string };
  }>();

  @Effect()
  getLanguages$ = this.actions$.pipe(
    ofType(FilterActions.FETCH_LANGUAGES),
    switchMap((action: ActionWithPayload) => {
      return this.languages.getLanguages(action.payload).pipe(
        map(results => ({
          type: 'FETCHED_LANGUAGES',
          payload: { results, searchValue: action.payload }
        })),
        catchError(() =>
          observableOf({
            type: 'FETCHED_LANGUAGES',
            payload: { results: [], searchValue: action.payload }
          })
        )
      );
    })
  );

  @Effect()
  getJobTitles$ = this.actions$.pipe(
    ofType(FilterActions.FETCH_JOB_TITLES),
    switchMap((action: ActionWithPayload) => {
      return this.jobTitle.get(action.payload).pipe(
        map(results => ({
          type: 'FETCHED_JOB_TITLES',
          payload: { results, searchValue: action.payload }
        })),
        catchError(() =>
          observableOf({
            type: 'FETCHED_JOB_TITLES',
            payload: { results: [], searchValue: action.payload }
          })
        )
      );
    })
  );

  @Effect()
  getLocations$ = this.actions$.pipe(
    ofType(FilterActions.FETCH_LOCATIONS),
    switchMap((action: ActionWithPayload) => {
      return this.locations.get(action.payload).pipe(
        map(results => ({
          type: 'FETCHED_LOCATIONS',
          payload: { results, searchValue: action.payload }
        })),
        catchError(() =>
          observableOf({
            type: 'FETCHED_LOCATIONS',
            payload: { results: [], searchValue: action.payload }
          })
        )
      );
    })
  );

  constructor(
    private actions$: Actions,
    private languages: LanguagesService,
    private jobTitle: JobTitlesService,
    private locations: LocationsService
  ) {
    this.getLanguages$.subscribe(action => this.autoComplete$.next(action));
    this.getJobTitles$.subscribe(action => this.autoComplete$.next(action));
    this.getLocations$.subscribe(action => this.autoComplete$.next(action));
  }

  public getAutoCompleteEffects(): Observable<{
    type: string;
    payload: { results: string[]; searchValue: string };
  }> {
    return this.autoComplete$.asObservable();
  }
}
