import { AutoCompleteEffects } from './auto-complete-effects.service';
import { TestBed } from '@angular/core/testing';
import { JobTitlesService } from '../job-titles/job-titles.service';
import { LocationsService } from '../locations/locations.service';
import { LanguagesService } from '../languages/languages.service';
import { Observable, Subject } from 'rxjs';
import { provideMockActions } from '@ngrx/effects/testing';

describe('AutoCompleteEffects', () => {
  let autoCompleteEffects: AutoCompleteEffects;
  const actions: Observable<any> = new Subject();
  const stub = <any>{};

  beforeEach(() => {
    stub.LanguagesService = jasmine.createSpyObj('LanguagesService', ['getLanguages']);
    stub.LanguagesServiceObservable = new Subject();
    stub.LanguagesService.getLanguages.and.returnValue(stub.LanguagesServiceObservable.asObservable());

    stub.JobTitlesService = jasmine.createSpyObj('JobTitlesService', ['get']);
    stub.JobTitlesServiceObservable = new Subject();
    stub.JobTitlesService.get.and.returnValue(stub.JobTitlesServiceObservable.asObservable());

    stub.LocationsService = jasmine.createSpyObj('LocationsService', ['get']);
    stub.LocationsServiceObservable = new Subject();
    stub.LocationsService.get.and.returnValue(stub.LocationsServiceObservable.asObservable());

    TestBed.configureTestingModule({
      imports: [],
      providers: [
        AutoCompleteEffects,
        provideMockActions(() => actions),
        { provide: LanguagesService, useValue: stub.LanguagesService },
        { provide: JobTitlesService, useValue: stub.JobTitlesService },
        { provide: LocationsService, useValue: stub.LocationsService }
      ]
    });

    autoCompleteEffects = TestBed.get(AutoCompleteEffects);
  });

  describe('#getAutoCompleteEffects', () => {
    it('should return an observable of the autocomplete subject', () => {
      autoCompleteEffects.getAutoCompleteEffects().subscribe(result => {
        expect(result.type).toBe('FETCHED_LANGUAGES');
      });

      autoCompleteEffects.autoComplete$.next(<any>{ type: 'FETCHED_LANGUAGES' });
    });
  });
});
