import { NumberFormatService } from './number-format.service';

describe('NumberFormatService', () => {
  let service: NumberFormatService;

  beforeEach(() => {
    service = new NumberFormatService();
  });

  describe('format', () => {
    it('should display numbers larger than 1 million with an M and rounded to millions with 1 decimal', () => {
      expect(service.format(1200000)).toBe('1.2M');
    });

    it('should display numbers larger than 10 thousand with a K and rounded to 10 thousands', () => {
      expect(service.format(12000)).toBe('12K');
    });

    it('should not reformat number smaller than 10 thousand', () => {
      expect(service.format(9999)).toBe('9999');
    });
  });

  describe('formatDots', () => {
    it('should display a dot for every three numbers', () => {
      expect(service.formatDots(1200000)).toBe('1.200.000');
    });
  });
});
