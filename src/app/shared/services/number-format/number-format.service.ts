import { Injectable } from '@angular/core';

@Injectable()
export class NumberFormatService {
  format(number: number): string {
    if (number > 999999) {
      return Math.round(number / 100000) / 10 + 'M';
    }
    if (number > 9999) {
      return Math.floor(number / 1000) + 'K';
    }
    return number.toString();
  }

  formatDots(number: number): string {
    return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  }
}
