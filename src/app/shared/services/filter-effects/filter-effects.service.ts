import { merge, take } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { Actions, ofType } from '@ngrx/effects';
import * as FilterActions from '../../../store/actions/filter/filter.actions';
import * as SearchActions from '../../../store/actions/search/search.actions';
import { Observable } from 'rxjs/index';

@Injectable()
export class FilterEffectsService {
  constructor(private actions$: Actions) {}

  getChangedResultSetActions(): Observable<any> {
    return this.actions$.pipe(
      ofType(SearchActions.SYNC_AFTER_URL_CHANGE, SearchActions.UPDATE_FROM_LEGACY_SEARCH_URL),
      take(1),
      merge(
        this.actions$.pipe(
          ofType(
            FilterActions.TOGGLE_FILTER_OPTION,
            FilterActions.SELECT_FILTER_OPTION,
            SearchActions.UPDATE_SEARCH,
            FilterActions.CLOSE_ALL_FILTERS
          )
        )
      )
    );
  }
}
