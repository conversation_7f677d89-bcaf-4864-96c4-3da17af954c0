import { FilterEffectsService } from './filter-effects.service';
import { TestBed } from '@angular/core/testing';
import { Observable, Subject } from 'rxjs';
import { provideMockActions } from '@ngrx/effects/testing';

describe('FilterEffectsService', () => {
  let filterEffectsService: FilterEffectsService;
  const actions: Observable<any> = new Subject();

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [FilterEffectsService, provideMockActions(() => actions)]
    });

    filterEffectsService = TestBed.get(FilterEffectsService);
  });

  describe('#getChangedResultSetActions', () => {
    it('should return an observable of the filter effects subject', () => {
      const expectedSetActions = filterEffectsService.getChangedResultSetActions();
      expect(expectedSetActions).toBeDefined();
    });
  });
});
