import { from } from 'rxjs';
import { LocationsService } from './locations.service';
import { HttpParams } from '@angular/common/http';

describe('LocationsService', () => {
  const stub: any = {};
  let service: LocationsService;

  beforeEach(() => {
    stub.HttpService = jasmine.createSpyObj('HttpService', ['get']);
    stub.HttpService.get.and.returnValue(from([{ result: ['test'] }]));
    service = new LocationsService(<any>stub.HttpService);
  });

  describe('get', () => {
    it('should get suggestions for jobtitle', () => {
      service.get('Ams').subscribe(results => {
        expect(results).toEqual(['test']);
      });
      const expectedSearch = new HttpParams();
      expectedSearch.set('city', 'Ams');

      expect(stub.HttpService.get).toHaveBeenCalledTimes(1);
      expect(typeof stub.HttpService.get['calls'].argsFor(0)[0]).toBe('string');
      expect(stub.HttpService.get['calls'].argsFor(0)[0].toString()).toEqual('/api/recruiter/autocomplete/cities');
      expect(stub.HttpService.get['calls'].argsFor(0)[1].toString()).toEqual('city=Ams');
    });
  });
});
