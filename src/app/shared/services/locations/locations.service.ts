import { Injectable } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpService } from '../http/http.service';

@Injectable()
export class LocationsService {
  constructor(private http: HttpService) {
    this.get = this.get.bind(this);
  }

  get(value: string): Observable<string[]> {
    let searchParams = new HttpParams();
    searchParams = searchParams.append('city', value);

    return this.http.get('/api/recruiter/autocomplete/cities', searchParams).pipe(map((results: any) => results.result));
  }
}
