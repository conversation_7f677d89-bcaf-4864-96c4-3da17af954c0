import { Injectable } from '@angular/core';
import { takeUntil } from 'rxjs/operators';
import { Observable, BehaviorSubject, Subject, of } from 'rxjs';
import { FEATURES, isFeatureEnabled } from '../feature-toggle/feature-toggle.service';
import { environment } from '../../../../environments/environment';
import { Recruiter } from '../../../classes';
import { RecruiterService } from '../recruiter/recruiter.service';

@Injectable()
export class PlatformCoreService {
  public hasPlatformCoreAccount: Subject<boolean>;

  constructor(
    private recruiterService: RecruiterService
  ) {
    if (environment.site !== 'b2b') {
      this.hasPlatformCoreAccount = new BehaviorSubject<boolean>(true);
    } else {
      this.hasPlatformCoreAccount = new BehaviorSubject<boolean>(false);

      recruiterService.getRecruiter().subscribe((recruiter: Recruiter) => {
        if (recruiter) {
          this.hasPlatformCoreAccount.next(recruiter.hasNdpAccount);
        } else {
          this.hasPlatformCoreAccount.next(false);
        }
      });
    }
  }
}
