import { PlatformCoreService } from './platform-core.service';
import { environment } from '../../../../environments/environment';
import { Recruiter } from '../../../classes';
import { Observable, Subject, empty, of } from 'rxjs';

describe('PlatformCoreService', () => {
  let service: PlatformCoreService;
  const stub = <any>{};
  let recruiterSubject: Subject<Recruiter>;
  beforeEach(() => {
    recruiterSubject = new Subject<Recruiter>();
    stub.RecruiterService = {
      getRecruiter: () => recruiterSubject,
    };
    environment.site = 'nvb';
  });

  describe('hasPlatformCoreAccount', () => {
    it('defaults to true for nvb', () => {
      service = new PlatformCoreService(stub.RecruiterService);
      service.hasPlatformCoreAccount.subscribe((result: boolean) => {
        expect(result).toBe(true);
      });
    });

    it('defaults to false for b2b', () => {
      environment.site = 'b2b';
      service = new PlatformCoreService(stub.RecruiterService);
      service.hasPlatformCoreAccount.subscribe((result: boolean) => {
        expect(result).toBe(false);
      });
    });

    it('sets hasPlatformCoreAccount to true when the recruiter hasNdpAccount (for b2b)', () => {
      environment.site = 'b2b';
      service = new PlatformCoreService(stub.RecruiterService);
      recruiterSubject.next({
        isLogged: true,
        hasValidProduct: true,
        hasSubscription: true,
        hasDeductibleSubscription: true,
        credits: 1000,
        companyVerified: true,
        hasNdpAccount: true
      });

      service.hasPlatformCoreAccount.subscribe((result: boolean) => {
        expect(result).toBe(true);
      });
    });

    it('sets hasPlatformCoreAccount to false when the recruiter information fails to load (for b2b)', () => {
      environment.site = 'b2b';
      service = new PlatformCoreService(stub.RecruiterService);
      recruiterSubject.next({
        isLogged: true,
        hasValidProduct: true,
        hasSubscription: true,
        hasDeductibleSubscription: true,
        credits: 1000,
        companyVerified: true,
        hasNdpAccount: true
      });
      recruiterSubject.next(null);

      service.hasPlatformCoreAccount.subscribe((result: boolean) => {
        expect(result).toBe(false);
      });
    });
  });
});
