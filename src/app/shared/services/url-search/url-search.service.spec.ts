import { UrlSearchService } from './url-search.service';
import { Filter } from '../../../store/models/filter.modal';
import { Subject } from 'rxjs';
import * as SearchActions from '../../../store/actions/search/search.actions';

enum FilterTypes {
  CheckBox = 0,
  Dropdown = 1,
  AutoComplete = 2
}

describe('UrlSearchService', () => {
  let service: UrlSearchService;
  const stub: any = {};

  beforeEach(() => {
    stub.Router = jasmine.createSpyObj('Router', ['navigate']);
    stub.StoreSubject = new Subject();
    stub.StoreDefaultSubject = new Subject();
    stub.StoreSearchSubject = new Subject();
    stub.StoreFilterSubject = new Subject();
    stub.Store = {
      dispatch: jasmine.createSpy('dispatch'),
      search: stub.StoreSearchSubject,
      filter: {
        filters: stub.StoreFilterSubject
      }
    };

    stub.Store.select = (storeSelect: Function) => {
      return storeSelect(stub.Store);
    };

    service = new UrlSearchService(stub.Store, stub.Router);
  });

  describe('#constructor', () => {
    it('should skip update of URL for the initial store update', () => {
      const updateUrlSpy = jasmine.createSpy('updateUrl');
      updateUrlSpy.and.returnValue(null);
      service.updateUrl = updateUrlSpy;

      stub.StoreSearchSubject.next();
      stub.StoreFilterSubject.next();

      expect(service.updateUrl).not.toHaveBeenCalled();
    });

    it('should update the URL with the correct parameters from the store', () => {
      const updateUrlSpy = jasmine.createSpy('updateUrl');
      updateUrlSpy.and.returnValue(null);
      service.updateUrl = updateUrlSpy;

      updateUrlSpy.calls.reset();

      stub.StoreSearchSubject.next();
      stub.StoreFilterSubject.next();

      stub.StoreSearchSubject.next({ urlParsed: true });
      stub.StoreFilterSubject.next({ filters: ['filter'] });

      expect(service.updateUrl).toHaveBeenCalledTimes(1);
      expect(service.updateUrl).toHaveBeenCalledWith({
        search: { urlParsed: true },
        filters: { filters: ['filter'] }
      });
    });
  });

  describe('#syncStateFromUrl', () => {
    let filters: Filter[];
    beforeEach(() => {
      spyOn(service, 'updateFromUrl');
      filters = [
        {
          filterOptions: [
            {
              name: 'filterOptionName',
              count: '0',
              order: 1,
              selected: true
            }
          ],
          name: 'filterName',
          title: 'title',
          open: true,
          type: FilterTypes.CheckBox
        },
        {
          filterOptions: [
            {
              name: 'filterOptionName',
              count: '0',
              order: 1,
              selected: true
            },
            {
              name: 'filterOptionName2',
              count: '0',
              order: 1,
              selected: true
            }
          ],
          name: 'filterName2',
          title: 'title',
          open: true,
          type: FilterTypes.CheckBox
        },
        {
          filterOptions: [],
          name: 'filterName3',
          title: 'title',
          open: true,
          type: FilterTypes.AutoComplete,
          allowNotListedOptions: true
        }
      ];
    });

    it('should call search with correct parameters', () => {
      service.syncStateFromUrl([], {
        'search[all][0]': 'bus driver',
        locationQuery: 'Amsterdam',
        pageSize: 15,
        currentPage: 1
      });

      expect(service.updateFromUrl).toHaveBeenCalledWith(
        {
          searchQuery: { all: ['bus driver'] },
          locationQuery: 'Amsterdam',
          pageSize: 15,
          currentPage: 1,
          chosenSorting: 'default',
          sort: 'default'
        },
        []
      );
    });

    it('should reset filters to empty state', () => {
      service['removeAllFilters'] = jasmine.createSpy('removeAllFilters');

      service.syncStateFromUrl(filters, { currentPage: 1 });

      expect(service['removeAllFilters']).toHaveBeenCalledTimes(1);
      expect(service['removeAllFilters']).toHaveBeenCalledWith(filters);
    });

    it('should not reset filters to empty state if query is empty', () => {
      service['removeAllFilters'] = jasmine.createSpy('removeAllFilters');

      service.syncStateFromUrl(filters, {});

      expect(service['removeAllFilters']).not.toHaveBeenCalled();
    });

    it('should change ref to filter and filterOption when it sets it to false', () => {
      const oldFilter = filters[1];
      const oldFilterOption = oldFilter.filterOptions[0];

      service.syncStateFromUrl(filters, { currentPage: 1 });
      const args = service.updateFromUrl['calls'].argsFor(0);

      const filter = args[1][1];
      const filterOption = filter.filterOptions.pop();

      expect(oldFilter).not.toBe(filter);
      expect(oldFilterOption).not.toBe(filterOption);
    });

    it('should set filters to selected if in URL', () => {
      filters[0].filterOptions[0].selected = false;
      service.syncStateFromUrl(filters, {
        '[filterName2][0]': 'filterOptionName2'
      });

      const args = service.updateFromUrl['calls'].argsFor(0);
      expect(args[1][1].filterOptions.pop().selected).toEqual(true);
    });

    it('should not set filters to selected if in not URL', () => {
      service.syncStateFromUrl(filters, {
        '[filterName][0]': 'filterOptionName'
      });

      const args = service.updateFromUrl['calls'].argsFor(0);
      expect(args[1][1].filterOptions.pop().selected).toEqual(false);
    });

    it('should keep filteroptions when allowNotListedOptions is true', () => {
      service.syncStateFromUrl(filters, {
        '[filterName3][0]': 'filterOptionName'
      });
      const args = service.updateFromUrl['calls'].argsFor(0);
      expect(args[1][2].filterOptions.length).toBe(1);
    });

    it('should add queries to the field', () => {
      service.syncStateFromUrl([], {
        'search[all][1]': 'tram driver',
        'search[all][0]': 'bus driver',
        'search[some][0]': 'taxi driver',
        locationQuery: 'Amsterdam',
        pageSize: 15,
        currentPage: 1
      });

      expect(service.updateFromUrl).toHaveBeenCalledWith(
        {
          searchQuery: {
            all: ['bus driver', 'tram driver'],
            some: ['taxi driver']
          },
          locationQuery: 'Amsterdam',
          pageSize: 15,
          currentPage: 1,
          chosenSorting: 'default',
          sort: 'default'
        },
        []
      );
    });

    it('should iterate only on properties owned by query object', () => {
      class SearchQuery {
        one = 1;
      }

      SearchQuery.prototype = <any>{ two: 2 };

      const query = new SearchQuery();

      service.syncStateFromUrl([], query);

      expect(service.updateFromUrl).toHaveBeenCalledWith(
        {
          searchQuery: { all: [''] },
          locationQuery: '',
          pageSize: 15,
          currentPage: 1,
          chosenSorting: 'default',
          sort: 'default'
        },
        []
      );
    });
  });

  describe('#updateUrl', () => {
    it('should navigate to the correct URL when called with search and filters', () => {
      const filters = [
        {
          filterOptions: [
            {
              name: 'filterOptionName',
              count: '0',
              order: 1,
              selected: true
            },
            {
              name: 'filterOptionName2',
              count: '0',
              order: 1,
              selected: false
            }
          ],
          name: 'filterName',
          title: 'title',
          open: true,
          type: FilterTypes.CheckBox
        }
      ];

      const search = {
        searchQuery: { all: ['bus driver'] },
        locationQuery: 'Amsterdam',
        pageSize: 15,
        currentPage: 1,
        chosenSorting: 'default',
        sort: 'default'
      };

      const expectedNavigationExtras = {
        queryParams: {
          'search[all][0]': 'bus driver',
          locationQuery: 'Amsterdam',
          pageSize: 15,
          currentPage: 1,
          sort: 'default',
          'filters[filterName][0]': 'filterOptionName'
        }
      };

      service.updateUrl({ search, filters });

      expect(stub.Router.navigate).toHaveBeenCalledWith(['/kandidaten/zoeken'], expectedNavigationExtras);
    });

    it('should ignore not own properties', () => {
      class SearchQuery {
        one = 1;
      }

      SearchQuery.prototype = <any>{ two: 2 };

      const searchQuery = new SearchQuery();

      const filters = [
        {
          filterOptions: [
            {
              name: 'filterOptionName',
              count: '0',
              order: 1,
              selected: true
            }
          ],
          name: 'filterName',
          title: 'title',
          open: true,
          type: FilterTypes.CheckBox
        }
      ];

      const search = {
        searchQuery: <any>searchQuery,
        locationQuery: 'Amsterdam',
        pageSize: 15,
        currentPage: 1,
        chosenSorting: 'default',
        sort: 'default'
      };

      const expectedNavigationExtras = {
        queryParams: {
          locationQuery: 'Amsterdam',
          pageSize: 15,
          currentPage: 1,
          sort: 'default',
          'filters[filterName][0]': 'filterOptionName'
        }
      };

      service.updateUrl({ search, filters });

      expect(stub.Router.navigate).toHaveBeenCalledWith(['/kandidaten/zoeken'], expectedNavigationExtras);
    });
  });

  describe('#updateFromUrl', () => {
    it('should fire a SYNC_AFTER_URL_CHANGE event with the correct payload', () => {
      const search = {
        searchQuery: { all: ['bus driver'] },
        locationQuery: 'Amsterdam',
        pageSize: 15,
        currentPage: 1,
        chosenSorting: 'default',
        sort: 'default'
      };
      const expectedAction = new SearchActions.SynchronizeAfterUrlChange({ search, filters: <Filter[]>[] });

      service.updateFromUrl(search, []);

      expect(stub.Store.dispatch).toHaveBeenCalledWith(expectedAction);
    });
  });

  describe('#changePageSize', () => {
    it('should fire a CHANGE_PAGE_SIZE event with the correct payload when page size change from page one', () => {
      const expectedAction = new SearchActions.ChangePageSize({ pageSize: 50, newCurrentPage: 1 });

      service.changePageSize(50, 1);

      expect(stub.Store.dispatch).toHaveBeenCalledWith(expectedAction);
    });

    it('should fire a CHANGE_PAGE_SIZE event with the correct payload when page size changes from another page', () => {
      const expectedAction = new SearchActions.ChangePageSize({ pageSize: 50, newCurrentPage: 2 });

      service.changePageSize(50, 51);

      expect(stub.Store.dispatch).toHaveBeenCalledWith(expectedAction);
    });
  });

  describe('#changePageNumber', () => {
    it('should fire a CHANGE_CURRENT_PAGE event with the correct payload', () => {
      const newCurrentPage = 2;
      const expectedAction = new SearchActions.ChangeCurrentPage(newCurrentPage);

      service.changePageNumber(newCurrentPage);

      expect(stub.Store.dispatch).toHaveBeenCalledWith(expectedAction);
    });
  });

  describe('#updateSearch', () => {
    it('should fire a UPDATE_SEARCH event with the correct payload', () => {
      const searchQuery = { all: ['bus driver'] };
      const location = 'Roma';
      const expectedAction = new SearchActions.UpdateSearch({
        searchQuery: searchQuery,
        locationQuery: location
      });

      service.updateSearch(searchQuery, location);

      expect(stub.Store.dispatch).toHaveBeenCalledWith(expectedAction);
    });
  });
});
