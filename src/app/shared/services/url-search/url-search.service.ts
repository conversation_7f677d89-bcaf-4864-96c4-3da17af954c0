import { combineLatest } from 'rxjs';
import { skip } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { Router } from '@angular/router';
import * as SearchActions from '../../../store/actions/search/search.actions';
import { Filter } from '../../../store/models/filter.modal';
import { Search } from '../../../store/models/search.model';

interface AppState {
  filter: { filter: boolean; filters: Filter[] };
  search: Search;
}

@Injectable()
export class UrlSearchService {
  constructor(public store: Store<AppState>, private router: Router) {
    this.updateUrl = this.updateUrl.bind(this);

    const search$ = this.store.select(s => s.search).pipe(skip(1));
    const filters$ = this.store.select(s => s.filter.filters).pipe(skip(1));

    combineLatest(search$, filters$, (search, filters) => {
      return { search, filters };
    }).subscribe(state => {
      this.updateUrl(state);
    });
  }

  syncStateFromUrl(filters: Filter[], query: any) {
    const searchQuery: {
      [key: string]: string[];
    } = this.buildSearchQueryFromSearchParameters(query);

    const locationQuery: string = query.locationQuery !== undefined ? query.locationQuery.trim() : '';
    const pageSize: number = query.pageSize !== undefined ? parseInt(query.pageSize, 10) : 15;
    const currentPage: number = query.currentPage !== undefined ? parseInt(query.currentPage, 10) : 1;
    const sort = query.sort || 'default';

    if (Object.keys(query).length !== 0) {
      filters = this.removeAllFilters(filters);
    }

    const filterRegex = /(.*)\[(.*)\]\[\d+\]/;

    Object.keys(query).map((key: string) => {
      if (key.match(filterRegex)) {
        const [, , filterName] = filterRegex.exec(key);
        filters = filters.map(filter => {
          if (filter.name === filterName) {
            if (filter.allowNotListedOptions) {
              filter.filterOptions.push({
                name: query[key],
                selected: true,
                count: '0',
                order: 0
              });
            } else {
              filter.filterOptions = filter.filterOptions.map(filterOption => {
                if (filterOption.name === query[key]) {
                  return Object.assign({}, filterOption, { selected: true });
                }
                return filterOption;
              });
            }
          }
          return Object.assign({}, filter);
        });
      }
    });

    this.updateFromUrl(
      {
        searchQuery,
        locationQuery,
        pageSize,
        currentPage,
        sort,
        chosenSorting: sort
      },
      filters
    );
  }

  updateFromUrl(search: Search, filters: Filter[]) {
    this.store.dispatch(new SearchActions.SynchronizeAfterUrlChange({ search, filters }));
  }

  private removeAllFilters(filters: Filter[]) {
    return filters.map(filter => {
      let filterOptionChanged = false;
      filter.filterOptions = filter.filterOptions.map(f => {
        if (f.selected) {
          filterOptionChanged = true;
          f.selected = false;
          return { ...f, selected: false };
        }
        return f;
      });
      if (filterOptionChanged) {
        return { ...filter };
      }
      return filter;
    });
  }

  updateUrl(state: { search: Search; filters: Filter[] }) {
    const navigationExtras = {
      queryParams: {
        locationQuery: state.search.locationQuery,
        pageSize: state.search.pageSize,
        currentPage: state.search.currentPage,
        sort: state.search.sort
      }
    };

    for (const property in state.search.searchQuery) {
      if (state.search.searchQuery.hasOwnProperty(property) && state.search.searchQuery[property].length > 0) {
        state.search.searchQuery[property].forEach(function(value: string, index: number) {
          navigationExtras.queryParams[`search[${property}][${index}]`] = value;
        });
      }
    }

    if (state.filters) {
      state.filters.map(filter => {
        let i = 0;
        filter.filterOptions.map(filterOption => {
          if (filterOption.selected) {
            navigationExtras.queryParams[`filters[${filter.name}][${i}]`] = filterOption.name;
            i++;
          }
        });
      });
    }

    this.router.navigate(['/kandidaten/zoeken'], navigationExtras);
  }

  changePageSize(pageSize: number, currentIndex: number) {
    const newCurrentPage = Math.ceil((currentIndex + 1) / pageSize);

    this.store.dispatch(new SearchActions.ChangePageSize({ pageSize, newCurrentPage }));
  }

  changePageNumber(pageNumber: number) {
    this.store.dispatch(new SearchActions.ChangeCurrentPage(pageNumber));
  }

  updateSearch(searchQuery: { [key: string]: string[] }, locationQuery: string) {
    this.store.dispatch(new SearchActions.UpdateSearch({ searchQuery, locationQuery }));
  }

  private buildSearchQueryFromSearchParameters(searchParameters: any) {
    let searchQuery = {};

    for (const property in searchParameters) {
      if (searchParameters.hasOwnProperty(property)) {
        const result = property.match(/search\[(\w+)\]\[(\d+)\]/);

        if (result !== null) {
          if (searchQuery[result[1]] === undefined) {
            searchQuery[result[1]] = [];
          }

          searchQuery[result[1]][result[2]] = searchParameters[property];
        }
      }
    }

    if (Object.keys(searchQuery).length === 0) {
      searchQuery = { all: [''] };
    }

    return searchQuery;
  }
}
