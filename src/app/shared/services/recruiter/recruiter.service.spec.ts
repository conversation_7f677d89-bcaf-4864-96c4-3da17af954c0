import { of } from 'rxjs';
import { RecruiterLimitationsType, RecruiterService } from './recruiter.service';

describe('RecruiterService', () => {
  let service: RecruiterService;
  let stub: any;

  beforeEach(() => {
    stub = {
      ProfileApiService: jasmine.createSpyObj('ProfileApiService', ['getRecruiter', 'getSuggestibleProducts']),
      recruiter: {
        isLogged: true,
        hasValidProduct: true,
        hasSubscription: false,
        hasDeductibleSubscription: true,
        credits: 9,
        companyVerified: true
      },
      suggestibleProducts: ['a', 'b']
    };
    stub.ProfileApiService.getRecruiter.and.returnValue(of(stub.recruiter));
    stub.ProfileApiService.getSuggestibleProducts.and.returnValue(of(stub.suggestibleProducts));

    service = new RecruiterService(stub.ProfileApiService);
  });

  it('should fetch recruiter on first call', () => {
    spyOn(service, 'refreshRecruiter');

    service.getRecruiter();

    expect(service.refreshRecruiter).toHaveBeenCalledTimes(1);
  });

  it('should return cached recruiter', () => {
    service['recruiter'] = stub.recruiter;
    service['initialized'] = true;
    spyOn(service, 'refreshRecruiter');

    service.getRecruiter().subscribe(result => {
      expect(result).toBe(stub.recruiter);
    });
  });

  it('should return observable when the call is in progress', () => {
    service['observable'] = of(stub.recruiter);
    spyOn(service, 'refreshRecruiter');

    service.getRecruiter().subscribe(result => {
      expect(result).toBe(stub.recruiter);
    });
  });

  it('should be able to refresh recruiter', () => {
    service['recruiter'] = stub.recruiter;

    const observable = service.refreshRecruiter();

    expect(service['recruiter']).toBeNull();

    observable.subscribe(result => {
      expect(result).toEqual(stub.recruiter);
    });
  });

  it('should update recruiter with his provided limitations type', () => {
    service.setRecruiterLimitationsType(RecruiterLimitationsType.UnverifiedCompany);

    expect(service.limitationsType).toEqual(RecruiterLimitationsType.UnverifiedCompany);
  });

  it('should emit a change limitations type update', () => {
    spyOn(service.limitationsTypeChange, 'emit');

    service.setRecruiterLimitationsType(RecruiterLimitationsType.DailyLimit);

    expect(service.limitationsTypeChange.emit).toHaveBeenCalledWith(RecruiterLimitationsType.DailyLimit);
    expect(service.limitationsTypeChange.emit).toHaveBeenCalledTimes(1);
  });

  it('should fetch products on first call', () => {
    spyOn(service, 'refreshProducts');

    service.getSuggestibleProducts();

    expect(service.refreshProducts).toHaveBeenCalledTimes(1);
  });

  it('should return cached products', () => {
    service['suggestibleProducts'] = stub.suggestibleProducts;
    service['productsFetched'] = true;
    spyOn(service, 'refreshProducts');

    service.getSuggestibleProducts().subscribe(result => {
      expect(result).toBe(stub.suggestibleProducts);
    });
  });

  it('should return observable products when the call is in progress', () => {
    service['productsObservable'] = of(stub.suggestibleProducts);
    spyOn(service, 'refreshProducts');

    service.getSuggestibleProducts().subscribe(result => {
      expect(result).toBe(stub.suggestibleProducts);
    });
  });

  it('should refresh products', () => {
    service['suggestibleProducts'] = stub.suggestibleProducts;

    const observable = service.refreshProducts();

    expect(service['suggestibleProducts']).toBeNull();

    observable.subscribe(result => {
      expect(result).toEqual(stub.suggestibleProducts);
    });
  });
});
