import { EventEmitter, Injectable, Output } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map, share, finalize } from 'rxjs/operators';
import { ProfileAPIService } from '../../api';
import { Recruiter } from '../../../classes';

export enum RecruiterLimitationsType {
  DailyLimit = 'daily_limit',
  InvalidSubscription = 'invalid_subscription',
  NoProfile = 'no_profile',
  Unlimited = '',
  UnverifiedCompany = 'unverified_company'
}

@Injectable({
  providedIn: 'root'
})
export class RecruiterService {
  private recruiter: Recruiter;
  private suggestibleProducts: any;
  private observable: Observable<Recruiter>;
  private productsObservable: Observable<any>;
  private initialized = false;
  private productsFetched = false;
  public limitationsType = '';
  @Output()
  public limitationsTypeChange = new EventEmitter<string>();

  constructor(private profileAPIService: ProfileAPIService) {}

  public getRecruiter(authCode?: string): Observable<Recruiter> {
    if (this.initialized) {
      return of(this.recruiter);
    } else if (this.observable) {
      return this.observable;
    } else {
      return this.refreshRecruiter(authCode);
    }
  }

  public setRecruiterLimitationsType(limitationsType: string): void {
    this.limitationsType = limitationsType;
    this.limitationsTypeChange.emit(limitationsType);
  }

  public refreshRecruiter(authCode?: string) {
    // check session

    this.recruiter = null;
    this.observable = this.fetchRecruiter(authCode);
    return this.observable;
  }

  private fetchRecruiter(authCode?: string): Observable<Recruiter> {
    return this.profileAPIService.getRecruiter(authCode).pipe(
      map((recruiter: Recruiter) => {
        this.recruiter = recruiter;
        return this.recruiter;
      }),
      finalize(() => {
        this.observable = null;
        this.initialized = true;
      }),
      share()
    );
  }

  public getSuggestibleProducts(): Observable<any> {
    if (this.productsFetched) {
      return of(this.suggestibleProducts);
    } else if (this.productsObservable) {
      return this.productsObservable;
    } else {
      return this.refreshProducts();
    }
  }

  public refreshProducts() {
    this.suggestibleProducts = null;
    this.productsObservable = this.fetchSuggestibleProducts();
    return this.productsObservable;
  }

  private fetchSuggestibleProducts(): Observable<any> {
    return this.profileAPIService.getSuggestibleProducts().pipe(
      map((recruiter: Recruiter) => {
        this.suggestibleProducts = recruiter;
        return this.suggestibleProducts;
      }),
      finalize(() => {
        this.productsObservable = null;
        this.productsFetched = true;
      }),
      share()
    );
  }
}
