import { SessionProfileFavoriteTrackerService } from './session-profile-favorite-tracker.service';

describe('SessionProfileFavoriteTrackerService', () => {
  let service: SessionProfileFavoriteTrackerService;
  let isSessionStorageAvailableSpy: any;

  beforeEach(() => {
    service = new SessionProfileFavoriteTrackerService();
    window.sessionStorage.clear();
  });

  describe('#recordFavorite', () => {
    beforeEach(() => {
      isSessionStorageAvailableSpy = jasmine.createSpy('isSessionStorageAvailable');
      isSessionStorageAvailableSpy.and.returnValue(true);
      service['isSessionStorageAvailable'] = isSessionStorageAvailableSpy;
    });

    it('should do nothing', () => {
      isSessionStorageAvailableSpy.and.returnValue(false);
      service['isSessionStorageAvailable'] = isSessionStorageAvailableSpy;

      spyOn(window.sessionStorage, 'setItem');

      const profileId = '123-abc';

      service.recordFavorite(profileId);

      expect(window.sessionStorage.setItem).not.toHaveBeenCalled();
    });

    it('should record favorite profile as JSON string', () => {
      const profileId = '123-abc';

      service.recordFavorite(profileId);

      const sessionStorageData = window.sessionStorage.getItem('profile-favorite-tracker');

      let isValidJson = false;
      let savedFavorite = null;

      try {
        savedFavorite = JSON.parse(sessionStorageData);
        isValidJson = true;
      } catch (exception) {
        isValidJson = false;
      }

      expect(isValidJson).toBe(true);
      expect(typeof savedFavorite === 'object').toBe(true);
      expect(Object.keys(savedFavorite).length).toBe(1);
      expect(savedFavorite[profileId]).toBeDefined();
    });

    it('should record multiple favorite profiles', () => {
      const profileIds = ['123-abc', '1234-abc', '12345-abc'];

      profileIds.forEach((profileId: string) => service.recordFavorite(profileId));

      const savedFavorite = JSON.parse(window.sessionStorage.getItem('profile-favorite-tracker'));

      expect(Object.keys(savedFavorite).length).toBe(profileIds.length);

      profileIds.forEach((profileId: string) => {
        expect(savedFavorite[profileId]).toBeDefined();
      });
    });
  });

  describe('#isProfileMarkAsFavorite', () => {
    beforeEach(() => {
      isSessionStorageAvailableSpy = jasmine.createSpy('isSessionStorageAvailable');
      isSessionStorageAvailableSpy.and.returnValue(true);
      service['isSessionStorageAvailable'] = isSessionStorageAvailableSpy;
    });

    it('should return false', () => {
      isSessionStorageAvailableSpy.and.returnValue(false);
      service['isSessionStorageAvailable'] = isSessionStorageAvailableSpy;

      const profileId = '123-abc';

      const isProfileMarkAsFavorite = service.isProfileMarkAsFavorite(profileId);

      expect(isProfileMarkAsFavorite).toEqual(false);
    });

    it('should return false if no profiles is mark as favorite', () => {
      expect(service.isProfileMarkAsFavorite('123-abc')).toBe(false);
    });

    it('should return false if provided profile was not saved as favorite', () => {
      service.recordFavorite('123-abc');

      expect(service.isProfileMarkAsFavorite('1234-abc')).toBe(false);
    });

    it('should return true if provided profile was saved as favorite', () => {
      const profileId = '123-abc';

      service.recordFavorite(profileId);

      expect(service.isProfileMarkAsFavorite(profileId)).toBe(true);
    });
  });

  describe('#deleteProfileFavorite', () => {
    beforeEach(() => {
      isSessionStorageAvailableSpy = jasmine.createSpy('isSessionStorageAvailable');
      isSessionStorageAvailableSpy.and.returnValue(true);
      service['isSessionStorageAvailable'] = isSessionStorageAvailableSpy;
    });

    it('should return false', () => {
      isSessionStorageAvailableSpy.and.returnValue(false);
      service['isSessionStorageAvailable'] = isSessionStorageAvailableSpy;

      const profileId = '123-abc';

      const isDeletedProfileFavorite = service.deleteProfileFavorite(profileId);

      expect(isDeletedProfileFavorite).toEqual(false);
    });

    it('should delete profile from sessionStorage', () => {
      service.recordFavorite('123-abc');
      service.deleteProfileFavorite('123-abc');
      expect(service.isProfileMarkAsFavorite('1234-abc')).toBe(false);
    });

    it('should return false if there is no profile on sessionStorage', () => {
      window.sessionStorage.clear();
      expect(service.deleteProfileFavorite('222-abc')).toBeFalsy();
    });

    it('should not store data in session storage', () => {
      const profileId = '123-abc';

      spyOn(JSON, 'parse').and.returnValue([]);
      spyOn(window.sessionStorage, 'setItem');

      const isDeletedProfileFavorite = service.deleteProfileFavorite(profileId);

      expect(window.sessionStorage.setItem).not.toHaveBeenCalled();
      expect(isDeletedProfileFavorite).toEqual(true);
    });
  });

  describe('#isSessionStorageAvailable', () => {
    it('should return true', () => {
      const isSessionStorageAvailable = service['isSessionStorageAvailable']();

      expect(isSessionStorageAvailable).toEqual(true);
    });
  });
});
