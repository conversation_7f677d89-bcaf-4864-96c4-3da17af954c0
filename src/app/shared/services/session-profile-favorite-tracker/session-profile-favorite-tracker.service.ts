import { Injectable } from '@angular/core';

@Injectable()
export class SessionProfileFavoriteTrackerService {
  private profileFavoriteTracker = 'profile-favorite-tracker';

  constructor() {}

  recordFavorite(profileId: string): void {
    if (!this.isSessionStorageAvailable()) {
      return;
    }

    let favoritesProfiles = JSON.parse(window.sessionStorage.getItem(this.profileFavoriteTracker));

    if (favoritesProfiles === null) {
      favoritesProfiles = {};
    }

    favoritesProfiles[profileId] = Date.now();

    window.sessionStorage.setItem(this.profileFavoriteTracker, JSON.stringify(favoritesProfiles));
  }

  isProfileMarkAsFavorite(profileId: string): boolean {
    if (!this.isSessionStorageAvailable()) {
      return false;
    }

    const favoritedProfiles = JSON.parse(window.sessionStorage.getItem(this.profileFavoriteTracker));

    if (favoritedProfiles === null) {
      return false;
    }

    if (typeof favoritedProfiles[profileId] === 'undefined') {
      return false;
    }

    return true;
  }

  deleteProfileFavorite(profileId: string): boolean {
    if (!this.isSessionStorageAvailable()) {
      return false;
    }

    const favoriteProfiles = JSON.parse(window.sessionStorage.getItem(this.profileFavoriteTracker));

    if (favoriteProfiles === null) {
      return false;
    }

    if (!(typeof favoriteProfiles[profileId] === 'undefined')) {
      delete favoriteProfiles[profileId];
      window.sessionStorage.setItem(this.profileFavoriteTracker, JSON.stringify(favoriteProfiles));
    }

    return true;
  }

  private isSessionStorageAvailable(): boolean {
    return typeof window.sessionStorage !== 'undefined';
  }
}
