import { WindowService } from './window.service';

describe('WindowService', () => {
  const windowService = new WindowService();

  describe('#getWindow', () => {
    it('should return window object', () => {
      const browserWindow = windowService.getWindow();

      expect(browserWindow).toEqual(window);
    });
  });

  describe('#scrollTo', () => {
    it('should scroll a certain position of the screen', () => {
      spyOn(window, 'scrollTo');

      const scrollPosition = { x: 100, y: 200 };

      windowService.scrollTo(scrollPosition.x, scrollPosition.y);

      expect(window.scrollTo).toHaveBeenCalledTimes(1);
      expect(window.scrollTo).toHaveBeenCalledWith(scrollPosition.x, scrollPosition.y);
    });
  });
});
