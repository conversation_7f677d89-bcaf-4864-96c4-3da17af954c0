import { Injectable } from '@angular/core';

@Injectable()
export class SessionProfileViewTrackerService {
  private profileViewTracker = 'profile-view-tracker';

  constructor() {}

  recordProfileView(profileId: string, page: number): void {
    if (!this.isSessionStorageAvailable()) {
      return;
    }

    let viewedProfiles = JSON.parse(window.sessionStorage.getItem(this.profileViewTracker));

    if (viewedProfiles === null) {
      viewedProfiles = {};
    }

    viewedProfiles[profileId] = {
      time: Date.now(),
      page: page
    };

    window.sessionStorage.setItem(this.profileViewTracker, JSON.stringify(viewedProfiles));
  }

  getProfileViewed(profileId: string, jobseekerViewedDate?: number, skipStorage?: boolean): number {
    if (jobseekerViewedDate) {
      return jobseekerViewedDate;
    }

    if (!this.isSessionStorageAvailable() || skipStorage) {
      return null;
    }

    const viewedProfiles = JSON.parse(window.sessionStorage.getItem(this.profileViewTracker));
    if (viewedProfiles === null) {
      return null;
    }

    if (typeof viewedProfiles[profileId] === 'undefined') {
      return null;
    }

    return viewedProfiles[profileId].time;
  }

  private isSessionStorageAvailable(): boolean {
    return typeof window.sessionStorage !== 'undefined';
  }
}
