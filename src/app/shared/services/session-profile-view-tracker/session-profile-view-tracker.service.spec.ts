import { SessionProfileViewTrackerService } from './session-profile-view-tracker.service';

describe('SessionProfileViewTrackerService', () => {
  let service: SessionProfileViewTrackerService;
  let isSessionStorageAvailableSpy: any;

  beforeEach(() => {
    service = new SessionProfileViewTrackerService();
    window.sessionStorage.clear();
  });

  describe('#recordProfileView', () => {
    beforeEach(() => {
      isSessionStorageAvailableSpy = jasmine.createSpy('isSessionStorageAvailable');
      isSessionStorageAvailableSpy.and.returnValue(true);
      service['isSessionStorageAvailable'] = isSessionStorageAvailableSpy;
    });

    it('should record viewed profile as JSON string', () => {
      const profileId = '123-abc';
      const page = 5;

      service.recordProfileView(profileId, page);

      const sessionStorageData = window.sessionStorage.getItem('profile-view-tracker');

      let isValidJson = false;
      let viewedProfiles = null;

      try {
        viewedProfiles = JSON.parse(sessionStorageData);
        isValidJson = true;
      } catch (exception) {
        isValidJson = false;
      }

      expect(isValidJson).toBe(true);
      expect(typeof viewedProfiles === 'object').toBe(true);
      expect(Object.keys(viewedProfiles).length).toBe(1);
      expect(typeof viewedProfiles[profileId] === 'object').toBe(true);
      expect(viewedProfiles[profileId].page).toBe(page);
    });

    it('should do nothing', () => {
      isSessionStorageAvailableSpy.and.returnValue(false);
      service['isSessionStorageAvailable'] = isSessionStorageAvailableSpy;

      spyOn(window.sessionStorage, 'setItem');

      const profileId = '123-abc';
      const page = 5;

      service.recordProfileView(profileId, page);

      expect(window.sessionStorage.setItem).not.toHaveBeenCalled();
    });

    it('should update viewed profile if viewed again', () => {
      const profileId = '123-abc';
      const page = 5;

      service.recordProfileView(profileId, page);
      service.recordProfileView(profileId, page);
      service.recordProfileView(profileId, page);

      const viewedProfiles = JSON.parse(window.sessionStorage.getItem('profile-view-tracker'));

      expect(Object.keys(viewedProfiles).length).toBe(1);
      expect(typeof viewedProfiles[profileId] === 'object').toBe(true);
      expect(viewedProfiles[profileId].page).toBe(page);
    });

    it('should record multiple viewed profiles', () => {
      const profileIds = ['123-abc', '1234-abc', '12345-abc'];
      const page = 5;

      profileIds.forEach((profileId: string) => service.recordProfileView(profileId, page));

      const viewedProfiles = JSON.parse(window.sessionStorage.getItem('profile-view-tracker'));

      expect(Object.keys(viewedProfiles).length).toBe(profileIds.length);
      profileIds.forEach((profileId: string) => {
        expect(typeof viewedProfiles[profileId] === 'object').toBe(true);
        expect(viewedProfiles[profileId].page).toBe(page);
      });
    });
  });

  describe('#getProfileViewed', () => {
    beforeEach(() => {
      isSessionStorageAvailableSpy = jasmine.createSpy('isSessionStorageAvailable');
      isSessionStorageAvailableSpy.and.returnValue(true);
      service['isSessionStorageAvailable'] = isSessionStorageAvailableSpy;
    });

    it('should return profile viewed if already defined', () => {
      const expectedViewed = Date.now();
      expect(service.getProfileViewed('123-abc', expectedViewed)).toBe(expectedViewed);
    });

    it('should return null if no profiles viewed', () => {
      expect(service.getProfileViewed('123-abc')).toBe(null);
    });

    it('should return null when viewed is not defined and skip storage is set', () => {
      expect(service.getProfileViewed('123-abc', undefined, true)).toBe(null);
    });

    it('should return null if provided profile was not viewed', () => {
      service.recordProfileView('123-abc', 1);

      expect(service.getProfileViewed('1234-abc')).toBe(null);
    });

    it('should be defined if provided profile was viewed', () => {
      const profileId = '123-abc';

      service.recordProfileView(profileId, 1);

      expect(service.getProfileViewed(profileId)).toBeDefined();
    });

    it('should return null', () => {
      isSessionStorageAvailableSpy.and.returnValue(false);
      service['isSessionStorageAvailable'] = isSessionStorageAvailableSpy;

      const profileId = '123-abc';

      const getProfileViewed = service.getProfileViewed(profileId);

      expect(getProfileViewed).toEqual(null);
    });
  });

  describe('#isSessionStorageAvailable', () => {
    it('should return true', () => {
      const isSessionStorageAvailable = service['isSessionStorageAvailable']();

      expect(isSessionStorageAvailable).toEqual(true);
    });
  });
});
