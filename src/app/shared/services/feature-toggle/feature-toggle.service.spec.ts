describe('featureToggleService', () => {
  const featureToggleService = require('./feature-toggle.service');

  beforeEach(() => {
    window.document.cookie.split(';').forEach(function(cookie) {
      window.document.cookie = cookie.replace(/^ +/, '').replace(/=.*/, '=;expires=' + new Date().toUTCString() + ';path=/');
    });
  });

  describe('#isFeatureEnabled', () => {
    it('should return true if feature cookie present and set to true', () => {
      window.document.cookie = 'foo=bar;';
      window.document.cookie = 'feature-awesome=true;';
      window.document.cookie = 'baz=boo';
      expect(featureToggleService.isFeatureEnabled('feature-awesome')).toBe(true);
    });

    it('should return false if feature cookie not present', () => {
      window.document.cookie = 'foo=bar;';
      window.document.cookie = 'baz=boo';
      expect(featureToggleService.isFeatureEnabled('feature-awesome')).toBe(false);
    });

    it('should return false if feature cookie present but set to an empty string', () => {
      window.document.cookie = 'foo=bar;';
      window.document.cookie = 'feature-awesome=;';
      window.document.cookie = 'baz=boo';
      expect(featureToggleService.isFeatureEnabled('feature-awesome')).toBe(false);
    });
  });
});
