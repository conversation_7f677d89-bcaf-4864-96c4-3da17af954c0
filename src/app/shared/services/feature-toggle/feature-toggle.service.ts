export const FEATURES = {
  FEATURE_B2B_HAS_PLATFORM_CORE_ACCOUNT: 'feature-platform-core-account',
};

export function isFeatureEnabled(featureName: string) {
  try {
    return getCookie(featureName) === 'true';
  } catch (error) {
    return false;
  }

  function getCookie(name: string): string {
    const cookieNameAssignment = `${name}=`;

    const decodedCookie = decodeURIComponent(document.cookie);

    const allCookies = decodedCookie.split(';');

    for (let i = 0; i < allCookies.length; i++) {
      let cookie = allCookies[i];

      while (cookie.charAt(0) === ' ') {
        cookie = cookie.substring(1);
      }

      if (cookie.indexOf(cookieNameAssignment) === 0) {
        return cookie.substring(cookieNameAssignment.length, cookie.length);
      }
    }

    throw new Error('Not found');
  }
}
