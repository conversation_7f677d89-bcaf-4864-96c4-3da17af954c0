import { Injectable } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { Subject, Subscription } from 'rxjs';
import { Filter } from '../../../store/models/filter.modal';
import { HttpService } from '../http/http.service';

interface Cache {
  timestamp: number;
  params: HttpParams;
  url: string;
  response: any;
}

const API_FILTERS = '/api/recruiter/search/filters';
const API_SEARCH = '/api/recruiter/search';
const CACHE_EXPIRE_TIME = 1000 * 60 * 10;

@Injectable()
export class SearchService {
  public filters$ = new Subject();
  private filtersSub: Subscription;
  public search$ = new Subject();
  private searchSub: Subscription;
  private cache: Cache[] = [];

  constructor(private http: HttpService) {}

  updateFilters(query: { [key: string]: string[] } = { all: [''] }, location: string = '', filters: Filter[]): void {
    const params = this.getParams(filters);
    const url = `${API_FILTERS}?location=${location}` + this.buildSearchQueryUri(query);
    this.cleanExpiredCache();
    const cachedResponse = this.isCached(this.cache, params, url);

    if (cachedResponse) {
      this.filters$.next(cachedResponse.response);
    } else {
      if (this.filtersSub && !this.filtersSub.closed) {
        this.filtersSub.unsubscribe();
      }

      this.filtersSub = this.fetch(url, params, this.filters$);
    }
  }

  updateSearch(
    query: { [key: string]: string[] } = { all: [''] },
    location = '',
    pageSize: number,
    currentPage: number,
    sort: string,
    filters: Filter[]
  ): void {
    if (pageSize * currentPage > 10000) {
      this.search$.error({ type: 'SEARCH_ERROR_RESULT_WINDOW_TOO_LARGE' });
    }

    if (sort === 'default') {
      sort = 'update';
    }
    const params = this.getParams(filters);
    const url = `${API_SEARCH}?location=${location}${this.buildSearchQueryUri(query)}&limit=${pageSize}&page=${currentPage}&sort=${sort}`;
    this.cleanExpiredCache();
    const cachedResponse = this.isCached(this.cache, params, url);

    if (cachedResponse) {
      this.search$.next(cachedResponse.response);
    } else {
      if (this.searchSub && !this.searchSub.closed) {
        this.searchSub.unsubscribe();
      }

      this.searchSub = this.fetch(url, params, this.search$);
    }
  }

  private getParams(filters: Filter[]): HttpParams {
    let params = new HttpParams();

    filters.map(filter => {
      filter.filterOptions
        .filter(filterOption => filterOption.selected)
        .map(filterOption => {
          const filterOptionValue = filterOption.value || filterOption.name;
          params = params.append(`filters[${filter.name}][]`, filterOptionValue);
        });
    });

    return params;
  }

  private fetch(url: string, params: HttpParams, subject: Subject<any>): Subscription {
    return this.http.get(url, params).subscribe(response => {
      this.cache.push(<Cache>{
        timestamp: +new Date(),
        url,
        params,
        response: response
      });
      subject.next(response);
    });
  }

  private cleanExpiredCache(): void {
    const currentTimeStamp = +new Date();
    this.cache = this.cache.filter(({ timestamp }) => timestamp > currentTimeStamp - CACHE_EXPIRE_TIME);
  }

  private isCached(cache: Cache[], newSearch: HttpParams, newUrl: string): Cache {
    return cache
      .filter(({ params, url }) => {
        return params.toString() === newSearch.toString() && newUrl === url;
      })
      .pop();
  }

  public purgeCache(): void {
    this.cache = [];
  }

  private buildSearchQueryUri(searchQuery: { [key: string]: string[] }): string {
    let uri = '';

    for (const property in searchQuery) {
      if (searchQuery.hasOwnProperty(property) && searchQuery[property].length > 0) {
        searchQuery[property].forEach(function(value: string) {
          value = encodeURIComponent(value);
          uri += `&query[${property}][]=${value}`;
        });
      }
    }
    return uri;
  }
}
