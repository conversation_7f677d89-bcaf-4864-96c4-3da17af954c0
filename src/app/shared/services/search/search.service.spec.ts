import { SearchService } from './search.service';
import { HttpParams } from '@angular/common/http';
import { of, Subject, Subscription } from 'rxjs';
import { Filter } from '../../../store/models/filter.modal';

enum FilterTypes {
  CheckBox = 0,
  Dropdown = 1,
  AutoComplete = 2
}

interface Cache {
  timestamp: number;
  params: HttpParams;
  url: string;
  response: any;
}

describe('SearchService', () => {
  const stub: any = {};
  let service: SearchService;

  beforeEach(() => {
    stub.HttpService = jasmine.createSpyObj('HttpService', ['get']);
    stub.HttpService.get.and.returnValue(of({}));

    service = new SearchService(<any>stub.HttpService);
  });

  describe('#updateFilters', () => {
    let dataQuery: any;
    let dataLocation: string;
    let dataFilters: Filter[];

    beforeEach(() => {
      dataQuery = { all: [''] };
      dataLocation = 'Arnhem';
      dataFilters = [
        {
          name: 'test1',
          title: 'testFilter',
          open: true,
          type: FilterTypes.CheckBox,
          filterOptions: [
            {
              name: 'filterOption',
              selected: true,
              count: '4',
              order: 0
            }
          ]
        }
      ];
    });

    it('should get parameters out of the filters and call cache functionalities', () => {
      service['getParams'] = jasmine.createSpy('getParams');
      service['cleanExpiredCache'] = jasmine.createSpy('cleanExpiredCache');
      service['isCached'] = jasmine.createSpy('isCached');

      service.updateFilters(dataQuery, dataLocation, dataFilters);

      expect(service['getParams']).toHaveBeenCalledTimes(1);
      expect(service['getParams']).toHaveBeenCalledWith(dataFilters);

      expect(service['cleanExpiredCache']).toHaveBeenCalledTimes(1);
      expect(service['cleanExpiredCache']).toHaveBeenCalledWith();

      expect(service['isCached']).toHaveBeenCalledTimes(1);
    });

    it('should manage search with cached data', () => {
      dataQuery = undefined;
      const cacheResponse = { response: 'cache' };

      const isCachedSpy = jasmine.createSpy('isCachedSpy');
      isCachedSpy.and.returnValue(cacheResponse);
      service['isCached'] = isCachedSpy;
      service.filters$.next = jasmine.createSpy('next');

      service.updateFilters(dataQuery, dataLocation, dataFilters);

      expect(service.filters$.next).toHaveBeenCalledTimes(1);
      expect(service.filters$.next).toHaveBeenCalledWith(cacheResponse.response);
    });

    it('should manage search with fetched data', () => {
      dataLocation = undefined;
      const isCachedSpy = jasmine.createSpy('isCachedSpy');
      isCachedSpy.and.returnValue(false);
      service['isCached'] = isCachedSpy;
      service['fetch'] = jasmine.createSpy('fetch');
      service['filtersSub'] = undefined;

      service.updateFilters(dataQuery, dataLocation, dataFilters);

      expect(service['fetch']).toHaveBeenCalledTimes(1);
    });

    it('should manage search with fetched data and search is unsubscribed', () => {
      const filtersSubscription = new Subscription();
      filtersSubscription.closed = false;
      filtersSubscription.unsubscribe = jasmine.createSpy('unsubscribe');

      const isCachedSpy = jasmine.createSpy('isCachedSpy');
      isCachedSpy.and.returnValue(false);
      service['isCached'] = isCachedSpy;

      const fetchSpy = jasmine.createSpy('fetch');
      fetchSpy.and.returnValue(filtersSubscription);
      service['fetch'] = fetchSpy;

      service['filtersSub'] = filtersSubscription;

      service.updateFilters(dataQuery, dataLocation, dataFilters);

      expect(service['filtersSub'].closed).toEqual(false);
      expect(service['filtersSub'].unsubscribe).toHaveBeenCalledTimes(1);
      expect(service['fetch']).toHaveBeenCalledTimes(1);
    });
  });

  describe('#updateSearch', () => {
    let dataQuery: any;
    let dataLocation: string;
    let dataPageSize: number;
    let dataCurrentPage: number;
    let dataSort: string;
    let dataFilters: Filter[];

    beforeEach(() => {
      dataQuery = { all: [''] };
      dataLocation = 'Arnhem';
      dataPageSize = 50;
      dataCurrentPage = 1;
      dataSort = 'default';
      dataFilters = [
        {
          name: 'test1',
          title: 'testFilter',
          open: true,
          type: FilterTypes.CheckBox,
          filterOptions: [
            {
              name: 'filterOption',
              selected: true,
              count: '4',
              order: 0
            }
          ]
        }
      ];
    });

    it('should set an error due to too many data', () => {
      dataCurrentPage = 10000;
      service.search$.error = jasmine.createSpy('error');

      service.updateSearch(dataQuery, dataLocation, dataPageSize, dataCurrentPage, dataSort, dataFilters);

      expect(service.search$.error).toHaveBeenCalledTimes(1);
      expect(service.search$.error).toHaveBeenCalledWith({
        type: 'SEARCH_ERROR_RESULT_WINDOW_TOO_LARGE'
      });
    });

    it('should get parameters out of the filters and call cache functionalities', () => {
      dataSort = 'sort';
      service['getParams'] = jasmine.createSpy('getParams');
      service['cleanExpiredCache'] = jasmine.createSpy('cleanExpiredCache');
      service['isCached'] = jasmine.createSpy('isCached');

      service.updateSearch(dataQuery, dataLocation, dataPageSize, dataCurrentPage, dataSort, dataFilters);

      expect(service['getParams']).toHaveBeenCalledTimes(1);
      expect(service['getParams']).toHaveBeenCalledWith(dataFilters);

      expect(service['cleanExpiredCache']).toHaveBeenCalledTimes(1);
      expect(service['cleanExpiredCache']).toHaveBeenCalledWith();

      expect(service['isCached']).toHaveBeenCalledTimes(1);
    });

    it('should manage search with cached data', () => {
      const cacheResponse = { response: 'cache' };

      const isCachedSpy = jasmine.createSpy('isCachedSpy');
      isCachedSpy.and.returnValue(cacheResponse);
      service['isCached'] = isCachedSpy;
      service.search$.next = jasmine.createSpy('next');

      service.updateSearch(dataQuery, dataLocation, dataPageSize, dataCurrentPage, dataSort, dataFilters);

      expect(service.search$.next).toHaveBeenCalledTimes(1);
      expect(service.search$.next).toHaveBeenCalledWith(cacheResponse.response);
    });

    it('should manage search with fetched data', () => {
      dataQuery = undefined;
      const isCachedSpy = jasmine.createSpy('isCachedSpy');
      isCachedSpy.and.returnValue(false);
      service['isCached'] = isCachedSpy;
      service['fetch'] = jasmine.createSpy('fetch');
      service['searchSub'] = undefined;

      service.updateSearch(dataQuery, dataLocation, dataPageSize, dataCurrentPage, dataSort, dataFilters);

      expect(service['fetch']).toHaveBeenCalledTimes(1);
    });

    it('should manage search with fetched data and search is unsubscribed', () => {
      dataLocation = undefined;
      const searchSubscription = new Subscription();
      searchSubscription.closed = false;
      searchSubscription.unsubscribe = jasmine.createSpy('unsubscribe');

      const isCachedSpy = jasmine.createSpy('isCachedSpy');
      isCachedSpy.and.returnValue(false);
      service['isCached'] = isCachedSpy;

      const fetchSpy = jasmine.createSpy('fetch');
      fetchSpy.and.returnValue(searchSubscription);
      service['fetch'] = fetchSpy;

      service['searchSub'] = searchSubscription;

      service.updateSearch(dataQuery, dataLocation, dataPageSize, dataCurrentPage, dataSort, dataFilters);

      expect(service['searchSub'].closed).toEqual(false);
      expect(service['searchSub'].unsubscribe).toHaveBeenCalledTimes(1);
      expect(service['fetch']).toHaveBeenCalledTimes(1);
    });
  });

  describe('#getParams', () => {
    let filters: Filter[];

    beforeEach(() => {
      filters = [
        {
          name: 'test1',
          title: 'testFilter',
          open: true,
          type: FilterTypes.CheckBox,
          filterOptions: [
            {
              name: 'filterOption',
              selected: true,
              count: '4',
              order: 0
            }
          ]
        }
      ];
    });

    it('should return empty data if no filters set', () => {
      const params = new HttpParams();

      expect(service['getParams']([])).toEqual(params);
    });

    it('should return filters http params', () => {
      let expectedHttpParams = new HttpParams();
      expectedHttpParams = expectedHttpParams.append(`filters[test1][]`, 'filterOption');

      expect(service['getParams'](filters)).toEqual(expectedHttpParams);
    });
  });

  describe('#fetch', () => {
    let httpUrl: string;
    let httpParams: HttpParams;
    let fetchSubject: Subject<any>;

    beforeEach(() => {
      stub.HttpService.get.calls.reset();

      httpUrl = 'url';
      httpParams = new HttpParams();
      httpParams.append('filter', 'php');
      fetchSubject = new Subject();
    });

    it('should call the get http service', () => {
      const response = {};
      spyOn(fetchSubject, 'next');

      service['fetch'](httpUrl, httpParams, fetchSubject);

      expect(stub.HttpService.get).toHaveBeenCalledTimes(1);
      expect(stub.HttpService.get).toHaveBeenCalledWith(httpUrl, httpParams);
      expect(service['cache'][service['cache'].length - 1].response).toEqual(response);
      expect(service['cache'][service['cache'].length - 1].url).toEqual(httpUrl);
      expect(service['cache'][service['cache'].length - 1].params).toEqual(httpParams);

      expect(fetchSubject.next).toHaveBeenCalledTimes(1);
      expect(fetchSubject.next).toHaveBeenCalledWith(response);
    });
  });

  describe('#cleanExpiredCache', () => {
    const cachedUrl = '/api/recruiter/search/filters?location=Amerongen&query[all][]=php';
    const httpParams = new HttpParams();
    httpParams.append('location', 'Ede');

    it('should clean old cache', () => {
      service['cache'] = [
        {
          timestamp: +new Date(1983),
          url: cachedUrl,
          params: httpParams,
          response: {}
        }
      ];

      service['cleanExpiredCache']();

      expect(service['cache']).toEqual([]);
    });

    it('should not clean recent cache', () => {
      const stillValidCache = [
        {
          timestamp: +new Date(),
          url: cachedUrl,
          params: httpParams,
          response: {}
        }
      ];

      service['cache'] = stillValidCache;

      service['cleanExpiredCache']();

      expect(service['cache']).toEqual(stillValidCache);
    });
  });

  describe('#isCached', () => {
    const cachedTimestamp = +new Date();
    const cachedUrl = '/api/recruiter/search/filters?location=Amerongen&query[all][]=php';
    const notCachedUrl = '/api/recruiter/search/filters?location=Ede&query[all][]=php';
    const newHttpParams = new HttpParams();
    newHttpParams.append('location', 'Ede');

    let currentCache: Cache[];

    beforeEach(() => {
      currentCache = [
        {
          timestamp: cachedTimestamp,
          url: cachedUrl,
          params: newHttpParams,
          response: {}
        },
        {
          timestamp: cachedTimestamp,
          url: cachedUrl,
          params: newHttpParams,
          response: {}
        }
      ];
    });

    it('should return an array with the correct cache', () => {
      expect(service['isCached'](currentCache, newHttpParams, notCachedUrl)).toBeUndefined();
    });

    it('should return an empty array with no associated cache', () => {
      expect(service['isCached'](currentCache, newHttpParams, cachedUrl)).toEqual({
        timestamp: cachedTimestamp,
        url: '/api/recruiter/search/filters?location=Amerongen&query[all][]=php',
        params: newHttpParams,
        response: {}
      });
    });
  });

  describe('#purgeCache', () => {
    it('should clear search and filter cache', () => {
      service['cache'] = <any>['foo', 'bar', 'baz'];

      service.purgeCache();

      expect(service['cache']).toEqual([]);
    });
  });

  describe('#buildSearchQueryUri', () => {
    it('should return a uri with search query parameters', () => {
      const searchQuery = { city: ['amsterdam', 'utrecht'] };
      const expectedUri = '&query[city][]=amsterdam&query[city][]=utrecht';

      expect(service['buildSearchQueryUri'](searchQuery)).toEqual(expectedUri);
    });

    it('should return an empty uri', () => {
      const searchQuery = { city: <any>[] };
      expect(service['buildSearchQueryUri'](searchQuery)).toEqual('');
    });
  });
});
