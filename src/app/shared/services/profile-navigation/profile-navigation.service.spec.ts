import { fakeAsync, flushMicrotasks } from '@angular/core/testing';
import { ReplaySubject, of } from 'rxjs';
import * as SpinnerActions from '../../../store/actions/spinner/spinner.actions';
import * as SearchActions from '../../../store/actions/search/search.actions';
import { ProfileNavigationService } from './profile-navigation.service';

describe('ProfileNavigationService', () => {
  let service: ProfileNavigationService;

  const stub = <any>{};
  const page = 1;
  const search = {
    searchQuery: { all: ['foo'] },
    locationQuery: 'Bar',
    pageSize: 15,
    currentPage: page,
    sort: 'relevance',
    chosenSorting: 'relevance'
  };
  const filter = {
    open: true,
    filters: [
      {
        filterOptions: [{ name: '5' }],
        name: 'radius',
        title: '5',
        open: true,
        type: 0
      }
    ]
  };
  const indexProfile = 5;

  beforeEach(() => {
    stub.Store = new ReplaySubject();
    stub.Store.next({ search, filter });
    stub.Store.dispatch = jasmine.createSpy('dispatch');

    stub.Router = jasmine.createSpyObj('Router', ['navigate']);

    stub.SearchService = {
      search$: jasmine.createSpyObj('search$', ['subscribe']),
      updateSearch: jasmine.createSpy()
    };

    stub.JobSeekerService = jasmine.createSpyObj('JobSeekerService', ['fromJson']);

    service = new ProfileNavigationService(stub.Store, stub.Router, stub.SearchService, stub.JobSeekerService);
  });

  describe('#constructor', () => {
    it('should subscribe to the store to set search and filter properties', () => {
      expect(service['search']).toEqual(<any>search);
      expect(service['filters']).toEqual(<any>filter.filters);
    });

    it('should update the search and filters properties if store is updated', () => {
      const newSearch = {
        searchQuery: { all: ['foo'] },
        locationQuery: 'Bar',
        pageSize: 15,
        currentPage: page,
        sort: 'relevance',
        chosenSorting: 'relevance'
      };
      const newFilter = {
        open: true,
        filters: [
          {
            filterOptions: [{ name: '5' }],
            name: 'radius',
            title: '5',
            open: true,
            type: 0
          }
        ]
      };

      stub.Store.next({ search: newSearch, filter: newFilter });

      expect(service['search']).toEqual(<any>search);
      expect(service['filters']).toEqual(<any>filter.filters);
    });
  });

  describe('#setCurrentPage', () => {
    it('should set current page', () => {
      const newPage = 5;

      service.setCurrentPage(newPage);

      expect(service['currentPage']).toBe(newPage);
    });
  });

  describe('#setIndexNumberOfProfileInResultSet', () => {
    it('should set the index number of profile in result set', () => {
      const indexNumberOfProfileInResultSet = 5;

      service.setIndexNumberOfProfileInResultSet(indexNumberOfProfileInResultSet);

      expect(service['indexNumberOfProfileInResultSet']).toBe(indexNumberOfProfileInResultSet);
    });
  });

  describe('#setInitialResultSet', () => {
    it('should set the initial result set', () => {
      const resultSet = [{}, {}];

      service.setInitialResultSet(resultSet);

      expect(service['resultSet']).toBe(resultSet);
    });
  });

  describe('#setAmountOfPages', () => {
    it('should set amount of pages', () => {
      const amountOfPages = 555;

      service.setAmountOfPages(amountOfPages);

      expect(service['amountOfPages']).toBe(amountOfPages);
    });
  });

  describe('#isProfileOpenedFromSearchResults', () => {
    it('should return false if index number of profile in result set is not set', () => {
      expect(service.isProfileOpenedFromSearchResults()).toBe(false);
    });

    it('should return true if index number of profile in result set is set', () => {
      service.setIndexNumberOfProfileInResultSet(5);

      expect(service.isProfileOpenedFromSearchResults()).toBe(true);
    });
  });

  describe('#isPreviousProfileAvailable', () => {
    it('should return false if profile is not opened from the search results', () => {
      expect(service.isPreviousProfileAvailable()).toBe(false);
    });

    it('should return false if current page is 1 and index number of profile in result set is 0', () => {
      service.setCurrentPage(1);

      service.setIndexNumberOfProfileInResultSet(0);

      expect(service.isPreviousProfileAvailable()).toBe(false);
    });

    it('should return true if current page greater than 1', () => {
      service.setCurrentPage(2);

      service.setIndexNumberOfProfileInResultSet(0);

      expect(service.isPreviousProfileAvailable()).toBe(true);
    });

    it('should return true if index number of profile in result set is greater than 0', () => {
      service.setCurrentPage(1);

      service.setIndexNumberOfProfileInResultSet(1);

      expect(service.isPreviousProfileAvailable()).toBe(true);
    });
  });

  describe('#isNextProfileAvailable', () => {
    it('should return false if profile is not opened from the search results', () => {
      expect(service.isNextProfileAvailable()).toBe(false);
    });

    it('should return false if current page is the last one and profile is the last one in the result set', () => {
      const resultSet = [{}, {}];

      service.setCurrentPage(10);

      service.setAmountOfPages(10);
      service.setInitialResultSet(resultSet);
      service.setIndexNumberOfProfileInResultSet(resultSet.length - 1);

      expect(service.isNextProfileAvailable()).toBe(false);
    });

    it('should return true if current page is not the last one', () => {
      const resultSet = [{}, {}];

      service.setCurrentPage(1);

      service.setAmountOfPages(10);
      service.setInitialResultSet(resultSet);
      service.setIndexNumberOfProfileInResultSet(resultSet.length - 1);

      expect(service.isNextProfileAvailable()).toBe(true);
    });

    it('should return true if profile is not the last one in the result set', () => {
      const resultSet = [{}, {}];

      service.setCurrentPage(10);

      service.setAmountOfPages(10);
      service.setInitialResultSet(resultSet);
      service.setIndexNumberOfProfileInResultSet(resultSet.length - 2);

      expect(service.isNextProfileAvailable()).toBe(true);
    });
  });

  describe('#goToSearchResultsPage', () => {
    it('should dispatch an action to change the current page', () => {
      service.goToSearchResultsPage();

      expect(stub.Store.dispatch).toHaveBeenCalledTimes(1);
      expect(stub.Store.dispatch).toHaveBeenCalledWith(new SearchActions.ChangeCurrentPage(service['currentPage']));
    });

    it('should unset lastNavigation data', () => {
      service['lastNavigation'] = 'next';

      service.goToSearchResultsPage();

      expect(service['lastNavigation']).toBeUndefined();
    });
  });

  describe('#goToPreviousProfile', () => {
    it('should navigate to the previous profile if the current one is not the first in the result set', () => {
      const resultSet = [{ id: 1 }, { id: 2 }];
      const indexNumberOfProfileInResultSet = 1;
      service['navigateToProfile'] = jasmine.createSpy();

      service.setCurrentPage(page);
      service.setAmountOfPages(10);
      service.setInitialResultSet(resultSet);
      service.setIndexNumberOfProfileInResultSet(indexNumberOfProfileInResultSet);

      service.goToPreviousProfile();

      expect(service['navigateToProfile']).toHaveBeenCalledTimes(1);
      expect(service['navigateToProfile']).toHaveBeenCalledWith(resultSet[indexNumberOfProfileInResultSet - 1]['id']);
      expect(service['indexNumberOfProfileInResultSet']).toBe(indexNumberOfProfileInResultSet - 1);
      expect(service['currentPage']).toBe(page);
    });

    it('should navigate to the last profile on the previous page if the current one is the first in the result set', () => {
      service = new ProfileNavigationService(stub.Store, stub.Router, stub.SearchService, stub.JobSeekerService);

      service.fetchResultSet = jasmine.createSpy('fetchResultSet');

      const currentPage = 2;
      const resultSet = [{ id: 1 }, { id: 2 }];
      const indexNumberOfProfileInResultSet = 0;

      service.setCurrentPage(currentPage);
      service.setAmountOfPages(10);
      service.setInitialResultSet(resultSet);
      service.setIndexNumberOfProfileInResultSet(indexNumberOfProfileInResultSet);

      service.goToPreviousProfile();

      expect(stub.Router.navigate).toHaveBeenCalledTimes(0);
      expect(service['currentPage']).toBe(currentPage - 1);
      expect(service.fetchResultSet).toHaveBeenCalledTimes(1);
      expect(service.fetchResultSet).toHaveBeenCalledWith(currentPage - 1);
      expect(service['isGoingForth']).toBeFalsy();
    });

    it('should set lastNavigation data as previous', () => {
      service['lastNavigation'] = '';
      const resultSet = [{ id: 1 }, { id: 2 }];
      const indexNumberOfProfileInResultSet = 1;

      service.setCurrentPage(page);
      service.setAmountOfPages(10);
      service.setInitialResultSet(resultSet);
      service.setIndexNumberOfProfileInResultSet(indexNumberOfProfileInResultSet);

      service.goToPreviousProfile();

      expect(service['lastNavigation']).toEqual('previous');
    });
  });

  describe('#goToNextProfile', () => {
    it('should navigate to the next profile if the current one is not the last in the result set', () => {
      const resultSet = [{ id: 1 }, { id: 2 }];
      const indexNumberOfProfileInResultSet = 0;

      service['navigateToProfile'] = jasmine.createSpy();
      service.setCurrentPage(page);
      service.setAmountOfPages(10);
      service.setInitialResultSet(resultSet);
      service.setIndexNumberOfProfileInResultSet(indexNumberOfProfileInResultSet);

      service.goToNextProfile();

      expect(service['navigateToProfile']).toHaveBeenCalledTimes(1);
      expect(service['navigateToProfile']).toHaveBeenCalledWith(resultSet[indexNumberOfProfileInResultSet + 1]['id']);
      expect(service['indexNumberOfProfileInResultSet']).toBe(indexNumberOfProfileInResultSet + 1);
      expect(service['currentPage']).toBe(page);
    });

    it('should navigate to the first profile on the next page if the current one is the last in the result set', () => {
      service = new ProfileNavigationService(stub.Store, stub.Router, stub.SearchService, stub.JobSeekerService);

      service.fetchResultSet = jasmine.createSpy('fetchResultSet');

      const resultSet = [{ id: 1 }, { id: 2 }];
      const indexNumberOfProfileInResultSet = 1;

      service.setCurrentPage(page);
      service.setAmountOfPages(10);
      service.setInitialResultSet(resultSet);
      service.setIndexNumberOfProfileInResultSet(indexNumberOfProfileInResultSet);

      service.goToNextProfile();

      expect(stub.Router.navigate).toHaveBeenCalledTimes(0);
      expect(service['currentPage']).toBe(page + 1);
      expect(service.fetchResultSet).toHaveBeenCalledTimes(1);
      expect(service.fetchResultSet).toHaveBeenCalledWith(page + 1);
      expect(service['isGoingForth']).toBeTruthy();
    });

    it('should set lastNavigation data as next', () => {
      service['lastNavigation'] = '';
      const resultSet = [{ id: 1 }, { id: 2 }];
      const indexNumberOfProfileInResultSet = 0;

      service.setCurrentPage(page);
      service.setAmountOfPages(10);
      service.setInitialResultSet(resultSet);
      service.setIndexNumberOfProfileInResultSet(indexNumberOfProfileInResultSet);

      service.goToNextProfile();

      expect(service['lastNavigation']).toEqual('next');
    });
  });

  describe('#fetchResultSet', () => {
    it('should fetch profiles and call correct method on success', <any>fakeAsync(() => {
      const fetchedData = {};
      stub.SearchService.search$.subscribe.and.callFake((success: Function, fail: Function) => {
        success(fetchedData);
      });
      stub.SearchService.updateSearch.and.returnValue(of(fetchedData));

      service = new ProfileNavigationService(stub.Store, stub.Router, stub.SearchService, stub.JobSeekerService);

      service['whenFetchingResultSetSucceeded'] = jasmine.createSpy('whenFetchingResultSetSucceeded');
      service['whenFetchingResultSetFailed'] = jasmine.createSpy('whenFetchingResultSetFailed');

      const targetPage = 2;
      service.fetchResultSet(targetPage);

      expect(stub.Store.dispatch).toHaveBeenCalledTimes(1);
      expect(stub.Store.dispatch).toHaveBeenCalledWith(new SpinnerActions.ShowSpinner());
      expect(stub.SearchService.updateSearch).toHaveBeenCalledTimes(1);
      expect(stub.SearchService.updateSearch).toHaveBeenCalledWith(
        search.searchQuery,
        search.locationQuery,
        search.pageSize,
        targetPage,
        search.sort,
        filter.filters
      );

      flushMicrotasks();

      expect(service['whenFetchingResultSetSucceeded']).toHaveBeenCalledTimes(1);
      expect(service['whenFetchingResultSetSucceeded']).toHaveBeenCalledWith(fetchedData);
      expect(service['whenFetchingResultSetFailed']).toHaveBeenCalledTimes(0);
    }));

    it('should fetch profiles and call correct method on failure', <any>fakeAsync(() => {
      stub.SearchService.search$.subscribe.and.callFake((success: Function, fail: Function) => {
        fail();
      });

      service = new ProfileNavigationService(stub.Store, stub.Router, stub.SearchService, stub.JobSeekerService);

      service['whenFetchingResultSetSucceeded'] = jasmine.createSpy('whenFetchingResultSetSucceeded');
      service['whenFetchingResultSetFailed'] = jasmine.createSpy('whenFetchingResultSetFailed');

      const targetPage = 2;

      service.fetchResultSet(targetPage);

      expect(stub.Store.dispatch).toHaveBeenCalledTimes(1);
      expect(stub.Store.dispatch).toHaveBeenCalledWith(new SpinnerActions.ShowSpinner());
      expect(stub.SearchService.updateSearch).toHaveBeenCalledTimes(1);
      expect(stub.SearchService.updateSearch).toHaveBeenCalledWith(
        search.searchQuery,
        search.locationQuery,
        search.pageSize,
        targetPage,
        search.sort,
        filter.filters
      );

      flushMicrotasks();

      expect(service['whenFetchingResultSetSucceeded']).toHaveBeenCalledTimes(0);
      expect(service['whenFetchingResultSetFailed']).toHaveBeenCalledTimes(1);
    }));
  });

  describe('#whenFetchingResultSetSucceeded', () => {
    it('should dispatch an action to hide the main spinner', () => {
      service = new ProfileNavigationService(stub.Store, stub.Router, stub.SearchService, stub.JobSeekerService);

      service['searchSub'] = jasmine.createSpyObj('searchSub', ['unsubscribe']);
      service.goToSearchResultsPage = jasmine.createSpy('goToSearchResultsPage');

      service['whenFetchingResultSetSucceeded']({ result: <any>[] });

      expect(stub.Store.dispatch).toHaveBeenCalledTimes(1);
      expect(stub.Store.dispatch).toHaveBeenCalledWith(new SpinnerActions.HideSpinner());
      expect(service['searchSub'].unsubscribe).toHaveBeenCalledTimes(1);
    });

    it('should go to search results page if result set is empty', () => {
      service = new ProfileNavigationService(stub.Store, stub.Router, stub.SearchService, stub.JobSeekerService);
      service['searchSub'] = jasmine.createSpyObj('searchSub', ['unsubscribe']);

      service.goToSearchResultsPage = jasmine.createSpy('goToSearchResultsPage');

      const fetchedData = { result: <any>[] };

      service['whenFetchingResultSetSucceeded'](fetchedData);

      expect(service.goToSearchResultsPage).toHaveBeenCalledTimes(1);
      expect(stub.Router.navigate).not.toHaveBeenCalled();
      expect(service['searchSub'].unsubscribe).toHaveBeenCalledTimes(1);
    });

    it('should go to search results page if any error occurred while processing fetched results', () => {
      service = new ProfileNavigationService(stub.Store, stub.Router, stub.SearchService, stub.JobSeekerService);
      service['searchSub'] = jasmine.createSpyObj('searchSub', ['unsubscribe']);

      service.goToSearchResultsPage = jasmine.createSpy('goToSearchResultsPage');

      const fetchedData = {};

      service['whenFetchingResultSetSucceeded'](<any>fetchedData);

      expect(service.goToSearchResultsPage).toHaveBeenCalledTimes(1);
      expect(stub.Router.navigate).not.toHaveBeenCalled();
      expect(service['searchSub'].unsubscribe).toHaveBeenCalledTimes(1);
    });

    it('should navigate to the first profile if going forth', () => {
      const fetchedData = { result: [{ id: 'abc-123' }, { id: 'def-456' }] };

      stub.JobSeekerService.fromJson.and.callFake((profile: object) => profile);

      service = new ProfileNavigationService(stub.Store, stub.Router, stub.SearchService, stub.JobSeekerService);

      service.goToSearchResultsPage = jasmine.createSpy('goToSearchResultsPage');
      service['searchSub'] = jasmine.createSpyObj('searchSub', ['unsubscribe']);
      service['navigateToProfile'] = jasmine.createSpy();

      service['whenFetchingResultSetSucceeded'](fetchedData);

      expect(service['searchSub'].unsubscribe).toHaveBeenCalledTimes(1);
      expect(service.goToSearchResultsPage).not.toHaveBeenCalled();
      expect(service['navigateToProfile']).toHaveBeenCalledTimes(1);
      expect(service['navigateToProfile']).toHaveBeenCalledWith(fetchedData.result[0]['id']);
      expect(service['resultSet']).toEqual(fetchedData.result);
      expect(service['indexNumberOfProfileInResultSet']).toBe(0);
      expect(stub.JobSeekerService.fromJson).toHaveBeenCalledTimes(fetchedData.result.length);

      fetchedData.result.forEach((currentValue, index, array) => {
        expect(stub.JobSeekerService.fromJson).toHaveBeenCalledWith(currentValue, index, array);
      });
    });

    it('should navigate to the last profile if going back', () => {
      const fetchedData = { result: [{ id: 'abc-123' }, { id: 'def-456' }] };

      stub.JobSeekerService.fromJson.and.callFake((profile: object) => profile);

      service = new ProfileNavigationService(stub.Store, stub.Router, stub.SearchService, stub.JobSeekerService);

      service['navigateToProfile'] = jasmine.createSpy();
      service['searchSub'] = jasmine.createSpyObj('searchSub', ['unsubscribe']);
      service.goToSearchResultsPage = jasmine.createSpy('goToSearchResultsPage');
      service['isGoingForth'] = false;

      service['whenFetchingResultSetSucceeded'](fetchedData);

      expect(service['searchSub'].unsubscribe).toHaveBeenCalledTimes(1);
      expect(service.goToSearchResultsPage).not.toHaveBeenCalled();
      expect(service['navigateToProfile']).toHaveBeenCalledTimes(1);
      expect(service['navigateToProfile']).toHaveBeenCalledWith(fetchedData.result[fetchedData.result.length - 1]['id']);
      expect(service['resultSet']).toEqual(fetchedData.result);
      expect(service['indexNumberOfProfileInResultSet']).toBe(fetchedData.result.length - 1);
      expect(stub.JobSeekerService.fromJson).toHaveBeenCalledTimes(fetchedData.result.length);

      fetchedData.result.forEach((currentValue, index, array) => {
        expect(stub.JobSeekerService.fromJson).toHaveBeenCalledWith(currentValue, index, array);
      });
    });
  });

  describe('#navigateToProfile', () => {
    it('should navigate to profile', () => {
      service = new ProfileNavigationService(stub.Store, stub.Router, stub.SearchService, stub.JobSeekerService);

      const profileId = 'test-id';
      service['navigateToProfile'](profileId);

      expect(stub.Router.navigate).toHaveBeenCalledTimes(1);
      expect(stub.Router.navigate).toHaveBeenCalledWith([`/kandidaat/${profileId}`]);
    });
  });

  describe('#whenFetchingResultSetFailed', () => {
    it('should dispatch an action to hide the main spinner', () => {
      service = new ProfileNavigationService(stub.Store, stub.Router, stub.SearchService, stub.JobSeekerService);
      service['searchSub'] = jasmine.createSpyObj('searchSub', ['unsubscribe']);
      service.goToSearchResultsPage = jasmine.createSpy('goToSearchResultsPage');

      service['whenFetchingResultSetFailed']();

      expect(service['searchSub'].unsubscribe).toHaveBeenCalledTimes(1);
      expect(stub.Store.dispatch).toHaveBeenCalledTimes(1);
      expect(stub.Store.dispatch).toHaveBeenCalledWith(new SpinnerActions.HideSpinner());
    });

    it('should go to search results page', () => {
      service = new ProfileNavigationService(stub.Store, stub.Router, stub.SearchService, stub.JobSeekerService);

      service['searchSub'] = jasmine.createSpyObj('searchSub', ['unsubscribe']);
      service.goToSearchResultsPage = jasmine.createSpy('goToSearchResultsPage');

      service['whenFetchingResultSetFailed']();

      expect(service['searchSub'].unsubscribe).toHaveBeenCalledTimes(1);
      expect(service.goToSearchResultsPage).toHaveBeenCalledTimes(1);
    });
  });
});
