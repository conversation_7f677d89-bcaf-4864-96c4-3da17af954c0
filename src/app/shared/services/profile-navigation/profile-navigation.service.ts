import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { SearchService } from '../search/search.service';
import { JobSeekerService } from '../jobseeker/jobseeker.service';
import { AppState } from '../../../types/app-state';
import { Filter } from '../../../store/models/filter.modal';
import { Search } from '../../../store/models/search.model';
import * as SpinnerActions from '../../../store/actions/spinner/spinner.actions';
import * as SearchActions from '../../../store/actions/search/search.actions';

@Injectable()
export class ProfileNavigationService {
  private currentPage: number;
  private indexNumberOfProfileInResultSet: number;
  private resultSet: Array<object>;
  private amountOfPages: number;
  private search: Search;
  private filters: Array<Filter>;
  private searchSub: Subscription;
  private isGoingForth = true;
  private lastNavigation: string;

  constructor(
    private store: Store<AppState>,
    private router: Router,
    private searchService: SearchService,
    private jobSeekerService: JobSeekerService
  ) {
    this.store.subscribe(state => {
      this.search = state.search;
      this.filters = state.filter.filters;
    });
  }

  setCurrentPage(currentPage: number): void {
    this.currentPage = currentPage;
  }

  setIndexNumberOfProfileInResultSet(indexNumberOfProfileInResultSet: number): void {
    this.indexNumberOfProfileInResultSet = indexNumberOfProfileInResultSet;
  }

  setInitialResultSet(resultSet: Array<object>): void {
    this.resultSet = resultSet;
  }

  setAmountOfPages(amountOfPages: number): void {
    this.amountOfPages = amountOfPages;
  }

  isProfileOpenedFromSearchResults(): boolean {
    return typeof this.indexNumberOfProfileInResultSet !== 'undefined';
  }

  isPreviousProfileAvailable(): boolean {
    if (!this.isProfileOpenedFromSearchResults()) {
      return false;
    }

    if (this.currentPage === 1 && this.indexNumberOfProfileInResultSet === 0) {
      return false;
    }

    return true;
  }

  isNextProfileAvailable(): boolean {
    if (!this.isProfileOpenedFromSearchResults()) {
      return false;
    }

    if (this.currentPage === this.amountOfPages && this.indexNumberOfProfileInResultSet === this.resultSet.length - 1) {
      return false;
    }

    return true;
  }

  goToSearchResultsPage(): void {
    this.store.dispatch(new SearchActions.ChangeCurrentPage(this.currentPage));

    delete this.lastNavigation;
  }

  goToPreviousProfile(): void {
    if (this.indexNumberOfProfileInResultSet === 0) {
      this.isGoingForth = false;
      this.fetchResultSet(--this.currentPage);
    } else {
      this.navigateToProfile(this.resultSet[--this.indexNumberOfProfileInResultSet]['id']);
    }

    this.lastNavigation = 'previous';
  }

  goToNextProfile(): void {
    if (this.indexNumberOfProfileInResultSet === this.resultSet.length - 1) {
      this.isGoingForth = true;
      this.fetchResultSet(++this.currentPage);
    } else {
      this.navigateToProfile(this.resultSet[++this.indexNumberOfProfileInResultSet]['id']);
    }

    this.lastNavigation = 'next';
  }

  fetchResultSet(targetPage: number): void {
    this.store.dispatch(new SpinnerActions.ShowSpinner());

    this.searchSub = this.searchService.search$.subscribe(
      this.whenFetchingResultSetSucceeded.bind(this),
      this.whenFetchingResultSetFailed.bind(this)
    );

    this.searchService.updateSearch(
      this.search.searchQuery,
      this.search.locationQuery,
      this.search.pageSize,
      targetPage,
      this.search.sort,
      this.filters
    );
  }

  private navigateToProfile(profileId: string) {
    this.router.navigate([`/kandidaat/${profileId}`]);
  }

  private whenFetchingResultSetSucceeded(data: { result: Array<object> }): void {
    this.store.dispatch(new SpinnerActions.HideSpinner());
    this.searchSub.unsubscribe();

    try {
      if (data.result.length === 0) {
        this.goToSearchResultsPage();
        return;
      }
      this.resultSet = data.result.map(this.jobSeekerService.fromJson);
      this.indexNumberOfProfileInResultSet = this.isGoingForth === true ? 0 : this.resultSet.length - 1;

      this.navigateToProfile(this.resultSet[this.indexNumberOfProfileInResultSet]['id']);
    } catch (error) {
      this.goToSearchResultsPage();
    }
  }

  private whenFetchingResultSetFailed(): void {
    this.store.dispatch(new SpinnerActions.HideSpinner());
    this.searchSub.unsubscribe();

    this.goToSearchResultsPage();
  }
}
