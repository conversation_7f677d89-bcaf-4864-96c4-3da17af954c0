import { PaginationService, Pages } from './pagination.service';

describe('PaginationService', () => {
  let service: PaginationService;
  beforeEach(() => {
    service = new PaginationService();
  });

  describe('pages', () => {
    describe('amount of pages', () => {
      it('should return 3 pages when a resultset is small enough to fit on 3 pages', () => {
        const pages: Pages = service.getPages(30);

        expect(pages.pages.length).toBe(3);
      });

      it('should return 5 pages', () => {
        const pages: Pages = service.getPages(100);

        expect(pages.pages.length).toBe(5);
      });
    });

    describe('start page', () => {
      it('should set first page to 1 if amount of pages is smaller than max', () => {
        const pages: Pages = service.getPages(30, 1);
        expect(pages.pages[0]).toBe(1);
        expect(pages.startPage).toBe(1);
      });

      it('should set first page to 1 if current page is 1', () => {
        const pages: Pages = service.getPages(100, 1);
        expect(pages.pages[0]).toBe(1);
        expect(pages.startPage).toBe(1);
      });

      it('should set first page to 1 if current page is 2', () => {
        const pages: Pages = service.getPages(100, 2);
        expect(pages.pages[0]).toBe(1);
        expect(pages.startPage).toBe(1);
      });

      it('should set first page to 5 if current page is 7', () => {
        const pages: Pages = service.getPages(200, 7);
        expect(pages.pages[0]).toBe(5);
        expect(pages.startPage).toBe(5);
      });
    });

    describe('end page', () => {
      it('should set last page to 3 if amount of pages 3', () => {
        const pages: Pages = service.getPages(100, 1, 34);
        expect(pages.pages[2]).toBe(3);
        expect(pages.endPage).toBe(3);
      });

      it('should set last page to 5 if current page is 1', () => {
        const pages: Pages = service.getPages(100, 1);
        expect(pages.pages[4]).toBe(5);
        expect(pages.endPage).toBe(5);
      });

      it('should set last page to 5 if current page is 2', () => {
        const pages: Pages = service.getPages(100, 2);
        expect(pages.pages[4]).toBe(5);
        expect(pages.endPage).toBe(5);
      });

      it('should set last page to 10 if current page is 9', () => {
        const pages: Pages = service.getPages(100, 9);
        expect(pages.pages[4]).toBe(10);
        expect(pages.endPage).toBe(10);
      });
    });
  });
});
