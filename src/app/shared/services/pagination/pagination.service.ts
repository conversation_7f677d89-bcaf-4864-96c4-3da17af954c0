import { Injectable } from '@angular/core';

export interface Pages {
  totalItems: number;
  currentPage: number;
  pageSize: number;
  totalPages: number;
  startPage: number;
  endPage: number;
  startIndex: number;
  endIndex: number;
  amountOfPages: number;
  pages: number[];
}

@Injectable()
export class PaginationService {
  private amountOfPages = 5;

  getPages(totalItems: number, currentPage = 1, pageSize = 10): Pages {
    const maxedTotalItems = Math.min(totalItems, 10000 - pageSize);
    const totalPages = Math.ceil(maxedTotalItems / pageSize);

    let startPage: number, endPage: number;
    if (totalPages <= this.amountOfPages) {
      startPage = 1;
      endPage = totalPages;
    } else {
      if (currentPage <= Math.ceil(this.amountOfPages / 2)) {
        startPage = 1;
        endPage = this.amountOfPages;
      } else if (currentPage + 2 >= totalPages) {
        startPage = totalPages - this.amountOfPages + 1;
        endPage = totalPages;
      } else {
        startPage = Math.ceil(currentPage - this.amountOfPages / 2);
        endPage = currentPage + 2;
      }
    }
    endPage = Math.min(endPage, totalPages);
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize - 1, maxedTotalItems - 1);

    const pages = Array.from(Array(endPage - startPage + 1), (_, i) => startPage + i);

    return {
      totalItems: totalItems,
      currentPage: currentPage,
      pageSize: pageSize,
      totalPages: totalPages,
      startPage: startPage,
      endPage: endPage,
      startIndex: startIndex,
      endIndex: endIndex,
      amountOfPages: this.amountOfPages,
      pages: pages
    };
  }
}
