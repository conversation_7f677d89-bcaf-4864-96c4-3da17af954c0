import { DaysAgoDatePipe } from './days-ago-date.pipe';

describe('DaysAgoDatePipe', () => {
  const pipe = new DaysAgoDatePipe();

  describe('#transform', () => {
    beforeEach(() => {
      jasmine.clock().install();
    });

    afterEach(() => {
      jasmine.clock().uninstall();
    });

    it('should display Vandaag if today', () => {
      const timestampToday = +new Date();
      expect(pipe.transform(timestampToday)).toBe('Vandaag');
    });

    it('should display Gisteren if 1 day ago', () => {
      const timestampYesterday = new Date().setHours(-24, 0, 0, 0);
      expect(pipe.transform(timestampYesterday)).toBe('Gisteren');
    });

    it('should display maandag / dinsdag / woensdag / donderdag / vrijdag / zaterdag / zondag if 2-6 days ago', () => {
      const date2DaysAgo = new Date().setHours(-48, 0, 0, 0);
      expect(pipe.transform(date2DaysAgo)).toBe(pipe['daysNames'][new Date(date2DaysAgo).getDay()]);

      const date3DaysAgo = new Date().setHours(-72, 0, 0, 0);
      expect(pipe.transform(date3DaysAgo)).toBe(pipe['daysNames'][new Date(date3DaysAgo).getDay()]);

      const date4DaysAgo = new Date().setHours(-96, 0, 0, 0);
      expect(pipe.transform(date4DaysAgo)).toBe(pipe['daysNames'][new Date(date4DaysAgo).getDay()]);

      const date5DaysAgo = new Date().setHours(-120, 0, 0, 0);
      expect(pipe.transform(date5DaysAgo)).toBe(pipe['daysNames'][new Date(date5DaysAgo).getDay()]);

      const date6DaysAgo = new Date().setHours(-144, 0, 0, 0);
      expect(pipe.transform(date6DaysAgo)).toBe(pipe['daysNames'][new Date(date6DaysAgo).getDay()]);
    });

    it('should display Vorige week correctly if monday', () => {
      const timestampMonday = new Date(2018, 11, 3);
      jasmine.clock().mockDate(timestampMonday);
      jasmine.clock().tick(50);

      expect(pipe.transform(new Date(2018, 10, 25).getTime())).not.toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 26).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 27).getTime())).not.toBe('Vorige week');
    });

    it('should display Vorige week correctly if tuesday', () => {
      const timestampMonday = new Date(2018, 11, 4);
      jasmine.clock().mockDate(timestampMonday);
      jasmine.clock().tick(50);

      expect(pipe.transform(new Date(2018, 10, 25).getTime())).not.toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 26).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 27).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 28).getTime())).not.toBe('Vorige week');
    });

    it('should display Vorige week correctly if wednesday', () => {
      const timestampMonday = new Date(2018, 11, 5);
      jasmine.clock().mockDate(timestampMonday);
      jasmine.clock().tick(50);

      expect(pipe.transform(new Date(2018, 10, 25).getTime())).not.toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 26).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 27).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 28).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 29).getTime())).not.toBe('Vorige week');
    });

    it('should display Vorige week correctly if thursday', () => {
      const timestampMonday = new Date(2018, 11, 6);
      jasmine.clock().mockDate(timestampMonday);
      jasmine.clock().tick(50);

      expect(pipe.transform(new Date(2018, 10, 25).getTime())).not.toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 26).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 27).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 28).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 29).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 30).getTime())).not.toBe('Vorige week');
    });

    it('should display Vorige week correctly if friday', () => {
      const timestampMonday = new Date(2018, 11, 7);
      jasmine.clock().mockDate(timestampMonday);
      jasmine.clock().tick(50);

      expect(pipe.transform(new Date(2018, 10, 25).getTime())).not.toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 26).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 27).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 28).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 29).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 30).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 11, 1).getTime())).not.toBe('Vorige week');
    });

    it('should display Vorige week correctly if saturday', () => {
      const timestampMonday = new Date(2018, 11, 8);
      jasmine.clock().mockDate(timestampMonday);
      jasmine.clock().tick(50);

      expect(pipe.transform(new Date(2018, 10, 25).getTime())).not.toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 26).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 27).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 28).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 29).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 30).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 11, 1).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 11, 2).getTime())).not.toBe('Vorige week');
    });

    it('should display Vorige week correctly if sunday', () => {
      const timestampMonday = new Date(2018, 11, 9);
      jasmine.clock().mockDate(timestampMonday);
      jasmine.clock().tick(50);

      expect(pipe.transform(new Date(2018, 10, 25).getTime())).not.toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 26).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 27).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 28).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 29).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 10, 30).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 11, 1).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 11, 2).getTime())).toBe('Vorige week');
      expect(pipe.transform(new Date(2018, 11, 3).getTime())).not.toBe('Vorige week');
    });

    it('should display Deze maand if this month', () => {
      const timestampCorrectTodayForCurrentMonth = new Date(2018, 9, 27);
      jasmine.clock().mockDate(timestampCorrectTodayForCurrentMonth);
      jasmine.clock().tick(50);

      const correctDateCurrentMonth = new Date(2018, 9, 3).getTime();
      expect(pipe.transform(correctDateCurrentMonth)).toBe('Deze maand');
    });

    it('should not display Deze maand if this month', () => {
      const timestampWrongTodayForCurrentMonth = new Date(2018, 9, 4);
      jasmine.clock().mockDate(timestampWrongTodayForCurrentMonth);
      jasmine.clock().tick(50);

      const correctDateCurrentMonth = new Date(2018, 9, 3).getTime();
      expect(pipe.transform(correctDateCurrentMonth)).not.toBe('Deze maand');
    });

    it('should display Vorige maand if last month', () => {
      const timestampTodayForPreviousMonth = new Date(2018, 6, 29);
      jasmine.clock().mockDate(timestampTodayForPreviousMonth);
      jasmine.clock().tick(50);

      const datePreviousMonth = new Date(2018, 5, 3).getTime();
      expect(pipe.transform(datePreviousMonth)).toBe('Vorige maand');
    });

    it('should display MM/YYYY if earlier', () => {
      const timestampEarlier = new Date().setFullYear(2010, 2, 1);
      expect(pipe.transform(timestampEarlier)).toBe('maart 2010');
    });
  });

  describe('#getTimestampDaysAgo', () => {
    it('should get proper days ago compared to today', () => {
      const timestampToday = new Date().setHours(0, 0, 0, 0);
      const timestamp3DaysAgo = new Date().setHours(-72, 0, 0, 0);

      const expectedTimestamp = pipe['getTimestampDaysAgo'](2);

      expect(expectedTimestamp).toBeGreaterThan(timestamp3DaysAgo);
      expect(expectedTimestamp).toBeLessThan(timestampToday);
    });

    it('should get proper days ago compared to specific day', () => {
      const timestamp2DaysAgo = new Date().setHours(-48, 0, 0, 0);
      const timestamp3DaysAgo = new Date().setHours(-72, 0, 0, 0);
      const timestamp5DaysAgo = new Date().setHours(-120, 0, 0, 0);

      const expectedTimestamp = pipe['getTimestampDaysAgo'](2, timestamp2DaysAgo);

      expect(expectedTimestamp).toBeLessThan(timestamp3DaysAgo);
      expect(expectedTimestamp).toBeGreaterThan(timestamp5DaysAgo);
    });
  });
});
