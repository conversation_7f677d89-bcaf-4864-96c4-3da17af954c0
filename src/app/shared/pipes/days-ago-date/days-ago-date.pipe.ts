import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'daysAgoDate'
})
export class DaysAgoDatePipe implements PipeTransform {
  private monthsNames = [
    'januari',
    'februari',
    'maart',
    'april',
    'mei',
    'juni',
    'juli',
    'augustus',
    'september',
    'oktober',
    'november',
    'december'
  ];
  private daysNames = ['zondag', 'maandag', 'dinsdag', 'woensdag', 'donderdag', 'vrijdag', 'zaterdag'];

  transform(timestampAgo: number): any {
    const dateAgo = new Date(timestampAgo);

    const dateToday = new Date();
    const timestampToday = dateToday.setHours(0, 0, 0, 0);
    const timestampYesterday = this.getTimestampDaysAgo(1, timestampToday);
    const timestamp6DaysAgo = this.getTimestampDaysAgo(6, timestampToday);
    const timestampLastWeekMonday = this.getTimestampDaysAgo(13 - ((dateToday.getDay() ? 7 : 0) - dateToday.getDay()), timestampToday);
    const timestampFirstDayCurrentMonth = new Date(new Date().getFullYear(), new Date().getMonth(), 1).setHours(0, 0, 0, 0);
    const timestampFirstDayPreviousMonth = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1).setHours(0, 0, 0, 0);

    if (timestampAgo >= timestampToday) {
      return 'Vandaag';
    }

    if (timestampAgo >= timestampYesterday) {
      return 'Gisteren';
    }

    if (timestampAgo < timestampYesterday && timestampAgo >= timestamp6DaysAgo) {
      return `${this.daysNames[new Date(timestampAgo).getDay()]}`;
    }

    if (timestampAgo < timestamp6DaysAgo && timestampAgo >= timestampLastWeekMonday) {
      return 'Vorige week';
    }

    if (timestampAgo < timestampLastWeekMonday && timestampAgo >= timestampFirstDayCurrentMonth) {
      return 'Deze maand';
    }

    if (timestampAgo < timestampFirstDayCurrentMonth && timestampAgo >= timestampFirstDayPreviousMonth) {
      return 'Vorige maand';
    }

    return `${this.monthsNames[dateAgo.getMonth()]} ${dateAgo.getFullYear()}`;
  }

  private getTimestampDaysAgo(days: number, timestamp?: number): number {
    const date = timestamp ? new Date(timestamp) : new Date();
    date.setDate(date.getDate() - days);
    return date.getTime();
  }
}
