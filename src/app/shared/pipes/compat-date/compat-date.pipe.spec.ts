import { CompatDatePipe } from './compat-date.pipe';

describe('CompatDatePipe', () => {
  const pipe = new CompatDatePipe();

  it('returns properly formatted date, refactored when needed', () => {
    expect(pipe.transform('2011-1')).toBe('2011-01');
  });

  it('returns properly formatted date, not refactored when not needed', () => {
    expect(pipe.transform('2011-11')).toBe('2011-11');
  });

  it('returns nothing if input is empty', () => {
    expect(pipe.transform(null)).toBeNull();
  });
});
