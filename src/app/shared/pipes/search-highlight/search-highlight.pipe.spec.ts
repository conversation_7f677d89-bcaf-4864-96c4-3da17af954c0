import { SearchHighlightPipe } from './search-highlight.pipe';
import { ReplaySubject } from 'rxjs/index';

describe('SearchHighlightPipe', () => {
  let pipe: SearchHighlightPipe;
  let sanitizer: any = {};
  let store: any;

  const stringMedewerker = 'ik ben een medewerker';
  const stringShortWords = 'Foo bar b<h1>az</h1>';

  beforeEach(() => {
    store = new ReplaySubject();
    sanitizer = jasmine.createSpyObj('DomSanitizer', ['bypassSecurityTrustHtml']);
    sanitizer.bypassSecurityTrustHtml.and.callFake((trustedHtml: any) => trustedHtml);

    pipe = new SearchHighlightPipe(sanitizer, store);
  });

  describe('#constructor', () => {
    it('should not modify content if container with specified ID not found', () => {
      store.next({ search: { searchQuery: 'a' } });
      expect(pipe.transform('a')).toBe('a');
    });
  });

  describe('#transform', () => {
    it('should highlight content with provided search query', () => {
      store.next({ search: { searchQuery: { all: ['medewerker'] } } });

      expect(pipe.transform(stringMedewerker)).toBe('ik ben een <span class="highlight">medewerker</span>');
    });

    it('should not modify content if search query is not a string', () => {
      store.next({ search: { searchQuery: { all: [123] } } });

      expect(pipe.transform(stringMedewerker)).toBe('ik ben een medewerker');
    });

    it('should highlight content with provided search query for specified field', () => {
      store.next({ search: { searchQuery: { courses: ['foo'] } } });

      expect(pipe.transform(stringShortWords)).toBe(`<span class="highlight">Foo</span> bar b<h1>az</h1>`);
    });

    it('should highlight content with provided multiple words search query', () => {
      store.next({ search: { searchQuery: { all: ['foo bAr'] } } });

      expect(pipe.transform(stringShortWords)).toBe(`<span class="highlight">Foo</span> <span class="highlight">bar</span> b<h1>az</h1>`);
    });

    it('should ignore non-word and non-space characters in search query', () => {
      store.next({ search: { searchQuery: { all: ['(**bar**)'] } } });

      expect(pipe.transform(stringShortWords)).toBe(`Foo <span class="highlight">bar</span> b<h1>az</h1>`);
    });

    it('should ignore EN in search query', () => {
      store.next({ search: { searchQuery: { all: ['foo en EN'] } } });

      expect(pipe.transform('Foo en BAZ EN')).toBe(`<span class="highlight">Foo</span> en BAZ EN`);
    });

    it('should ignore OF in search query', () => {
      store.next({ search: { searchQuery: { all: ['foo en OF'] } } });

      expect(pipe.transform('Foo en BAZ OF')).toBe(`<span class="highlight">Foo</span> en BAZ OF`);
    });

    it('should ignore XOR in search query', () => {
      store.next({ search: { searchQuery: { all: ['foo en XOR'] } } });

      expect(pipe.transform('Foo en BAZ XOR')).toBe(`<span class="highlight">Foo</span> en BAZ XOR`);
    });

    it('should ignore AND in search query', () => {
      store.next({ search: { searchQuery: { all: ['foo en AND'] } } });

      expect(pipe.transform('Foo en BAZ AND')).toBe(`<span class="highlight">Foo</span> en BAZ AND`);
    });

    it('should ignore OR in search query', () => {
      store.next({ search: { searchQuery: { all: ['foo en OR'] } } });

      expect(pipe.transform('Foo en BAZ OR')).toBe(`<span class="highlight">Foo</span> en BAZ OR`);
    });

    it('should ignore NIET in search query', () => {
      store.next({ search: { searchQuery: { all: ['foo en NIET'] } } });

      expect(pipe.transform('Foo en BAZ NIET')).toBe(`<span class="highlight">Foo</span> en BAZ NIET`);
    });

    it('should ignore NOT in search query', () => {
      store.next({ search: { searchQuery: { all: ['foo en NOT'] } } });

      expect(pipe.transform('Foo en BAZ NOT')).toBe(`<span class="highlight">Foo</span> en BAZ NOT`);
    });

    it('should not ignore the words that contain special words', () => {
      store.next({ search: { searchQuery: { all: ['management or administrator'] } } });

      expect(pipe.transform('management foo administrator bar')).toBe(
        `<span class="highlight">management</span> foo <span class="highlight">administrator</span> bar`
      );
    });

    it('should not highlight text nor throw an exception if search query consist of only special words', () => {
      store.next({ search: { searchQuery: { all: ['and or'] } } });

      expect(stringMedewerker).toBe(stringMedewerker);
    });

    it('should highlight multiple words query string', () => {
      store.next({
        search: {
          searchQuery: { all: [`foo "h & m" or '1 + 1' xor "(body)'`] }
        }
      });

      expect(pipe.transform(`Foo h or h & m xor (body) m 1 + 1 ' 1   + 1`)).toBe(
        `<span class="highlight">Foo</span> h or <span class="highlight">h & m</span> xor ` +
          `(<span class="highlight">body</span>) m <span class="highlight">1 + 1</span> ' 1   + 1`
      );
    });

    it('should not fail when empty string between quotes provided in the query', () => {
      store.next({
        search: { searchQuery: { all: [`''WTB'' OF ''Werktuigbouwkunde''`] } }
      });

      expect(pipe.transform('Foo wTb of Werktuigbouwkunde')).toBe(
        `Foo <span class="highlight">wTb</span> of <span class="highlight">Werktuigbouwkunde</span>`
      );
    });

    it('should return an empty string if element null', () => {
      store.next({ search: { searchQuery: { all: ['medewerker'] } } });

      expect(pipe.transform('')).toBe('');
    });

    it('should return an empty string if element is undefined', () => {
      store.next({ search: { searchQuery: { all: ['medewerker'] } } });

      expect(pipe.transform(undefined)).toBe('');
    });

    it('should replace words only within html nodes, but not inside the tags', () => {
      const weirdHtmlString =
        `I'm mister Span <span attribute="span">ecco uno span</span>` + `<img src="data:image/png;base64,43SPANrfq345spanasgf"/>`;
      const expectedHtmlString =
        `I'm mister <span class="highlight">Span</span> <span attribute="span">ecco uno <span class="highlight">span</span></span>` +
        `<img src="data:image/png;base64,43SPANrfq345spanasgf"/>`;

      store.next({ search: { searchQuery: { all: ['span'] } } });

      expect(pipe.transform(weirdHtmlString)).toBe(expectedHtmlString);
    });
  });

  describe('#buildHighlightingRegex', () => {
    it('should return empty string if input is less than 2 characters', () => {
      const stringToHighlight = pipe['buildHighlightingRegex']('a');
      expect(stringToHighlight).toEqual('');
    });

    it('should return empty string if input is less than 2 characters', () => {
      const stringToHighlight = pipe['buildHighlightingRegex']('foo bar');
      expect(stringToHighlight).toEqual('foo|bar');
    });

    it('should manage quotes matching with double quotes', () => {
      const stringToHighlight = pipe['buildHighlightingRegex']('"foo bar" zoo');
      expect(stringToHighlight).toEqual('zoo|foo bar');
    });

    it('should manage quotes matching with single quotes', () => {
      const stringToHighlight = pipe['buildHighlightingRegex'](`'foo bar' zoo`);
      expect(stringToHighlight).toEqual('zoo|foo bar');
    });
  });
});
