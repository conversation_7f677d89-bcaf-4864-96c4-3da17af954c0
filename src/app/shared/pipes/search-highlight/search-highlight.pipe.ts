import { Pipe, PipeTransform } from '@angular/core';
import { Store } from '@ngrx/store';
import { AppState } from '../../../types/app-state';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Pipe({
  name: 'searchHighlight'
})
export class SearchHighlightPipe implements PipeTransform {
  private ignoreWords = ['en', 'of', 'xor', 'and', 'or', 'niet', 'not'];
  private stringWithinQuotesRegex = /(["'])(?:(?=(\\?))\2.)*?\1/g;
  private stringHighlightRegex = '';

  constructor(private sanitizer: DomSanitizer, private store: Store<AppState>) {
    this.store.subscribe(state => {
      const searchQueryKeys = Object.keys(state.search.searchQuery);
      this.stringHighlightRegex = this.buildHighlightingRegex(<string>state.search.searchQuery[searchQueryKeys[0]][0]);
    });
  }

  transform(elementValue: string): SafeHtml {
    if (elementValue === '' || elementValue === null || elementValue === undefined) {
      return '';
    } else if (this.stringHighlightRegex.length === 0) {
      return this.sanitizer.bypassSecurityTrustHtml(elementValue);
    }

    const searchWords = this.stringHighlightRegex.split('|');
    let highlightedElementValue = elementValue;
    searchWords.forEach(searchWord => {
      highlightedElementValue = highlightedElementValue.replace(
        new RegExp(`(${searchWord})(?![^<]*>)`, 'gi'),
        '<span class="highlight">$1</span>'
      );
    });

    return this.sanitizer.bypassSecurityTrustHtml(highlightedElementValue);
  }

  private buildHighlightingRegex(searchQuery: string): string {
    if (typeof(searchQuery) !== 'string' || (searchQuery.length < 2)) {
      return '';
    }

    let multipleWordsQueries = [];

    let multipleWordsMatch = this.stringWithinQuotesRegex.exec(searchQuery);

    while (multipleWordsMatch !== null) {
      multipleWordsQueries.push(multipleWordsMatch[0].replace(/'|"/g, ''));
      multipleWordsMatch = this.stringWithinQuotesRegex.exec(searchQuery);
    }

    multipleWordsQueries = multipleWordsQueries
      .map(value => {
        searchQuery = searchQuery.replace(value, '');
        return value.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g, '\\$&');
      })
      .filter(value => value.length > 0);

    const highlightPattern = searchQuery.replace(/[^\w\s]/g, '');

    return highlightPattern
      .split(' ')
      .filter(token => {
        return token.length > 1 && this.ignoreWords.indexOf(token.toLowerCase()) === -1;
      })
      .filter((v, i, a) => a.indexOf(v) === i)
      .concat(multipleWordsQueries)
      .join('|');
  }
}
