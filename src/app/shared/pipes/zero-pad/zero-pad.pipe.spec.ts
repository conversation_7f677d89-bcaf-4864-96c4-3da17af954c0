import { ZeroPadPipe } from './zero-pad.pipe';

describe('ZeroPadPipe', () => {
  let pipe: ZeroPadPipe;
  beforeEach(() => {
    pipe = new ZeroPadPipe();
  });

  it('should return a string with a length of 7 numbers', () => {
    expect(pipe.transform(1).length).toBe(7);
  });

  it('should return a string left padded with zeros', () => {
    expect(pipe.transform(1)).toBe('0000001');
  });

  it('should pad no zeroes if number is 7 characters long', () => {
    expect(pipe.transform(1111111)).toBe('1111111');
  });

  it('should override default pading when provided', () => {
    expect(pipe.transform(1, 4)).toBe('0001');
  });
});
