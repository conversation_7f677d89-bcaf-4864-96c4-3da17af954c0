import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { Store } from '@ngrx/store';
import { AppState } from '../../../types/app-state';
import * as SearchActions from '../../../store/actions/search/search.actions';
import { LegacyUrlParser, UrlSearchService } from '../../services';

@Injectable()
export class LegacySearchUrlGuard implements CanActivate {
  /* tslint:disable:no-unused-variable */
  constructor(
    private legacyUrlParser: LegacyUrlParser,
    private store: Store<AppState>,
    // needs to be initialized while resolving as it listens to store changes
    private urlSearchService: UrlSearchService
  ) {}
  /* tslint:enable */

  public canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    const url = route.queryParams['url'];

    return this.legacyUrlParser.searchUrl(url).pipe(
      map((urlData: any) => {
        this.store.dispatch(new SearchActions.UpdateFromLegacySearchUrl(urlData));

        return false;
      })
    );
  }
}
