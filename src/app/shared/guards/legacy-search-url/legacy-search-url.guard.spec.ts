import { LegacySearchUrlGuard } from './legacy-search-url.guard';
import { of } from 'rxjs';
import * as SearchActions from '../../../store/actions/search/search.actions';

describe('LegacySearchUrlGuard', () => {
  const stub: any = {};
  let service: LegacySearchUrlGuard;

  beforeEach(() => {
    stub.LegacyUrlParser = jasmine.createSpyObj('LegacyUrlParser', ['searchUrl']);
    stub.LegacyUrlParser.searchUrl.and.returnValue(of({}));
    stub.Store = jasmine.createSpyObj('Store', ['dispatch']);
    stub.RouterStateSnapshot = {};
    stub.ActivatedRouteSnapshot = { queryParams: { url: '' } };
    stub.UrlSearchService = {};

    service = new LegacySearchUrlGuard(<any>stub.LegacyUrlParser, <any>stub.Store, <any>stub.UrlSearchService);
  });

  describe('canActivate', () => {
    it('should dispatch an action', () => {
      const canActivate = service.canActivate(<any>stub.ActivatedRouteSnapshot, <any>stub.RouterStateSnapshot);
      canActivate.subscribe(() => {
        expect(stub.Store.dispatch).toHaveBeenCalledWith(new SearchActions.UpdateFromLegacySearchUrl({}));
      });
    });

    it('should canActivate to false', () => {
      const canActivate = service.canActivate(<any>stub.ActivatedRouteSnapshot, <any>stub.RouterStateSnapshot);
      canActivate.subscribe(value => {
        expect(value).toBe(false);
      });
    });
  });
});
