import { of } from 'rxjs';
import { LegacyJobSeekerUrlGuard } from './legacy-jobseeker-url.guard';

describe('LegacyJobSeekerUrlGuard', () => {
  const stub: any = {};
  let service: LegacyJobSeekerUrlGuard;

  beforeEach(() => {
    stub.LegacyUrlParser = jasmine.createSpyObj('LegacyUrlParser', ['jobSeekerUrl']);
    stub.LegacyUrlParser.jobSeekerUrl.and.returnValue(of({ id: '123' }));

    stub.Router = jasmine.createSpyObj('Router', ['navigate']);
    stub.ActivatedRouteSnapshot = { queryParams: { url: 'http://www.test.com' } };

    service = new LegacyJobSeekerUrlGuard(<any>stub.LegacyUrlParser, <any>stub.Router);
  });

  describe('canActivate', () => {
    it('should redirect while resolving', done => {
      const canActivate = service.canActivate(<any>stub.ActivatedRouteSnapshot);
      canActivate.subscribe(value => {
        expect(stub.Router.navigate).toHaveBeenCalledWith(['/kandidaat', '123']);
        expect(value).toBe(false);
        done();
      });

      expect(stub.LegacyUrlParser.jobSeekerUrl).toHaveBeenCalledTimes(1);
      expect(stub.LegacyUrlParser.jobSeekerUrl).toHaveBeenCalledWith(stub.ActivatedRouteSnapshot.queryParams.url);
    });

    it('should proceed to error page when no jobseeker id found', done => {
      stub.LegacyUrlParser.jobSeekerUrl.and.returnValue(of({}));
      const canActivate = service.canActivate(<any>stub.ActivatedRouteSnapshot);
      canActivate.subscribe(value => {
        expect(stub.Router.navigate).not.toHaveBeenCalled();
        expect(value).toBe(true);
        done();
      });

      expect(stub.LegacyUrlParser.jobSeekerUrl).toHaveBeenCalledTimes(1);
      expect(stub.LegacyUrlParser.jobSeekerUrl).toHaveBeenCalledWith(stub.ActivatedRouteSnapshot.queryParams.url);
    });
  });
});
