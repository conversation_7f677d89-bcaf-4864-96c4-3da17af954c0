import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { LegacyUrlParser } from '../../services';

@Injectable()
export class LegacyJobSeekerUrlGuard implements CanActivate {
  constructor(private legacyUrlParser: LegacyUrlParser, private router: Router) {}

  public canActivate(route: ActivatedRouteSnapshot): Observable<any> {
    const url = route.queryParams['url'];

    return this.legacyUrlParser.jobSeekerUrl(url).pipe(
      map((urlData: any) => {
        const jobSeekerId = urlData['id'];
        if (jobSeekerId) {
          this.router.navigate(['/kandidaat', jobSeekerId]);
          return false;
        } else {
          return true;
        }
      })
    );
  }
}
