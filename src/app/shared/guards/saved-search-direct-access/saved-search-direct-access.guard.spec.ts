import { of } from 'rxjs';
import { SavedSearchDirectAccessGuard } from './saved-search-direct-access.guard';

describe('SavedSearchDirectAccessGuard', () => {
  let stub: any = {};
  let service: SavedSearchDirectAccessGuard;

  beforeEach(() => {
    stub = {
      SavedSearchService: jasmine.createSpyObj('SavedSearchService', ['getOne', 'getParamsFromSearch']),
      Router: jasmine.createSpyObj('Router', ['navigate']),
      ActivatedRouteSnapshot: {
        params: {
          recruiterId: 'jojo-boy-recruiter-id',
          savedSearchId: 'jojo-boy-saved-search-id'
        }
      },
      search: {
        searchParameters: {
          location: 'Amsterdam',
          workLevels: ['HBO']
        }
      },
      navigationExtras: {
        filter: 'test'
      }
    };
    stub.SavedSearchService.getOne.and.returnValue(of(stub.search));
    stub.SavedSearchService.getParamsFromSearch.and.returnValue(stub.navigationExtras);

    service = new SavedSearchDirectAccessGuard(<any>stub.Router, <any>stub.SavedSearchService);
  });

  describe('canActivate', () => {
    it('should redirect while resolving', done => {
      const canActivate = service.canActivate(<any>stub.ActivatedRouteSnapshot);
      canActivate.subscribe(value => {
        expect(stub.Router.navigate).toHaveBeenCalledWith(['/kandidaten/zoeken'], stub.navigationExtras);
        expect(value).toBe(false);
        done();
      });

      expect(stub.SavedSearchService.getOne).toHaveBeenCalledTimes(1);
      expect(stub.SavedSearchService.getOne).toHaveBeenCalledWith(
        stub.ActivatedRouteSnapshot.params.recruiterId,
        stub.ActivatedRouteSnapshot.params.savedSearchId
      );

      expect(stub.SavedSearchService.getParamsFromSearch).toHaveBeenCalledTimes(1);
      expect(stub.SavedSearchService.getParamsFromSearch).toHaveBeenCalledWith(stub.search.searchParameters);
    });

    it('should proceed to error page when no jobseeker id found', done => {
      stub.SavedSearchService.getOne.and.returnValue(of(null));
      const canActivate = service.canActivate(<any>stub.ActivatedRouteSnapshot);
      canActivate.subscribe(value => {
        expect(stub.Router.navigate).not.toHaveBeenCalled();
        expect(value).toBe(true);
        done();
      });

      expect(stub.SavedSearchService.getOne).toHaveBeenCalledTimes(1);
      expect(stub.SavedSearchService.getOne).toHaveBeenCalledWith(
        stub.ActivatedRouteSnapshot.params.recruiterId,
        stub.ActivatedRouteSnapshot.params.savedSearchId
      );
    });
  });
});
