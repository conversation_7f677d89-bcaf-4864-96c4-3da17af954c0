import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { SavedSearchService } from '../../services';

@Injectable()
export class SavedSearchDirectAccessGuard implements CanActivate {
  constructor(private router: Router, public savedSearchService: SavedSearchService) {}

  public canActivate(route: ActivatedRouteSnapshot): Observable<boolean> {
    const recruiterId = route.params['recruiterId'];
    const savedSearchId = route.params['savedSearchId'];
    return this.savedSearchService.getOne(recruiterId, savedSearchId).pipe(
      map(search => {
        if (search) {
          const navigationExtras = this.savedSearchService.getParamsFromSearch(search.searchParameters);
          this.router.navigate(['/kandidaten/zoeken'], navigationExtras);
          return false;
        } else {
          return true;
        }
      })
    );
  }
}
