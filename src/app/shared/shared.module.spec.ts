import { SharedModule } from './shared.module';

describe('SharedModule', () => {
  let sharedModule: SharedModule;

  beforeEach(() => {
    sharedModule = new SharedModule();
  });

  it('should create an instance', () => {
    expect(sharedModule).toBeTruthy();
  });

  it('should have forRoot method', () => {
    const output = SharedModule.forRoot();
    expect(output.ngModule).toBe(SharedModule);
  });
});
