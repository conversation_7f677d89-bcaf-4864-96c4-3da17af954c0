import { JobSeekerDetailResolver } from './jobseeker.resolver';
import { of, throwError, Subject } from 'rxjs';
import * as SpinnerActions from '../../../store/actions/spinner/spinner.actions';
import { NavigationEnd } from '@angular/router';
import { RecruiterLimitationsType } from '../../services';

describe('JobSeekerDetailResolver', () => {
  let stub: any = {};
  let service: JobSeekerDetailResolver;
  const initialPreviousUrl = 'previous';
  const initialCurrentUrl = 'current';

  beforeEach(() => {
    stub = {
      Store: jasmine.createSpyObj('Store', ['dispatch']),
      ProfileAPIService: jasmine.createSpyObj('ProfileAPIService', ['getJobSeeker', 'getJobSeekerLimited']),
      RouterStateSnapshot: {},
      ActivatedRouteSnapshot: { params: { id: '123' } },
      JobSeekerService: jasmine.createSpyObj('JobSeekerService', ['fromJson']),
      router: {
        url: 'currenturl',
        events: of(<any>{}),
        navigate: jasmine.createSpy('navigate')
      },
      RecruiterService: jasmine.createSpyObj('RecruiterService', ['getRecruiter', 'setRecruiterLimitationsType']),
      recruiter: {
        isLogged: true
      },
      recruiterResponse: new Subject()
    };

    stub.JobSeekerService.fromJson.and.returnValue({ firstName: 'jobseeker' });
    stub.ProfileAPIService.getJobSeeker.and.returnValue(of({ firstName: 'jobseeker' }));
    stub.ProfileAPIService.getJobSeekerLimited.and.returnValue(of({ availability: 'test' }));
    stub.RecruiterService.getRecruiter.and.returnValue(stub.recruiterResponse);

    service = new JobSeekerDetailResolver(stub.ProfileAPIService, stub.Store, stub.router, stub.JobSeekerService, stub.RecruiterService);
  });

  describe('#ngOnDestroy', () => {
    it('should unsubscribe to the service promises', () => {
      service['routerSubscription'] = jasmine.createSpyObj('routerSubscription', ['unsubscribe']);

      service.ngOnDestroy();

      expect(service['routerSubscription'].unsubscribe).toHaveBeenCalledWith();
      expect(service['routerSubscription'].unsubscribe).toHaveBeenCalledTimes(1);
    });
  });

  describe('#setNavigationUrls', () => {
    beforeEach(() => {
      service['previousUrl'] = initialPreviousUrl;
      service['currentUrl'] = initialCurrentUrl;
    });

    it('should do nothing if provided not a NavigationEnd event', () => {
      service['setNavigationUrls']('nothing relevant');

      expect(service['previousUrl']).toEqual(initialPreviousUrl);
      expect(service['currentUrl']).toEqual(initialCurrentUrl);
    });

    it('should update navigation urls if provided a NavigationEnd event', () => {
      const navigationEndEvent = new NavigationEnd(null, 'newCurrentUrl', null);

      service['setNavigationUrls'](navigationEndEvent);

      expect(service['previousUrl']).toEqual(initialCurrentUrl);
      expect(service['currentUrl']).toEqual(navigationEndEvent.url);
    });
  });

  describe('#resolve', () => {
    it('should get recruiter', () => {
      service.resolve(<any>stub.ActivatedRouteSnapshot, <any>stub.RouterStateSnapshot);

      expect(stub.RecruiterService.getRecruiter).toHaveBeenCalledTimes(1);
      expect(stub.RecruiterService.getRecruiter).toHaveBeenCalledWith();
    });

    it('should store jobseeker id', () => {
      service.resolve(<any>stub.ActivatedRouteSnapshot, <any>stub.RouterStateSnapshot);

      expect(service['jobSeekerId']).toEqual(stub.ActivatedRouteSnapshot.params.id);
    });

    it('should call jobseeker function to fetch jobseeker', done => {
      service['getJobSeeker'] = jasmine.createSpy('getJobSeeker').and.returnValue(of({}));

      const resolve = service.resolve(<any>stub.ActivatedRouteSnapshot, <any>stub.RouterStateSnapshot);

      resolve.subscribe(() => {
        expect(stub.RecruiterService.setRecruiterLimitationsType).toHaveBeenCalledTimes(1);
        expect(stub.RecruiterService.setRecruiterLimitationsType).toHaveBeenCalledWith(RecruiterLimitationsType.Unlimited);
        expect(service['getJobSeeker']).toHaveBeenCalledTimes(1);
        expect(service['getJobSeeker']).toHaveBeenCalledWith(stub.ActivatedRouteSnapshot.params['id'], 'getJobSeeker');
        done();
      });

      stub.recruiterResponse.next(stub.recruiter);
    });

    it('should call jobseeker function to fetch limited jobseeker', done => {
      service['getJobSeeker'] = jasmine.createSpy('getJobSeeker').and.returnValue(of({}));

      const resolve = service.resolve(<any>stub.ActivatedRouteSnapshot, <any>stub.RouterStateSnapshot);

      resolve.subscribe(() => {
        expect(stub.RecruiterService.setRecruiterLimitationsType).toHaveBeenCalledTimes(1);
        expect(stub.RecruiterService.setRecruiterLimitationsType).toHaveBeenCalledWith(RecruiterLimitationsType.NoProfile);
        expect(service['getJobSeeker']).toHaveBeenCalledTimes(1);
        expect(service['getJobSeeker']).toHaveBeenCalledWith(stub.ActivatedRouteSnapshot.params['id'], 'getJobSeekerLimited');
        done();
      });

      stub.recruiterResponse.next(null);
    });

    it('should call jobseeker function to fetch limited jobseeker on error', done => {
      service['getJobSeeker'] = jasmine.createSpy('getJobSeeker').and.returnValue(of({}));

      const resolve = service.resolve(<any>stub.ActivatedRouteSnapshot, <any>stub.RouterStateSnapshot);

      resolve.subscribe(() => {
        expect(stub.RecruiterService.setRecruiterLimitationsType).toHaveBeenCalledTimes(1);
        expect(stub.RecruiterService.setRecruiterLimitationsType).toHaveBeenCalledWith(RecruiterLimitationsType.NoProfile);
        expect(service['getJobSeeker']).toHaveBeenCalledTimes(1);
        expect(service['getJobSeeker']).toHaveBeenCalledWith(stub.ActivatedRouteSnapshot.params['id'], 'getJobSeekerLimited');
        done();
      });

      stub.recruiterResponse.error({ code: 404 });
    });
  });

  describe('#getJobSeeker', () => {
    it('should always dispatch an action to show the main spinner', () => {
      service['getJobSeeker']('id', 'getJobSeeker');

      expect(stub.Store.dispatch).toHaveBeenCalledTimes(1);
      expect(stub.Store.dispatch).toHaveBeenCalledWith(new SpinnerActions.ShowSpinner());
    });

    it('should always dispatch an action to hide the main spinner', () => {
      const result = service['getJobSeeker']('id', 'getJobSeeker');

      result.subscribe(() => {});

      expect(stub.Store.dispatch).toHaveBeenCalledWith(new SpinnerActions.HideSpinner());
    });

    it('should call error handler when there are errors', () => {
      service['handleError'] = jasmine.createSpy('handleError').and.returnValue(of({}));
      const error = { code: 404 };
      stub.ProfileAPIService.getJobSeeker.and.returnValue(throwError(error));
      const result = service['getJobSeeker']('id', 'getJobSeeker');

      result.subscribe(() => {});

      expect(service['handleError']).toHaveBeenCalledTimes(1);
      expect(service['handleError']).toHaveBeenCalledWith(error);
    });

    it('should get jobseeker from jobseeker service', done => {
      const result = service['getJobSeeker'](stub.ActivatedRouteSnapshot.params.id, 'getJobSeeker');

      expect(stub.ProfileAPIService.getJobSeeker).toHaveBeenCalledWith(stub.ActivatedRouteSnapshot.params.id);
      result.subscribe(value => {
        expect(value).toEqual(<any>{ firstName: 'jobseeker' });
        done();
      });
    });
  });

  describe('#handleError', () => {
    it('should hide spinner', () => {
      service['previousUrl'] = '/test';

      service['handleError']({
        code: 403
      });

      expect(stub.router.navigate).not.toHaveBeenCalled();
      expect(stub.Store.dispatch).toHaveBeenCalledWith(new SpinnerActions.HideSpinner());
    });

    it('should get limited jobseeker profile', () => {
      const jobseekerId = '1qa-2ws-3ed-4rf';
      service['previousUrl'] = '/test';
      service['jobSeekerId'] = jobseekerId;
      service['getJobSeeker'] = jasmine.createSpy('getJobSeeker').and.returnValue(of({}));

      service['handleError']({
        code: 403
      });

      expect(stub.router.navigate).not.toHaveBeenCalled();
      expect(service['getJobSeeker']).toHaveBeenCalledTimes(1);
      expect(service['getJobSeeker']).toHaveBeenCalledWith(jobseekerId, 'getJobSeekerLimited');
    });

    it('should set limited profile due to unverified company', () => {
      const jobseekerId = '1qa-2ws-3ed-4rf';
      service['previousUrl'] = '/test';
      service['jobSeekerId'] = jobseekerId;
      service['getJobSeeker'] = jasmine.createSpy('getJobSeeker').and.returnValue(of({}));

      service['handleError']({
        code: 403,
        message: RecruiterLimitationsType.UnverifiedCompany
      });

      expect(stub.RecruiterService.setRecruiterLimitationsType).toHaveBeenCalledTimes(1);
      expect(stub.RecruiterService.setRecruiterLimitationsType).toHaveBeenCalledWith(RecruiterLimitationsType.UnverifiedCompany);
    });

    it('should set limited profile due to invalid subscription', () => {
      const jobseekerId = '1qa-2ws-3ed-4rf';
      service['previousUrl'] = '/test';
      service['jobSeekerId'] = jobseekerId;
      service['getJobSeeker'] = jasmine.createSpy('getJobSeeker').and.returnValue(of({}));

      service['handleError']({
        code: 403,
        message: RecruiterLimitationsType.InvalidSubscription
      });

      expect(stub.RecruiterService.setRecruiterLimitationsType).toHaveBeenCalledTimes(1);
      expect(stub.RecruiterService.setRecruiterLimitationsType).toHaveBeenCalledWith(RecruiterLimitationsType.InvalidSubscription);
    });

    it('should set limited profile due to daily limitation', () => {
      const jobseekerId = '1qa-2ws-3ed-4rf';
      service['previousUrl'] = '/test';
      service['jobSeekerId'] = jobseekerId;
      service['getJobSeeker'] = jasmine.createSpy('getJobSeeker').and.returnValue(of({}));

      service['handleError']({
        code: 403
      });

      expect(stub.RecruiterService.setRecruiterLimitationsType).toHaveBeenCalledTimes(1);
      expect(stub.RecruiterService.setRecruiterLimitationsType).toHaveBeenCalledWith(RecruiterLimitationsType.DailyLimit);
    });

    it('should not redirect on other errors', () => {
      service['handleError']({
        code: 401
      });

      expect(stub.router.navigate).not.toHaveBeenCalled();
    });

    it('should return an observable with 404 when job seeker is not found', () => {
      service['handleError']({
        code: 404
      });

      expect(stub.router.navigate).toHaveBeenCalledTimes(1);
      expect(stub.router.navigate).toHaveBeenCalledWith(['/error'], {
        queryParams: { type: 'jobSeekerNotFound' },
        skipLocationChange: true
      });
    });

    it('should return an observable with 5** when an internal error has occurred', () => {
      service['handleError']({
        code: 500
      });

      expect(stub.router.navigate).toHaveBeenCalledWith(['/error'], {
        queryParams: { type: 'internalServerError' },
        skipLocationChange: true
      });
    });
  });
});
