import { Injectable, OnDestroy } from '@angular/core';
import { Observable, Subscription, EMPTY } from 'rxjs';
import { finalize, catchError, map, flatMap } from 'rxjs/operators';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot, Router, NavigationEnd } from '@angular/router';
import { Store } from '@ngrx/store';
import { AppState } from '../../../types/app-state';
import { JobSeeker } from '../../../classes';
import { JobSeekerService, RecruiterService, RecruiterLimitationsType } from '../../services';
import { ProfileAPIService } from '../../api';
import * as SpinnerActions from '../../../store/actions/spinner/spinner.actions';

@Injectable()
export class JobSeekerDetailResolver implements Resolve<JobSeeker>, OnDestroy {
  private previousUrl: string;
  private currentUrl: string;
  private routerSubscription: Subscription;
  private jobSeekerId: string;

  constructor(
    private profileAPIService: ProfileAPIService,
    private store: Store<AppState>,
    private router: Router,
    private jobSeekerService: JobSeekerService,
    private recruiterService: RecruiterService
  ) {
    this.currentUrl = this.router.url;
    this.setNavigationUrls = this.setNavigationUrls.bind(this);

    this.routerSubscription = router.events.subscribe(event => this.setNavigationUrls(event));
  }

  ngOnDestroy(): void {
    this.routerSubscription.unsubscribe();
  }

  private setNavigationUrls(event: any): void {
    if (event instanceof NavigationEnd) {
      this.previousUrl = this.currentUrl;
      this.currentUrl = event.url;
    }
  }

  public resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<JobSeeker> {
    this.jobSeekerId = route.params['id'];

    return this.recruiterService.getRecruiter().pipe(
      flatMap((recruiter: any) => {
        if (recruiter === null) {
          this.recruiterService.setRecruiterLimitationsType(RecruiterLimitationsType.NoProfile);
          return this.getJobSeeker(this.jobSeekerId, 'getJobSeekerLimited');
        }

        this.recruiterService.setRecruiterLimitationsType(RecruiterLimitationsType.Unlimited);
        return this.getJobSeeker(this.jobSeekerId, 'getJobSeeker');
      }),
      catchError(() => {
        this.recruiterService.setRecruiterLimitationsType(RecruiterLimitationsType.NoProfile);
        return this.getJobSeeker(this.jobSeekerId, 'getJobSeekerLimited');
      })
    );
  }

  private getJobSeeker(jobSeekerId: string, method: string): Observable<JobSeeker> {
    this.store.dispatch(new SpinnerActions.ShowSpinner());

    return this.profileAPIService[method](jobSeekerId).pipe(
      map(this.jobSeekerService.fromJson),
      catchError(error => this.handleError(error)),
      finalize(() => {
        this.store.dispatch(new SpinnerActions.HideSpinner());
      })
    );
  }

  private handleError(error: any) {
    if (error.code === 404) {
      this.router.navigate(['/error'], { queryParams: { type: 'jobSeekerNotFound' }, skipLocationChange: true });
    }

    if (error.code >= 500 && error.code < 600) {
      this.router.navigate(['/error'], { queryParams: { type: 'internalServerError' }, skipLocationChange: true });
    }

    if (error.code === 403) {
      this.store.dispatch(new SpinnerActions.HideSpinner());

      let recruiterLimitations = '';
      switch (error.message) {
        case RecruiterLimitationsType.UnverifiedCompany:
        case RecruiterLimitationsType.InvalidSubscription:
          recruiterLimitations = error.message;
          break;

        default:
          recruiterLimitations = RecruiterLimitationsType.DailyLimit;
          break;
      }
      this.recruiterService.setRecruiterLimitationsType(recruiterLimitations);

      return this.getJobSeeker(this.jobSeekerId, 'getJobSeekerLimited');
    }

    return EMPTY;
  }
}
