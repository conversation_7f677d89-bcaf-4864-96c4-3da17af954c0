import {Subject} from 'rxjs';
import {RecruiterResolver} from './recruiter.resolver';

describe('RecruiterResolver', () => {
  const stub: any = {};
  let service: RecruiterResolver;
  let response: Subject<any>;

  beforeEach(() => {
    response = new Subject();
    stub.recruiter = { firstName: 'test' };
    stub.RecruiterService = jasmine.createSpyObj('RecruiterService', ['getRecruiter']);
    stub.RecruiterService.getRecruiter.and.returnValue(response);
    stub.ActivatedRouteSnapshot = { queryParams: {} };
    stub.RouterStateSnapshot = {};

    service = new RecruiterResolver(stub.RecruiterService);
  });

  describe('#resolve', () => {
    it('should return recruiter', done => {
      service.resolve(stub.ActivatedRouteSnapshot, stub.RouterStateSnapshot).subscribe(result => {
        expect(result).toEqual(stub.recruiter);
        done();
      });

      response.next(stub.recruiter);

      expect(stub.RecruiterService.getRecruiter).toHaveBeenCalledTimes(1);
      expect(stub.RecruiterService.getRecruiter).toHaveBeenCalledWith(undefined);
    });

    it('should return empty observable when the call fails', done => {
      service.resolve(stub.ActivatedRouteSnapshot, stub.RouterStateSnapshot).subscribe(result => {
        expect(result).toEqual(null);
        done();
      });

      response.error({ code: 404 });

      expect(stub.RecruiterService.getRecruiter).toHaveBeenCalledTimes(1);
      expect(stub.RecruiterService.getRecruiter).toHaveBeenCalledWith(undefined);
    });

    it('should get the recruiter with code', done => {
      const code = 'here-is-the-code-123';
      const routeSnapshot: any = { queryParams: {code: code} };

      service.resolve(routeSnapshot, stub.RouterStateSnapshot).subscribe(result => {
        done();
      });

      response.next(stub.recruiter);

      expect(stub.RecruiterService.getRecruiter).toHaveBeenCalledTimes(1);
      expect(stub.RecruiterService.getRecruiter).toHaveBeenCalledWith(code);
    });

  });
});
