import {Injectable, Optional} from '@angular/core';
import {ActivatedRouteSnapshot, Resolve, RouterStateSnapshot} from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { RecruiterService } from '../../services';
import { Recruiter } from '../../../classes';

@Injectable()
export class RecruiterResolver implements Resolve<Recruiter> {
  constructor(private recruiterService: RecruiterService) {}

  public resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<Recruiter | null> {

    return this.recruiterService.getRecruiter(route.queryParams['code']).pipe(
      catchError(() => {
        return of(null);
      })
    );
  }
}
