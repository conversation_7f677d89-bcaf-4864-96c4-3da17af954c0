import { ModuleWithProviders, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { HttpClientModule } from '@angular/common/http';
import { ProfileAPIService } from './api';
import {
  AnalyticsService,
  DateFormatService,
  DomService,
  FilterEffectsService,
  JobSeekerService,
  HttpService,
  JobTitlesService,
  LanguagesService,
  LegacyUrlParser,
  LocationsService,
  NumberFormatService,
  PaginationService,
  PlatformCoreService,
  ProfileNavigationService,
  RecruiterService,
  RestoreScrollPositionService,
  SavedSearchService,
  SavedFavoritesService,
  SearchService,
  SessionProfileViewTrackerService,
  SessionProfileFavoriteTrackerService,
  UrlSearchService,
  WindowService
} from './services';
import { JobSeekerDetailResolver, RecruiterResolver } from './resolvers';
import { CompatDatePipe, MonthPipe, SearchHighlightPipe, DaysAgoDatePipe } from './pipes';
import { LegacySearchUrlGuard, LegacyJobSeekerUrlGuard, SavedSearchDirectAccessGuard } from './guards';
import {
  AutoCompleteComponent,
  SearchContainerComponent,
  SearchBarComponent,
  ReadMoreComponent,
  SaveSearchModalComponent,
  ShareProfileModalComponent,
  SearchTipsModalComponent
} from './components';

@NgModule({
  imports: [CommonModule, FormsModule, RouterModule, HttpClientModule],
  declarations: [
    SearchContainerComponent,
    SearchBarComponent,
    AutoCompleteComponent,
    ReadMoreComponent,
    SaveSearchModalComponent,
    ShareProfileModalComponent,
    SearchTipsModalComponent,
    CompatDatePipe,
    MonthPipe,
    SearchHighlightPipe,
    DaysAgoDatePipe
  ],
  exports: [
    SearchContainerComponent,
    SearchBarComponent,
    AutoCompleteComponent,
    ReadMoreComponent,
    SaveSearchModalComponent,
    ShareProfileModalComponent,
    SearchTipsModalComponent,
    CompatDatePipe,
    MonthPipe,
    SearchHighlightPipe,
    DaysAgoDatePipe,
    CommonModule,
    FormsModule,
    RouterModule
  ]
})
export class SharedModule {
  static forRoot(): ModuleWithProviders {
    return {
      ngModule: SharedModule,
      providers: [
        AnalyticsService,
        DateFormatService,
        DomService,
        FilterEffectsService,
        JobSeekerService,
        JobSeekerDetailResolver,
        HttpService,
        JobTitlesService,
        LanguagesService,
        LegacySearchUrlGuard,
        LegacyJobSeekerUrlGuard,
        LegacyUrlParser,
        LocationsService,
        NumberFormatService,
        PaginationService,
        PlatformCoreService,
        ProfileNavigationService,
        ProfileAPIService,
        RecruiterResolver,
        RecruiterService,
        RestoreScrollPositionService,
        SavedSearchService,
        SavedFavoritesService,
        SearchService,
        SessionProfileViewTrackerService,
        SessionProfileFavoriteTrackerService,
        UrlSearchService,
        SavedSearchDirectAccessGuard,
        WindowService
      ]
    };
  }
}
