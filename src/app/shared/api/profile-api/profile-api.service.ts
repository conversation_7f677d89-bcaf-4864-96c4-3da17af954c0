import { Injectable } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { HttpService } from '../../services/http/http.service';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable()
export class ProfileAPIService {
  constructor(private http: HttpService) {}

  getAutocompleteJobTitles(jobTitle?: string): Observable<string[]> {
    let search = new HttpParams();
    if (jobTitle != null) {
      search = search.append('jobTitle', jobTitle);
    }

    return this.http
      .get('/api/recruiter/autocomplete/job-titles', search)
      .pipe(map((results: any) => results.result.map((s: { text: string }) => s.text)));
  }

  getJobSeeker(jobSeekerId: string): Observable<any> {
    return this.http.get(`/api/recruiter/job-seeker/${jobSeekerId}`);
  }

  getJobSeekerLimited(jobSeekerId: string): Observable<any> {
    return this.http.get(`/api/recruiter/job-seeker/limited/${jobSeekerId}`);
  }

  getCv(jobSeekerId: string): Observable<any> {
    return this.http.get(`/api/recruiter/job-seeker/${jobSeekerId}/cv`);
  }

  getRecruiter(authCode?: string): Observable<any> {
    let params = new HttpParams();
    if (authCode !== undefined) {
      params = params.append('authCode', authCode);
    }

    return this.http.get('/api/recruiter/me', params);
  }

  getSuggestibleProducts(): Observable<any> {
    return this.http.get('/dashboard/api/products?displayOptions=4');
  }
}

export namespace ProfileAPIService {}
