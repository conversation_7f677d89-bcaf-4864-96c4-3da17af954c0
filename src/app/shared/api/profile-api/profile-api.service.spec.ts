import { empty, from, of } from 'rxjs';
import { HttpParams } from '@angular/common/http';
import { ProfileAPIService } from './profile-api.service';

describe('ProfileAPIService', () => {
  const stub: any = {};
  let service: ProfileAPIService;

  beforeEach(() => {
    stub.Http = jasmine.createSpyObj('HttpService', ['get', 'del', 'post', 'put']);

    stub.Http.get.and.returnValue(of({}));
    stub.Http.del.and.returnValue(empty());
    stub.Http.post.and.returnValue(empty());
    stub.Http.put.and.returnValue(empty());
    service = new ProfileAPIService(<any>stub.Http);
  });

  describe('getAutocompleteJobTitles', () => {
    beforeEach(() => {
      stub.Http.get.and.returnValue(from([{ result: [{ text: 'test' }] }]));
    });

    it('should get titles', done => {
      let params = new HttpParams();
      params = params.append('jobTitle', 'a');

      service.getAutocompleteJobTitles('a').subscribe(() => {
        expect(stub.Http.get).toHaveBeenCalledWith('/api/recruiter/autocomplete/job-titles', params);
        done();
      });
    });

    it('should convert autocomplete suggest to flat strings', done => {
      service.getAutocompleteJobTitles('te').subscribe(response => {
        expect(response).toEqual(['test']);
        done();
      });
    });

    it('should show job titles with no jobtitle parameters', done => {
      const params = new HttpParams();

      service.getAutocompleteJobTitles().subscribe(() => {
        expect(stub.Http.get).toHaveBeenCalledWith('/api/recruiter/autocomplete/job-titles', params);
        done();
      });
    });
  });

  describe('getJobSeeker', () => {
    it('should get jobseeker', done => {
      const jobSeekerId = 'test-id';

      service.getJobSeeker(jobSeekerId).subscribe(() => {
        expect(stub.Http.get).toHaveBeenCalledWith(`/api/recruiter/job-seeker/${jobSeekerId}`);
        done();
      });
    });
  });

  describe('getJobSeekerLimited', () => {
    it('should get limited jobseeker', done => {
      const jobSeekerId = 'test-id';

      service.getJobSeekerLimited(jobSeekerId).subscribe(() => {
        expect(stub.Http.get).toHaveBeenCalledWith(`/api/recruiter/job-seeker/limited/${jobSeekerId}`);
        done();
      });
    });
  });

  describe('getJobSeeker', () => {
    it('should get jobseekers cv', done => {
      const jobSeekerId = 'test-id';

      service.getCv(jobSeekerId).subscribe(() => {
        expect(stub.Http.get).toHaveBeenCalledWith(`/api/recruiter/job-seeker/${jobSeekerId}/cv`);
        done();
      });
    });
  });

  describe('getRecruiter', () => {
    it('should get recruiter', done => {
      service.getRecruiter().subscribe(() => {
        expect(stub.Http.get).toHaveBeenCalledWith(`/api/recruiter/me`, new HttpParams());
        done();
      });
    });

    it('should get recruiter with authCode when an authCode is provided', done => {
      const code = 'this-is-the-auth-code-123';
      let params = new HttpParams();
      params = params.append('authCode', code);

      service.getRecruiter(code).subscribe(() => {
        expect(stub.Http.get).toHaveBeenCalledWith(`/api/recruiter/me`, params);
        done();
      });
    });
  });

  describe('getSuggestibleProducts', () => {
    it('should get suggestible products', done => {
      service.getSuggestibleProducts().subscribe(() => {
        expect(stub.Http.get).toHaveBeenCalledWith(`/dashboard/api/products?displayOptions=4`);
        done();
      });
    });
  });
});
