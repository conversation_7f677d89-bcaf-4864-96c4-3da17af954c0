import { async, TestBed, ComponentFixture } from '@angular/core/testing';
import { AppComponent } from './app.component';
import { NavigationStart, Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { Store } from '@ngrx/store';
import { Title } from '@angular/platform-browser';
import { environment } from '../environments/environment';
import { of, Subject, Observable } from 'rxjs';
import { AnalyticsService } from './shared/services';
import { ProfileAPIService } from './shared/api';

describe('AppComponent', () => {
  const stub: any = {};
  let componentFixture: ComponentFixture<AppComponent>;
  let component: AppComponent;
  let titlePage: string;
  let pageTrackingEventPage: string;

  beforeEach(async(() => {
    titlePage = 'New jobs in the air';
    pageTrackingEventPage = 'recruiter-search';

    stub.Router = { events: new Subject() };
    stub.Title = jasmine.createSpyObj('Title', ['setTitle']);
    stub.StoreSpinnerSubject = new Subject();
    stub.StoreOpenSubject = new Subject();
    stub.StoreSubscribeSubject = new Subject();
    stub.Store = new Subject();
    stub.Store.dispatch = jasmine.createSpy('dispatch');
    stub.Store.spinner = {
      mainSpinnerShown: stub.StoreSpinnerSubject
    };
    stub.Store.filter = {
      open: stub.StoreOpenSubject
    };
    stub.Store.select = (storeSelect: Function) => {
      return storeSelect(stub.Store);
    };
    stub.PlatformCoreService = {
      hasPlatformCoreAccount: true
    };
    stub.activatedRouteParams = of({ id: 'test' });
    stub.activatedRouteQueryParams = new Subject();
    stub.ActivatedRoute = {
      queryParams: stub.activatedRouteQueryParams,
      params: stub.activatedRouteParams,
      outlet: 'primary',
      snapshot: {
        data: {
          title: titlePage,
          trackingEvent: pageTrackingEventPage
        }
      }
    };
    stub.AnalyticsService = jasmine.createSpyObj('AnalyticsService', ['pushPageViewToDataLayer']);

    stub.getRecruiter = of({ firstName: 'Test', lastName: 'User' });
    stub.ProfileAPIService = {
      getRecruiter: () => stub.getRecruiter
    };

    TestBed.configureTestingModule({
      declarations: [AppComponent],
      providers: [
        { provide: Router, useValue: stub.Router },
        { provide: Title, useValue: stub.Title },
        { provide: Store, useValue: stub.Store },
        { provide: ActivatedRoute, useValue: stub.ActivatedRoute },
        { provide: AnalyticsService, useValue: stub.AnalyticsService },
        { provide: ProfileAPIService, useValue: stub.ProfileAPIService }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  }));

  beforeEach(() => {
    componentFixture = TestBed.createComponent(AppComponent);
    component = componentFixture.componentInstance;
    spyOn(component, 'locationAssignFn');
    componentFixture.detectChanges();
  });

  describe('#handleLoggedInRecruiter', () => {
    it('should fetch the logged in user', () => {
      component.user.subscribe(user => {
        expect(user).toEqual({ firstName: 'Test', lastName: 'User' });
      });
    });
  });

  describe('#constructor', () => {
    it('should create component', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('#handleRouterEvents', () => {
    it('should properly change location if the NavigationStart event emitted and URL starts with /#/', () => {
      const url = '/#/legacy-search?url=/some/url';

      component['handleRouterEvents']();

      stub.Router.events.next(new NavigationStart(1, url));

      expect(component.locationAssignFn).toHaveBeenCalledWith(url.substr(3));
    });

    it('should not change location if other than NavigationStart event emitted', () => {
      component['handleRouterEvents']();

      stub.Router.events.next(new NavigationEnd(1, 'url', 'urlAfterRedirects'));

      expect(component.locationAssignFn).not.toHaveBeenCalled();
    });

    it('should not change location if the NavigationStart event emitted but URL does not start with /#/', () => {
      const url = 'legacy-search?url=/some/url';

      component['handleRouterEvents']();

      stub.Router.events.next(new NavigationStart(1, url));

      expect(component.locationAssignFn).not.toHaveBeenCalled();
    });

    it('should not set page title', () => {
      const setPageTitleSpy = jasmine.createSpy('setPageTitle');
      const getFirstActivatedRouteSpy = jasmine.createSpy('getFirstActivatedRoute');
      stub.ActivatedRoute.outlet = '';
      getFirstActivatedRouteSpy.and.returnValue(stub.ActivatedRoute);

      component['setPageTitle'] = setPageTitleSpy;
      component['getFirstActivatedRoute'] = getFirstActivatedRouteSpy;
      component['activatedRoute'] = stub.ActivatedRoute;

      component['handleRouterEvents']();

      stub.Router.events.next(new NavigationEnd(1, 'kandidaten', 'kandidaten/zoeken'));

      expect(component['getFirstActivatedRoute']).toHaveBeenCalledWith(stub.ActivatedRoute);
      expect(component['setPageTitle']).not.toHaveBeenCalled();
    });

    it('should set page title', () => {
      const setPageTitleSpy = jasmine.createSpy('setPageTitle');
      const getFirstActivatedRouteSpy = jasmine.createSpy('getFirstActivatedRoute');
      getFirstActivatedRouteSpy.and.returnValue(stub.ActivatedRoute);

      component['setPageTitle'] = setPageTitleSpy;
      component['getFirstActivatedRoute'] = getFirstActivatedRouteSpy;
      component['activatedRoute'] = stub.ActivatedRoute;

      component['handleRouterEvents']();

      stub.Router.events.next(new NavigationEnd(1, 'kandidaten', 'kandidaten/zoeken'));

      expect(component['getFirstActivatedRoute']).toHaveBeenCalledWith(stub.ActivatedRoute);
      expect(component['setPageTitle']).toHaveBeenCalledWith(titlePage);
    });

    it('should push data layer when navigation end event is triggered', () => {
      component['activatedRoute'] = stub.ActivatedRoute;
      const isDataLayerChangeUrlEventSpy = jasmine.createSpy('isDataLayerChangeUrlEvent');
      isDataLayerChangeUrlEventSpy.and.returnValue(true);
      component['isDataLayerChangeUrlEvent'] = isDataLayerChangeUrlEventSpy;

      component['handleRouterEvents']();

      stub.AnalyticsService.pushPageViewToDataLayer.calls.reset();

      stub.Router.events.next(new NavigationEnd(1, 'account/registreren/email', 'account/registreren/functies'));

      expect(stub.AnalyticsService.pushPageViewToDataLayer).toHaveBeenCalledWith(pageTrackingEventPage);
    });

    it('should not push data layer when navigation end event is triggered', () => {
      stub.ActivatedRoute.outlet = '';
      delete stub.ActivatedRoute.snapshot.data.trackingEvent;
      component['activatedRoute'] = stub.ActivatedRoute;
      const isDataLayerChangeUrlEventSpy = jasmine.createSpy('isDataLayerChangeUrlEvent');
      isDataLayerChangeUrlEventSpy.and.returnValue(false);
      component['isDataLayerChangeUrlEvent'] = isDataLayerChangeUrlEventSpy;

      component['handleRouterEvents']();

      stub.AnalyticsService.pushPageViewToDataLayer.calls.reset();

      stub.Router.events.next(new NavigationEnd(2, 'account/registreren/email', 'account/registreren/functies'));

      expect(stub.AnalyticsService.pushPageViewToDataLayer).not.toHaveBeenCalled();
    });

    it('should push data layer when query params change', () => {
      component['activatedRoute'] = stub.ActivatedRoute;

      component['handleRouterEvents']();

      stub.AnalyticsService.pushPageViewToDataLayer.calls.reset();

      stub.ActivatedRoute.queryParams.next('provinces=Limburg');

      expect(stub.AnalyticsService.pushPageViewToDataLayer).toHaveBeenCalledWith(pageTrackingEventPage);
    });

    it('should not push data layer when query params change', () => {
      stub.ActivatedRoute.outlet = '';
      delete stub.ActivatedRoute.snapshot.data.trackingEvent;
      component['activatedRoute'] = stub.ActivatedRoute;

      component['handleRouterEvents']();

      stub.AnalyticsService.pushPageViewToDataLayer.calls.reset();

      stub.ActivatedRoute.queryParams.next('pageSize=50');

      expect(stub.AnalyticsService.pushPageViewToDataLayer).not.toHaveBeenCalled();
    });
  });

  describe('#handleFilter', () => {
    it('should expose filter status', () => {
      component['handleFilter']();

      stub.Store.next({ spinner: { mainSpinnerShown: true } });

      expect(component.filter).toBeDefined();
    });
  });

  describe('#handleSpinner', () => {
    it('should expose spinner status', () => {
      component['handleSpinner']();

      stub.Store.next({ spinner: { mainSpinnerShown: true } });

      expect(component.isLoading).toBe(true);
    });
  });

  describe('#getFirstActivatedRoute', () => {
    it('should get first activated route', () => {
      const testFirstActivatedRoute = <ActivatedRoute>{};
      const activatedRouteParams: ActivatedRoute = <any>{ firstChild: testFirstActivatedRoute };

      const expectedFirstActivatedRoute = component['getFirstActivatedRoute'](activatedRouteParams);

      expect(expectedFirstActivatedRoute).toEqual(testFirstActivatedRoute);
    });

    it('should get the entire route object', () => {
      const testFirstActivatedRoute = { data: '' };
      const activatedRouteParams: ActivatedRoute = <any>{ randomData: testFirstActivatedRoute };

      const expectedFirstActivatedRoute = component['getFirstActivatedRoute'](activatedRouteParams);

      expect(expectedFirstActivatedRoute).toEqual(activatedRouteParams);
    });
  });

  describe('#setPageTitle', () => {
    it('should set the specific title of the page', () => {
      const title = 'Search Page';

      component['setPageTitle'](title);

      expect(stub.Title.setTitle).toHaveBeenCalledTimes(1);
      expect(stub.Title.setTitle).toHaveBeenCalledWith(title);
    });

    it('should set the default title of the page if undefined title', () => {
      const noTitle: string = undefined;

      component['setPageTitle'](noTitle);

      expect(stub.Title.setTitle).toHaveBeenCalledTimes(1);
      expect(stub.Title.setTitle).toHaveBeenCalledWith(environment.titles.default);
    });

    it('should set the default title of the page if empty title', () => {
      const emptyTitle = '';

      component['setPageTitle'](emptyTitle);

      expect(stub.Title.setTitle).toHaveBeenCalledTimes(1);
      expect(stub.Title.setTitle).toHaveBeenCalledWith(environment.titles.default);
    });
  });

  describe('#isDataLayerChangeUrlEvent', () => {
    it('should return false if snapshot does not exist', () => {
      const isEvent = component['isDataLayerChangeUrlEvent'](undefined, 'zoeken');
      expect(isEvent).toEqual(false);
    });

    it('should return false if snapshot tracking event does not exist', () => {
      delete stub.ActivatedRoute.snapshot.data.trackingEvent;
      const isEvent = component['isDataLayerChangeUrlEvent'](stub.ActivatedRoute.snapshot, 'zoeken');

      expect(isEvent).toEqual(false);
    });

    it('should return false if snapshot tracking skip is true and the url has no query params', () => {
      stub.ActivatedRoute.snapshot.data.skipBaseUrlTrackingEvent = true;
      const isEvent = component['isDataLayerChangeUrlEvent'](stub.ActivatedRoute.snapshot, 'zoeken');
      expect(isEvent).toEqual(false);
    });

    it('should return true if snapshot tracking skip is true and the url has query params', () => {
      stub.ActivatedRoute.snapshot.data.skipBaseUrlTrackingEvent = true;
      const isEvent = component['isDataLayerChangeUrlEvent'](stub.ActivatedRoute.snapshot, 'zoeken?a=b');
      expect(isEvent).toEqual(true);
    });
  });
});
