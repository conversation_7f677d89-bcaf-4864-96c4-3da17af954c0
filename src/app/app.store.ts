import { ActionReducerMap, ActionReducer, MetaReducer } from '@ngrx/store';
import { AppState } from './app.state';
import { filterReducer } from './store/reducers/filter/filter.reducer';
import { searchReducer } from './store/reducers/search/search.reducer';
import { modalsReducer } from './store/reducers/modals/modals.reducer';
import { spinnerReducer } from './store/reducers/spinner/spinner.reducer';
import { storeLogger } from 'ngrx-store-logger';
import { environment } from '../environments/environment';

export function logger(reducer: ActionReducer<AppState>): any {
  return storeLogger({ collapsed: true })(reducer);
}

export const reducers: ActionReducerMap<AppState> = {
  filter: filterReducer,
  search: searchReducer,
  modals: modalsReducer,
  spinner: spinnerReducer
};

export const metaReducers: MetaReducer<AppState>[] = !environment.production ? [logger] : [];
