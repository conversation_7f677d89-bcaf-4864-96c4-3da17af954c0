import { Education } from './education';

describe('Education', () => {
  const stub: any = {};

  beforeEach(() => {
    stub.data = [
      {
        city: 'city',
        fieldOfStudy: 'fieldOfStudy',
        fromDate: 'fromDate',
        grade: 'grade',
        diploma: 'diploma',
        educationId: '1',
        school: 'school',
        toDate: 'toDate',
        description: 'description **very** important',
        descriptionHtml: 'description <b>very</b> important'
      },
      {
        city: 'city',
        fieldOfStudy: 'fieldOfStudy',
        fromDate: 'fromDate',
        grade: 'grade',
        diploma: 'diploma',
        educationId: '2',
        school: 'school',
        toDate: 'toDate',
        description: 'description *less* important',
        descriptionHtml: 'description <i>less</i> important'
      }
    ];
  });

  describe('constructor', () => {
    it('should create a education for every object in data array', () => {
      const edu = new Education('city', 'field', 'fromDate', 'grade', true, 'id', 'school', 'toDate', '## Descr', '<h2>Descr</h2>');
      expect(edu.city).toBe('city');
      expect(edu.fieldOfStudy).toBe('field');
      expect(edu.fromDate).toBe('fromDate');
      expect(edu.grade).toBe('grade');
      expect(edu.diploma).toBe(true);
      expect(edu.id).toBe('id');
      expect(edu.school).toBe('school');
      expect(edu.toDate).toBe('toDate');
      expect(edu.description).toBe('## Descr');
      expect(edu.descriptionHtml).toBe('<h2>Descr</h2>');
    });
  });

  describe('#fromJson', () => {
    it('should create an education for every object in data array', () => {
      const educations = Education.fromJson(stub.data);
      expect(educations.length).toBe(2);
    });

    it('should skip unapproved education', () => {
      const unapprovedEducationIds = ['2', '3'];

      const educations = Education.fromJson(stub.data, unapprovedEducationIds);

      expect(educations.length).toBe(1);

      educations.forEach(education => {
        expect(unapprovedEducationIds.indexOf(education.id)).toBe(-1);
      });
    });
  });

  describe('#empty', () => {
    it('should create an empty eduification', () => {
      const edu = Education.empty();
      expect(edu.city).toBeUndefined();
      expect(edu.fieldOfStudy).toBeUndefined();
      expect(edu.fromDate).toBeUndefined();
      expect(edu.grade).toBeUndefined();
      expect(edu.diploma).toBeUndefined();
      expect(edu.id).toBeUndefined();
      expect(edu.school).toBeUndefined();
      expect(edu.toDate).toBeUndefined();
      expect(edu.description).toBeUndefined();
      expect(edu.descriptionHtml).toBeUndefined();
    });
  });
});
