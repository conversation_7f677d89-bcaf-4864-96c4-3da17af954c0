export class Training {
  constructor(
    public id: string,
    public institute: string,
    public name: string,
    public year: string,
    public month: string,
    public description: string,
    public descriptionHtml: string
  ) {}

  static fromJson(data: any): Array<Training> {
    const array = [];
    for (const training of data) {
      const t = new Training(
        training.trainingId,
        training.institute,
        training.name,
        training.year,
        training.month,
        training.description,
        training.descriptionHtml
      );
      array.push(t);
    }
    return array;
  }

  static empty() {
    return new Training(undefined, undefined, undefined, undefined, undefined, undefined, undefined);
  }
}
