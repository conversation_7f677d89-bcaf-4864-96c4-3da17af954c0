import { <PERSON>Seeker } from './jobseeker';
import { Experience } from './experience';
import { JobCriteria } from './job-criteria';
import { PersonalInfo } from './personal-info';
import { Radius } from './radius';

describe('JobSeeker', () => {
  const stub: any = {};
  beforeEach(() => {
    stub.jobSeeker = new JobSeeker(
      'id',
      **********,
      true,
      [],
      [],
      [],
      new JobCriteria([], 2, 40, [], [], undefined, '', 'availability', 'city'),
      new PersonalInfo('', '', '', '', '', ''),
      null,
      null,
      'site',
      'aanvullende info',
      [],
      [],
      [],
      false,
      null
    );
  });

  describe('#constructor', () => {
    it('should expose fileCvString', () => {
      const jobSeeker = new JobSeeker(
        'id',
        **********,
        true,
        [],
        [],
        [],
        new JobCriteria(['HBO'], 2, 40, ['desiredFunction'], [], new Radius(10), '1011AA', 'availability', 'city'),
        new PersonalInfo('firstName', 'lastName', 'email', 'phoneNumber', 'city', ''),
        null,
        'test.pdf',
        'site',
        'aanvullende info',
        [],
        [],
        [],
        false,
        null
      );
      expect(jobSeeker.fileCvString).toBe('test.pdf');
    });
  });

  describe('#lastExperience', () => {
    it('should return lastExperience', () => {
      const exp = Experience.empty();
      stub.jobSeeker.experience.push(exp);
      const exp2 = Experience.empty();
      stub.jobSeeker.experience.push(exp2);
      expect(stub.jobSeeker.lastExperience()).toBe(exp);
    });
  });

  describe('#getSiteString', () => {
    it('should return nvb for nationalevacaturebank.nl', () => {
      const jobSeeker = new JobSeeker(
        'id',
        **********,
        true,
        [],
        [],
        [],
        new JobCriteria([], 2, 8, [], [], undefined, '', 'availability', ''),
        new PersonalInfo('', '', '', '', '', ''),
        '',
        '',
        'nationalevacaturebank.nl',
        'aanvullende info',
        [],
        [],
        [],
        false,
        null
      );

      expect(jobSeeker.getSiteString()).toEqual('nvb');
    });

    it('should return itbanen for itbanen.nl', () => {
      const jobSeeker = new JobSeeker(
        'id',
        **********,
        true,
        [],
        [],
        [],
        new JobCriteria([], 2, 8, [], [], undefined, '', 'availability', ''),
        new PersonalInfo('', '', '', '', '', ''),
        '',
        '',
        'itbanen.nl',
        'aanvullende info',
        [],
        [],
        [],
        false,
        null
      );

      expect(jobSeeker.getSiteString()).toEqual('itbanen');
    });

    it('should return intermediair for intermediair.nl', () => {
      const jobSeeker = new JobSeeker(
        'id',
        **********,
        true,
        [],
        [],
        [],
        new JobCriteria([], 2, 8, [], [], undefined, '', 'availability', ''),
        new PersonalInfo('', '', '', '', '', ''),
        '',
        '',
        'intermediair.nl',
        'aanvullende info',
        [],
        [],
        [],
        false,
        null
      );

      expect(jobSeeker.getSiteString()).toEqual('intermediair');
    });
  });



  describe('#getCdn', () => {

    it('should return https://cdn.intermediair.nl/ for intermediair.nl', () => {
      const jobSeeker = new JobSeeker(
          'id',
          **********,
          true,
          [],
          [],
          [],
          new JobCriteria([], 2, 8, [], [], undefined, '', 'availability', ''),
          new PersonalInfo('', '', '', '', '', ''),
          '',
          '',
          'intermediair.nl',
          'aanvullende info',
          [],
          [],
          [],
          false,
          null
      );

      expect(jobSeeker.getCdn()).toEqual('https://cdn.intermediair.nl/');
    });
    it('should return https://cdn.nationalevacaturebank.nl/ for nationalevacaturebank.nl/', () => {
      const jobSeeker = new JobSeeker(
          'id',
          **********,
          true,
          [],
          [],
          [],
          new JobCriteria([], 2, 8, [], [], undefined, '', 'availability', ''),
          new PersonalInfo('', '', '', '', '', ''),
          '',
          '',
          'nationalevacaturebank.nl',
          'aanvullende info',
          [],
          [],
          [],
          false,
          null
      );

      expect(jobSeeker.getCdn()).toEqual('https://cdn.nationalevacaturebank.nl/');
    });
  });




});
