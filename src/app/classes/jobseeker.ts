import { Education } from './education';
import { Experience } from './experience';
import { Training } from './training';
import { JobCriteria } from './job-criteria';
import { PersonalInfo } from './personal-info';
import { Language } from './language';
import { DriversLicense } from './drivers-license';
import { Attachment } from './attachment';

export class JobSeeker {
  constructor(
    public id: string,
    public updatedDate: number,
    public noWorkExperience: boolean,
    public education: Array<Education>,
    public experience: Array<Experience>,
    public training: Array<Training>,
    public jobCriteria: JobCriteria,
    public personalInfo: PersonalInfo,
    public photo: string,
    public fileCvString: string = null,
    public site: string,
    public extraInfo: string,
    public languages: Language[],
    public driversLicenses: DriversLicense[],
    public attachments: Attachment[],
    public isFavorite: boolean,
    public migrationStatus?: boolean,
    public introductionText?: string,
    public introductionTextHtml?: string,
    public viewedDate?: number,
    public originalJobSeeker?: any
  ) {
    this.updatedDate = this.updatedDate ? this.updatedDate * 1000 : this.updatedDate;
    this.viewedDate = this.viewedDate ? this.viewedDate * 1000 : this.viewedDate;
  }

  public lastExperience() {
    return this.experience[0];
  }

  public getSiteString() {
    switch (this.site) {
      case 'intermediair.nl':
        return 'intermediair';
      case 'nationalevacaturebank.nl':
        return 'nvb';
      case 'itbanen.nl':
        return 'itbanen';
    }
  }

  public getCdn() {
    switch (this.site) {
      case 'intermediair.nl':
        return 'https://cdn.intermediair.nl/';
      case 'nationalevacaturebank.nl':
        return 'https://cdn.nationalevacaturebank.nl/';
    }
  }
}
