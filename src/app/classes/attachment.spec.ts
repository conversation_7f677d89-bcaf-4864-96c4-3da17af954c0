import { Attachment } from './attachment';

describe('Attachment', () => {
  describe('#constructor', () => {
    it('should have correct properties', () => {
      const a = new Attachment('filName.pdf', 'name');
      expect(a.fileName).toBe('filName.pdf');
      expect(a.name).toBe('name');
    });
  });

  describe('#fromJson', () => {
    it('should create a drivers license for every object in data array', () => {
      const data = [{ name: 'fileName', fileName: 'fileName.pdf' }, { name: 'fileName', fileName: 'fileName.txt' }];
      const attachments = Attachment.fromJson(data);
      expect(attachments.length).toBe(2);
    });

    it('should expect no attachments', () => {
      const attachments = Attachment.fromJson(undefined);
      expect(attachments.length).toBe(0);
    });
  });
});
