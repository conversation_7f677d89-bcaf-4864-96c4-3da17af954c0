export class Education {
  constructor(
    public city: string,
    public fieldOfStudy: string,
    public fromDate: string,
    public grade: string,
    public diploma: boolean,
    public id: string,
    public school: string,
    public toDate: string,
    public description: string,
    public descriptionHtml: string
  ) {}

  static fromJson(data: any, unapprovedEductionIds: string[] = []): Array<Education> {
    const educations = [];

    for (const education of data) {
      if (unapprovedEductionIds.indexOf(education.educationId) !== -1) {
        continue;
      }

      educations.push(
        new Education(
          education.city,
          education.fieldOfStudy,
          education.fromDate,
          education.grade,
          education.diploma,
          education.educationId,
          education.school,
          education.toDate,
          education.description,
          education.descriptionHtml
        )
      );
    }

    return educations;
  }

  static empty() {
    return new Education(undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined);
  }
}
