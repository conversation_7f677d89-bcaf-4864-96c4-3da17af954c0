export class Radius {
  static availableRadii = [
    { label: '5 km', value: 5 },
    { label: '10 km', value: 10 },
    { label: '20 km', value: 20 },
    { label: '30 km', value: 30 },
    { label: '40 km', value: 40 },
    { label: '50 km', value: 50 },
    { label: '60 km', value: 60 },
    { label: '70 km', value: 70 },
    { label: '80 km', value: 80 },
    { label: '90 km', value: 90 },
    { label: '100 km', value: 100 },
    { label: 'Heel Nederland', value: 9999 }
  ];

  constructor(public value: number) {}

  parse(): string {
    let parsedRadius: string = undefined;
    Radius.availableRadii.map(r => {
      if (this.value === r.value) {
        parsedRadius = r.label;
      }
    });
    return parsedRadius;
  }
}
