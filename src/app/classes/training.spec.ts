import { Training } from './training';

describe('Training', () => {
  const stub: any = {};

  beforeEach(() => {
    stub.data = [
      {
        id: '1',
        institute: 'institute',
        name: 'name',
        year: '2012',
        month: '11'
      },
      {
        id: '2',
        institute: 'institute',
        name: 'name',
        year: '2014',
        month: '03'
      }
    ];
  });

  describe('#constructor', () => {
    it('should create a training for every object in data array', () => {
      const training = new Training('id', 'institute', 'name', 'year', 'expiry_date', '*Try*', '<i>Try</i>');
      expect(training.id).toBe('id');
      expect(training.institute).toBe('institute');
      expect(training.name).toBe('name');
      expect(training.year).toBe('year');
      expect(training.month).toBe('expiry_date');
      expect(training.description).toBe('*Try*');
      expect(training.descriptionHtml).toBe('<i>Try</i>');
    });
  });

  describe('#fromJson', () => {
    it('should create a training for every object in data array', () => {
      const trainings = Training.fromJson(stub.data);
      expect(trainings.length).toBe(2);
    });
  });

  describe('#empty', () => {
    it('should create an empty training', () => {
      const training = Training.empty();
      expect(training.id).toBeUndefined();
      expect(training.institute).toBeUndefined();
      expect(training.name).toBeUndefined();
      expect(training.year).toBeUndefined();
      expect(training.month).toBeUndefined();
      expect(training.description).toBeUndefined();
      expect(training.descriptionHtml).toBeUndefined();
    });
  });
});
