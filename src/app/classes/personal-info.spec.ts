import { PersonalInfo } from './personal-info';

describe('PersonalInfo', () => {
  const stub: any = {};

  beforeEach(() => {
    stub.data = {
      firstName: 'firstName',
      lastName: 'lastName',
      emailAddress: 'emailAddress',
      phoneNumber: 'phoneNumber',
      commute: {
        city: 'city'
      },
      country: 'country'
    };
  });

  describe('#constructor', () => {
    it('should create a personal info', () => {
      const pi = new PersonalInfo('firstName', 'lastName', 'emailAddress', 'phoneNumber', 'city', 'country');
      expect(pi.firstName).toEqual('firstName');
      expect(pi.lastName).toEqual('lastName');
      expect(pi.email).toEqual('emailAddress');
      expect(pi.phoneNumber).toEqual('phoneNumber');
      expect(pi.city).toEqual('city');
      expect(pi.country).toEqual('country');
    });
  });

  describe('#fromJson', () => {
    it('should create a personal info from JSON', () => {
      const personalInfo = PersonalInfo.fromJson(stub.data);
      expect(personalInfo.firstName).toEqual(stub.data.firstName);
      expect(personalInfo.lastName).toEqual(stub.data.lastName);
      expect(personalInfo.email).toEqual(stub.data.emailAddress);
      expect(personalInfo.phoneNumber).toEqual(stub.data.phoneNumber);
      expect(personalInfo.city).toEqual(stub.data.commute.city);
      expect(personalInfo.country).toEqual(stub.data.country);
    });

    it('should set empty string as a city if the commute object is not provided', () => {
      const data = {
        firstName: 'firstName',
        lastName: 'lastName',
        emailAddress: 'emailAddress',
        phoneNumber: 'phoneNumber',
        country: 'country'
      };

      const personalInfo = PersonalInfo.fromJson(data);
      expect(personalInfo.firstName).toEqual(data.firstName);
      expect(personalInfo.lastName).toEqual(data.lastName);
      expect(personalInfo.email).toEqual(data.emailAddress);
      expect(personalInfo.phoneNumber).toEqual(data.phoneNumber);
      expect(personalInfo.city).toEqual('');
      expect(personalInfo.country).toEqual(data.country);
    });
  });
});
