export class Experience {
  constructor(
    public company: string,
    public description: string,
    public fromDate: string,
    public functionName: string,
    public id: string,
    public toDate: string,
    public city: string,
    public industry: string,
    public descriptionHtml: string
  ) {}

  static fromJson(data: any, unapprovedExperienceIds: string[] = []): Array<Experience> {
    const experiences = [];

    for (const experience of data) {
      if (unapprovedExperienceIds.indexOf(experience.experienceId) !== -1) {
        continue;
      }

      experiences.push(
        new Experience(
          experience.companyName,
          experience.description,
          experience.fromDate,
          experience.jobTitle,
          experience.experienceId,
          experience.toDate,
          experience.city,
          experience.industry,
          experience.descriptionHtml
        )
      );
    }

    return experiences;
  }

  static empty() {
    return new Experience(undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined);
  }

  isWorkingHere() {
    return this.toDate === undefined;
  }
}
