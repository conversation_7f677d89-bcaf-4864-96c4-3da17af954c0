export class PersonalInfo {
  constructor(
    public firstName: string,
    public lastName: string,
    public email: string,
    public phoneNumber: string,
    public city: string,
    public country: string
  ) {}

  static from<PERSON>son(data: any): PersonalInfo {
    return new PersonalInfo(
      data.firstName,
      data.lastName,
      data.emailAddress,
      data.phoneNumber,
      data.commute ? data.commute.city : '',
      data.country
    );
  }
}
