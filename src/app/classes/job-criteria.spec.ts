import { JobCriteria } from './job-criteria';
import { Radius } from './radius';

describe('JobCriteria', () => {
  const stub: any = {};

  beforeEach(() => {
    stub.data = {
      workLevels: ['work_level'],
      minWorkingHours: 2,
      maxWorkingHours: 40,
      preferredJobs: ['hours'],
      functionGroups: ['a'],
      commute: { maxTravelDistance: 10 },
      zipCode: 'zip_code',
      city: 'city'
    };
  });

  describe('#constructor', () => {
    it('should create a job criteria for every object in data array', () => {
      const jc = new JobCriteria(
        ['work_level'],
        2,
        40,
        ['desiredFunction'],
        ['functionGroups'],
        new Radius(10),
        'zip_code',
        'availability',
        'city'
      );
      expect(jc.workLevels).toEqual(['work_level']);
      expect(jc.minWorkingHours).toEqual(2);
      expect(jc.maxWorkingHours).toEqual(40);
      expect(jc.desiredFunctions).toEqual(['desiredFunction']);
      expect(jc.functionGroups).toEqual(['functionGroups']);
      expect(jc.radius.value).toEqual(10);
      expect(jc.zipCode).toEqual('zip_code');
      expect(jc.availability).toEqual('availability');
      expect(jc.city).toEqual('city');
    });
  });

  describe('#fromJson', () => {
    it('should create a job criteria for every object in data array', () => {
      const jc = JobCriteria.fromJson(stub.data);
      expect(jc.workLevels).toEqual(['work_level']);
      expect(jc.minWorkingHours).toEqual(2);
      expect(jc.maxWorkingHours).toEqual(40);
      expect(jc.radius.value).toEqual(10);
      expect(jc.zipCode).toEqual('zip_code');
    });

    it('should create a job criteria with default values', () => {
      const jc = JobCriteria.fromJson({});
      expect(jc.workLevels).toEqual([]);
      expect(jc.minWorkingHours).toEqual(null);
      expect(jc.maxWorkingHours).toEqual(null);
      expect(jc.radius).toEqual(null);
      expect(jc.city).toEqual('');
    });
  });
});
