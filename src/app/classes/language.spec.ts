import { Language } from './language';

describe('Language', () => {
  describe('#constructor', () => {
    it('should have correct properties', () => {
      const lang = new Language('isoCode', 'name', true);
      expect(lang.isoCode).toBe('isoCode');
      expect(lang.name).toBe('name');
      expect(lang.isNative).toBe(true);
    });
  });

  describe('#fromJson', () => {
    it('should create a eduification for every object in data array', () => {
      const data = [
        {
          isoCode: 'isoCode',
          name: 'name',
          isNative: true
        }
      ];
      const languages = Language.fromJson(data);
      expect(languages.length).toBe(1);
    });

    it('should expect no language', () => {
      const languages = Language.fromJson(undefined);
      expect(languages.length).toBe(0);
    });
  });
});
