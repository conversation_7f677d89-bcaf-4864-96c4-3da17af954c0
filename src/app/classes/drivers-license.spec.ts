import { DriversLicense } from './drivers-license';

describe('DriversLicense', () => {
  describe('#constructor', () => {
    it('should have correct properties', () => {
      const d = new DriversLicense('A');
      expect(d.type).toBe('A');
    });
  });

  describe('#fromJson', () => {
    it('should create a drivers license for every object in data array', () => {
      const data = [{ type: 'A' }, { type: 'B' }];
      const licenses = DriversLicense.fromJson(data);
      expect(licenses.length).toBe(2);
    });

    it('should expect no drivers licenses', () => {
      const licenses = DriversLicense.fromJson(undefined);
      expect(licenses.length).toBe(0);
    });
  });
});
