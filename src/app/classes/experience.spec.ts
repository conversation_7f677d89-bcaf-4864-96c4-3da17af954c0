import { Experience } from './experience';

describe('Experience', () => {
  const stub: any = {};

  beforeEach(() => {
    stub.data = [
      {
        companyName: 'company',
        description: '**maiuscolo**',
        fromDate: 'from_date',
        function: 'function',
        experienceId: '1',
        toDate: 'to_date',
        industry: 'industry',
        city: 'city',
        descriptionHtml: '<b>maiuscolo</b>'
      },
      {
        companyName: 'company',
        description: '*corsivo*',
        fromDate: 'from_date',
        function: 'function',
        experienceId: '2',
        toDate: 'to_date',
        industry: 'industry',
        city: 'city',
        descriptionHtml: '<i>corsivo</i>'
      }
    ];
  });

  describe('#constructor', () => {
    it('should create an experience for every object in data array', () => {
      const exp = new Experience('company', '##### Descr', 'from_date', 'function', 'id', 'to_date', 'city', 'industry', '<h5>Descr</h5>');
      expect(exp.company).toBe('company');
      expect(exp.description).toBe('##### Descr');
      expect(exp.fromDate).toBe('from_date');
      expect(exp.functionName).toBe('function');
      expect(exp.id).toBe('id');
      expect(exp.toDate).toBe('to_date');
      expect(exp.industry).toBe('industry');
      expect(exp.city).toBe('city');
      expect(exp.descriptionHtml).toBe('<h5>Descr</h5>');
    });
  });

  describe('#fromJson', () => {
    it('should create a education for every object in data array', () => {
      const experiences = Experience.fromJson(stub.data);
      expect(experiences.length).toBe(2);
    });

    it('should skip unapproved experiences', () => {
      const unapprovedExperienceIds = ['2', '3'];

      const experiences = Experience.fromJson(stub.data, unapprovedExperienceIds);

      expect(experiences.length).toBe(1);

      experiences.forEach(experience => {
        expect(unapprovedExperienceIds.indexOf(experience.id)).toBe(-1);
      });
    });
  });

  describe('#empty', () => {
    it('should create an empty education', () => {
      const exp = Experience.empty();
      expect(exp.company).toBeUndefined();
      expect(exp.description).toBeUndefined();
      expect(exp.fromDate).toBeUndefined();
      expect(exp.functionName).toBeUndefined();
      expect(exp.id).toBeUndefined();
      expect(exp.toDate).toBeUndefined();
      expect(exp.industry).toBeUndefined();
      expect(exp.descriptionHtml).toBeUndefined();
    });
  });

  describe('#isWorkingHere', () => {
    it('should return true if toDate is undefined', () => {
      const exp = Experience.empty();
      expect(exp.isWorkingHere()).toBe(true);
    });

    it('should return false if toDate is defined', () => {
      const exp = Experience.empty();
      exp.toDate = '2016-01-01';
      expect(exp.isWorkingHere()).toBe(false);
    });
  });
});
