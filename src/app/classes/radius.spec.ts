import { Radius } from './radius';

describe('Radius', () => {
  describe('#constructor', () => {
    it('should create a job criteria for every object in data array', () => {
      const radius = new Radius(10);

      expect(radius.value).toEqual(10);
    });
  });

  describe('#parse', () => {
    it('should return a human readable version of the value', () => {
      const radius = new Radius(10);

      expect(radius.parse()).toEqual('10 km');
    });

    it('should return Heel Nederland for the special value 9999', () => {
      const radius = new Radius(9999);

      expect(radius.parse()).toEqual('Heel Nederland');
    });
  });
});
