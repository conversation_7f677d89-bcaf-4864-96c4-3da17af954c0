import { Radius } from './radius';

export class JobCriteria {
  constructor(
    public workLevels: Array<string>,
    public minWorkingHours: number,
    public maxWorkingHours: number,
    public desiredFunctions: Array<string>,
    public functionGroups: Array<string>,
    public radius: Radius,
    public zipCode: string,
    public availability: string,
    public city: string
  ) {}

  static from<PERSON>son(data: any): JobCriteria {
    return new JobCriteria(
      data.workLevels ? data.workLevels : [],
      data.minWorkingHours ? data.minWorkingHours : null,
      data.maxWorkingHours ? data.maxWorkingHours : null,
      data.preferredJobs ? data.preferredJobs : [],
      data.functionGroups ? data.functionGroups : [],
      data.commute ? new Radius(data.commute.maxTravelDistance) : null,
      data.zipCode,
      data.availability,
      data.commute ? data.commute.city : ''
    );
  }
}
