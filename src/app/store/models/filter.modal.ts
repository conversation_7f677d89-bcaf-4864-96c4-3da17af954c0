export interface FilterOption {
  name: string;
  title?: string;
  value?: string;
  count: string;
  order: number;
  selected: boolean;
}

export enum FilterTypes {
  CheckBox = 0,
  Dropdown = 1,
  AutoComplete = 2
}

export interface Filter {
  filterOptions: FilterOption[];
  name: string;
  title: string;
  open: boolean;
  type: FilterTypes;
  defaultSelectedValue?: string;
  action?: string;
  allowNotListedOptions?: boolean;
  placeholder?: string;
  resultAction?: string;
  collapsible?: boolean;
  numberOfShownItems?: number;
}
