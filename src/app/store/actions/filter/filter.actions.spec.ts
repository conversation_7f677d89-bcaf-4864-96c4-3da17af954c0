import * as FilterActions from './filter.actions';

describe('FilterActions', () => {
  describe('#OpenFilter', () => {
    it('should create OpenFilter action', () => {
      const action = new FilterActions.OpenFilter();
      expect({ ...action }).toEqual({
        type: FilterActions.OPEN_FILTER
      });
      expect(action.type).toEqual(FilterActions.OPEN_FILTER);
    });
  });

  describe('#CloseFilter', () => {
    it('should create CloseFilter action', () => {
      const action = new FilterActions.CloseFilter();
      expect({ ...action }).toEqual({
        type: FilterActions.CLOSE_FILTER
      });
      expect(action.type).toEqual(FilterActions.CLOSE_FILTER);
    });
  });

  describe('#CloseAllFilters', () => {
    it('should create CloseAllFilters action', () => {
      const action = new FilterActions.CloseAllFilters();
      expect({ ...action }).toEqual({
        type: FilterActions.CLOSE_ALL_FILTERS
      });
      expect(action.type).toEqual(FilterActions.CLOSE_ALL_FILTERS);
    });
  });

  describe('#SelectFilterOption', () => {
    it('should create SelectFilterOption action', () => {
      const payload: any = {
        filter: 'filter',
        filterOption: { name: 'test', order: 1, selected: true }
      };
      const action = new FilterActions.SelectFilterOption(payload);

      expect({ ...action }).toEqual({
        type: FilterActions.SELECT_FILTER_OPTION,
        payload
      });
      expect(action.type).toEqual(FilterActions.SELECT_FILTER_OPTION);
    });
  });

  describe('#AggregationsUpdated', () => {
    it('should create AggregationsUpdated action', () => {
      const payload: any = {};
      const action = new FilterActions.AggregationsUpdated(payload);

      expect({ ...action }).toEqual({
        type: FilterActions.AGGREGATIONS_UPDATED,
        payload
      });
      expect(action.type).toEqual(FilterActions.AGGREGATIONS_UPDATED);
    });
  });

  describe('#FetchLanguages', () => {
    it('should create FetchLanguages action', () => {
      const action = new FilterActions.FetchLanguages();
      expect({ ...action }).toEqual({
        type: FilterActions.FETCH_LANGUAGES
      });
      expect(action.type).toEqual(FilterActions.FETCH_LANGUAGES);
    });
  });

  describe('#FetchJobTitles', () => {
    it('should create FetchJobTitles action', () => {
      const action = new FilterActions.FetchJobTitles();
      expect({ ...action }).toEqual({
        type: FilterActions.FETCH_JOB_TITLES
      });
      expect(action.type).toEqual(FilterActions.FETCH_JOB_TITLES);
    });
  });

  describe('#FetchLocations', () => {
    it('should create FetchLocations action', () => {
      const action = new FilterActions.FetchLocations();
      expect({ ...action }).toEqual({
        type: FilterActions.FETCH_LOCATIONS
      });
      expect(action.type).toEqual(FilterActions.FETCH_LOCATIONS);
    });
  });

  describe('#ForceSelectFilterOption', () => {
    it('should create ForceSelectFilterOption action', () => {
      const payload: any = {
        filter: 'radius',
        filterOption: { name: 'Option 5', selected: true }
      };
      const action = new FilterActions.ForceSelectFilterOption(payload);

      expect({ ...action }).toEqual({
        type: FilterActions.FORCE_SELECT_FILTER_OPTION,
        payload
      });
      expect(action.type).toEqual(FilterActions.FORCE_SELECT_FILTER_OPTION);
    });
  });
});
