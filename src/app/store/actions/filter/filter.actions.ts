import { Action } from '@ngrx/store';

export const OPEN_FILTER = 'OPEN_FILTER';
export const CLOSE_FILTER = 'CLOSE_FILTER';
export const CLOSE_ALL_FILTERS = 'CLOSE_ALL_FILTERS';
export const TOGGLE_FILTER_OPTION = 'TOGGLE_FILTER_OPTION';
export const SELECT_FILTER_OPTION = 'SELECT_FILTER_OPTION';
export const AGGREGATIONS_UPDATED = 'AGGREGATIONS_UPDATED';
export const FETCH_LANGUAGES = 'FETCH_LANGUAGES';
export const FETCH_JOB_TITLES = 'FETCH_JOB_TITLES';
export const FETCH_LOCATIONS = 'FETCH_LOCATIONS';
export const FORCE_SELECT_FILTER_OPTION = 'FORCE_SELECT_FILTER_OPTION';

export class OpenFilter implements Action {
  readonly type = OPEN_FILTER;
}

export class CloseFilter implements Action {
  readonly type = CLOSE_FILTER;
}

export class CloseAllFilters implements Action {
  readonly type = CLOSE_ALL_FILTERS;
}

export class ToggleFilterOption implements Action {
  readonly type = TOGGLE_FILTER_OPTION;

  constructor(public payload: any) {}
}

export class SelectFilterOption implements Action {
  readonly type = SELECT_FILTER_OPTION;

  constructor(public payload: any) {}
}

export class AggregationsUpdated implements Action {
  readonly type = AGGREGATIONS_UPDATED;

  constructor(public payload: any) {}
}

export class FetchLanguages implements Action {
  readonly type = FETCH_LANGUAGES;
}

export class FetchJobTitles implements Action {
  readonly type = FETCH_JOB_TITLES;
}

export class FetchLocations implements Action {
  readonly type = FETCH_LOCATIONS;
}

export class ForceSelectFilterOption implements Action {
  readonly type = FORCE_SELECT_FILTER_OPTION;

  constructor(public payload: any) {}
}

export type FilterActions =
  | OpenFilter
  | CloseFilter
  | CloseAllFilters
  | ToggleFilterOption
  | SelectFilterOption
  | AggregationsUpdated
  | FetchLanguages
  | FetchJobTitles
  | FetchLocations
  | ForceSelectFilterOption;
