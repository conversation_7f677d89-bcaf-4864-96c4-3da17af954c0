import * as SpinnerActions from './spinner.actions';

describe('SpinnerActions', () => {
  describe('#ShowSpinner', () => {
    it('should create the show spinner action', () => {
      const action = new SpinnerActions.ShowSpinner();

      expect({ ...action }).toEqual({
        type: SpinnerActions.SHOW_MAIN_SPINNER
      });
      expect(action.type).toEqual(SpinnerActions.SHOW_MAIN_SPINNER);
    });
  });

  describe('#HideSpinner', () => {
    it('should create the hide spinner action', () => {
      const action = new SpinnerActions.HideSpinner();

      expect({ ...action }).toEqual({
        type: SpinnerActions.HIDE_MAIN_SPINNER
      });
      expect(action.type).toEqual(SpinnerActions.HIDE_MAIN_SPINNER);
    });
  });
});
