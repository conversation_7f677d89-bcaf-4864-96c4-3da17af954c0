import * as SearchActions from './search.actions';

describe('SearchActions', () => {
  describe('#UpdateSearch', () => {
    it('should create an action', () => {
      const payload = { searchQuery: { all: ['new search query'] } };
      const action = new SearchActions.UpdateSearch(payload);
      expect({ ...action }).toEqual({
        type: SearchActions.UPDATE_SEARCH,
        payload
      });
      expect(action.type).toEqual(SearchActions.UPDATE_SEARCH);
    });
  });

  describe('#ChangeSort', () => {
    it('should create an action', () => {
      const payload = 'default';
      const action = new SearchActions.ChangeSort(payload);
      expect({ ...action }).toEqual({
        type: SearchActions.CHANGE_SORT,
        payload
      });
      expect(action.type).toEqual(SearchActions.CHANGE_SORT);
    });
  });

  describe('#ChangePageSize', () => {
    it('should create an action', () => {
      const payload = { pageSize: 50, newCurrentPage: 2 };
      const action = new SearchActions.ChangePageSize(payload);
      expect({ ...action }).toEqual({
        type: SearchActions.CHANGE_PAGE_SIZE,
        payload
      });
      expect(action.type).toEqual(SearchActions.CHANGE_PAGE_SIZE);
    });
  });

  describe('#ChangeCurrentPage', () => {
    it('should create an action', () => {
      const payload = 3;
      const action = new SearchActions.ChangeCurrentPage(payload);
      expect({ ...action }).toEqual({
        type: SearchActions.CHANGE_CURRENT_PAGE,
        payload
      });
      expect(action.type).toEqual(SearchActions.CHANGE_CURRENT_PAGE);
    });
  });

  describe('#SynchronizeAfterUrlChange', () => {
    it('should create an action', () => {
      const payload = {
        search: {
          searchQuery: { all: ['bus driver'] },
          locationQuery: 'Amsterdam',
          pageSize: 15,
          currentPage: 1,
          chosenSorting: 'default',
          sort: 'default'
        }
      };
      const action = new SearchActions.SynchronizeAfterUrlChange(payload);
      expect({ ...action }).toEqual({
        type: SearchActions.SYNC_AFTER_URL_CHANGE,
        payload
      });
      expect(action.type).toEqual(SearchActions.SYNC_AFTER_URL_CHANGE);
    });
  });

  describe('#UpdateFromLegacySearchUrl', () => {
    it('should create an action', () => {
      const payload = {
        search: {
          searchQuery: { all: ['bus driver'] },
          locationQuery: 'Amsterdam',
          pageSize: 15,
          currentPage: 1,
          chosenSorting: 'default',
          sort: 'default'
        }
      };
      const action = new SearchActions.UpdateFromLegacySearchUrl(payload);
      expect({ ...action }).toEqual({
        type: SearchActions.UPDATE_FROM_LEGACY_SEARCH_URL,
        payload
      });
      expect(action.type).toEqual(SearchActions.UPDATE_FROM_LEGACY_SEARCH_URL);
    });
  });
});
