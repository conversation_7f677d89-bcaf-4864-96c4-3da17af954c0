import { Action } from '@ngrx/store';

export const UPDATE_SEARCH = 'UPDATE_SEARCH';
export const CHANGE_SORT = 'CHANGE_SORT';
export const CHANGE_PAGE_SIZE = 'CHANGE_PAGE_SIZE';
export const CHANGE_CURRENT_PAGE = 'CHANGE_CURRENT_PAGE';
export const SYNC_AFTER_URL_CHANGE = 'SYNC_AFTER_URL_CHANGE';
export const UPDATE_FROM_LEGACY_SEARCH_URL = 'UPDATE_FROM_LEGACY_SEARCH_URL';

export class UpdateSearch implements Action {
  readonly type = UPDATE_SEARCH;

  constructor(public payload: any) {}
}

export class ChangeSort implements Action {
  readonly type = CHANGE_SORT;

  constructor(public payload: string) {}
}

export class ChangePageSize implements Action {
  readonly type = CHANGE_PAGE_SIZE;

  constructor(public payload: any) {}
}

export class ChangeCurrentPage implements Action {
  readonly type = CHANGE_CURRENT_PAGE;

  constructor(public payload: number) {}
}

export class SynchronizeAfterUrlChange implements Action {
  readonly type = SYNC_AFTER_URL_CHANGE;

  constructor(public payload: any) {}
}

export class UpdateFromLegacySearchUrl implements Action {
  readonly type = UPDATE_FROM_LEGACY_SEARCH_URL;

  constructor(public payload: any) {}
}

export type SearchActions =
  | UpdateSearch
  | ChangeSort
  | ChangePageSize
  | ChangeCurrentPage
  | SynchronizeAfterUrlChange
  | UpdateFromLegacySearchUrl;
