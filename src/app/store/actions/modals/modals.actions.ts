import { Action } from '@ngrx/store';

export const OPEN_SHARE_PROFILE_MODAL = 'OPEN_SHARE_PROFILE_MODAL';
export const OPEN_SAVE_SEARCH_MODAL = 'OPEN_SAVE_SEARCH_MODAL';
export const OPEN_SEARCH_TIPS_MODAL = 'OP<PERSON>_SEARCH_TIPS_MODAL';
export const CLOSE_MODALS = 'CLOSE_MODALS';

export class OpenShareProfileModal implements Action {
  readonly type = OPEN_SHARE_PROFILE_MODAL;
}

export class OpenSaveSearchModal implements Action {
  readonly type = OPEN_SAVE_SEARCH_MODAL;
}

export class OpenSearchTipsModal implements Action {
  readonly type = OPEN_SEARCH_TIPS_MODAL;
}

export class CloseModals implements Action {
  readonly type = CLOSE_MODALS;
}

export type ModalsActions = OpenShareProfileModal | OpenSaveSearchModal | CloseModals;
