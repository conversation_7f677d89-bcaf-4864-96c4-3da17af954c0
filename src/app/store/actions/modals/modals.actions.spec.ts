import * as ModalsActions from './modals.actions';

describe('ModalsActions', () => {
  describe('#OpenShareProfileModal', () => {
    it('should create an action', () => {
      const action = new ModalsActions.OpenShareProfileModal();
      expect({ ...action }).toEqual({
        type: ModalsActions.OPEN_SHARE_PROFILE_MODAL
      });
      expect(action.type).toEqual(ModalsActions.OPEN_SHARE_PROFILE_MODAL);
    });
  });

  describe('#OpenSaveSearchModal', () => {
    it('should create an action', () => {
      const action = new ModalsActions.OpenSaveSearchModal();
      expect({ ...action }).toEqual({
        type: ModalsActions.OPEN_SAVE_SEARCH_MODAL
      });
      expect(action.type).toEqual(ModalsActions.OPEN_SAVE_SEARCH_MODAL);
    });
  });

  describe('#OpenSearchTipsModal', () => {
    it('should create an action', () => {
      const action = new ModalsActions.OpenSearchTipsModal();
      expect({ ...action }).toEqual({
        type: ModalsActions.OPEN_SEARCH_TIPS_MODAL
      });
      expect(action.type).toEqual(ModalsActions.OPEN_SEARCH_TIPS_MODAL);
    });
  });

  describe('#CloseModals', () => {
    it('should create an action', () => {
      const action = new ModalsActions.CloseModals();
      expect({ ...action }).toEqual({
        type: ModalsActions.CLOSE_MODALS
      });
      expect(action.type).toEqual(ModalsActions.CLOSE_MODALS);
    });
  });
});
