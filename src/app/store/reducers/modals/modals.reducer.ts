import { Action } from '@ngrx/store';
import { initialModalsState } from './modals.config';
import { Modals } from '../../models/modals.model';
import * as ModalsActions from '../../actions/modals/modals.actions';

export function modalsReducer(state = initialModalsState, action: Action): Modals {
  switch (action.type) {
    case ModalsActions.OPEN_SHARE_PROFILE_MODAL:
      return Object.assign({}, state, {
        displayShareProfileModal: true
      });

    case ModalsActions.OPEN_SAVE_SEARCH_MODAL:
      return Object.assign({}, state, {
        displaySaveSearchModal: true
      });

    case ModalsActions.OPEN_SEARCH_TIPS_MODAL:
      return Object.assign({}, state, {
        displaySearchTipsModal: true
      });

    case ModalsActions.CLOSE_MODALS:
      return Object.assign({}, state, {
        displayShareProfileModal: false,
        displaySaveSearchModal: false,
        displaySearchTipsModal: false
      });

    default:
      return state;
  }
}
