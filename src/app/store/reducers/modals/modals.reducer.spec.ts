import { modalsReducer } from './modals.reducer';
import { Modals } from '../../models/modals.model';
import { initialModalsState } from './modals.config';

describe('modalsReducer', () => {
  let reducer: any, initialState: Modals;
  beforeEach(() => {
    reducer = modalsReducer;
    initialState = {
      displayShareProfileModal: false,
      displaySaveSearchModal: false,
      displaySearchTipsModal: false
    };
  });

  describe('#OPEN_SHARE_PROFILE_MODAL', () => {
    it('should update ', () => {
      const newState = reducer(initialState, {
        type: 'OPEN_SHARE_PROFILE_MODAL'
      });
      expect(newState.displayShareProfileModal).toBe(true);
    });
  });

  describe('#OPEN_SAVE_SEARCH_MODAL', () => {
    it('should update ', () => {
      const newState = reducer(initialState, {
        type: 'OPEN_SAVE_SEARCH_MODAL'
      });
      expect(newState.displaySaveSearchModal).toBe(true);
    });
  });

  describe('#OPEN_SEARCH_TIPS_MODAL', () => {
    it('should update ', () => {
      const newState = reducer(initialState, {
        type: 'OPEN_SEARCH_TIPS_MODAL'
      });
      expect(newState.displaySearchTipsModal).toBe(true);
    });
  });

  describe('#CLOSE_MODALS', () => {
    it('should update ', () => {
      const newState = reducer(initialState, {
        type: 'CLOSE_MODALS'
      });

      expect(newState.displayShareProfileModal).toBe(false);
      expect(newState.displaySaveSearchModal).toBe(false);
      expect(newState.displaySearchTipsModal).toBe(false);
    });
  });

  describe('#DEFAULT', () => {
    it('should return current state for unknown actions', () => {
      expect(reducer(undefined, { type: 'UNKNOWN' })).toEqual(initialModalsState);
    });
  });
});
