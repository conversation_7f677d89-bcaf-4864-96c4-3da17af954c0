import { initialFiltersState, parseAggregation } from './filter.config';
import * as FilterActions from '../../actions/filter/filter.actions';
import { Filter, FilterOption } from '../../models/filter.modal';
import * as SearchActions from '../../actions/search/search.actions';
import { ActionWithPayload } from '../../../types/app-actions';
export type Action = FilterActions.FilterActions;

interface FilterReducerState {
  open: boolean;
  filters: Filter[];
}

export function filterReducer(state = initialFiltersState, action: ActionWithPayload): FilterReducerState {
  switch (action.type) {
    case FilterActions.OPEN_FILTER:
      return Object.assign({}, state, { open: true });

    case FilterActions.CLOSE_FILTER:
      return Object.assign({}, state, { open: false });

    case FilterActions.TOGGLE_FILTER_OPTION:
      let filterName = action.payload.filter;
      let filterOption = action.payload.filterOption;
      let newFilters = state.filters.map((filter: Filter) => {
        if (filter.name === filterName) {
          let arr = filter.filterOptions.map((f: FilterOption) => {
            if (f.name === filterOption) {
              f.selected = !f.selected;
            }

            f = Object.assign({}, f);
            return f;
          });

          if (filter.allowNotListedOptions) {
            arr = arr.filter((f: FilterOption) => f.name !== filterOption);
          }

          return Object.assign({}, filter, { filterOptions: arr });
        }
        return filter;
      });
      return Object.assign({}, state, { filters: newFilters });

    case FilterActions.SELECT_FILTER_OPTION:
      filterName = action.payload.filter;
      filterOption = action.payload.filterOption;
      newFilters = state.filters.map((filter: Filter) => {
        if (filter.name === filterName) {
          const arr = filter.filterOptions.map((f: FilterOption) => {
            if (f.name === filterOption) {
              f.selected = !f.selected;
            } else {
              f.selected = false;
            }

            f = Object.assign({}, f);
            return f;
          });
          return Object.assign({}, filter, { filterOptions: arr });
        }
        return filter;
      });
      return Object.assign({}, state, { filters: newFilters });

    case FilterActions.FORCE_SELECT_FILTER_OPTION:
      filterName = action.payload.filter;
      filterOption = action.payload.filterOption;
      newFilters = state.filters.map((filter: Filter) => {
        if (filter.name === filterName) {
          filter.filterOptions.push(filterOption);
        }
        return filter;
      });
      return Object.assign({}, state, { filters: newFilters });

    case FilterActions.AGGREGATIONS_UPDATED:
      newFilters = state.filters.map(filter => {
        if (action.payload[filter.name]) {
          return parseAggregation(action.payload[filter.name].buckets, filter);
        }
        return filter;
      });
      return Object.assign({}, state, { filters: newFilters });

    case FilterActions.CLOSE_ALL_FILTERS:
      newFilters = state.filters.map((filter: Filter) => {
        let filterOptions = filter.filterOptions.map(fO => {
          fO.selected = false;
          return fO;
        });
        if (filter.allowNotListedOptions) {
          filterOptions = [];
        }
        return Object.assign({}, filter, { filterOptions: filterOptions });
      });

      return Object.assign({}, state, { filters: newFilters });

    case SearchActions.SYNC_AFTER_URL_CHANGE:
      return Object.assign({}, state, { filters: action.payload.filters });

    case SearchActions.UPDATE_FROM_LEGACY_SEARCH_URL:
      newFilters = state.filters.map((filter: Filter) => {
        const parsedFilterOptions = action.payload.filters[filter.name];
        if (parsedFilterOptions === undefined) {
          return filter;
        }

        const filterOptions = filter.filterOptions.map(fO => {
          if (parsedFilterOptions.indexOf(fO.name) > -1) {
            fO = Object.assign({}, fO, { selected: true });
          }
          return fO;
        });

        return Object.assign({}, filter, {
          filterOptions: filterOptions
        });
      });
      return Object.assign({}, state, { filters: newFilters });

    case SearchActions.UPDATE_SEARCH:
      if (action.payload.locationQuery === '') {
        state.filters.map((filter: Filter) => {
          if (filter.name === 'radius') {
            filter.filterOptions = filter.filterOptions.map((option: FilterOption) => {
              return option.selected ? Object.assign({}, option, { selected: false }) : option;
            });
          }

          return filter;
        });
      } else {
        state.filters.map((filter: Filter) => {
          if (filter.name === 'provinces') {
            filter.filterOptions = filter.filterOptions.map((option: FilterOption) => {
              return option.selected ? Object.assign({}, option, { selected: false }) : option;
            });
          }

          return filter;
        });
      }
      return Object.assign({}, state);

    default:
      return state;
  }
}
