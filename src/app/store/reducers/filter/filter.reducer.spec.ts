import { filterReducer } from './filter.reducer';
import { Filter, FilterTypes } from '../../models/filter.modal';
import { initialFiltersState } from './filter.config';

describe('filterReducer', () => {
  let reducer: any, initialState: any, radiusFilter: Filter, provincesFilter: Filter;

  beforeEach(() => {
    reducer = filterReducer;

    radiusFilter = {
      filterOptions: [
        { name: 'Option 1', order: 1, count: '0', selected: true },
        { name: 'Option 2', order: 2, count: '0', selected: false },
        { name: 'Option 3', order: 3, count: '0', selected: false },
        { name: 'Option 4', order: 4, count: '0', selected: false }
      ],
      name: 'radius',
      title: 'Filter 1',
      open: true,
      type: FilterTypes.Dropdown
    };

    provincesFilter = {
      filterOptions: [
        { name: '<PERSON>ent<PERSON>', order: 1, count: '0', selected: true },
        { name: 'Flevoland', order: 2, count: '0', selected: false },
        { name: 'Friesland', order: 3, count: '0', selected: false }
      ],
      name: 'provinces',
      title: 'Provincies',
      open: true,
      type: FilterTypes.CheckBox
    };

    initialState = {
      open: false,
      filters: [
        radiusFilter,
        {
          filterOptions: [{ name: 'Option 1', order: 1, count: '0', selected: true }],
          name: 'filter_2',
          title: 'Filter 2',
          open: true
        },
        {
          filterOptions: [{ name: 'Option 1', order: 1, count: '0', selected: true }],
          name: 'filter_3',
          title: 'Filter 3',
          open: true,
          allowNotListedOptions: true
        },
        provincesFilter
      ]
    };
  });

  describe('#OPEN_FILTER', () => {
    it('should change state to true on OPEN_FILTER action', () => {
      expect(reducer(initialState, { type: 'OPEN_FILTER' }).open).toBe(true);
    });
  });

  describe('#CLOSE_FILTER', () => {
    it('should change state to false on CLOSE_FILTER action', () => {
      expect(reducer(initialState, { type: 'CLOSE_FILTER' }).open).toBe(false);
    });
  });

  describe('#TOGGLE_FILTER_OPTION', () => {
    it('should reverse selected value of filterOption that is toggled', () => {
      const newState = reducer(initialState, {
        type: 'TOGGLE_FILTER_OPTION',
        payload: {
          filter: 'radius',
          filterOption: 'Option 2'
        }
      });
      expect(newState.filters[0].filterOptions[1].selected).toBe(true);
    });

    it('should filter out options when a filter allows options not in filterOptions original state', () => {
      const newState = reducer(initialState, {
        type: 'TOGGLE_FILTER_OPTION',
        payload: {
          filter: 'filter_3',
          filterOption: 'Option 1'
        }
      });
      expect(newState.filters[2].filterOptions.length).toBe(0);
    });

    it('should not reverse selected value of filterOption that are not toggled', () => {
      const newState = reducer(initialState, {
        type: 'TOGGLE_FILTER_OPTION',
        payload: {
          filter: 'radius',
          filterOption: 'Option 2'
        }
      });
      expect(newState.filters[0].filterOptions[0].selected).toBe(true);
      expect(newState.filters[0].filterOptions[2].selected).toBe(false);
    });

    it('should not reverse selected value of filters that are not toggled', () => {
      const newState = reducer(initialState, {
        type: 'TOGGLE_FILTER_OPTION',
        payload: {
          filter: 'radius',
          filterOption: 'Option 1'
        }
      });
      expect(newState.filters[0].filterOptions[0].selected).toBe(false);
    });
  });

  describe('#SELECT_FILTER_OPTION', () => {
    it('should select value of filterOption', () => {
      const newState = reducer(initialState, {
        type: 'SELECT_FILTER_OPTION',
        payload: {
          filter: 'radius',
          filterOption: 'Option 2'
        }
      });
      expect(newState.filters[0].filterOptions[1].selected).toBe(true);
    });

    it('should deselect other filter options', () => {
      const newState = reducer(initialState, {
        type: 'SELECT_FILTER_OPTION',
        payload: {
          filter: 'radius',
          filterOption: 'Option 2'
        }
      });
      expect(newState.filters[0].filterOptions[0].selected).toBe(false);
      expect(newState.filters[0].filterOptions[2].selected).toBe(false);
    });
  });

  describe('#FORCE_SELECT_FILTER_OPTION', () => {
    it('should push a new filterOption to the list', () => {
      const newState = reducer(initialState, {
        type: 'FORCE_SELECT_FILTER_OPTION',
        payload: {
          filter: 'radius',
          filterOption: { name: 'Option 5', selected: true }
        }
      });
      expect(newState.filters[0].filterOptions[4].selected).toBe(true);
    });
  });

  describe('#SYNC_AFTER_URL_CHANGE', () => {
    it('should replace the filters in the state with the new Filters from the payload', () => {
      const newState = reducer(initialState, {
        type: 'SYNC_AFTER_URL_CHANGE',
        payload: { filters: 'newState' }
      });
      expect(newState.filters).toBe('newState');
    });
  });

  describe('#CLOSE_ALL_FILTERS', () => {
    it('should return current state for unknown actions', () => {
      const newState = reducer(initialState, { type: 'CLOSE_ALL_FILTERS' });

      expect(newState.filters[0].filterOptions[0].selected).toBe(false);
    });

    it('should empty all the filter options if this filter allows not listed options', () => {
      const newState = reducer(initialState, { type: 'CLOSE_ALL_FILTERS' });
      expect(newState.filters[2].filterOptions.length).toBe(0);
    });
  });

  describe('#AGGREGATIONS_UPDATED', () => {
    let payload: any;
    beforeEach(() => {
      payload = {
        radius: {
          buckets: [{ doc_count: 10, key: 'Option 1' }]
        }
      };
    });

    it('should update aggregations for returned filters', () => {
      const state = reducer(initialState, {
        type: 'AGGREGATIONS_UPDATED',
        payload
      });
      expect(state.filters[0].filterOptions[0].count).toBe('10');
    });

    it('should reset all filters not returned to 0 doc dount', () => {
      initialState.filters[0].filterOptions[1].count = 10;
      const state = reducer(initialState, {
        type: 'AGGREGATIONS_UPDATED',
        payload
      });
      expect(state.filters[0].filterOptions[1].count).toBe('0');
    });

    it('should not set aggregations not in the initial set', () => {
      payload.radius.buckets.push({ doc_count: 0, key: 'Not existing key' });
      const state = reducer(initialState, {
        type: 'AGGREGATIONS_UPDATED',
        payload
      });
      expect(state.filters[0].filterOptions.length).toBe(4);
    });

    it('should format larger doc counts than 999 as x.xxx (for example 1.000)', () => {
      payload.radius.buckets[0].doc_count = 1000;
      const state = reducer(initialState, {
        type: 'AGGREGATIONS_UPDATED',
        payload
      });
      expect(state.filters[0].filterOptions[0].count).toBe('1.000');
    });

    it('should format larger doc counts than 9999 as xxK (for example 10 K)', () => {
      payload.radius.buckets[0].doc_count = 10000;
      const state = reducer(initialState, {
        type: 'AGGREGATIONS_UPDATED',
        payload
      });
      expect(state.filters[0].filterOptions[0].count).toBe('10K');
    });

    it('should format larger doc counts than 999999 as xxM (for example 10 M)', () => {
      payload.radius.buckets[0].doc_count = 1000000;
      const state = reducer(initialState, {
        type: 'AGGREGATIONS_UPDATED',
        payload
      });
      expect(state.filters[0].filterOptions[0].count).toBe('1M');
    });
  });

  describe('#UPDATE_FROM_LEGACY_SEARCH_URL', () => {
    let payload: any;
    beforeEach(() => {
      payload = {
        filters: {
          radius: ['Option 3', 'Option 2']
        }
      };
    });

    it('should set all filters in the payload to selected', () => {
      const state = reducer(initialState, {
        type: 'UPDATE_FROM_LEGACY_SEARCH_URL',
        payload
      });
      expect(state.filters[0].filterOptions[1].selected).toBe(true);
      expect(state.filters[0].filterOptions[2].selected).toBe(true);
    });

    it('should leave all filters not in the payload not selected', () => {
      const state = reducer(initialState, {
        type: 'UPDATE_FROM_LEGACY_SEARCH_URL',
        payload
      });
      expect(state.filters[0].filterOptions[3].selected).toBe(false);
    });
  });

  describe('#UPDATE_SEARCH', () => {
    it('should deselect all options of radius filter when location query is empty', () => {
      const state = reducer(initialState, {
        type: 'UPDATE_SEARCH',
        payload: { locationQuery: '' }
      });

      state.filters.forEach((filter: Filter) => {
        if (filter.name === 'radius') {
          filter.filterOptions.forEach(filterOption => {
            expect(filterOption.selected).toBe(false);
          });
        }
      });
    });

    it('should not modify options of radius filter if location query is not empty', () => {
      const state = reducer(initialState, {
        type: 'UPDATE_SEARCH',
        payload: { locationQuery: 'Amsterdam' }
      });

      state.filters.forEach((filter: Filter) => {
        if (filter.name === 'radius') {
          expect(filter).toBe(radiusFilter);
        }
      });
    });

    it('should deselect all options of provinces filter when location query is not empty', () => {
      const state = reducer(initialState, {
        type: 'UPDATE_SEARCH',
        payload: { locationQuery: 'Amsterdam' }
      });

      state.filters.forEach((filter: Filter) => {
        if (filter.name === 'provinces') {
          filter.filterOptions.forEach(filterOption => {
            expect(filterOption.selected).toBe(false);
          });
        }
      });
    });

    it('should not modify options of provinces filter if location query is empty', () => {
      const state = reducer(initialState, {
        type: 'UPDATE_SEARCH',
        payload: { locationQuery: '' }
      });

      state.filters.forEach((filter: Filter) => {
        if (filter.name === 'provinces') {
          expect(filter).toBe(provincesFilter);
        }
      });
    });
  });

  describe('#DEFAULT', () => {
    it('should return current state for unknown actions', () => {
      expect(reducer(undefined, { type: 'DEFAULT' })).toEqual(initialFiltersState);
    });
  });
});
