import { parseAggregation } from './filter.config';
import { Filter } from '../../models/filter.modal';

describe('#parseAggregation', () => {
  const unknownBuckets = [{ doc_count: 5, key: 'Roma' }];
  const knownBuckets = [{ doc_count: 9, key: 'Gelderland' }, { doc_count: 0, key: '<PERSON>ent<PERSON>' }];
  const baseFilter: Filter = {
    filterOptions: [
      { name: '<PERSON><PERSON><PERSON>', order: 1, count: '0', selected: false },
      { name: 'Flevoland', order: 2, count: '0', selected: false },
      { name: 'Friesland', order: 3, count: '0', selected: false },
      { name: 'Gelderland', order: 4, count: '0', selected: false },
      { name: 'Groningen', order: 5, count: '0', selected: false }
    ],
    name: 'provinces',
    title: 'Provincies',
    open: true,
    type: null
  };

  let filter: Filter;

  beforeEach(() => {
    filter = {
      filterOptions: [
        { name: '<PERSON><PERSON><PERSON>', order: 1, count: '0', selected: false },
        { name: '<PERSON>levoland', order: 2, count: '0', selected: false },
        { name: 'Fries<PERSON>', order: 3, count: '0', selected: false },
        { name: 'Gelderland', order: 4, count: '0', selected: false },
        { name: 'Groningen', order: 5, count: '0', selected: false }
      ],
      name: 'provinces',
      title: 'Provincies',
      open: true,
      type: null
    };
  });

  it('should not change filter if not related aggregations', () => {
    expect(parseAggregation(unknownBuckets, filter)).toEqual(baseFilter);
  });

  it('should change filter if related aggregations', () => {
    expect(parseAggregation(knownBuckets, filter)).not.toEqual(baseFilter);
  });
});
