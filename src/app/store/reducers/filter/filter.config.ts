import { languages } from '../../../classes/languages';
import { environment } from '../../../../environments/environment';
import { Filter, FilterOption } from '../../models/filter.modal';

enum FilterTypes {
  CheckBox = 0,
  Dropdown = 1,
  AutoComplete = 2
}

export const initialFiltersState = {
  open: false,
  filters: [
    {
      filterOptions: [
        {
          value: '5',
          name: '*-5.0',
          title: '5 km',
          order: 1,
          count: '0',
          selected: false
        },
        {
          value: '10',
          name: '*-10.0',
          title: '10 km',
          order: 2,
          count: '0',
          selected: false
        },
        {
          value: '20',
          name: '*-20.0',
          title: '20 km',
          order: 3,
          count: '0',
          selected: false
        },
        {
          value: '30',
          name: '*-30.0',
          title: '30 km',
          order: 4,
          count: '0',
          selected: false
        },
        {
          value: '50',
          name: '*-50.0',
          title: '50 km',
          order: 5,
          count: '0',
          selected: false
        },
        {
          value: '75',
          name: '*-75.0',
          title: '75 km',
          order: 6,
          count: '0',
          selected: false
        },
        {
          value: '1000',
          name: '*-1000.0',
          title: 'He<PERSON> Nederland',
          order: 7,
          count: '0',
          selected: false
        }
      ],
      name: 'radius',
      title: 'Afstand tot werklocatie',
      open: true,
      type: FilterTypes.Dropdown,
      defaultSelectedValue: '*-1000.0'
    },
    {
      filterOptions: [
        { name: 'Drenthe', order: 1, count: '0', selected: false },
        { name: 'Flevoland', order: 2, count: '0', selected: false },
        { name: 'Friesland', order: 3, count: '0', selected: false },
        { name: 'Gelderland', order: 4, count: '0', selected: false },
        { name: 'Groningen', order: 5, count: '0', selected: false },
        { name: 'Limburg', order: 6, count: '0', selected: false },
        { name: 'Noord-Brabant', order: 7, count: '0', selected: false },
        { name: 'Noord-Holland', order: 8, count: '0', selected: false },
        { name: 'Overijssel', order: 9, count: '0', selected: false },
        { name: 'Utrecht', order: 10, count: '0', selected: false },
        { name: 'Zeeland', order: 11, count: '0', selected: false },
        { name: 'Zuid-Holland', order: 12, count: '0', selected: false }
      ],
      name: 'provinces',
      title: 'Provincies',
      open: true,
      type: FilterTypes.CheckBox
    },
    {
      filterOptions: [
        {
          name: 'Afgelopen 24 uur',
          title: 'Laatste 24 uur',
          order: 1,
          count: '0',
          selected: false
        },
        {
          name: 'Afgelopen 3 dagen',
          title: 'Laatste 3 dagen',
          order: 2,
          count: '0',
          selected: false
        },
        {
          name: 'Afgelopen week',
          title: 'Laatste week',
          order: 3,
          count: '0',
          selected: false
        },
        {
          name: 'Afgelopen maand',
          title: 'Laatste maand',
          order: 4,
          count: '0',
          selected: false
        },
        {
          name: 'Afgelopen 3 maanden',
          title: 'Laatste 3 maanden',
          order: 5,
          count: '0',
          selected: false
        },
        {
          name: 'Afgelopen 6 maanden',
          title: 'Laatste 6 maanden',
          order: 6,
          count: '0',
          selected: false
        },
        {
          name: 'Afgelopen jaar',
          title: 'Laatste jaar',
          order: 7,
          count: '0',
          selected: false
        },
        { name: 'Alles', title: 'Alles', order: 8, count: '0', selected: false }
      ],
      name: 'updatedDate',
      title: 'Wijzigingsdatum',
      open: true,
      type: FilterTypes.Dropdown,
      defaultSelectedValue: 'Alles'
    },
    {
      filterOptions: [
        {
          name: 'Administratief/Secretarieel',
          order: 1,
          count: '0',
          selected: false
        },
        {
          name: 'Automatisering/Internet',
          order: 2,
          count: '0',
          selected: false
        },
        { name: 'Beleid/Bestuur/Staf', order: 3, count: '0', selected: false },
        {
          name: 'Beveiliging/Defensie/Politie',
          order: 4,
          count: '0',
          selected: false
        },
        { name: 'Commercieel/Verkoop', order: 5, count: '0', selected: false },
        { name: 'Consultancy/Advies', order: 6, count: '0', selected: false },
        {
          name: 'Design/Creatie/Journalistiek',
          order: 7,
          count: '0',
          selected: false
        },
        {
          name: 'Directie/Management algemeen',
          order: 8,
          count: '0',
          selected: false
        },
        {
          name: 'Financieel/Accounting',
          order: 9,
          count: '0',
          selected: false
        },
        {
          name: 'Financiele dienstverlening',
          order: 10,
          count: '0',
          selected: false
        },
        {
          name: 'HR/Training/Opleiding',
          order: 11,
          count: '0',
          selected: false
        },
        { name: 'Horeca/Detailhandel', order: 12, count: '0', selected: false },
        {
          name: 'Inkoop/Logistiek/Transport',
          order: 13,
          count: '0',
          selected: false
        },
        { name: 'Juridisch', order: 14, count: '0', selected: false },
        {
          name: 'Klantenservice/Callcenter/Receptie',
          order: 15,
          count: '0',
          selected: false
        },
        {
          name: 'Marketing/PR/Communicatie',
          order: 16,
          count: '0',
          selected: false
        },
        { name: 'Medisch/Zorg', order: 17, count: '0', selected: false },
        {
          name: 'Onderwijs/Onderzoek/Wetenschap',
          order: 18,
          count: '0',
          selected: false
        },
        { name: 'Overig', order: 19, count: '0', selected: false },
        {
          name: 'Productie/Uitvoerend',
          order: 20,
          count: '0',
          selected: false
        },
        { name: 'Techniek', order: 21, count: '0', selected: false }
      ],
      name: 'functionGroups',
      title: 'Functiegroep',
      open: true,
      collapsible: true,
      numberOfShownItems: 5,
      type: FilterTypes.CheckBox
    },
    environment.filters.workLevels,
    {
      filterOptions: [
        { name: 'Tot 16 uur', order: 1, count: '0', selected: false },
        { name: '16 tot 24 uur', order: 2, count: '0', selected: false },
        { name: '24 tot 32 uur', order: 3, count: '0', selected: false },
        { name: '32 tot en met 40 uur', order: 4, count: '0', selected: false }
      ],
      name: 'workingHours',
      title: 'Aantal uren',
      open: true,
      type: FilterTypes.CheckBox
    },
    {
      filterOptions: [
        { name: 'Starter', order: 1, count: '0', selected: false },
        { name: 'Ervaren', order: 2, count: '0', selected: false },
        { name: 'Leidinggevend', order: 3, count: '0', selected: false },
        { name: 'Senior management', order: 4, count: '0', selected: false },
        { name: 'Directie', order: 5, count: '0', selected: false }
      ],
      name: 'careerLevel',
      title: 'Carrièreniveau',
      open: true,
      type: FilterTypes.CheckBox
    },
    {
      filterOptions: [
        { name: '< 1.750', order: 1, count: '0', selected: false },
        { name: '1.750 - 2.500', order: 2, count: '0', selected: false },
        { name: '2.500 - 3.500', order: 3, count: '0', selected: false },
        { name: '3.500 - 5.000', order: 4, count: '0', selected: false },
        { name: '5.000 - 7.000', order: 5, count: '0', selected: false },
        { name: '> 7.000', order: 6, count: '0', selected: false },
        { name: 'Niet opgegeven', order: 7, count: '0', selected: false }
      ],
      name: 'requestedSalary',
      title: 'Gewenst salaris',
      open: true,
      type: FilterTypes.CheckBox
    },
    {
      filterOptions: [
        { name: 'Per direct', order: 1, count: '0', selected: false },
        { name: 'In overleg', order: 2, count: '0', selected: false }
      ],
      name: 'availability',
      title: 'Beschikbaarheid',
      open: true,
      type: FilterTypes.CheckBox
    },
    {
      filterOptions: [
        { name: 'A - motor', order: 1, count: '0', selected: false },
        { name: 'B - personenauto', order: 2, count: '0', selected: false },
        { name: 'C - vrachtwagen', order: 3, count: '0', selected: false },
        { name: 'D - bus >8 personen', order: 4, count: '0', selected: false },
        { name: 'T - trekkerrijbewijs', order: 5, count: '0', selected: false }
      ],
      name: 'driverLicenses',
      title: 'Rijbewijzen',
      open: true,
      type: FilterTypes.CheckBox
    },
    {
      filterOptions: languages.map((l, index) => {
        return { name: l, order: index, count: '0', selected: false };
      }),
      name: 'languages',
      title: 'Talen',
      open: true,
      type: FilterTypes.AutoComplete,
      action: 'FETCH_LANGUAGES',
      resultAction: 'FETCHED_LANGUAGES',
      placeholder: 'Voeg een taal toe...'
    }
  ]
};

export function parseAggregation(aggregation: any, filter: Filter): Filter {
  const filterOptions = filter.filterOptions.map((f: FilterOption) => {
    return Object.assign({}, f, { count: format(0) });
  });

  for (const a of aggregation) {
    const index = filterOptions.findIndex(f => f.name === a.key);

    if (index > -1) {
      filterOptions[index].count = format(a.doc_count);
    }
  }
  filter.filterOptions = filterOptions;
  return Object.assign({}, filter);
}

function format(number: number): string {
  if (number > 999999) {
    return Math.round(number / 100000) / 10 + 'M';
  }
  if (number > 9999) {
    return Math.floor(number / 1000) + 'K';
  }

  return number.toString().replace(/\B(?=(\d{3})+(?!\d))/, '.');
}
