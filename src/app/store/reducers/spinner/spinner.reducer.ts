import { Action } from '@ngrx/store';
import { initialSpinnerState } from './spinner.config';
import { Spinner } from '../../models/spinner.modal';
import * as SpinnerActions from '../../actions/spinner/spinner.actions';

export function spinnerReducer(state = initialSpinnerState, action: Action): Spinner {
  switch (action.type) {
    case SpinnerActions.SHOW_MAIN_SPINNER:
      return Object.assign({}, state, { mainSpinnerShown: true });

    case SpinnerActions.HIDE_MAIN_SPINNER:
      return Object.assign({}, state, { mainSpinnerShown: false });

    default:
      return state;
  }
}
