import { Spinner } from '../../models/spinner.modal';
import { spinnerReducer } from './spinner.reducer';

describe('spinnerReducer', () => {
  let reducer: any, initialState: Spinner;

  beforeEach(() => {
    reducer = spinnerReducer;
    initialState = {
      mainSpinnerShown: false
    };
  });

  describe('#SHOW_MAIN_SPINNER', () => {
    it('should show the spinner', () => {
      const shownSpinner = {
        mainSpinnerShown: true
      };

      const newState = reducer(initialState, {
        type: 'SHOW_MAIN_SPINNER'
      });
      expect(newState).toEqual(shownSpinner);
    });
  });

  describe('#HIDE_MAIN_SPINNER', () => {
    it('should hide the spinner', () => {
      const hiddenSpinner = {
        mainSpinnerShown: false
      };

      const newState = reducer(initialState, {
        type: 'HIDE_MAIN_SPINNER'
      });
      expect(newState).toEqual(hiddenSpinner);
    });
  });

  describe('#DEFAULT', () => {
    it('should behave default', () => {
      const newState = reducer(initialState, {
        type: 'DEFAULT'
      });
      expect(newState).toEqual(initialState);
    });
  });
});
