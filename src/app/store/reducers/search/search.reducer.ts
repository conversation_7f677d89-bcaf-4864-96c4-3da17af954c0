import { initialSearchState, isSearchQueryPresent } from './search.config';
import { Search } from '../../models/search.model';
import * as SearchActions from '../../actions/search/search.actions';
import * as FilterActions from '../../../store/actions/filter/filter.actions';
import { ActionWithPayload } from '../../../types/app-actions';

export function searchReducer(state = initialSearchState, action: ActionWithPayload): Search {
  switch (action.type) {
    case SearchActions.UPDATE_SEARCH:
      let sort = state.sort;
      if (!isSearchQueryPresent(action.payload.searchQuery)) {
        sort = 'default';
        state.chosenSorting = 'default';
      } else {
        if (state.chosenSorting === 'default') {
          sort = 'relevance';
        }
      }
      return Object.assign({}, state, {
        currentPage: 1,
        searchQuery: action.payload.searchQuery,
        locationQuery: action.payload.locationQuery,
        sort
      });

    case SearchActions.CHANGE_SORT:
      return Object.assign({}, state, {
        chosenSorting: action.payload,
        sort: action.payload
      });

    case SearchActions.CHANGE_PAGE_SIZE:
      return Object.assign({}, state, {
        pageSize: action.payload.pageSize,
        currentPage: action.payload.newCurrentPage
      });

    case SearchActions.CHANGE_CURRENT_PAGE:
      return Object.assign({}, state, {
        currentPage: action.payload
      });

    case SearchActions.SYNC_AFTER_URL_CHANGE:
      return Object.assign({}, action.payload.search, {
        chosenSorting: state.chosenSorting
      });

    case FilterActions.TOGGLE_FILTER_OPTION:
      return Object.assign({}, state, {
        currentPage: 1
      });

    case SearchActions.UPDATE_FROM_LEGACY_SEARCH_URL:
      const searchString = typeof action.payload.search === 'undefined' ? '' : action.payload.search;

      return Object.assign({}, state, {
        searchQuery: {
          all: [searchString]
        },
        locationQuery: action.payload.locationQuery,
        sort: action.payload.sort,
        chosenSorting: action.payload.sort,
        pageSize: action.payload.pageSize,
        currentPage: action.payload.currentPage
      });

    default:
      return state;
  }
}
