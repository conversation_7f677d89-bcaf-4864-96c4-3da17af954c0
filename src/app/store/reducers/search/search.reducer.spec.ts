import { searchReducer } from './search.reducer';
import { Search } from '../../models/search.model';
import { initialSearchState } from './search.config';

describe('searchReducer', () => {
  let reducer: any, initialState: Search;

  beforeEach(() => {
    reducer = searchReducer;
    initialState = initialSearchState;
  });

  describe('#UPDATE_SEARCH', () => {
    it('should update searchQuery', () => {
      const newState = reducer(initialState, {
        type: 'UPDATE_SEARCH',
        payload: { searchQuery: { all: ['new search query'] } }
      });

      expect(newState.searchQuery).toEqual({ all: ['new search query'] });
    });

    it('should set currentPage to 1', () => {
      const newState = reducer(initialState, {
        type: 'UPDATE_SEARCH',
        payload: { searchQuery: { all: ['new search query'] } }
      });

      expect(newState.currentPage).toBe(1);
    });

    it('should update locationQuery', () => {
      const newState = reducer(initialState, {
        type: 'UPDATE_SEARCH',
        payload: { locationQuery: 'new location query' }
      });
      expect(newState.locationQuery).toBe('new location query');
    });

    it('should set sort to default if the search query is empty', () => {
      initialState.sort = 'relevance';
      initialState.chosenSorting = 'relevance';
      const newState = reducer(initialState, {
        type: 'UPDATE_SEARCH',
        payload: { searchQuery: '' }
      });
      expect(newState.sort).toBe('default');
      expect(newState.chosenSorting).toBe('default');
    });

    it('should set sort to relevance if the search query is not empty', () => {
      initialState.sort = 'default';
      initialState.chosenSorting = 'default';

      const newState = reducer(initialState, {
        type: 'UPDATE_SEARCH',
        payload: { searchQuery: { all: ['new location query'] } }
      });

      expect(newState.sort).toBe('relevance');
    });

    it('should set sort to relevance if the search query is not empty and chosenSorting is default', () => {
      initialState.sort = 'update';
      initialState.chosenSorting = 'default';
      const newState = reducer(initialState, {
        type: 'UPDATE_SEARCH',
        payload: { searchQuery: { all: ['new location query'] } }
      });
      expect(newState.sort).toBe('relevance');
    });

    it('should keep sort if the search query is not empty and chosenSorting is not default', () => {
      initialState.sort = 'update';
      initialState.chosenSorting = 'update';
      const newState = reducer(initialState, {
        type: 'UPDATE_SEARCH',
        payload: { searchQuery: { all: ['new location query'] } }
      });

      expect(newState.sort).toBe('update');
    });
  });

  describe('#CHANGE_SORT', () => {
    it('should change the sort', () => {
      const newState = reducer(initialState, {
        type: 'CHANGE_SORT',
        payload: 'update'
      });
      expect(newState.sort).toBe('update');
    });
  });

  describe('#CHANGE_PAGE_SIZE', () => {
    it('update the pageSize', () => {
      const newState = reducer(initialState, {
        type: 'CHANGE_PAGE_SIZE',
        payload: { pageSize: 25, newCurrentPage: 2 }
      });
      expect(newState.pageSize).toBe(25);
    });

    it('update the currentPage', () => {
      const newState = reducer(initialState, {
        type: 'CHANGE_PAGE_SIZE',
        payload: { pageSize: 25, newCurrentPage: 2 }
      });
      expect(newState.currentPage).toBe(2);
    });
  });

  describe('#CHANGE_CURRENT_PAGE', () => {
    it('should update the current page', () => {
      const newState = reducer(initialState, {
        type: 'CHANGE_CURRENT_PAGE',
        payload: 15
      });
      expect(newState.currentPage).toBe(15);
    });
  });

  describe('#SYNC_AFTER_URL_CHANGE', () => {
    it('should replace the entire state with the new Search from the payload', () => {
      const newState = reducer(initialSearchState, {
        type: 'SYNC_AFTER_URL_CHANGE',
        payload: { search: {} }
      });
      expect(newState).toEqual({ chosenSorting: initialSearchState.chosenSorting });
    });

    it('should set chosenSorting to default', () => {
      const newState = reducer(initialSearchState, {
        type: 'SYNC_AFTER_URL_CHANGE',
        payload: { search: {} }
      });
      expect(newState.chosenSorting).toEqual(initialSearchState.chosenSorting);
    });
  });

  describe('#TOGGLE_FILTER_OPTION', () => {
    it('should navigate to the first page', () => {
      const newState = reducer(
        {},
        {
          type: 'TOGGLE_FILTER_OPTION'
        }
      );
      expect(newState.currentPage).toEqual(1);
    });
  });

  describe('#UPDATE_FROM_LEGACY_SEARCH_URL', () => {
    it('should set the parameters set in the payload', () => {
      const payload = {
        search: 'search',
        locationQuery: 'location',
        sort: 'relevance',
        chosenSorting: 'relevance',
        pageSize: 15,
        currentPage: 1
      };

      expect(
        reducer(initialState, {
          type: 'UPDATE_FROM_LEGACY_SEARCH_URL',
          payload
        })
      ).toEqual({
        searchQuery: { all: ['search'] },
        locationQuery: 'location',
        sort: 'relevance',
        chosenSorting: 'relevance',
        pageSize: 15,
        currentPage: 1
      });
    });

    it('should set search to empty string if not provided', () => {
      const payload = {
        locationQuery: 'location',
        sort: 'relevance',
        chosenSorting: 'relevance',
        pageSize: 15,
        currentPage: 1
      };

      expect(
        reducer(initialState, {
          type: 'UPDATE_FROM_LEGACY_SEARCH_URL',
          payload
        })
      ).toEqual({
        searchQuery: { all: [''] },
        locationQuery: 'location',
        sort: 'relevance',
        chosenSorting: 'relevance',
        pageSize: 15,
        currentPage: 1
      });
    });
  });

  describe('#DEFAULT', () => {
    it('should return current state for unknown actions', () => {
      expect(reducer(undefined, { type: 'DEFAULT' })).toEqual(initialSearchState);
    });
  });
});
