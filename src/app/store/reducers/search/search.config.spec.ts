import { isSearchQueryPresent } from './search.config';

describe('#isSearchQueryPresent', () => {
  it('should return true if there is at least one query ', () => {
    const searchQuery = { all: ['query'] };

    expect(isSearchQueryPresent(searchQuery)).toBe(true);
  });

  it('should return false if there is no none empty query ', () => {
    const searchQuery = { all: [''] };

    expect(isSearchQueryPresent(searchQuery)).toBe(false);
  });

  it('should ignore not own properties of search query object', () => {
    class SearchQuery {
      one = 1;
    }

    SearchQuery.prototype = <any>{ two: 2 };

    const searchQuery = new SearchQuery();

    expect(isSearchQueryPresent(<any>searchQuery)).toBe(false);
  });
});
