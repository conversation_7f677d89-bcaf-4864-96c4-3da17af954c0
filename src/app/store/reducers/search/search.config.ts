import { Search } from '../../models/search.model';

export const initialSearchState: Search = {
  searchQuery: {
    all: ['']
  },
  locationQuery: '',
  sort: 'default',
  chosenSorting: 'default',
  pageSize: 15,
  currentPage: 1
};

export function isSearchQueryPresent(searchQuery: { [key: string]: string[] }): boolean {
  let searchQueryPresent = false;

  for (const property in searchQuery) {
    if (searchQuery.hasOwnProperty(property) && searchQuery[property].length > 0) {
      searchQuery[property].forEach(value => {
        if (value.length > 0) {
          searchQueryPresent = true;
        }
      });
    }
  }

  return searchQueryPresent;
}
