import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ProfileNavigationService, WindowService } from '../shared/services';
import { environment } from '../../environments/environment';

@Component({
  selector: 'app-error',
  templateUrl: './error.component.html',
  styleUrls: ['./error.component.scss']
})
export class ErrorComponent implements OnInit {
  public helpdeskEmail = environment['email']['helpdesk'];
  public errorType: string;

  constructor(public profileNavigation: ProfileNavigationService, private windowService: WindowService, private route: ActivatedRoute) {}

  ngOnInit(): void {
    this.windowService.scrollTo(0, 0);

    this.route.queryParams.subscribe(params => {
      this.errorType = params.type;
    });
  }

  goBackToPreviousPage(): void {
    window.history.back();
  }
}
