<section class="error-component">
  <div class="error-component-body">
    <header>
      <div class="header-body" [ngSwitch]="errorType">
        <div *ngSwitchCase="'jobSeekerNotFound'">
          <h1>Het profiel is niet gevonden</h1>
          <p class="lead">
              Het profiel is verwijderd of op “niet zichtbaar” gezet.
          </p>
          <a href="javascript:;"
            class="btn btn-primary"
            *ngIf="profileNavigation.isProfileOpenedFromSearchResults()"
            (click)="profileNavigation.goToSearchResultsPage()">Terug naar zoekresultaten</a>
        </div>
        <div *ngSwitchCase="'internalServerError'">
          <h1>Er is iets misgegaan</h1>
          <p class="lead">
              Het lijkt erop dat zich een technisch probleem heeft voorgedaan. Probeer het nogmaals. Blijft het probeem
              zich voordoen, neem dan contact op.
          </p>
          <a href="javascript:;"
            class="btn btn-primary"
            routerLink="/search" >Homepage</a>
        </div>
        <div *ngSwitchDefault>
          <h1>Er is iets misgegaan…</h1>
          <p class="lead">
            De pagina die je wilt zien kan nu niet getoond worden.
          </p>
          <a href="javascript:;"
            class="btn btn-primary"
            (click)="goBackToPreviousPage()">Vorige pagina</a>
        </div>
      </div>
    </header>

    <main>
      <div class="block"></div>
    </main>

    <footer>
      <div class="footer-body">
        <span>Heb je vragen of opmerkingen? </span>
        <span>Neem dan contact op met <a href="mailto:{{helpdeskEmail}}">{{helpdeskEmail}}</a></span>
      </div>
    </footer>
  </div>
</section>
