import { RavenErrorHandler } from './raven-error';
import * as <PERSON> from 'raven-js';

describe('RavenErrorHandler', () => {
  describe('#handleError', () => {
    it('should capture an exception for Sen<PERSON>', () => {
      spyOn(Raven, 'captureException');
      const ravenErrorHandler = new RavenErrorHandler();
      const error = new Error('error');

      ravenErrorHandler.handleError(error);

      expect(Raven.captureException).toHaveBeenCalledWith(error);
      expect(Raven.captureException).toHaveBeenCalledTimes(1);
    });
  });
});
