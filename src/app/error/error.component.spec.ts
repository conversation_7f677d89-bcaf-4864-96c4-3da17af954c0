import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subject } from 'rxjs';
import { ErrorComponent } from './error.component';
import { ProfileNavigationService, WindowService } from '../shared/services';
import { environment } from '../../environments/environment';

describe('ErrorComponent', () => {
  let stub: any;

  let component: ErrorComponent;
  let fixture: ComponentFixture<ErrorComponent>;

  beforeEach(async(() => {
    stub = {
      ProfileNavigationService: jasmine.createSpyObj('ProfileNavigationService', [
        'isProfileOpenedFromSearchResults',
        'goToSearchResultsPage'
      ]),
      WindowService: jasmine.createSpyObj('WindowService', ['scrollTo']),
      ActivatedRoute: {
        queryParams: new Subject()
      }
    };

    TestBed.configureTestingModule({
      declarations: [ErrorComponent],
      providers: [
        { provide: ProfileNavigationService, useValue: stub.ProfileNavigationService },
        { provide: WindowService, useValue: stub.WindowService },
        { provide: ActivatedRoute, useValue: stub.ActivatedRoute }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    window.history.back = jasmine.createSpy('back');
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ErrorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  describe('#constructor', () => {
    it('should create component', () => {
      expect(component).toBeTruthy();
    });

    it('should have correct helpdesk email', () => {
      expect(component.helpdeskEmail).toEqual(environment['email']['helpdesk']);
    });
  });

  describe('#ngOnInit', () => {
    it('should make search property observing store', () => {
      expect(stub.WindowService.scrollTo).toHaveBeenCalledTimes(1);
      expect(stub.WindowService.scrollTo).toHaveBeenCalledWith(0, 0);
    });

    it('should retrive error type from the query params', () => {
      expect(stub.errorType).toBeUndefined();

      stub.ActivatedRoute.queryParams.next({ type: 'test-type' });

      expect(component.errorType).toEqual('test-type');
    });

    it('should leave error type undefinded when there is no type in the query params', () => {
      stub.ActivatedRoute.queryParams.next({ whatevern: 'test-type' });

      expect(component.errorType).toBeUndefined();
    });
  });

  describe('#goBackToPreviousPage', () => {
    it('should go to the previous page', () => {
      component.goBackToPreviousPage();

      expect(window.history.back).toHaveBeenCalledTimes(1);
      expect(window.history.back).toHaveBeenCalledWith();
    });
  });
});
