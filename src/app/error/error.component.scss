@import "components";

.error-component {
  background: {
    color: $error-background-color;
    image: $error-background-image;
  }

  .error-component-body {
    background: {
      color: $error-body-background-color;
      image: $error-body-background-image;
      position: right bottom;
      repeat: no-repeat;
    }
    overflow: hidden;
    position: relative;

    @include media-breakpoint-up(xl) {
      background-image: $error-body-background-image-lg;
    }
  }

  header {
    display: block;

    .header-body {
      background: {
        image: $error-header-background-image;
        position: left bottom;
        repeat: no-repeat;
      }
      overflow: hidden;

      @include media-breakpoint-up(xl) {
        background-position: center bottom;
      }

      @include media-breakpoint-up(md) {
        background-image: $error-header-background-image-lg;
      }

      h1 {
        color: $error-title-color;
        margin-bottom: 1.5rem;
      }

      p {
        color: $error-description-color;
        font-size: $error-description-font-size;

        @include media-breakpoint-up(md) {
          font-size: $error-description-font-size-lg;
          width: $error-description-width;
        }
      }
    }
  }

  footer {
    background-color: $error-footer-background-color;
    color: $error-footer-color;

    a {
      color: $error-footer-color;
    }

    .footer-body {
      font-size: 1.1rem;
      font-weight: 300;
      line-height: 1.33;
      padding-bottom: $error-footer-padding;
      padding-top: $error-footer-padding;

      @include media-breakpoint-up(md) {
        font-size: 1.5rem;
        line-height: 2.08;
      }
    }
  }
}
