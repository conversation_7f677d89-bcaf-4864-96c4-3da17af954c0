import { NgModule } from '@angular/core';
import { SharedModule } from '../shared/shared.module';
import { ErrorModule } from '../error/error.module';
import { JobSeekerProfileComponent } from './profile.component';
import { NavigationalBarComponent } from './navigational-bar/navigational-bar.component';
import { OperationalBarComponent } from './operational-bar/operational-bar.component';
import { HeaderComponent } from './header/header.component';
import { DemandsComponent } from './demands/demands.component';
import { FunctionsComponent } from './functions/functions.component';
import { ExperienceComponent } from './work-experience/experience.component';
import { EducationComponent } from './education/education.component';
import { TrainingComponent } from './training/training.component';
import { DriversLicensesComponent } from './drivers-licenses/drivers-licenses.component';
import { AttachmentsComponent } from './attachments/attachments.component';
import { FileCVComponent } from './file-cv/file-cv.component';
import { JobSeekerProfileRoutingModule } from './profile-routing.module';
import { LimitedProfileBannerComponent } from './limited-profile-banner/limited-profile-banner.component';
import { PlaceholderComponent } from './placeholder/placeholder.component';

@NgModule({
  imports: [JobSeekerProfileRoutingModule, SharedModule, ErrorModule],
  declarations: [
    JobSeekerProfileComponent,
    NavigationalBarComponent,
    OperationalBarComponent,
    HeaderComponent,
    DemandsComponent,
    FunctionsComponent,
    ExperienceComponent,
    EducationComponent,
    TrainingComponent,
    DriversLicensesComponent,
    AttachmentsComponent,
    FileCVComponent,
    LimitedProfileBannerComponent,
    PlaceholderComponent
  ]
})
export class JobSeekerProfileModule {}
