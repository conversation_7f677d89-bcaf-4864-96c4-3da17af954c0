import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Store } from '@ngrx/store';
import { Subject, Observable } from 'rxjs';
import { takeUntil, map } from 'rxjs/operators';
import { AppState } from '../types/app-state';
import { JobSeeker, Recruiter } from '../classes';
import { SessionProfileViewTrackerService, JobSeekerService, WindowService } from '../shared/services';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss']
})
export class JobSeekerProfileComponent implements OnDestroy {
  public jobSeekerProfile: any = null;
  public jobSeekerProfileCv: string;
  public currentPage: number;
  private componentDestroyed: Subject<any> = new Subject();
  public recruiter: Recruiter;
  public isLimitedProfile = false;

  constructor(
    private route: ActivatedRoute,
    private profileViewTracker: SessionProfileViewTrackerService,
    private store: Store<AppState>,
    private jobSeekerService: JobSeekerService,
    private windowService: WindowService
  ) {
    this.subscribeToStore();
    this.subscribeToRoute();
  }

  ngOnDestroy() {
    this.componentDestroyed.next();
    this.componentDestroyed.unsubscribe();
  }

  private subscribeToStore(): void {
    this.store.pipe(takeUntil(this.componentDestroyed)).subscribe(s => {
      this.currentPage = s.search.currentPage;
    });
  }

  private subscribeToRoute(): void {
    this.route.data.pipe(takeUntil(this.componentDestroyed)).subscribe((data: any) => {
      this.recruiter = data.recruiter;

      this.windowService.scrollTo(0, 0);
      this.jobSeekerProfileCv = null;
      this.jobSeekerProfile = null;

      if (data['jobSeeker'] instanceof JobSeeker) {
        this.isLimitedProfile = typeof data['jobSeeker'].introductionText === 'undefined';

        if (this.isLimitedProfile) {
          const arr = ['experience', 'education', 'training'];
          for (let i = 0; i < 3; i++) {
            const count = Math.floor(Math.random() * (5 - 2) + 2);
            data['jobSeeker'][arr[i]] = [];

            for (let l = 0; l < count; l++) {
              data['jobSeeker'][arr[i]].push({ preview: true });
            }
          }

          data['jobSeeker'].attachments = [{ preview: true }];
        }

        this.profileViewTracker.recordProfileView(data['jobSeeker'].id, this.currentPage);
        this.jobSeekerProfile = this.parseJobSeekerProfile(data['jobSeeker']);
        this.downloadCv();
      }
    });
  }

  downloadCv() {
    if (!this.jobSeekerProfile.fileCvString) {
      return;
    }

    this.jobSeekerService
      .getCv(this.jobSeekerProfile)
      .pipe(takeUntil(this.componentDestroyed))
      .subscribe({
        next: response => {
          this.jobSeekerProfileCv = response.fileCvHtml;
        }
      });
  }

  parseJobSeekerProfile(jobSeeker: JobSeeker): any {
    const parsedJobSeeker: any = Object.assign({}, jobSeeker);
    const lastWorkExperience = jobSeeker.lastExperience();
    const lastEducation = jobSeeker.education.filter(e => e.diploma !== false);

    parsedJobSeeker.lastWorkExperience = lastWorkExperience && lastWorkExperience.functionName;
    parsedJobSeeker.lastWorkExperienceCompany = lastWorkExperience && lastWorkExperience.company;
    parsedJobSeeker.lastEducation = lastEducation.length > 0 ? lastEducation[0] : undefined;
    parsedJobSeeker.site = jobSeeker.getSiteString();
    parsedJobSeeker.cdn = jobSeeker.getCdn();
    parsedJobSeeker.viewedDate = this.profileViewTracker.getProfileViewed(jobSeeker.id, jobSeeker.viewedDate, true);
    return parsedJobSeeker;
  }

  shouldShowWorkExperience(): boolean {
    return !this.isNotMigratedFileCvUser();
  }

  shouldShowEducation(): boolean {
    return !this.isNotMigratedFileCvUser();
  }

  isNotMigratedFileCvUser(): boolean {
    return !!(this.jobSeekerProfile.fileCvString && !this.jobSeekerProfile.migrationStatus);
  }
}
