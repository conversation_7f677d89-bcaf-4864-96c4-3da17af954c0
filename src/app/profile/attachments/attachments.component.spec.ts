import { TestBed, ComponentFixture } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { AttachmentsComponent } from './attachments.component';
import { environment } from '../../../environments/environment';

describe('AttachmentsComponent', () => {
  let componentFixture: ComponentFixture<AttachmentsComponent>, component: AttachmentsComponent;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [AttachmentsComponent],
      providers: [],
      schemas: [NO_ERRORS_SCHEMA]
    });
    TestBed.compileComponents();
    componentFixture = TestBed.createComponent(AttachmentsComponent);
    component = componentFixture.componentInstance;
    componentFixture.detectChanges();
    component.jobseekerId = '1qa-2ws-3ed';
  });

  describe('#constructor', () => {
    it('should exist', () => {
      expect(component).toBeDefined();
    });
  });

  describe('#attachmentUrl', () => {
    it('should return correct attachment url', () => {
      const fileName = 'CertificationScrumMaster.pdf';
      const expectedUrlAttachment =
        environment.profileApi.mainUrl +
        '/api/recruiter/job-seeker/' +
        component.jobseekerId +
        '/attachments/' +
        encodeURIComponent(fileName) +
        '?X-Site=' +
        environment.siteHeader;

      expect(component['attachmentUrl'](fileName)).toEqual(expectedUrlAttachment);
    });
  });
});
