import { Component, Input } from '@angular/core';
import { Attachment } from '../../classes';
import { environment, getLoginUrl } from '../../../environments/environment';

@Component({
  selector: 'app-profile-attachments',
  templateUrl: 'attachments.component.html',
  styleUrls: ['./attachments.component.scss']
})
export class AttachmentsComponent {
  @Input()
  jobseekerId: string;
  @Input()
  attachments: Attachment[];

  loginUrl: string = getLoginUrl();

  protected attachmentUrl(fileName: string) {
    return (
      environment.profileApi.mainUrl +
      '/api/recruiter/job-seeker/' +
      this.jobseekerId +
      '/attachments/' +
      encodeURIComponent(fileName) +
      '?X-Site=' +
      environment.siteHeader
    );
  }
}
