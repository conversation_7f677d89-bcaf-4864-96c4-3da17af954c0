<section id="profile-attachments" class="card attachment" *ngIf="attachments && attachments.length > 0">
    <div class="card-body">
        <h2 class="text-md-center mb-3 mt-3 acc-attachments-header">Bestanden</h2>
        <div class="attachments mx-md-auto">
            <div *ngFor="let attachment of attachments; index as idx" class="d-sm-flex attachments__file py-3">
                <ng-template [ngIf]="!attachment.preview" [ngIfElse]="placehoder">
                    <div class="col-10 col-sm-8 attachments__info">
                        {{ attachment.name ? attachment.name : attachment.fileName}}
                        <span *ngIf="attachment.uploadDate" class="attachments__date-added">
                            toegevoegd op {{ attachment.uploadDate * 1000 | date:'d-M-y' }}
                        </span>
                    </div>
                    <div class="col-2 col-sm-4 d-flex attachments__interactions">
                        <a class="attachment-download"
                           href="{{ attachmentUrl(attachment.fileName) }}"
                           data-gtm="pro-attachment-download">
                            <i class="icon icon-download"></i>
                        </a>
                    </div>
                  </ng-template>

                <ng-template #placehoder>
                    <div class="col-12 col-sm-5">
                        PDF
                    </div>
                    <div class="col-12 col-sm-7 text-sm-right">
                        <a href="{{ loginUrl }}"
                          data-gtm="limited-profile-attachment-login">Log in</a>
                        <span> of </span>
                        <a href="/werkgever/account/nieuw"
                           data-gtm="limited-profile-attachment-register">
                          registreer
                        </a>
                        <span> om te bekijken</span>
                    </div>
                </ng-template>
            </div>
        </div>
    </div>
</section>
