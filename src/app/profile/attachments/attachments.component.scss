@import "components";

.attachments {
  max-width: 800px;

  &__file {
    border-top: 1px solid $profile-attachments-border-color;
    font-weight: 300;

    &:last-child {
      border-bottom: 1px solid $profile-attachments-border-color;
    }
  }

  &__date-added {
    display: block;
  }

  &__info {
    @include media-breakpoint-down(xs) {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }

  &__interactions {
    justify-content: flex-end;

    @include media-breakpoint-down(xs) {
      flex: 0 0 100%;
      justify-content: flex-start;
      margin-top: 0.9375rem;
      max-width: 100%;
    }

    a {
      .icon-download {
        background-image: url("#{$project-icons-folder}download.svg");
        cursor: pointer;
        display: block;
        height: 22px;
        vertical-align: middle;
        width: 22px;
      }
    }
  }
}
