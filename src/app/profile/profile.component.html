<section *ngIf="jobSeekerProfile" class="profile pb-5">

    <app-operational-bar *ngIf="!isLimitedProfile"
                         class="profile__operationalbar--header"
                         [firstName]="jobSeekerProfile?.personalInfo?.firstName"
                         [lastName]="jobSeekerProfile?.personalInfo?.lastName"
                         [phoneNumber]="jobSeekerProfile?.personalInfo?.phoneNumber"
                         [email]="jobSeekerProfile?.personalInfo?.email"
                         [jobseekerId]="jobSeekerProfile?.id"
                         [isFavorite]="jobSeekerProfile?.isFavorite"
                         [isHeader]="true"
                         [jobSeeker]="jobSeekerProfile"></app-operational-bar>

    <app-navigational-bar [sticky]="!isLimitedProfile"></app-navigational-bar>

    <main class="container">
        <div class="card">
            <app-profile-header [name]="(jobSeekerProfile?.personalInfo?.firstName
                                  && jobSeekerProfile?.personalInfo?.lastName)
                                  ? jobSeekerProfile?.personalInfo?.firstName + ' ' + jobSeekerProfile?.personalInfo?.lastName
                                  : 'Profiel preview'"
                                [lastWorkExperience]="jobSeekerProfile?.lastWorkExperience"
                                [lastWorkExperienceCompany]="jobSeekerProfile?.lastWorkExperienceCompany"
                                [lastEducation]="jobSeekerProfile?.lastEducation"
                                [introductionTextHtml]="jobSeekerProfile?.introductionTextHtml"
                                [cdn]="jobSeekerProfile?.cdn"
                                [photo]="jobSeekerProfile?.photo"
                                [site]="jobSeekerProfile?.site"
                                [updatedDate]="jobSeekerProfile?.updatedDate"
                                [viewedDate]="jobSeekerProfile?.viewedDate"></app-profile-header>

            <app-profile-demands [availability]="jobSeekerProfile?.jobCriteria?.availability"
                                 [minWorkingHours]="jobSeekerProfile?.jobCriteria?.minWorkingHours"
                                 [maxWorkingHours]="jobSeekerProfile?.jobCriteria?.maxWorkingHours"
                                 [workLevels]="jobSeekerProfile?.jobCriteria?.workLevels"
                                 [city]="jobSeekerProfile?.jobCriteria?.city"
                                 [radius]="jobSeekerProfile?.jobCriteria?.radius"
                                 [site]="jobSeekerProfile?.site"
                                 [updatedDate]="jobSeekerProfile?.updatedDate"
                                 [viewedDate]="jobSeekerProfile?.viewedDate"></app-profile-demands>
        </div>

        <app-operational-bar *ngIf="!isLimitedProfile"
                             class="profile__operationalbar--body"
                             [firstName]="jobSeekerProfile?.personalInfo?.firstName"
                             [lastName]="jobSeekerProfile?.personalInfo?.lastName"
                             [phoneNumber]="jobSeekerProfile?.personalInfo?.phoneNumber"
                             [email]="jobSeekerProfile?.personalInfo?.email"
                             [jobseekerId]="jobSeekerProfile?.id"
                             [isFavorite]="jobSeekerProfile?.isFavorite"
                             [isHeader]="false"
                             [jobSeeker]="jobSeekerProfile"></app-operational-bar>


        <app-profile-functions [preferredJobs]="jobSeekerProfile?.jobCriteria?.desiredFunctions"
                               [functionGroups]="jobSeekerProfile?.jobCriteria?.functionGroups"></app-profile-functions>

        <app-profile-experience *ngIf="shouldShowWorkExperience()"
                                [experiences]="jobSeekerProfile?.experience"></app-profile-experience>

        <app-profile-education *ngIf="shouldShowEducation()"
                               [educations]="jobSeekerProfile?.education"></app-profile-education>

        <app-profile-training [trainings]="jobSeekerProfile?.training"></app-profile-training>

        <app-profile-licenses *ngIf="!(isLimitedProfile && jobSeekerProfile?.driversLicenses?.length === 0)"
                              [driversLicenses]="jobSeekerProfile?.driversLicenses"></app-profile-licenses>

        <app-profile-attachments [attachments]="jobSeekerProfile?.attachments"
                                 [jobseekerId]="jobSeekerProfile?.id"></app-profile-attachments>

        <app-profile-file-cv *ngIf="jobSeekerProfileCv"
                             [fileCVHtml]="jobSeekerProfileCv"></app-profile-file-cv>
    </main>
</section>

<app-limited-profile-banner *ngIf="isLimitedProfile"
                            [recruiter]="recruiter"></app-limited-profile-banner>
