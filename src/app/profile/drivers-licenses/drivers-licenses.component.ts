import { Component, Input, OnChanges } from '@angular/core';
import { DriversLicense } from '../../classes';
import { driversLicenses } from '../../config/drivers-licenses';

@Component({
  selector: 'app-profile-licenses',
  templateUrl: 'drivers-licenses.component.html',
  styleUrls: ['./drivers-licenses.component.scss']
})
export class DriversLicensesComponent implements OnChanges {
  @Input()
  driversLicenses: DriversLicense[];
  licenses: DriversLicense[] = [];

  ngOnChanges(): void {
    this.licenses = this.mapDriverLicenses(this.driversLicenses);
  }

  mapDriverLicenses(userLicenses: DriversLicense[]): DriversLicense[] {
    const licenses = [];

    for (const license of driversLicenses) {
      const index = userLicenses.findIndex(i => i.type === license.type);
      if (index !== -1) {
        licenses.push(license);
      }
    }

    return licenses;
  }
}
