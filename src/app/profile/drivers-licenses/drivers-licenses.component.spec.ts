import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { DriversLicensesComponent } from './drivers-licenses.component';

describe('DriversLicensesComponent', () => {
  let component: DriversLicensesComponent;
  let fixture: ComponentFixture<DriversLicensesComponent>;
  let stub: any;

  beforeEach(async(() => {
    stub = {
      license: {
        type: 'B - personenauto',
        group: 'B - personenauto'
      }
    };

    TestBed.configureTestingModule({
      imports: [],
      declarations: [DriversLicensesComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DriversLicensesComponent);
    component = fixture.componentInstance;
    component.driversLicenses = [stub.license];
    fixture.detectChanges();
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('#ngOnChanges', () => {
    it('should call map licenses on change', () => {
      spyOn(component, 'mapDriverLicenses').and.returnValue([]);
      component.driversLicenses = [stub.license];

      component.ngOnChanges();

      expect(component.mapDriverLicenses).toHaveBeenCalledWith([stub.license]);
      expect(component.licenses).toEqual([]);
    });
  });

  describe('#ngOnChanges', () => {
    it('should be able to map licenses', () => {
      const result = component.mapDriverLicenses([stub.license]);
      expect(result.length).toBe(1);
      expect(result[0].id).toEqual('b');
    });
  });
});
