import { TestBed, ComponentFixture } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { JobSeekerProfileComponent } from './profile.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { JobSeeker } from '../classes';
import { Subject, of } from 'rxjs';
import { Store } from '@ngrx/store';
import { SessionProfileViewTrackerService, JobSeekerService, WindowService } from '../shared/services';

describe('JobSeekerProfile', () => {
  const stub: any = {};
  const page = 5;
  const displayShareProfileModal = false;
  let componentFixture: ComponentFixture<JobSeekerProfileComponent>, component: JobSeekerProfileComponent;

  beforeEach(() => {
    stub.data = new Subject();
    stub.jobSeeker = new JobSeeker(
      '1',
      1479117411,
      true,
      [],
      [],
      [],
      <any>{},
      <any>{ firstName: 'Ashley', lastName: 'Kendall' },
      'photo',
      null,
      'site',
      'aanvullende info',
      [],
      [],
      [],
      false,
      null,
      'intro *text*',
      'intro <b>text</b>'
    );

    stub.ActivatedRoute = {
      data: stub.data
    };
    stub.WindowService = jasmine.createSpyObj('WindowService', ['scrollTo']);
    stub.SessionProfileViewTrackerService = jasmine.createSpyObj('SessionProfileViewTrackerService', [
      'recordProfileView',
      'getProfileViewed'
    ]);
    stub.SessionProfileViewTrackerService.getProfileViewed.and.returnValue(Date.now());
    stub.Store = of({
      search: { currentPage: page },
      modals: { displayShareProfileModal: displayShareProfileModal }
    });

    stub.StoreObservable = new Subject();
    stub.StoreObservable.next({
      modals: {}
    });

    stub.Store.select = jasmine.createSpy('select');
    stub.Store.select.and.returnValue(stub.StoreObservable.asObservable());

    stub.JobSeekerService = jasmine.createSpyObj('JobSeekerService', ['getCv']);

    TestBed.configureTestingModule({
      declarations: [JobSeekerProfileComponent],
      providers: [
        { provide: ActivatedRoute, useValue: stub.ActivatedRoute },
        {
          provide: SessionProfileViewTrackerService,
          useValue: stub.SessionProfileViewTrackerService
        },
        { provide: Store, useValue: stub.Store },
        { provide: WindowService, useValue: stub.WindowService },
        { provide: JobSeekerService, useValue: stub.JobSeekerService }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    });

    TestBed.compileComponents();
    componentFixture = TestBed.createComponent(JobSeekerProfileComponent);
    componentFixture.detectChanges();
    component = componentFixture.componentInstance;
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('#ngOnDestroy', () => {
    it('should unsubscribe to the component promises', () => {
      component['componentDestroyed'] = jasmine.createSpyObj('componentDestroyed', ['next', 'unsubscribe']);

      component.ngOnDestroy();

      expect(component['componentDestroyed'].next).toHaveBeenCalledTimes(1);
      expect(component['componentDestroyed'].next).toHaveBeenCalledWith();
      expect(component['componentDestroyed'].unsubscribe).toHaveBeenCalledTimes(1);
      expect(component['componentDestroyed'].unsubscribe).toHaveBeenCalledWith();
    });
  });

  describe('#subscribeToStore', () => {
    it('should set current page property from the store', () => {
      component['subscribeToStore']();

      expect(component.currentPage).toBe(page);
    });
  });

  describe('#subscribeToRoute', () => {
    it('should return job seeker data without any error', () => {
      spyOn(component, 'downloadCv');
      stub.data.next({ jobSeeker: stub.jobSeeker });

      expect(component.jobSeekerProfile.id).toBe(stub.jobSeeker.id);
      expect(stub.SessionProfileViewTrackerService.recordProfileView).toHaveBeenCalledTimes(1);
      expect(stub.SessionProfileViewTrackerService.recordProfileView).toHaveBeenCalledWith(stub.jobSeeker.id, page);
      expect(component.downloadCv).toHaveBeenCalledTimes(1);
      expect(component.downloadCv).toHaveBeenCalledWith();
    });

    it('should enable limited profile when applicable', () => {
      delete stub.jobSeeker.introductionText;

      expect(component.isLimitedProfile).toBeFalsy();
      stub.data.next({ jobSeeker: stub.jobSeeker });

      expect(component.isLimitedProfile).toBeTruthy();
    });

    it('should generate fake properties', () => {
      delete stub.jobSeeker.introductionText;

      expect(stub.jobSeeker.experience.length).toEqual(0);
      expect(stub.jobSeeker.education.length).toEqual(0);
      expect(stub.jobSeeker.training.length).toEqual(0);

      stub.data.next({ jobSeeker: stub.jobSeeker });

      expect(component.jobSeekerProfile.experience.length).toBeGreaterThan(1);
      expect(component.jobSeekerProfile.education.length).toBeGreaterThan(1);
      expect(component.jobSeekerProfile.training.length).toBeGreaterThan(1);
    });

    it('should scroll to the top', () => {
      stub.data.next({ jobSeeker: stub.jobSeeker });

      expect(component.jobSeekerProfile.id).toBe(stub.jobSeeker.id);
      expect(stub.WindowService.scrollTo).toHaveBeenCalledTimes(1);
      expect(stub.WindowService.scrollTo).toHaveBeenCalledWith(0, 0);
    });

    it('should return job seeker not found error if job seeker is not found', () => {
      stub.data = new Subject();

      stub.ActivatedRoute = {
        data: stub.data
      };

      const jobSeekerProfileComponent = new JobSeekerProfileComponent(
        stub.ActivatedRoute,
        stub.SessionProfileViewTrackerService,
        stub.Store,
        stub.JobSeekerService,
        stub.WindowService
      );

      stub.data.next({ jobSeeker: 404 });

      expect(jobSeekerProfileComponent.jobSeekerProfile).toBeNull();
      expect(stub.SessionProfileViewTrackerService.recordProfileView).not.toHaveBeenCalled();
    });

    it('should return server error if an internal server error has occurred', () => {
      stub.data = new Subject();

      stub.ActivatedRoute = {
        data: stub.data
      };

      const jobSeekerProfileComponent = new JobSeekerProfileComponent(
        stub.ActivatedRoute,
        stub.SessionProfileViewTrackerService,
        stub.Store,
        stub.JobSeekerService,
        stub.WindowService
      );

      stub.data.next({ jobSeeker: 500 });

      expect(jobSeekerProfileComponent.jobSeekerProfile).toBeNull();
      expect(stub.SessionProfileViewTrackerService.recordProfileView).not.toHaveBeenCalled();
    });
  });

  describe('#downloadCv', () => {
    it('should do nothing when jobseeker is undefined', () => {
      stub.data.next({ jobSeeker: stub.jobSeeker });

      component.downloadCv();

      expect(stub.JobSeekerService.getCv).not.toHaveBeenCalled();
    });

    it('should get CV from jobseeker service', () => {
      const cv = {
        fileCvHtml: '<div>Hello, Jojo boy!</div>'
      };
      stub.jobSeeker.fileCvString = 'blob';
      stub.JobSeekerService.getCv.and.returnValue(of(cv));

      stub.data.next({ jobSeeker: stub.jobSeeker });

      component.downloadCv();

      expect(component.jobSeekerProfileCv).toEqual(cv.fileCvHtml);
    });
  });

  describe('#parseJobSeekerProfile', () => {
    it('should expose jobSeekerProfile', () => {
      expect(component.parseJobSeekerProfile(stub.jobSeeker).personalInfo.firstName).toEqual(stub.jobSeeker.personalInfo.firstName);
    });

    it('should expose the jobseeker latest experience as a string', () => {
      stub.jobSeeker.experience.push({ functionName: 'Tester' });
      expect(component.parseJobSeekerProfile(stub.jobSeeker).lastWorkExperience).toBe('Tester');
    });

    it('should expose last education with a valid diploma', () => {
      const woEducation = { diploma: true, grade: 'WO' };
      const hboEducation = { diploma: false, grade: 'HBO' };
      const havoEducation = { diploma: true, grade: 'HAVO' };
      stub.jobSeeker.education.push(woEducation, hboEducation, havoEducation);
      expect(component.parseJobSeekerProfile(stub.jobSeeker).lastEducation).toBe(woEducation);
    });

    it('should expose last education with a valid diploma, even if not the last of the education list', () => {
      const hboEducation = { diploma: false, grade: 'HBO' };
      const havoEducation = { diploma: true, grade: 'HAVO' };
      stub.jobSeeker.education.push(hboEducation, havoEducation);
      expect(component.parseJobSeekerProfile(stub.jobSeeker).lastEducation).toBe(havoEducation);
    });

    it('should expose the jobseeker last education with a valid diploma', () => {
      const invalidEducation = { diploma: false };
      stub.jobSeeker.education.push(invalidEducation);
      expect(component.parseJobSeekerProfile(stub.jobSeeker).lastEducation).toBeUndefined();
    });

    it('should get the viewed profile date', () => {
      component.parseJobSeekerProfile(stub.jobSeeker);

      expect(stub.SessionProfileViewTrackerService.getProfileViewed).toHaveBeenCalledTimes(1);
      expect(stub.SessionProfileViewTrackerService.getProfileViewed).toHaveBeenCalledWith(
        stub.jobSeeker.id,
        stub.jobSeeker.viewedDate,
        true
      );
    });
  });

  describe('#shouldShowWorkExperience', () => {
    beforeEach(() => {
      stub.JobSeekerService.getCv.and.returnValue(of({}));
    });

    it('should not show timeline workexperience for not-migrated file-cv jobseekers', () => {
      stub.jobSeeker.fileCvString = 'blob';
      stub.jobSeeker.migrationStatus = null;

      stub.data.next({ jobSeeker: stub.jobSeeker });

      expect(component.shouldShowWorkExperience()).toBeFalsy();
    });

    it('should show timeline for migrated file-cv jobseekers', () => {
      stub.jobSeeker.fileCvString = 'blob';
      stub.jobSeeker.migrationStatus = 'migrating';

      stub.data.next({ jobSeeker: stub.jobSeeker });

      expect(component.shouldShowWorkExperience()).toBeTruthy();
    });

    it('should show timeline for new jobseekers', () => {
      stub.jobSeeker.fileCvString = null;
      stub.jobSeeker.migrationStatus = null;

      stub.data.next({ jobSeeker: stub.jobSeeker });

      expect(component.shouldShowWorkExperience()).toBeTruthy();
    });
  });

  describe('#shouldShowEducation', () => {
    beforeEach(() => {
      stub.JobSeekerService.getCv.and.returnValue(of({}));
    });

    it('should not show education list for not-migrated file-cv jobseekers', () => {
      stub.jobSeeker.fileCvString = 'blob';
      stub.jobSeeker.migrationStatus = null;

      stub.data.next({ jobSeeker: stub.jobSeeker });

      expect(component.shouldShowEducation()).toBeFalsy();
    });

    it('should show education list for migrated file-cv jobseekers', () => {
      stub.jobSeeker.fileCvString = 'blob';
      stub.jobSeeker.migrationStatus = 'migrating';

      stub.data.next({ jobSeeker: stub.jobSeeker });

      expect(component.shouldShowEducation()).toBeTruthy();
    });

    it('should show education list for new jobseekers', () => {
      stub.jobSeeker.fileCvString = null;
      stub.jobSeeker.migrationStatus = null;

      stub.data.next({ jobSeeker: stub.jobSeeker });

      expect(component.shouldShowEducation()).toBeTruthy();
    });
  });

  describe('#isNotMigratedFileCvUser', () => {
    it('should return true if not migrated file cv user', () => {
      component.jobSeekerProfile = {
        fileCvString: 'cv html',
        migrationStatus: false
      };

      expect(component.isNotMigratedFileCvUser()).toEqual(true);
    });

    it('should be migrated cv user if migration started', () => {
      component.jobSeekerProfile = {
        fileCvString: 'cv html',
        migrationStatus: true
      };

      expect(component.isNotMigratedFileCvUser()).toEqual(false);
    });

    it('should be migrated cv user if no file cv available', () => {
      component.jobSeekerProfile = {
        fileCvString: null,
        migrationStatus: false
      };

      expect(component.isNotMigratedFileCvUser()).toEqual(false);
    });
  });
});
