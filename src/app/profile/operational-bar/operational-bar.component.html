<div class="operational-bar shadow-sm"
     [ngClass]="{
        'operational-bar--regular': operationalBarRegular,
        'operational-bar--sticky': !operationalBarRegular,
        'operational-bar--inner mt-4 card': !isHeader
     }">
    <div class="operational-bar__container"
         [ngClass]="{'operational-bar__container--inner': !isHeader}">
        <div class="action phone">
            <a *ngIf="phoneNumber" class="phone" [href]="'tel:' + phoneNumber" (click)="onClickPhoneNumber($event)" data-gtm="pro-show-phone">
                {{ phoneNumberShown ? phoneNumber : 'Toon telefoonnummer' }}
            </a>
            <a *ngIf="!phoneNumber" class="phone" href="javascript:">
                &#8212;
            </a>
        </div>

        <div class="action email">
            <a class="email" href="mailto:{{ email }}" data-gtm="pro-email">
                {{ email }}
            </a>
        </div>

        <div class="action download">
            <a class="download" href="javascript:" (click)="onClickDownload()" data-gtm="pro-download">
                <span>download</span>
            </a>
        </div>

        <div class="action share">
            <a class="share" href="javascript:" (click)="openSharerModal()" data-gtm="pro-share">
                <span>deel</span>
            </a>
        </div>

        <div class="action favorite" *ngIf="hasPlatformCoreAccount">
            <a [ngClass]="{'not-bookmarked': !isProfileFavorite(), 'bookmarked': isProfileFavorite()}" href="javascript:"
               (click)="toggleFavorite()"
                data-gtm="pro-bookmark">
                <span>favoriet</span>
            </a>
        </div>
    </div>

    <div class="operational-bar__modal" *ngIf="displayShareProfileModal" (keydown.escape)="closeSharerModal($event)">
        <app-share-profile-modal [jobseekerId]="jobseekerId"
                                 [firstName]="firstName"
                                 [lastName]="lastName"></app-share-profile-modal>
    </div>
</div>

<div [ngClass]="{'operational-bar__space': !operationalBarRegular}"></div>
