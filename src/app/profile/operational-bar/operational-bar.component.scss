@import "components";

.operational-bar {
  background-color: $profile-operationalbar-backgroundcolor;
  height: $operationalbar-height;
  width: 100%;
  z-index: 101;

  &--sticky {
    position: fixed;
    top: 0;
  }

  &--regular {
    position: relative;
  }

  &--inner {
    height: auto;
  }

  &__container {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    height: $operationalbar-height;
    justify-content: space-between;
    margin: 0 auto;
    max-width: 1100px;
    padding: {
      left: 15px;
      right: 15px;
    };
    width: 100%;

    &--inner {
      height: auto;

      > div{
        &.phone, &.email {
          margin-top: 20px;
          width: 100%;
        }

        &.download, &.share, &.favorite, &.unfavorite {
          margin-top: 20px;
          width: 33%;

          span {
            display: block;
            padding-top: 5px;
          }
        }

        a {
          &.phone, &.email, &.download, &.share, &.favorite, &.unfavorite {
            font-weight: 400;
          }
        }
      }
    }

    > div {
      a {
        color: $profile-operationalbar-text-color;
        font: {
          size: $profile-operationalbar-text-size;
          weight: $profile-operationalbar-text-weight;
        }
        height: $profile-operationalbar-icons-size;
        padding-left: 40px;
        position: relative;
        text-transform: lowercase;
        width: $profile-operationalbar-icons-size;

        &:hover {
          color: $profile-operationalbar-text-color;
          text-decoration: none;
        }

        &::before {
          background: {
            position: center center;
            repeat: no-repeat;
          }
          content: "";
          display: block;
          height: $profile-operationalbar-icons-size;
          left: 0;
          position: absolute;
          top: 4px;
          width: $profile-operationalbar-icons-size;
        }

        &.phone::before {
          background-image: url("#{$project-icons-folder}phone-profile.svg");
        }

        &.email::before {
          background-image: url("#{$project-icons-folder}email-profile.svg");
        }

        &.download::before {
          background-image: url("#{$project-icons-folder}download-profile.svg");
        }

        &.share::before {
          background-image: url("#{$project-icons-folder}share-profile.svg");
        }

        &.not-bookmarked::before {
          background-image: url("#{$project-icons-folder}bookmark.svg");
        }

        &.bookmarked::before {
          background-image: url("#{$project-icons-folder}bookmarked.svg");
        }
      }
    }
  }

  &__space {
    height: $operationalbar-height;
    width: 100%;
  }
}
