import { TestBed, ComponentFixture } from '@angular/core/testing';
import { ChangeDetectionStrategy, NO_ERRORS_SCHEMA } from '@angular/core';
import { OperationalBarComponent } from './operational-bar.component';
import { Store } from '@ngrx/store';
import { BehaviorSubject, Subject } from 'rxjs';
import * as ModalsActions from '../../store/actions/modals/modals.actions';
import {
  DomService,
  HttpService,
  SearchService,
  SavedFavoritesService,
  SessionProfileFavoriteTrackerService,
  WindowService,
  PlatformCoreService
} from '../../shared/services';

describe('OperationalBarComponent', () => {
  let componentFixture: ComponentFixture<OperationalBarComponent>, component: OperationalBarComponent;
  const stub: any = {};
  const favoriteServicePostObservable = new Subject();
  const favoriteServiceDeleteObservable = new Subject();

  beforeEach(() => {
    stub.DomService = jasmine.createSpyObj('DomService', ['getMainHeaderHeight']);
    stub.DomService.getMainHeaderHeight.and.returnValue(200);
    stub.SearchService = jasmine.createSpyObj('SearchService', ['purgeCache']);
    stub.SavedFavoritesService = jasmine.createSpyObj('SavedFavoritesService', ['del', 'post']);
    stub.SavedFavoritesService.del.and.returnValue(favoriteServiceDeleteObservable);
    stub.SavedFavoritesService.post.and.returnValue(favoriteServicePostObservable);
    stub.SessionProfileFavoriteTrackerService = jasmine.createSpyObj('SessionProfileFavoriteTrackerService', [
      'deleteProfileFavorite',
      'recordFavorite',
      'isProfileMarkAsFavorite'
    ]);
    stub.PlatformCoreService = {
      hasPlatformCoreAccount: new BehaviorSubject(true)
    };

    stub.StoreModalsSubject = new Subject();
    stub.Store = {
      dispatch: jasmine.createSpy('dispatch'),
      modals: stub.StoreModalsSubject
    };
    stub.Store.select = (storeSelect: Function) => {
      return storeSelect(stub.Store);
    };

    stub.HttpService = jasmine.createSpyObj('HttpService', ['post']);
    stub.WindowService = jasmine.createSpyObj('WindowService', ['getWindow']);
    stub.WindowService.getWindow = jasmine.createSpy('getWindow');
    stub.WindowService.getWindow.and.returnValue({
      location: { href: '' },
      addEventListener: () => {},
      removeEventListener: () => {}
    });

    TestBed.configureTestingModule({
      declarations: [OperationalBarComponent],
      providers: [
        { provide: DomService, useValue: stub.DomService },
        { provide: SearchService, useValue: stub.SearchService },
        { provide: SavedFavoritesService, useValue: stub.SavedFavoritesService },
        {
          provide: SessionProfileFavoriteTrackerService,
          useValue: stub.SessionProfileFavoriteTrackerService
        },
        { provide: Store, useValue: stub.Store },
        { provide: WindowService, useValue: stub.WindowService },
        { provide: PlatformCoreService, useValue: stub.PlatformCoreService }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).overrideComponent(OperationalBarComponent, {
      set: { changeDetection: ChangeDetectionStrategy.Default }
    });

    TestBed.compileComponents();
    componentFixture = TestBed.createComponent(OperationalBarComponent);
    component = componentFixture.componentInstance;
    componentFixture.detectChanges();
  });

  describe('#constructor', () => {
    it('should exist', () => {
      expect(component).toBeDefined();
    });
  });

  describe('#ngOnInit', () => {
    it('should subscribe to modals store', () => {
      component['subscribeToStore'] = jasmine.createSpy('subscribeToStore');

      component.ngOnInit();

      expect(component['subscribeToStore']).toHaveBeenCalledTimes(1);
      expect(component['subscribeToStore']).toHaveBeenCalledWith();
    });

    it('should add the scroll window listener', () => {
      component.isHeader = true;
      stub.WindowService.getWindow.calls.reset();

      component.ngOnInit();

      expect(stub.WindowService.getWindow).toHaveBeenCalledTimes(1);
      expect(stub.WindowService.getWindow).toHaveBeenCalledWith();
    });

    it('should do nothing', () => {
      component.isHeader = false;
      stub.WindowService.getWindow.calls.reset();

      component.ngOnInit();

      expect(stub.WindowService.getWindow).not.toHaveBeenCalled();
    });
  });

  describe('#ngOnChanges', () => {
    it('should reset visibility of phone number after changes detected', () => {
      component.phoneNumberShown = true;

      component.ngOnChanges(<any>{});

      expect(component.phoneNumberShown).toBe(false);
    });

    it('should set favorite flag to false if component input value is false', () => {
      component['isMarkedAsFavorite'] = true;

      component.isFavorite = false;

      component.ngOnChanges(<any>{});

      expect(component['isMarkedAsFavorite']).toBe(component.isFavorite);
    });

    it('should set favorite flag to true if component input value is true', () => {
      component['isMarkedAsFavorite'] = false;

      component.isFavorite = true;

      component.ngOnChanges(<any>{});

      expect(component['isMarkedAsFavorite']).toBe(component.isFavorite);
    });
  });

  describe('#ngOnDestroy', () => {
    it('should remove the scroll window listener', () => {
      component['componentDestroyed'] = jasmine.createSpyObj('componentDestroyed', ['next', 'unsubscribe']);
      component.isHeader = true;
      stub.WindowService.getWindow.calls.reset();

      component.ngOnDestroy();

      expect(stub.WindowService.getWindow).toHaveBeenCalledTimes(1);
      expect(stub.WindowService.getWindow).toHaveBeenCalledWith();
    });

    it('should do nothing with window when isHeader is false', () => {
      component['componentDestroyed'] = jasmine.createSpyObj('componentDestroyed', ['next', 'unsubscribe']);
      component.isHeader = false;
      stub.WindowService.getWindow.calls.reset();

      component.ngOnDestroy();

      expect(stub.WindowService.getWindow).not.toHaveBeenCalled();
    });

    it('should unsubscribe to the component promises', () => {
      component['componentDestroyed'] = jasmine.createSpyObj('componentDestroyed', ['next', 'unsubscribe']);

      component.ngOnDestroy();

      expect(component['componentDestroyed'].next).toHaveBeenCalledTimes(1);
      expect(component['componentDestroyed'].next).toHaveBeenCalledWith();
      expect(component['componentDestroyed'].unsubscribe).toHaveBeenCalledTimes(1);
      expect(component['componentDestroyed'].unsubscribe).toHaveBeenCalledWith();
    });
  });

  describe('#subscribeToStore', () => {
    it('should subscribe to modals store', () => {
      component['subscribeToStore']();

      stub.StoreModalsSubject.next({ displayShareProfileModal: true });

      expect(component.displayShareProfileModal).toBe(true);
    });
  });

  describe('#onWindowScroll', () => {
    it('should get the current header height', () => {
      component['onWindowScroll']();

      expect(stub.DomService.getMainHeaderHeight).toHaveBeenCalledWith();
      expect(stub.DomService.getMainHeaderHeight).toHaveBeenCalledTimes(1);
    });

    it('should check the new scroll top position during the scroll', () => {
      component['ticking'] = false;

      stub.WindowService.getWindow.calls.reset();

      component['onWindowScroll']();

      expect(stub.WindowService.getWindow).toHaveBeenCalledTimes(1);
      expect(stub.WindowService.getWindow).toHaveBeenCalledWith();
      expect(component['ticking']).toEqual(true);
    });

    it('should avoid the check during the scroll', () => {
      component['ticking'] = true;

      stub.WindowService.getWindow.calls.reset();

      component['onWindowScroll']();

      expect(stub.WindowService.getWindow).toHaveBeenCalledTimes(1);
      expect(stub.WindowService.getWindow).toHaveBeenCalledWith();
      expect(component['ticking']).toEqual(true);
    });
  });

  describe('#updateScrollPosition', () => {
    it('it should set regular operational bar', () => {
      component['scrollPosition'] = 100;
      component.operationalBarSwitcher = 200;

      component['updateScrollPosition']();

      expect(component.operationalBarRegular).toEqual(true);
      expect(component['ticking']).toEqual(false);
    });

    it('it should set sticky operational bar', () => {
      component['scrollPosition'] = 400;
      component.operationalBarSwitcher = 200;

      component['updateScrollPosition']();

      expect(component.operationalBarRegular).toEqual(false);
      expect(component['ticking']).toEqual(false);
    });
  });

  describe('#onClickPhoneNumber', () => {
    const eventStub: any = {};

    beforeEach(() => {
      eventStub.preventDefault = jasmine.createSpy('preventDefault');
    });

    it('should prevent default event handling and make phone number shown', () => {
      expect(component.phoneNumberShown).toBe(false);

      component.onClickPhoneNumber(eventStub);

      expect(eventStub.preventDefault).toHaveBeenCalledTimes(1);
      expect(component['phoneNumberShown']).toBe(true);
    });

    it('should do nothing if phone number is shown', () => {
      component.phoneNumberShown = true;
      component.onClickPhoneNumber(eventStub);

      expect(eventStub.preventDefault).not.toHaveBeenCalled();
      expect(component.phoneNumberShown).toBe(true);
    });
  });

  describe('#onClickDownload', () => {
    it('should call pdfForger', () => {
      component.pdfGeneratorFn = jasmine.createSpy('pdfGeneratorFn');
      component.createObjectURLFn = (blob) => 'some_url';
      component.revokeObjectURLFn = () => {};

      (component as any).jobSeeker = {
        originalJobSeeker: { arbitratyData: 'some_data' },
        personalInfo: {
          firstName: '',
          lastName: '',
        },
      };
      component.onClickDownload();

      expect(component.pdfGeneratorFn).toHaveBeenCalledTimes(1);
      expect(component.pdfGeneratorFn).toHaveBeenCalledWith({ arbitratyData: 'some_data' }, 'blob');
    });
  });

  describe('#isProfileFavorite', () => {
    it('should return true if profile is favorite in data', () => {
      component['isMarkedAsFavorite'] = true;

      stub.SessionProfileFavoriteTrackerService.isProfileMarkAsFavorite.and.returnValue(false);

      expect(component.isProfileFavorite()).toBe(true);
    });

    it('should return true if profile is favorite in session storage', () => {
      component['isMarkedAsFavorite'] = false;

      stub.SessionProfileFavoriteTrackerService.isProfileMarkAsFavorite.and.returnValue(true);

      expect(component.isProfileFavorite()).toBe(true);
    });

    it('should return false if profile is not favorite in both data and session storage', () => {
      component['isMarkedAsFavorite'] = false;

      stub.SessionProfileFavoriteTrackerService.isProfileMarkAsFavorite.and.returnValue(false);

      expect(component.isProfileFavorite()).toBe(false);
    });
  });

  describe('#toggleFavorite', () => {
    it('should remove favorite a profile', () => {
      component['isMarkedAsFavorite'] = true;

      component.toggleFavorite();

      expect(stub.SavedFavoritesService.del).toHaveBeenCalledTimes(1);
      expect(stub.SavedFavoritesService.del).toHaveBeenCalledWith(component.jobseekerId);

      expect(stub.SessionProfileFavoriteTrackerService.deleteProfileFavorite).not.toHaveBeenCalled();
      expect(component['isMarkedAsFavorite']).toBe(false);

      favoriteServiceDeleteObservable.next({});

      expect(stub.SessionProfileFavoriteTrackerService.deleteProfileFavorite).toHaveBeenCalledTimes(1);
      expect(stub.SessionProfileFavoriteTrackerService.deleteProfileFavorite).toHaveBeenCalledWith(component.jobseekerId);

      expect(stub.SearchService.purgeCache).toHaveBeenCalledTimes(1);
      expect(stub.SearchService.purgeCache).toHaveBeenCalledWith();
    });

    it('should favorite a profile', () => {
      component['isMarkedAsFavorite'] = false;

      component.toggleFavorite();

      expect(stub.SavedFavoritesService.post).toHaveBeenCalledTimes(1);
      expect(stub.SavedFavoritesService.post).toHaveBeenCalledWith(component.jobseekerId);

      expect(stub.SessionProfileFavoriteTrackerService.recordFavorite).not.toHaveBeenCalled();
      expect(component['isMarkedAsFavorite']).toBe(true);

      favoriteServicePostObservable.next({});

      expect(stub.SessionProfileFavoriteTrackerService.recordFavorite).toHaveBeenCalledTimes(1);
      expect(stub.SessionProfileFavoriteTrackerService.recordFavorite).toHaveBeenCalledWith(component.jobseekerId);
    });
  });

  describe('#openSharerModal', () => {
    it('should dispatch an OPEN_SHARE_PROFILE_MODAL action', () => {
      stub.Store.dispatch.calls.reset();

      component.openSharerModal();

      expect(stub.Store.dispatch).toHaveBeenCalledTimes(1);
      expect(stub.Store.dispatch).toHaveBeenCalledWith(new ModalsActions.OpenShareProfileModal());
    });

    it('should not dispatch an OPEN_SHARE_PROFILE_MODAL action if modal already open', () => {
      stub.Store.dispatch.calls.reset();

      component.displayShareProfileModal = true;

      component.openSharerModal();

      expect(stub.Store.dispatch).not.toHaveBeenCalled();
    });
  });
});
