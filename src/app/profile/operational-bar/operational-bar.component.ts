import { Component, OnInit, OnChanges, OnDestroy, SimpleChanges, Input } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AppState } from '../../types/app-state';
import * as ModalsActions from '../../store/actions/modals/modals.actions';
import {
  DomService,
  SearchService,
  SavedFavoritesService,
  SessionProfileFavoriteTrackerService,
  WindowService,
  PlatformCoreService
} from '../../shared/services';
import pdfForger from '@dpgr/pdf-forger';
import { JobSeeker } from 'app/classes';

@Component({
  selector: 'app-operational-bar',
  templateUrl: './operational-bar.component.html',
  styleUrls: ['./operational-bar.component.scss']
})
export class OperationalBarComponent implements OnInit, OnChanges, OnDestroy {
  @Input()
  jobseekerId: string;
  @Input()
  firstName: string;
  @Input()
  lastName: string;
  @Input()
  email: string;
  @Input()
  phoneNumber: string;
  @Input()
  isFavorite: boolean;
  @Input()
  isHeader: boolean;
  @Input()
  jobSeeker: JobSeeker;

  private isMarkedAsFavorite: boolean;
  public phoneNumberShown = false;
  public pdfGeneratorFn = pdfForger;
  public createObjectURLFn = URL.createObjectURL;
  public revokeObjectURLFn = URL.revokeObjectURL;
  private scrollPosition = 0;
  private ticking = false;
  public operationalBarSwitcher = 0;
  public operationalBarRegular = true;
  public displayShareProfileModal = false;
  public hasPlatformCoreAccount = false;
  private componentDestroyed: Subject<void> = new Subject();

  constructor(
    private domService: DomService,
    private searchService: SearchService,
    private favoritesService: SavedFavoritesService,
    private sessionFavoritesService: SessionProfileFavoriteTrackerService,
    private store: Store<AppState>,
    private windowService: WindowService,
    private platformCoreService: PlatformCoreService
  ) {
    this.updateScrollPosition = this.updateScrollPosition.bind(this);
    this.onWindowScroll = this.onWindowScroll.bind(this);
    platformCoreService.hasPlatformCoreAccount.pipe(takeUntil(this.componentDestroyed)).subscribe((hasPlatformCoreAccount: boolean) => {
      this.hasPlatformCoreAccount = hasPlatformCoreAccount;
    });
  }

  ngOnInit(): void {
    this.subscribeToStore();

    if (this.isHeader) {
      this.windowService.getWindow().addEventListener('scroll', this.onWindowScroll, true);
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.phoneNumberShown = false;
    this.isMarkedAsFavorite = this.isFavorite;
  }

  ngOnDestroy(): void {
    if (this.isHeader) {
      this.windowService.getWindow().removeEventListener('scroll', this.onWindowScroll, true);
    }
    this.componentDestroyed.next();
    this.componentDestroyed.unsubscribe();
  }

  private subscribeToStore(): void {
    this.store
      .select(s => s.modals)
      .subscribe(modals => {
        this.displayShareProfileModal = modals.displayShareProfileModal;
      });
  }

  private onWindowScroll(): void {
    this.scrollPosition = this.windowService.getWindow().pageYOffset;
    this.operationalBarSwitcher = this.domService.getMainHeaderHeight();

    if (!this.ticking) {
      window.requestAnimationFrame(this.updateScrollPosition);

      this.ticking = true;
    }
  }

  private updateScrollPosition(): void {
    this.operationalBarRegular = this.scrollPosition <= this.operationalBarSwitcher;
    this.ticking = false;
  }

  onClickPhoneNumber(event: Event) {
    if (this.phoneNumberShown) {
      return;
    }

    event.preventDefault();

    this.phoneNumberShown = true;
  }

  onClickDownload(): void {
    let blob = new Blob();
    try {
      blob = this.pdfGeneratorFn(this.jobSeeker.originalJobSeeker, 'blob') as Blob;
    } catch (e) {
      /* istanbul ignore next */
      console.log(e);
    }
    const url = this.createObjectURLFn(blob);
    const link = document.createElement('a');
    link.href = url;
    const fileName = `${this.jobSeeker.personalInfo.firstName}_${this.jobSeeker.personalInfo.lastName}.pdf`.trim().toLowerCase();
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    this.revokeObjectURLFn(url);
  }

  isProfileFavorite() {
    return this.isMarkedAsFavorite === true || this.sessionFavoritesService.isProfileMarkAsFavorite(this.jobseekerId);
  }

  toggleFavorite(): void {
    if (this.isProfileFavorite()) {
      this.favoritesService.del(this.jobseekerId).subscribe(() => {
        this.sessionFavoritesService.deleteProfileFavorite(this.jobseekerId);
        this.searchService.purgeCache();
      });

      this.isMarkedAsFavorite = false;
    } else {
      this.favoritesService.post(this.jobseekerId).subscribe(() => {
        this.sessionFavoritesService.recordFavorite(this.jobseekerId);
      });

      this.isMarkedAsFavorite = true;
    }
  }

  openSharerModal() {
    if (!this.displayShareProfileModal) {
      this.store.dispatch(new ModalsActions.OpenShareProfileModal());
    }
  }
}
