@import "components";

.file-cv {
  margin: 0 auto;
  overflow-x: auto;
  overflow-y: hidden;
  position: relative;
}

.title {
  color: #312d38;
  font-size: 18px;
  font-weight: 600;
  line-height: 54px;
  @include media-breakpoint-up(md) {
    font-size: 30px;
    margin-bottom: 20px;
    margin-top: 40px;
  }
}

.values {
  @include outer-container();
  @include media-breakpoint-up(md) {
    margin: 0 0 20px 0;
  }
  &:last-of-type {
    margin-bottom: 20px;
  }
}

.divider {
  border-top: 1px solid #d8d8d8;
  display: block;
  margin: 24px -10px 0px -10px;
  @include media-breakpoint-up(md) {
    margin: 0;
  }
}
