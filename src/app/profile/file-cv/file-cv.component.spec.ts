import { TestBed, ComponentFixture } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { FileCVComponent } from './file-cv.component';
import { SearchHighlightPipe } from '../../shared/pipes';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs/index';

describe('JobSeekerProfile FileCVComponent', () => {
  let componentFixture: ComponentFixture<FileCVComponent>, component: FileCVComponent;
  const stub: any = {};

  beforeEach(() => {
    stub.Store = new Subject();

    TestBed.configureTestingModule({
      declarations: [FileCVComponent, SearchHighlightPipe],
      providers: [{ provide: Store, useValue: stub.Store }],
      schemas: [NO_ERRORS_SCHEMA]
    });

    TestBed.compileComponents();
    componentFixture = TestBed.createComponent(FileCVComponent);
    componentFixture.detectChanges();
    component = componentFixture.componentInstance;
  });

  describe('#constructor', () => {
    it('should exist', () => {
      expect(component).toBeDefined();
    });
  });
});
