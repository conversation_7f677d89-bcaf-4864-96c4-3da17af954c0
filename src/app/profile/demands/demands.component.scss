@import "components";

.demands {
  font-size: $profile-demands-fontsize;
  font-weight: 300;
  .col {
    border-bottom: 1px solid $profile-demands-border-color;
    border-left: 2px solid $profile-demands-border-color;
    border-right: 2px solid $profile-demands-border-color;
    flex: 1 1 auto;
    margin: auto 10px;
    @include media-breakpoint-up(lg) {
      border-bottom: 2px solid $profile-demands-border-color;
      border-left: none;
      border-right: 1px solid $profile-demands-border-color;
      border-top: 2px solid $profile-demands-border-color;
      margin: 0;
    }
    &:first-child {
      border-radius: ($spacer * 2) ($spacer * 2) 0 0;
      border-top: 2px solid $profile-demands-border-color;
      @include media-breakpoint-up(lg) {
        border-left: 2px solid $profile-demands-border-color;
        border-radius: ($spacer * 2) 0 0 ($spacer * 2);
      }
    }
    &:last-child {
      border-bottom: 2px solid $profile-demands-border-color;
      border-radius: 0 0 ($spacer * 2) ($spacer * 2);
      @include media-breakpoint-up(lg) {
        border-radius: 0 ($spacer * 2) ($spacer * 2) 0;
        border-right: 2px solid $profile-demands-border-color;
      }
    }
  }

  .city {
    cursor: pointer;
  }

  span {
    &.desktop {
      @include media-breakpoint-down(md) {
        display: none;
      }
    }
  }

  .updated-date, .via {
    color: $profile-intro-color;
    font-size: 14px;
    margin-top: 15px;
    text-align: center;
    width: 100%;

    @include media-breakpoint-up(lg) {
      display: none;
    }
  }

  .updated-date {
    span {
      color: #6d6875;
      font-size: 16px;
      padding-left: 20px;
      padding-right: 25px;
      position: relative;

      &:last-child {
        padding-right: 0;
      }

      .icon-dates {
        background-size: contain;
        display: inline-block;
        height: 16px;
        left: 0;
        position: absolute;
        top: calc(50% - 8px);
        width: 16px;

        &__update {
          background-image: url("#{$project-icons-folder}edit.svg");
        }
        &__view {
          background-image: url("#{$project-icons-folder}viewed.svg");
        }
      }
    }
  }

  .via {
    left: 20px;

    span {
      display: inline-block;
      position: relative;

      &:after {
        background-repeat: no-repeat;
        background-size: contain;
        content: "";
        display: inline-block;
        height: 30px;
        left: 5px;
        position: absolute;
      }

      &.intermediair {
        width: 118px;

        &:after {
          background-image: url("#{$project-images-folder}intermediair.svg");
          top: -14px;
          width: 118px;
        }
      }

      &.nvb {
        width: 166px;

        &:after {
          background-image: url("#{$project-images-folder}nvb.svg");
          top: -19px;
          width: 166px;
        }
      }

      &.itbanen{
        width: 142px;

        &:after {
          background-image: url("#{$project-images-folder}itbanen.svg");
          top: -21px;
          width: 142px;
        }
      }
    }
  }
}
