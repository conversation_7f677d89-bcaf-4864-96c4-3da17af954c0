<div class="card-body pt-0 pb-5">
    <main class="mt-2">
        <section class="demands mt-4">
            <div class="row text-center justify-content-lg-around flex-lg-nowrap">
                <div class="col col-lg-auto ml-lg-3">
                    <div>
                        <span class="acc-availability py-1 py-lg-3 d-block d-lg-inline-block">
                              <span *ngIf="availability">
                                  Beschikbaar per {{availability}}
                              </span>
                        </span>
                    </div>
                </div>
                <div class="col col-lg-auto">
                    <div>
                        <span class="acc-max-working-hours py-1 py-lg-3 d-block d-lg-inline-block">
                            {{minWorkingHours}} - {{maxWorkingHours}} uur p/w
                        </span>
                    </div>
                </div>
                <div class="col col-lg-auto">
                    <div>
                        <span class="py-1 py-lg-3 d-block d-lg-inline-block">
                            <span>Opleidingsniveau</span>
                            <span *ngIf="workLevels && workLevels.length > 0"> {{workLevels[0]}}</span>
                            <span *ngIf="!workLevels"> /</span>
                        </span>
                    </div>
                </div>
                <div class="col col-lg-auto" *ngIf="city">
                    <div class="d-block d-lg-inline-block dropdown-part-city">
                        <span class="py-1 py-lg-3 d-block d-lg-inline-block">
                            <span class="desktop">Wil werken in </span>
                            <span>{{city}}</span>
                            <span *ngIf="radius.value <= 100"> +{{radius.value}}km</span>
                            <span *ngIf="radius.value > 100"> +300km</span>
                        </span>
                    </div>
                </div>
            </div>

            <div class="via">
                Profiel afkomstig van
                <span [ngClass]="{
                    'intermediair' : site === 'intermediair',
                    'nvb' : site === 'nvb',
                    'itbanen' : site === 'itbanen'
                }"></span>
            </div>

            <div class="updated-date">
                <span class="icons"
                      *ngIf="updatedDate">
                    <i class="icon-dates icon-dates__update"></i>
                    {{updatedDate | daysAgoDate}}
                </span>
                <span class="icons"
                      *ngIf="viewedDate">
                    <i class="icon-dates icon-dates__view"></i>
                    {{viewedDate | daysAgoDate}}
                </span>
            </div>
        </section>
    </main>
</div>
