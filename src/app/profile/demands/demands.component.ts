import { Component, Input } from '@angular/core';
import { Radius } from '../../classes';

@Component({
  selector: 'app-profile-demands',
  templateUrl: './demands.component.html',
  styleUrls: ['./demands.component.scss']
})
export class DemandsComponent {
  @Input()
  availability: string;
  @Input()
  minWorkingHours: number;
  @Input()
  maxWorkingHours: number;
  @Input()
  workLevels: Array<string>;
  @Input()
  city: string;
  @Input()
  radius: Radius;
  @Input()
  site: string;
  @Input()
  updatedDate: number;
  @Input()
  viewedDate: number;

  constructor() {}
}
