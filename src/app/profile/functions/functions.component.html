<main class="card-deck" id="profile-functions">
    <section class="card my-4 function-groups" *ngIf="functionGroups && functionGroups.length > 0">
        <div class="card-body">
            <h2 class="card-title mb-5 mt-3 text-md-center acc-preferred-jobs-header">Type functie</h2>
            <div class="text-md-center acc-function-groups">
                <span *ngFor="let functionGroup of functionGroups;"
                      class="mb-3 comma-separate"
                      [innerHTML]="functionGroup | searchHighlight"></span>
            </div>
        </div>
    </section>

    <section class="card my-4 preferred-jobs" *ngIf="preferredJobs && preferredJobs.length > 0">
        <div class="card-body">
            <h2 class="card-title mb-5 mt-3 text-md-center acc-function-groups-header">Gewenste banen</h2>
            <div class="text-md-center acc-preferred-jobs">
                <span *ngFor="let preferredJob of preferredJobs;"
                      class="mb-3 comma-separate"
                      [innerHTML]="preferredJob | searchHighlight"></span>
            </div>
        </div>
    </section>
</main>
