import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { FunctionsComponent } from './functions.component';
import { Subject } from 'rxjs/index';
import { Store } from '@ngrx/store';
import { SearchHighlightPipe } from '../../shared/pipes';

describe('FunctionsComponent', () => {
  let component: FunctionsComponent;
  let fixture: ComponentFixture<FunctionsComponent>;
  const stub: any = {};

  beforeEach(async(() => {
    stub.Store = new Subject();

    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [FunctionsComponent, SearchHighlightPipe],
      providers: [{ provide: Store, useValue: stub.Store }],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FunctionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });
});
