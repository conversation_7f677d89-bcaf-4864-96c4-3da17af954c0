<div class="navigational-bar shadow-sm"
     *ngIf="profileNavigation.isProfileOpenedFromSearchResults()"
     [ngClass]="{
        'navigational-bar--regular': navigationalBarRegular,
        'navigational-bar--sticky': !navigationalBarRegular
    }">
    <div class="navigational-bar__container">
        <div class="navigational-bar__previous" [ngClass]="{ 'navigational-bar--invisible': !profileNavigation.isPreviousProfileAvailable() }">
            <a href="javascript:"
               (click)="profileNavigation.goToPreviousProfile()"
               title="Vorige profiel (pijltoets &larr;)"
               data-gtm="pro-previous">
                <i class="navigational-bar__arrow"></i>
                <span>vorige profiel</span>
            </a>
        </div>

        <div class="navigational-bar__back" [ngClass]="{ 'navigational-bar--invisible': !profileNavigation.isProfileOpenedFromSearchResults() }">
            <a href="javascript:"
               (click)="profileNavigation.goToSearchResultsPage()"
               title="Terug naar zoekresultaten (Esc)"
               data-gtm="pro-back">
                <span>terug</span>
                <span>&nbsp;naar zoekresultaten</span>
            </a>
        </div>

        <div class="navigational-bar__next" [ngClass]="{ 'navigational-bar--invisible': !profileNavigation.isNextProfileAvailable() }">
            <a href="javascript:"
               (click)="profileNavigation.goToNextProfile()"
               title="Volgend profiel (pijltoets &rarr;)"
               data-gtm="pro-next">
                <i class="navigational-bar__arrow"></i>
                <span>volgend profiel</span>
            </a>
        </div>
    </div>
</div>

<div *ngIf="profileNavigation.isProfileOpenedFromSearchResults()"
     [ngClass]="{'navigational-bar__space': !navigationalBarRegular}"></div>
