import { TestBed, ComponentFixture } from '@angular/core/testing';
import { ChangeDetectionStrategy, NO_ERRORS_SCHEMA } from '@angular/core';
import { NavigationalBarComponent } from './navigational-bar.component';
import { DomService, ProfileNavigationService, WindowService } from '../../shared/services';
import { Subject } from 'rxjs/index';
import { Store } from '@ngrx/store';

describe('NavigationalBarComponent', () => {
  let componentFixture: ComponentFixture<NavigationalBarComponent>, component: NavigationalBarComponent;
  const stub: any = {};

  beforeEach(() => {
    stub.DomService = jasmine.createSpyObj('DomService', ['getMainHeaderHeight']);
    stub.DomService.getMainHeaderHeight.and.returnValue(90);
    stub.ProfileNavigationService = jasmine.createSpyObj('ProfileNavigationService', [
      'isProfileOpenedFromSearchResults',
      'isPreviousProfileAvailable',
      'goToPreviousProfile',
      'isNextProfileAvailable',
      'goToNextProfile',
      'goToSearchResultsPage'
    ]);
    stub.WindowService = jasmine.createSpyObj('WindowService', ['getWindow']);
    stub.WindowService.getWindow = jasmine.createSpy('getWindow');
    stub.WindowService.getWindow.and.returnValue({
      location: { href: '' },
      addEventListener: () => {},
      removeEventListener: () => {}
    });
    stub.StoreModalsSubject = new Subject();
    stub.Store = {
      dispatch: jasmine.createSpy('dispatch'),
      modals: stub.StoreModalsSubject
    };
    stub.Store.select = (storeSelect: Function) => {
      return storeSelect(stub.Store);
    };

    TestBed.configureTestingModule({
      declarations: [NavigationalBarComponent],
      providers: [
        { provide: DomService, useValue: stub.DomService },
        { provide: ProfileNavigationService, useValue: stub.ProfileNavigationService },
        { provide: WindowService, useValue: stub.WindowService },
        { provide: Store, useValue: stub.Store }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).overrideComponent(NavigationalBarComponent, {
      set: { changeDetection: ChangeDetectionStrategy.Default }
    });

    TestBed.compileComponents();
    componentFixture = TestBed.createComponent(NavigationalBarComponent);
    component = componentFixture.componentInstance;
    componentFixture.detectChanges();
  });

  describe('#constructor', () => {
    it('should exist', () => {
      expect(component).toBeDefined();
    });
  });

  describe('#ngOnInit', () => {
    it('should subscribe to modals store', () => {
      component['subscribeToStore'] = jasmine.createSpy('subscribeToStore');

      component.ngOnInit();

      expect(component['subscribeToStore']).toHaveBeenCalledTimes(1);
      expect(component['subscribeToStore']).toHaveBeenCalledWith();
    });

    it('should not add the scroll window listener when sticky is disalbed', () => {
      stub.WindowService.getWindow.calls.reset();

      component.ngOnInit();

      expect(stub.WindowService.getWindow).not.toHaveBeenCalled();
    });

    it('should add the scroll window listener when sticky is enabled', () => {
      stub.WindowService.getWindow.calls.reset();

      component.sticky = true;
      component.ngOnInit();

      expect(stub.WindowService.getWindow).toHaveBeenCalledTimes(1);
      expect(stub.WindowService.getWindow).toHaveBeenCalledWith();
    });

    it('should create a subscription for the keydown event observable on the document', () => {
      component.ngOnInit();

      expect(component['keyDownSubscription']).toBeDefined();
    });
  });

  describe('#ngOnDestroy', () => {
    it('should remove the scroll window listener', () => {
      stub.WindowService.getWindow.calls.reset();

      component.ngOnDestroy();

      expect(stub.WindowService.getWindow).toHaveBeenCalledTimes(1);
      expect(stub.WindowService.getWindow).toHaveBeenCalledWith();
    });

    it('should unsubscribe from the keydown event observable on the document', () => {
      spyOn(component['keyDownSubscription'], 'unsubscribe');

      component.ngOnDestroy();

      expect(component['keyDownSubscription'].unsubscribe).toHaveBeenCalledTimes(1);
    });
  });

  describe('#subscribeToStore', () => {
    it('should subscribe to modals store', () => {
      component['subscribeToStore']();

      stub.StoreModalsSubject.next({ displayShareProfileModal: true });

      expect(component.displayShareProfileModal).toBe(true);
    });
  });

  describe('#onWindowScroll', () => {
    it('should get the current header height', () => {
      component['onWindowScroll']();

      expect(stub.DomService.getMainHeaderHeight).toHaveBeenCalledWith();
      expect(stub.DomService.getMainHeaderHeight).toHaveBeenCalledTimes(1);
    });

    it('should check the new scroll top position during the scroll', () => {
      component['ticking'] = false;

      stub.WindowService.getWindow.calls.reset();

      component['onWindowScroll']();

      expect(stub.WindowService.getWindow).toHaveBeenCalledTimes(1);
      expect(stub.WindowService.getWindow).toHaveBeenCalledWith();
      expect(component['ticking']).toEqual(true);
    });

    it('should avoid the check during the scroll', () => {
      component['ticking'] = true;

      stub.WindowService.getWindow.calls.reset();

      component['onWindowScroll']();

      expect(stub.WindowService.getWindow).toHaveBeenCalledTimes(1);
      expect(stub.WindowService.getWindow).toHaveBeenCalledWith();
      expect(component['ticking']).toEqual(true);
    });
  });

  describe('#updateScrollPosition', () => {
    it('it should set regular navigational bar', () => {
      component['scrollPosition'] = 100;
      component.navigationalBarSwitcher = 200;

      component['updateScrollPosition']();

      expect(component.navigationalBarRegular).toEqual(true);
      expect(component['ticking']).toEqual(false);
    });

    it('it should set sticky navigational bar', () => {
      component['scrollPosition'] = 400;
      component.navigationalBarSwitcher = 200;

      component['updateScrollPosition']();

      expect(component.navigationalBarRegular).toEqual(false);
      expect(component['ticking']).toEqual(false);
    });
  });

  describe('#navigateToProfileFromKeyboard', () => {
    it('should navigate to the previous profile if left arrow button is clicked', () => {
      component.displayShareProfileModal = false;
      component['navigateToPreviousProfileFromKeyboard'] = jasmine.createSpy('navigateToPreviousProfileFromKeyboard');
      component['navigateToNextProfileFromKeyboard'] = jasmine.createSpy('navigateToNextProfileFromKeyboard');
      component['navigateToSearchResultsPageFromKeyboard'] = jasmine.createSpy('navigateToSearchResultsPageFromKeyboard');

      component['navigateToProfileFromKeyboard'](<any>{ key: 'ArrowLeft' });

      expect(component['navigateToPreviousProfileFromKeyboard']).toHaveBeenCalledTimes(1);
      expect(component['navigateToPreviousProfileFromKeyboard']).toHaveBeenCalledWith();
      expect(component['navigateToNextProfileFromKeyboard']).not.toHaveBeenCalled();
      expect(component['navigateToSearchResultsPageFromKeyboard']).not.toHaveBeenCalled();
    });

    it('should navigate to the next profile if right arrow button is clicked', () => {
      component.displayShareProfileModal = false;
      component['navigateToPreviousProfileFromKeyboard'] = jasmine.createSpy('navigateToPreviousProfileFromKeyboard');
      component['navigateToNextProfileFromKeyboard'] = jasmine.createSpy('navigateToNextProfileFromKeyboard');
      component['navigateToSearchResultsPageFromKeyboard'] = jasmine.createSpy('navigateToSearchResultsPageFromKeyboard');

      component['navigateToProfileFromKeyboard'](<any>{ key: 'ArrowRight' });

      expect(component['navigateToPreviousProfileFromKeyboard']).not.toHaveBeenCalled();
      expect(component['navigateToNextProfileFromKeyboard']).toHaveBeenCalledTimes(1);
      expect(component['navigateToNextProfileFromKeyboard']).toHaveBeenCalledWith();
      expect(component['navigateToSearchResultsPageFromKeyboard']).not.toHaveBeenCalled();
    });

    it('should navigate to search results page if escape button is clicked', () => {
      component.displayShareProfileModal = false;
      component['navigateToPreviousProfileFromKeyboard'] = jasmine.createSpy('navigateToPreviousProfileFromKeyboard');
      component['navigateToNextProfileFromKeyboard'] = jasmine.createSpy('navigateToNextProfileFromKeyboard');
      component['navigateToSearchResultsPageFromKeyboard'] = jasmine.createSpy('navigateToSearchResultsPageFromKeyboard');

      component['navigateToProfileFromKeyboard'](<any>{ key: 'Escape' });

      expect(component['navigateToPreviousProfileFromKeyboard']).not.toHaveBeenCalled();
      expect(component['navigateToNextProfileFromKeyboard']).not.toHaveBeenCalled();
      expect(component['navigateToSearchResultsPageFromKeyboard']).toHaveBeenCalledTimes(1);
      expect(component['navigateToSearchResultsPageFromKeyboard']).toHaveBeenCalledWith();
    });

    it('should not navigate if ArrowLeft is pressed when sharing profile modal is open', () => {
      component.displayShareProfileModal = true;
      component['navigateToPreviousProfileFromKeyboard'] = jasmine.createSpy('navigateToPreviousProfileFromKeyboard');
      component['navigateToNextProfileFromKeyboard'] = jasmine.createSpy('navigateToNextProfileFromKeyboard');
      component['navigateToSearchResultsPageFromKeyboard'] = jasmine.createSpy('navigateToSearchResultsPageFromKeyboard');

      component['navigateToProfileFromKeyboard'](<any>{ key: 'ArrowLeft' });

      expect(component['navigateToPreviousProfileFromKeyboard']).not.toHaveBeenCalled();
      expect(component['navigateToNextProfileFromKeyboard']).not.toHaveBeenCalled();
      expect(component['navigateToSearchResultsPageFromKeyboard']).not.toHaveBeenCalled();
    });

    it('should not navigate if ArrowRight is pressed when sharing profile modal is open', () => {
      component.displayShareProfileModal = true;
      component['navigateToPreviousProfileFromKeyboard'] = jasmine.createSpy('navigateToPreviousProfileFromKeyboard');
      component['navigateToNextProfileFromKeyboard'] = jasmine.createSpy('navigateToNextProfileFromKeyboard');
      component['navigateToSearchResultsPageFromKeyboard'] = jasmine.createSpy('navigateToSearchResultsPageFromKeyboard');

      component['navigateToProfileFromKeyboard'](<any>{ key: 'ArrowRight' });

      expect(component['navigateToPreviousProfileFromKeyboard']).not.toHaveBeenCalled();
      expect(component['navigateToNextProfileFromKeyboard']).not.toHaveBeenCalled();
      expect(component['navigateToSearchResultsPageFromKeyboard']).not.toHaveBeenCalled();
    });

    it('should not navigate if Escape is pressed when sharing profile modal is open', () => {
      component.displayShareProfileModal = true;
      component['navigateToPreviousProfileFromKeyboard'] = jasmine.createSpy('navigateToPreviousProfileFromKeyboard');
      component['navigateToNextProfileFromKeyboard'] = jasmine.createSpy('navigateToNextProfileFromKeyboard');
      component['navigateToSearchResultsPageFromKeyboard'] = jasmine.createSpy('navigateToSearchResultsPageFromKeyboard');

      component['navigateToProfileFromKeyboard'](<any>{ key: 'Escape' });

      expect(component['navigateToPreviousProfileFromKeyboard']).not.toHaveBeenCalled();
      expect(component['navigateToNextProfileFromKeyboard']).not.toHaveBeenCalled();
      expect(component['navigateToSearchResultsPageFromKeyboard']).not.toHaveBeenCalled();
    });
  });

  describe('#navigateToPreviousProfileFromKeyboard', () => {
    it('should navigate to the previous profile if available', () => {
      stub.ProfileNavigationService.isPreviousProfileAvailable.and.returnValue(true);

      component['navigateToPreviousProfileFromKeyboard']();

      expect(stub.ProfileNavigationService.goToPreviousProfile).toHaveBeenCalled();
    });

    it('should not navigate to the previous profile if not available', () => {
      stub.ProfileNavigationService.isPreviousProfileAvailable.and.returnValue(false);

      component['navigateToPreviousProfileFromKeyboard']();

      expect(stub.ProfileNavigationService.goToPreviousProfile).not.toHaveBeenCalled();
    });
  });

  describe('#navigateToNextProfileFromKeyboard', () => {
    it('should navigate to the next profile if available', () => {
      stub.ProfileNavigationService.isNextProfileAvailable.and.returnValue(true);

      component['navigateToNextProfileFromKeyboard']();

      expect(stub.ProfileNavigationService.goToNextProfile).toHaveBeenCalled();
    });

    it('should not navigate to the next profile if not available', () => {
      stub.ProfileNavigationService.isNextProfileAvailable.and.returnValue(false);

      component['navigateToNextProfileFromKeyboard']();

      expect(stub.ProfileNavigationService.goToNextProfile).not.toHaveBeenCalled();
    });
  });

  describe('#navigateToSearchResultsPageFromKeyboard', () => {
    it('should navigate to the search results page if profile opened from the search results', () => {
      stub.ProfileNavigationService.isProfileOpenedFromSearchResults.and.returnValue(true);

      component['navigateToSearchResultsPageFromKeyboard']();

      expect(stub.ProfileNavigationService.goToSearchResultsPage).toHaveBeenCalled();
    });

    it('should not navigate to the search results page if profile not opened from the search results', () => {
      stub.ProfileNavigationService.isProfileOpenedFromSearchResults.and.returnValue(false);

      component['navigateToSearchResultsPageFromKeyboard']();

      expect(stub.ProfileNavigationService.goToSearchResultsPage).not.toHaveBeenCalled();
    });
  });
});
