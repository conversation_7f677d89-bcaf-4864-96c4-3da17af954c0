@import "components";

.navigational-bar {
  background-color: $profile-navigationbar-backgroundcolor;
  height: $navigationalbar-height;
  width: 100%;
  z-index: 100;

  &--sticky {
    position: fixed;
    top: 0;

    @include media-breakpoint-up(md) {
      top: $operationalbar-height;
    }
  }

  &--regular {
    position: relative;
  }

  &__container {
    align-items: center;
    display: flex;
    height: 60px;
    justify-content: space-between;
    margin: 0 auto;
    max-width: 1100px;
    padding: {
      left: 15px;
      right: 15px;
    };
    width: 100%;
  }

  &__previous, &__back, &__next {
    a {
      color: $profile-navigationbar-text-color;
      &:hover {
        color: $profile-navigationbar-text-color;
        text-decoration: none;
      }
    }

    span {
      font: {
        family: $profile-navigationbar-text-fontfamily;
        size: $profile-navigationbar-text-fontsize;
        weight: 700;
      }
      text-transform: $profile-navigationbar-text-transform;
    }
  }

  &__previous, &__next {
    position: relative;

    i {
      background: {
        position: center center;
        repeat: no-repeat;
      }
      display: block;
      height: 24px;
      top: $profile-navigationbar-icons-top;
      width: 20px;
    }

    span {
      @include media-breakpoint-down(md) {
        visibility: hidden;
      }
    }
  }

  &__previous {
    i {
      background-image: url("#{$project-icons-folder}left.svg");
      left: 0;
    }

    span {
      padding-left: 25px;
    }
  }

  &__back {
    span + span {
      @include media-breakpoint-down(md) {
        display: none;
      }
    }
  }

  &__next {
    i {
      background-image: url("#{$project-icons-folder}right.svg");
      right: 0;
    }

    span {
      padding-right: 25px;
    }
  }

  &__arrow {
    position: absolute;
  }

  &--invisible {
    visibility: hidden;
  }

  &__space {
    height: $navigationalbar-height;
    width: 100%;
  }
}
