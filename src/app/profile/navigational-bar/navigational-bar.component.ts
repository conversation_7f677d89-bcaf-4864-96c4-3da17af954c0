import { Component, OnDestroy, OnInit, Input } from '@angular/core';
import { Subscription, fromEvent } from 'rxjs';
import { DomService, ProfileNavigationService, WindowService } from '../../shared/services';
import { Store } from '@ngrx/store';
import { AppState } from '../../types/app-state';

@Component({
  selector: 'app-navigational-bar',
  templateUrl: './navigational-bar.component.html',
  styleUrls: ['./navigational-bar.component.scss']
})
export class NavigationalBarComponent implements OnInit, OnDestroy {
  @Input()
  sticky: boolean;
  private keyDownSubscription: Subscription;
  private scrollPosition = 0;
  private ticking = false;
  public navigationalBarSwitcher = 0;
  public navigationalBarRegular = true;
  public displayShareProfileModal = false;

  constructor(
    private domService: DomService,
    public profileNavigation: ProfileNavigationService,
    private windowService: WindowService,
    private store: Store<AppState>
  ) {
    this.updateScrollPosition = this.updateScrollPosition.bind(this);
    this.onWindowScroll = this.onWindowScroll.bind(this);
  }

  ngOnInit(): void {
    this.subscribeToStore();

    this.keyDownSubscription = fromEvent(document, 'keydown').subscribe(this.navigateToProfileFromKeyboard.bind(this));

    if (this.sticky) {
      this.windowService.getWindow().addEventListener('scroll', this.onWindowScroll, true);
    }
  }

  ngOnDestroy(): void {
    this.keyDownSubscription.unsubscribe();

    this.windowService.getWindow().removeEventListener('scroll', this.onWindowScroll, true);
  }

  private subscribeToStore(): void {
    this.store
      .select(s => s.modals)
      .subscribe(modals => {
        this.displayShareProfileModal = modals.displayShareProfileModal;
      });
  }

  private onWindowScroll(): void {
    this.scrollPosition = this.windowService.getWindow().pageYOffset;
    this.navigationalBarSwitcher = this.domService.getMainHeaderHeight();

    if (!this.ticking) {
      window.requestAnimationFrame(this.updateScrollPosition);

      this.ticking = true;
    }
  }

  private updateScrollPosition(): void {
    this.navigationalBarRegular = this.scrollPosition <= this.navigationalBarSwitcher;
    this.ticking = false;
  }

  private navigateToProfileFromKeyboard(event: KeyboardEvent) {
    if (!this.displayShareProfileModal) {
      switch (event.key) {
        case 'ArrowLeft':
          this.navigateToPreviousProfileFromKeyboard();
          break;
        case 'ArrowRight':
          this.navigateToNextProfileFromKeyboard();
          break;
        case 'Escape':
          this.navigateToSearchResultsPageFromKeyboard();
          break;
      }
    }
  }

  private navigateToPreviousProfileFromKeyboard() {
    if (this.profileNavigation.isPreviousProfileAvailable()) {
      this.profileNavigation.goToPreviousProfile();
    }
  }

  private navigateToNextProfileFromKeyboard() {
    if (this.profileNavigation.isNextProfileAvailable()) {
      this.profileNavigation.goToNextProfile();
    }
  }

  private navigateToSearchResultsPageFromKeyboard() {
    if (this.profileNavigation.isProfileOpenedFromSearchResults()) {
      this.profileNavigation.goToSearchResultsPage();
    }
  }
}
