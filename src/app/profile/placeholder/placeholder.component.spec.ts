import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { PlaceholderComponent } from './placeholder.component';

describe('PlaceholderComponent', () => {
  let component: PlaceholderComponent;
  let fixture: ComponentFixture<PlaceholderComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [PlaceholderComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PlaceholderComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('#ngOnInit', () => {
    it('should populate lines', () => {
      component.lines = [];
      component.ngOnInit();
      expect(component.lines.length).toBeGreaterThan(0);
    });

    it('should make first line title', () => {
      component.lines = [];
      component.ngOnInit();

      expect(component.lines[0].title).toBeTruthy();

      if (component.lines.length > 1) {
        expect(component.lines[1].title).toBeFalsy();
      }
    });
  });

  describe('#getRandomArbitrary', () => {
    it('should return a number between provided minimum and maximum', () => {
      const min = 1;
      const max = 4;
      const num = component.getRandomArbitrary(min, max);
      expect(num).toBeLessThan(max);
      expect(num).toBeGreaterThanOrEqual(min);
    });
  });
});
