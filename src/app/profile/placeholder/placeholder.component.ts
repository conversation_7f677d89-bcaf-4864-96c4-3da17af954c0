import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-placeholder',
  templateUrl: './placeholder.component.html',
  styleUrls: ['./placeholder.component.scss']
})
export class PlaceholderComponent implements OnInit {
  public lines: any[] = [];

  constructor() {}

  ngOnInit() {
    const length = this.getRandomArbitrary(1, 4);
    for (let i = 0; i < length; i++) {
      this.lines.push({
        width: this.getRandomArbitrary(45, 90) + '%',
        title: i === 0
      });
    }
  }

  getRandomArbitrary(min: number, max: number) {
    return Math.floor(Math.random() * (max - min) + min);
  }
}
