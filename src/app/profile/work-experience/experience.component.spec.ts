import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ExperienceComponent } from './experience.component';
import { DateFormatService } from '../../shared/services';
import { CompatDatePipe, SearchHighlightPipe } from '../../shared/pipes';
import { Subject } from 'rxjs/index';
import { Store } from '@ngrx/store';

describe('ExperienceComponent', () => {
  let component: ExperienceComponent;
  let fixture: ComponentFixture<ExperienceComponent>;
  const stub: any = {};

  beforeEach(async(() => {
    const dateFormatService = new DateFormatService();
    stub.Store = new Subject();

    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [ExperienceComponent, CompatDatePipe, SearchHighlightPipe],
      providers: [{ provide: DateFormatService, useValue: dateFormatService }, { provide: Store, useValue: stub.Store }],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ExperienceComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });
});
