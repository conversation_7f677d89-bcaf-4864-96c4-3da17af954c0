<section id="profile-work-experience" class="card work-experience mb-4" *ngIf="experiences && experiences.length > 0">
    <div class="card-body">
        <h2 class="card-title mb-5 mt-3 text-md-center acc-experience-header">Werkervaring</h2>

        <div class="timeline-item row pb-4 acc-experience" *ngFor="let experience of experiences; index as idx">
            <ng-template [ngIf]="!experience.preview" [ngIfElse]="placehoder">
                <div class="col-md-6 pl-5 pr-md-4 text-md-right">
                    <span class="d-block font-weight-normal pb-1">
                        {{ experience.fromDate | compatDate | date:'MMMM y' }} -
                        {{ experience.toDate ? (experience.toDate | compatDate | date:'MMMM y') : 'Nu' }}
                        <span class="timeline-item__dash"> - </span>
                        <span class="timeline-item__period">({{ dateFormatter.parseTimeDiff(experience.fromDate, experience.toDate) }})</span>
                    </span>
                    <span class="d-block" [innerHTML]="experience.functionName | searchHighlight"></span>
                    <span class="d-block" [innerHTML]="experience.company | searchHighlight"></span>
                </div>
                <div class="col-md-6 pl-5 pl-md-4 pr-md-5 d-md-inline-block">
                    <app-read-more [fullText]="experience?.descriptionHtml" [slug]="'experience'"></app-read-more>
                </div>
            </ng-template>

            <ng-template #placehoder>
                <div class="col-md-6 pl-5 pr-md-4 text-md-right">
                    <app-placeholder></app-placeholder>
                </div>
                <div class="col-md-6 pl-5 pl-md-4 pr-md-5 d-md-inline-block">
                    <app-placeholder></app-placeholder>
                </div>
            </ng-template>
        </div>
    </div>
</section>
