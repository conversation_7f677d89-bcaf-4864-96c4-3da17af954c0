import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { JobSeekerProfileComponent } from './profile.component';
import { JobSeekerDetailResolver } from '../shared/resolvers';

const routes: Routes = [
  {
    path: ':id',
    component: JobSeekerProfileComponent,
    resolve: {
      jobSeeker: JobSeekerDetailResolver
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class JobSeekerProfileRoutingModule {}
