import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { EducationComponent } from './education.component';
import { Subject } from 'rxjs/index';
import { Store } from '@ngrx/store';
import { SearchHighlightPipe } from '../../shared/pipes';

describe('EducationComponent', () => {
  let component: EducationComponent;
  let fixture: ComponentFixture<EducationComponent>;
  const stub: any = {};

  beforeEach(async(() => {
    stub.Store = new Subject();

    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [EducationComponent, SearchHighlightPipe],
      providers: [{ provide: Store, useValue: stub.Store }],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(EducationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });
});
