<section id="profile-education" class="education card mb-4" *ngIf="educations && educations.length > 0">
    <div class="card-body">
        <h2 class="card-title text-md-center mb-5 mt-3 acc-education-header">Opleiding</h2>

        <div class="timeline-item row pb-4 acc-education" *ngFor="let education of educations">
            <ng-template [ngIf]="!education.preview" [ngIfElse]="placehoder">
                <div class="col-md-6 pl-5 pr-md-4 text-md-right">
                    <p class="m-0 font-weight-normal">
                        {{ education.fromDate | date:'y' }} -
                        {{ education.toDate ? (education.toDate | date:'y') : 'Nu' }}
                        <span [innerHTML]="education.grade | searchHighlight"></span>
                    </p>
                    <p class="m-0" *ngIf="education.diploma === true">Diploma behaald</p>
                </div>
                <div class="col-md-6 pl-5 pl-md-4 pr-md-5 d-md-inline-block">
                    <p class="m-0" [innerHTML]="education.school | searchHighlight"></p>
                    <p class="m-0" [innerHTML]="education.fieldOfStudy | searchHighlight"></p>
                    <p class="m-0">
                        <app-read-more [fullText]="education.descriptionHtml" [slug]="'education'"></app-read-more>
                    </p>
                </div>
            </ng-template>

            <ng-template #placehoder>
                <div class="col-md-6 pl-5 pr-md-4 text-md-right">
                    <app-placeholder></app-placeholder>
                </div>
                <div class="col-md-6 pl-5 pl-md-4 pr-md-5 d-md-inline-block">
                    <app-placeholder></app-placeholder>
                </div>
            </ng-template>
        </div>
    </div>
</section>
