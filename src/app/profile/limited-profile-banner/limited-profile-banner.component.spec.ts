import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Recruiter } from '../../classes';
import { LimitedProfileBannerComponent } from './limited-profile-banner.component';
import { ProfileNavigationService, RecruiterLimitationsType, RecruiterService } from '../../shared/services';
import { Subject } from 'rxjs/index';
import { BehaviorSubject, Observable } from 'rxjs';

describe('LimitedProfileBannerComponent', () => {
  let component: LimitedProfileBannerComponent;
  let fixture: ComponentFixture<LimitedProfileBannerComponent>;
  const stub: any = {};

  const recruiterData = <Recruiter>{
    isLogged: true,
    hasValidProduct: true,
    hasSubscription: true,
    hasDeductibleSubscription: true,
    credits: 14,
    companyVerified: true
  };

  beforeEach(() => {
    stub.ProfileNavigationService = jasmine.createSpyObj('ProfileNavigationService', ['goToSearchResultsPage']);
    stub.RecruiterService = {
      setRecruiterLimitationsType: jasmine.createSpy('setRecruiterLimitationsType'),
      limitationsTypeChange: new Subject(),
      limitationsType: RecruiterLimitationsType.NoProfile,
      fetchRecruiter: jasmine.createSpy('fetchRecruiter'),
      getSuggestibleProducts: (prods: string) => new BehaviorSubject<string>(prods),
      refreshProducts: jasmine.createSpy('refreshProducts'),
      fetchSuggestibleProducts: jasmine.createSpy('fetchSuggestibleProducts')
    };

    TestBed.configureTestingModule({
      declarations: [LimitedProfileBannerComponent],
      providers: [
        { provide: ProfileNavigationService, useValue: stub.ProfileNavigationService },
        { provide: RecruiterService, useValue: stub.RecruiterService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(LimitedProfileBannerComponent);
    component = fixture.componentInstance;
    component['recruiter'] = recruiterData;
    component.type = '';
    component.suggestibleProducts = [];
    fixture.detectChanges();
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('#ngOnInit', () => {
    it('should set the banner type', () => {
      component['setLimitationsType'] = jasmine.createSpy('setLimitationsType');
      component['setSuggestibleProducts'] = jasmine.createSpy('setSuggestibleProducts');

      component.ngOnInit();

      expect(component['setLimitationsType']).toHaveBeenCalledWith(stub.RecruiterService.limitationsType);
      expect(component['setLimitationsType']).toHaveBeenCalledTimes(1);
    });

    it('should listen to banner type changes', () => {
      const setLimitationsTypeSpy = jasmine.createSpy('setLimitationsType');
      component['setLimitationsType'] = setLimitationsTypeSpy;

      setLimitationsTypeSpy.calls.reset();

      stub.RecruiterService.limitationsTypeChange.next(RecruiterLimitationsType.DailyLimit);

      expect(component['setLimitationsType']).toHaveBeenCalledWith(RecruiterLimitationsType.DailyLimit);
      expect(component['setLimitationsType']).toHaveBeenCalledTimes(1);
    });

    it('should observe suggestible products result', () => {
      const setSuggestibleProductsSpy = jasmine.createSpy('setSuggestibleProducts');
      component['setSuggestibleProducts'] = setSuggestibleProductsSpy;

      setSuggestibleProductsSpy.calls.reset();
      stub.RecruiterService.getSuggestibleProducts('kasjbdkjbdkajbd').next('abcd');

      expect(component['setSuggestibleProducts']).toHaveBeenCalledTimes(0);
    });
  });

  describe('#ngOnDestroy', () => {
    it('should unsubscribe to the component promises', () => {
      component['componentDestroyed'] = jasmine.createSpyObj('componentDestroyed', ['next', 'unsubscribe']);

      component.ngOnDestroy();

      expect(component['componentDestroyed'].next).toHaveBeenCalledTimes(1);
      expect(component['componentDestroyed'].next).toHaveBeenCalledWith();
      expect(component['componentDestroyed'].unsubscribe).toHaveBeenCalledTimes(1);
      expect(component['componentDestroyed'].unsubscribe).toHaveBeenCalledWith();
    });
  });

  describe('#navigateToSearchResultsPage', () => {
    it('should set the banner type', () => {
      component['setBannerType'] = jasmine.createSpy('setBannerType');

      component.navigateToSearchResultsPage();

      expect(stub.ProfileNavigationService.goToSearchResultsPage).toHaveBeenCalledTimes(1);
      expect(stub.ProfileNavigationService.goToSearchResultsPage).toHaveBeenCalledWith();
    });
  });

  describe('#setLimitationsType', () => {
    it('should set a limitations type', () => {
      component['setLimitationsType'](RecruiterLimitationsType.NoProfile);

      expect(component.type).toEqual(RecruiterLimitationsType.NoProfile);
    });

    it('should set an empty limitations type', () => {
      component['setLimitationsType']('');

      expect(component.type).toEqual('');
    });
  });

  describe('#setSelectedProductCode', () => {
    it('should set selected product code', () => {
      component['setSelectedProductCode']('some_product_code');
      expect(component.selectedProductCode).toEqual('some_product_code');
    });
  });

  describe('#notHot', () => {
    it('should set isNotInterestedInBuyingProfileCredits flag to false', () => {
      component['notHot']();
      expect(component.isInterestedInBuyingProfileCredits).toEqual(false);
    });
  });

  // describe('#toTheBasket', () => {
  //   it('should send recruiter to the basket', () => {
  //     const replaceSpy = spyOn(window.location, 'replace');

  //     const webShopUrl = 'www.webshopurl.com';
  //     const productCode = 'somePromocode';
  //     component['setSelectedProductCode'](productCode);
  //     component['purchaseProfileCreditsUrl'] = webShopUrl;
  //     component['toTheBasket']();
  //     const url = `${webShopUrl}/products/${component.selectedProductCode}`;

  //     expect(replaceSpy).toHaveBeenCalledTimes(1);
  //     expect(replaceSpy).toHaveBeenCalledWith(url);
  //   });
  // });
});
