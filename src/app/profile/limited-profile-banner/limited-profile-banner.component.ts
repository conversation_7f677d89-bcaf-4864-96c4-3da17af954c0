import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Recruiter } from '../../classes';
import { ProfileNavigationService, RecruiterLimitationsType, RecruiterService } from '../../shared/services';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs/index';
import { environment, getLoginUrl } from '../../../environments/environment';
import { SuggestibleProduct } from '../../classes';

@Component({
  selector: 'app-limited-profile-banner',
  templateUrl: './limited-profile-banner.component.html',
  styleUrls: ['./limited-profile-banner.component.scss']
})
export class LimitedProfileBannerComponent implements OnInit, OnDestroy {
  @Input()
  private recruiter: Recruiter;
  public type = '';
  public suggestibleProducts: Array<SuggestibleProduct> = [];
  private componentDestroyed: Subject<any> = new Subject();
  public recruiterLimitationsType = RecruiterLimitationsType;
  public loginUrl = getLoginUrl(document && document.location ? document.location.href : undefined);
  public purchaseProfileCreditsUrl = environment.url.purchaseProfileCreditsUrl;
  public isInterestedInBuyingProfileCredits = true;
  public selectedProductCode = '';
  public locationReplaceFn = window.location.replace;
  private suggestProductsOn = ['localhost', 'b2b-acc.persgroep.digital', 'www.persgroepemploymentsolutions.nl'];
  private dataGtmAfrekenen = 'limited-profile-to-shop';

  constructor(public profileNavigation: ProfileNavigationService, private recruiterService: RecruiterService) {}

  ngOnInit(): void {
    this.setLimitationsType(this.recruiterService.limitationsType);

    this.recruiterService.limitationsTypeChange.pipe(takeUntil(this.componentDestroyed)).subscribe(limitationsType => {
      this.setLimitationsType(limitationsType);
    });

    if (this.isNdp()) {
      this.recruiterService
        .getSuggestibleProducts()
        .pipe(takeUntil(this.componentDestroyed))
        .subscribe(products => {
          this.setSuggestibleProducts(products);
        });
    }
  }

  ngOnDestroy() {
    this.componentDestroyed.next();
    this.componentDestroyed.unsubscribe();
  }

  navigateToSearchResultsPage(): void {
    this.profileNavigation.goToSearchResultsPage();
  }

  notHot(): void {
    this.isInterestedInBuyingProfileCredits = false;
  }

  toTheBasket(): void {
    const url = `${this.purchaseProfileCreditsUrl}/products/${this.selectedProductCode}`;
    window.location.replace(url);
  }

  setSelectedProductCode(productCode: string) {
    this.selectedProductCode = this.selectedProductCode === productCode ? '' : productCode;
    this.dataGtmAfrekenen = `limited-profile-to-shop-${productCode}`;
  }

  shouldSuggestProducts() {
    return (
      this.type === this.recruiterLimitationsType.InvalidSubscription &&
      this.isNdp() &&
      this.isInterestedInBuyingProfileCredits
    );
  }

  shouldDisplayOldFooter() {
    return this.type === this.recruiterLimitationsType.InvalidSubscription && !this.isNdp();
  }

  private isNdp(): boolean {
    return this.suggestProductsOn.indexOf(document.location.hostname) > -1;
  }

  private setLimitationsType(limitationsType: string): void {
    this.type = limitationsType;
  }

  private setSuggestibleProducts(products: Array<SuggestibleProduct>): void {
    this.suggestibleProducts = products;
  }
}
