@import "components";

.limited-profile-banner {
  background-color: white;
  bottom: 0;
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.5);
  left: 0;
  position: fixed;
  right: 0;
  width: 100%;
  z-index: 9;

  @include media-breakpoint-up(lg) {
    .content {
      margin: auto;
      width: 85%;
    }
  }

  @include media-breakpoint-down(md) {
    h2 {
      font-size: 1.5em;
    }

    p {
      font-size: 0.9em;
    }
  }

  .products {
    display: flex;
  }

  header {
    font-size: 2rem;
    padding: 0 0 0.5rem 1.5rem;
  }

  article {

    ol {
      list-style-type: none;
      margin: 0;
      padding: 1rem 1rem 0;
    }

    header {
      font-size: 1rem;
      font-weight: 800;
      padding: 0.25rem;
      text-transform: uppercase;
    }

    button {
      background: #e9e9e9;
      border: 0;
      outline: none;
    }

    input {
      -webkit-appearance: none;
      background-color: white;
      border: 1px solid #afafaf;
      border-radius: 50%;
      cursor: pointer;
      height: 1.3rem;
      margin-top: 0.5rem;
      outline: none;
      vertical-align: middle;
      width: 1.3rem;
    }

    input:checked {
      background-color: gray;
    }

    .price {
      border-bottom: 1px solid #979797;
      font-size: 1rem;
      padding: 0 0 0.2rem 0.3rem;
    }

    background-color: #e9e9e9;
    display: flex;
    flex: 1;
    flex-direction: column;
    font-size: 0.9rem;
    justify-content: space-between;
    margin: 0.5rem;
    padding: 0.5rem;

  }

  .footer {
    margin: auto;
    width: fit-content;
  }

  .footer button {
    border: 1px solid black;
    border-radius: 1rem;
    margin: 0.5rem;
    outline: none;
    padding: 0.5rem 5rem;
  }

  .footer button.afrekenen {
    background-color: #fabb22;
    border-color: #fabb22;
  }

  .footer button:disabled {
    background-color: #bdbdbd;
    border: none;
    color: black;
  }

  label {
    cursor: pointer;
    display: inline-block;
    font-size: 1rem;
    margin: 0.36rem 0 0rem 0.5rem;
  }

  .select {
    display: flex;
  }
}
