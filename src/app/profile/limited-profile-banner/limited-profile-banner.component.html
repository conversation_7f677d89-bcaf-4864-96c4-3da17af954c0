<section class="limited-profile-banner" *ngIf="type !== recruiterLimitationsType.Unlimited">
    <div class="container">
        <div class="content py-2 py-md-4" *ngIf="type === recruiterLimitationsType.NoProfile">
            <h2>Bekijk volledige profielen</h2>
            <p class="mb-2 mb-md-5">Je hebt profielcredits nodig om volledige profielen te bekijken en op te slaan.</p>

            <div class="row">
                <div class="mb-3 mb-md-0 col-md-6 text-center">
                    <span>Al toegang?</span>
                    &nbsp;
                    <a href="{{ loginUrl }}" data-gtm="limited-profile-login">Log in</a>
                </div>
                <div class="col-12 col-md-6 text-center">
                    <a href="{{ purchaseProfileCreditsUrl }}">
                        <button class="btn btn-primary"
                                data-gtm="limited-profile-buy-credits">
                            Koop profielcredits
                        </button>
                    </a>

                </div>
            </div>
        </div>

        <div class="content py-2 py-md-4 " *ngIf="shouldSuggestProducts()">

            <header>Profielen Bekijken?</header>
            <div class="products">
                <article *ngFor="let product of suggestibleProducts; let i = index">
                    <div>
                        <header>{{product.name}}</header>
                        <ol>
                            <li *ngFor="let description of product.descriptions">
                                <p>{{description}}</p>
                            </li>
                        </ol>
                    </div>
                    <div>
                        <div class="price">&euro;&nbsp;{{product.price}},-</div>
                        <div class="select">
                            <input
                                    id={{product.productCode}}
                                    type="checkbox"
                                    (click)="setSelectedProductCode(product.productCode)"
                                    [checked]="selectedProductCode === product.productCode"
                            />
                            <label for={{product.productCode}} >selecteer</label>
                        </div>
                    </div>
                </article>
            </div>
            <div class="footer" >
                <button (click)="notHot()"
                        data-gtm="limited-profile-no-sub-cancel">
                    Geen interesse
                </button>
                <button class="afrekenen"
                        [disabled]="selectedProductCode === ''"
                        (click)="toTheBasket()"
                        [attr.data-gtm]="dataGtmAfrekenen">
                    Afrekenen
                </button>
            </div>
        </div>

        <div class="content py-2 py-md-4" *ngIf="shouldDisplayOldFooter()">
            <h2>Profielen bekijken?</h2>
            <p>Koop nieuwe credits of sluit een abonnement af om meer profielen te bekijken.</p>

            <div class="row">
                <div class="mb-3 mb-md-0 col-md-6 text-center">
                    <button (click)="navigateToSearchResultsPage()"
                            class="btn btn-transparent"
                            data-gtm="limited-profile-no-sub-cancel">Geen interesse</button>
                </div>
                <div class="col-12 col-md-6 text-center">
                    <a href="{{ purchaseProfileCreditsUrl }}">
                        <button class="btn btn-primary"
                                data-gtm="limited-profile-to-shop">
                            Naar de webshop
                        </button>
                    </a>
                </div>
            </div>
        </div>

        <div class="content py-2 py-md-4" *ngIf="type === recruiterLimitationsType.UnverifiedCompany">
            <h2>Verifieer jouw account om dit profiel te bekijken</h2>
            <p>Om dit profiel te kunnen bekijken, moet dit account geverifieerd zijn. Bel 020-2042200 om direct geverifieerd te worden door onze customer service.</p>
        </div>

        <div class="content py-2 py-md-4" *ngIf="type === recruiterLimitationsType.DailyLimit">
            <h2>Maximum bereikt</h2>
            Je kunt maximaal 250 profielen per dag bekijken.
        </div>
    </div>
</section>
