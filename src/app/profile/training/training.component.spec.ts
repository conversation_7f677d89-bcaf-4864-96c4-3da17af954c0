import { TestBed, ComponentFixture } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { TrainingComponent } from './training.component';
import { MonthPipe, SearchHighlightPipe } from '../../shared/pipes';
import { Subject } from 'rxjs/index';
import { Store } from '@ngrx/store';

describe('TrainingComponent', () => {
  let componentFixture: ComponentFixture<TrainingComponent>, component: TrainingComponent;
  const stub: any = {};

  beforeEach(() => {
    stub.Store = new Subject();

    TestBed.configureTestingModule({
      declarations: [TrainingComponent, MonthPipe, SearchHighlightPipe],
      providers: [{ provide: Store, useValue: stub.Store }],
      schemas: [NO_ERRORS_SCHEMA]
    });

    TestBed.compileComponents();
    componentFixture = TestBed.createComponent(TrainingComponent);
    componentFixture.detectChanges();
    component = componentFixture.componentInstance;
    component.trainings = [];
  });

  describe('#constructor', () => {
    it('should exist', () => {
      expect(component).toBeDefined();
    });
  });
});
