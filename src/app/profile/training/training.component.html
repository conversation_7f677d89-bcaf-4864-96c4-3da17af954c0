<section id="profile-training" class="training card mb-4" *ngIf="trainings && trainings.length > 0">
    <div class="card-body">
        <h2 class="card-title text-md-center mb-5 mt-3 acc-training-header">Training en certificering</h2>

        <div class="timeline-item row pb-4 acc-training"
             *ngFor="let training of trainings; index as idx">
            <ng-template [ngIf]="!training.preview" [ngIfElse]="placehoder">
                <div class="col-md-6 pl-5 pr-md-4 text-md-right">
                    <span class="d-block font-weight-normal pb-1">
                        <span class="acc-training-date d-block d-md-inline">
                            {{ training.month | month | date:'MMMM' }} {{ training.year }}
                        </span>
                        <span class="acc-training-name" [innerHTML]="training.name | searchHighlight"></span>
                    </span>
                </div>
                <div class="col-md-6 pl-5 pl-md-4 pr-md-5 d-md-inline-block">
                    <p class="m-0" [innerHTML]="training.institute | searchHighlight"></p>
                    <p class="m-0">
                        <app-read-more [fullText]="training.descriptionHtml" [slug]="'training'"></app-read-more>
                    </p>
                </div>
            </ng-template>

            <ng-template #placehoder>
                <div class="col-md-6 pl-5 pr-md-4 text-md-right">
                    <app-placeholder></app-placeholder>
                </div>
                <div class="col-md-6 pl-5 pl-md-4 pr-md-5 d-md-inline-block">
                    <app-placeholder></app-placeholder>
                </div>
            </ng-template>
        </div>
    </div>
</section>
