@import "components";

.profile {
  &-image-container {
    border-radius: 60px;
    margin-top: -60px;
    overflow: hidden;

    .img {
      height: 120px;
      width: 120px;
    }

    .default-img {
      background: transparent url("#{$project-images-folder}dummy-profile.svg") no-repeat scroll center center / 100%;
    }
  }

  &-introduction {
    margin: 0 auto;
    width: 98%;

    p {
      color: #6d6875;
      font-size: 16px;

      &.bold {
        font-weight: 700;
      }
    }

    ol, ul {
      list-style-position: inside;
      list-style-type: disc;
      padding-left: 0;

      li {
        white-space: nowrap;
        &:before {
          display: none;
        }
      }
    }
  }

  &-updated-date, &-via {
    position: absolute;
    top: 15px;

    @include media-breakpoint-down(md) {
      display: none;
    }

    p {
      color: $profile-intro-detail-color;
      font-size: $profile-intro-detail-text-fontsize;

      &.bold {
        font-weight: $profile-intro-detail-text-fontweight;
      }
    }
  }

  &-updated-date {
    right: 20px;

    p {
      span.icons {
        padding-left: 20px;
        padding-right: 25px;
        position: relative;

        .icon-dates {
          background-size: contain;
          display: inline-block;
          height: 16px;
          left: 0;
          position: absolute;
          top: calc(50% - 8px);
          width: 16px;

          &__update {
            background-image: url("#{$project-icons-folder}edit.svg");
          }
          &__view {
            background-image: url("#{$project-icons-folder}viewed.svg");
          }
        }
      }
    }
  }

  &-via {
    left: 20px;

    span {
      display: inline-block;
      position: relative;

      &:after {
        background-repeat: no-repeat;
        background-size: contain;
        content: "";
        display: inline-block;
        height: 30px;
        left: 5px;
        position: absolute;
      }

      &.intermediair:after {
        background-image: url("#{$project-images-folder}intermediair.svg");
        top: -14px;
        width: 118px;
      }
      &.nvb:after {
        background-image: url("#{$project-images-folder}nvb.svg");
        top: -19px;
        width: 166px;
      }
      &.itbanen:after {
        background-image: url("#{$project-images-folder}itbanen.svg");
        top: -21px;
        width: 142px;
      }
    }
  }
}
