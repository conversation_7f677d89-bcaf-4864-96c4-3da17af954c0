<header class="profile-header" id="profile-header">
    <div class="justify-content-center row">
        <div class="col-auto">
            <div class="profile-image-container">
                <img *ngIf="displayProfileManagerImage" class="img" [src]="profileImage" (error)="fallBackToOldProfileImage($event)" />
                <img *ngIf="displayFallbackImage" class="img" [src]="profileImageFallBack" />
                <div *ngIf="(!displayProfileManagerImage && !displayFallbackImage)" class="default-img img acc-default-img"></div>
            </div>
        </div>
    </div>
    <div class="profile-name-surname text-center h1 mt-4">
        <h1>{{name}}</h1>
    </div>

    <div class="profile-introduction text-center mt-4 px-4">
        <p *ngIf="lastWorkExperience" class="experience bold mb-0" >
            <span [innerHTML]="lastWorkExperience | searchHighlight"></span>
            <span *ngIf="lastWorkExperienceCompany"> bij {{lastWorkExperienceCompany}}</span>
        </p>
        <p *ngIf="lastEducation" class="education bold mb-3">
            <span>{{ lastEducation?.fieldOfStudy }}</span>
            <span *ngIf="lastEducation?.fieldOfStudy && lastEducation?.grade">, </span>
            <span>{{ lastEducation?.grade }}</span>
        </p>

        <div *ngIf="introductionTextHtml" class="regular" [innerHTML]="introductionTextHtml"></div>
    </div>

    <div class="profile-updated-date">
        <p>
            <span class="icons"
                  *ngIf="updatedDate">
                <i class="icon-dates icon-dates__update"></i>
                {{updatedDate | daysAgoDate}}
            </span>
            <span class="icons"
                  *ngIf="viewedDate">
                <i class="icon-dates icon-dates__view"></i>
                {{viewedDate | daysAgoDate}}
            </span>
        </p>
    </div>

    <div class="profile-via" *ngIf="site">
        <p>
            Profiel afkomstig van
            <span [ngClass]="{
                'intermediair' : site === 'intermediair',
                'nvb' : site === 'nvb',
                'itbanen' : site === 'itbanen'
            }"></span>
        </p>
    </div>
</header>
