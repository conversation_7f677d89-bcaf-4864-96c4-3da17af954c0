import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { DomSanitizer, SafeStyle } from '@angular/platform-browser';
import { Education } from '../../classes';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-profile-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class HeaderComponent {
  public profileImage: SafeStyle;
  public profileImageFallBack: SafeStyle;
  public displayShareProfileModal = false;
  public displayProfileManagerImage: boolean;
  public displayFallbackImage: boolean;
  @Input()
  name: string;
  @Input()
  lastEducation: Education;
  @Input()
  lastWorkExperience: string;
  @Input()
  lastWorkExperienceCompany: string;
  @Input()
  introductionTextHtml: string;
  @Input()
  site: string;
  @Input()
  updatedDate: number;
  @Input()
  viewedDate: number;
  @Input()
  cdn: string;
  @Input()
  set photo(photo: string) {
    if (photo && this.cdn) {
      this.profileImage = this.sanitizer.bypassSecurityTrustUrl(this.cdn + photo);
      this.profileImageFallBack = this.sanitizer.bypassSecurityTrustUrl(environment.photoPath + photo);
    } else {
      this.profileImage = null;
      this.profileImageFallBack = null;
    }
    this.displayProfileManagerImage = !!this.profileImage;
    this.displayFallbackImage = false;
  }

  constructor(private sanitizer: DomSanitizer) {}

  fallBackToOldProfileImage(error: any) {
    this.displayProfileManagerImage = false;
    this.displayFallbackImage = true;
  }

}
