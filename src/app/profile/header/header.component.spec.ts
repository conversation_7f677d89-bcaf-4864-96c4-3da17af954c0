import { TestBed, ComponentFixture } from '@angular/core/testing';
import { ChangeDetectionStrategy, NO_ERRORS_SCHEMA } from '@angular/core';
import { HeaderComponent } from './header.component';
import { SearchHighlightPipe, DaysAgoDatePipe } from '../../shared/pipes';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs/index';

describe('HeaderComponent', () => {
  let componentFixture: ComponentFixture<HeaderComponent>, component: HeaderComponent;
  const stub: any = {};

  beforeEach(() => {
    stub.Store = new Subject();

    TestBed.configureTestingModule({
      declarations: [HeaderComponent, SearchHighlightPipe, DaysAgoDatePipe],
      providers: [{ provide: Store, useValue: stub.Store }],
      schemas: [NO_ERRORS_SCHEMA]
    }).overrideComponent(HeaderComponent, {
      set: { changeDetection: ChangeDetectionStrategy.Default }
    });

    TestBed.compileComponents();
    componentFixture = TestBed.createComponent(HeaderComponent);
    component = componentFixture.componentInstance;
    component.photo = 'photo';
    spyOn(component['sanitizer'], 'bypassSecurityTrustUrl').and.returnValue('/');
    componentFixture.detectChanges();
  });

  describe('#constructor', () => {
    it('should exist', () => {
      expect(component).toBeDefined();
    });

    it('should set a null photo', () => {
      component.cdn = null;
      component.photo = null;

      expect(component).toBeDefined();
      expect(component.profileImage).toEqual(null);
      expect(component.profileImageFallBack).toEqual(null);
    });

    it('should set a null photo', () => {
      component.cdn = 'cdn';
      component.photo = 'photo';

      expect(component).toBeDefined();
      expect(component.profileImage).toEqual('/');
      expect(component.profileImageFallBack).toEqual('/');
    });

  });

  describe('fallBackToOldProfileImage', () => {
    it('should fall back to old profile image, if image cannot be retrieved from cdn', () => {
      component.fallBackToOldProfileImage(null);
      expect(component.displayProfileManagerImage).toEqual(false);
      expect(component.displayFallbackImage).toEqual(true);
    });
  });

});

