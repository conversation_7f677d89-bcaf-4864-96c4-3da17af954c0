@import "components";

section.profile /deep/ {
  background-color: $profile-backgroundcolor;
  font-size: $profile-fontsize;
  line-height: $profile-lineheight;

  .profile__operationalbar {
    &--header {
      display: none;
      @include media-breakpoint-up(md) {
        display: block;
      }
    }

    &--body {
      display: block;
      @include media-breakpoint-up(md) {
        display: none;
      }
    }
  }

  main {
    &.container {
      padding-top: (1rem * 7);
    }

    section {
      position: relative;

      .card-body {
        position: relative;
      }
    }
  }

  .timeline-item {
    font-weight: 300;
    position: relative;
    &::before {
      background-color: $profile-timeline-default-color;
      bottom: -1rem;
      content: "";
      display: block;
      left: calc(1rem - 1px);
      position: absolute;
      top: 1rem;
      width: 2px;
      z-index: 1;
      @include media-breakpoint-up(md) {
        left: calc(50% - 1px);
      }
    }
    &:last-child::before {
      display: none;
    }

    > div:first-of-type {
      position: relative;
      &::before {
        background: $profile-timeline-default-backgroundcolor;
        border: 2px solid $profile-timeline-default-color;
        border-radius: 50%;
        content: "";
        display: block;
        height: 1rem;
        left: (1rem / 2);
        position: absolute;
        top: (1rem / 5);
        width: 1rem;
        z-index: 2;
        @include media-breakpoint-up(md) {
          left: auto;
          right: -(1rem / 2);
        }
      }
    }

    &:first-of-type > div:first-of-type::before {
      background: $profile-timeline-current-color;
      border-color: $profile-timeline-current-color;
    }

    &__dash {
      @include media-breakpoint-down(sm) {
        display: none;
      }
    }

    &__period {
      @include media-breakpoint-down(sm) {
        display: block;
      }
    }
  }
}
