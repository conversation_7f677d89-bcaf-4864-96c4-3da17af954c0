import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CUSTOM_ELEMENTS_SCHEMA, LOCALE_ID } from '@angular/core';
import { registerLocaleData } from '@angular/common';
import localeNl from '@angular/common/locales/nl';
import { BrowserModule } from '@angular/platform-browser';
import { ReactiveFormsModule } from '@angular/forms';
import { AppComponent } from './app.component';
import { SharedModule } from './shared/shared.module';
import { AppRoutingModule } from './app.routing';
import { StoreDevtoolsModule } from '@ngrx/store-devtools';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { FilterEffectsService, AutoCompleteEffects } from 'app/shared/services';
import { reducers, metaReducers } from 'app/app.store';
import { environment } from '../environments/environment';
import * as <PERSON> from 'raven-js';
import { RavenErrorHandler } from './error/raven-error';
import { ServiceWorkerModule } from '@angular/service-worker';

Raven.config('https://<EMAIL>/1237820', {
  release: environment.version,
  ignoreUrls: [
    // GTM
    /\/(gtm|ga|analytics)\.js/i,
    // Chrome extensions
    /extensions\//i,
    /^chrome:\/\//i,
  ]
}).install();
registerLocaleData(localeNl);

if ('serviceWorker' in navigator) {
  navigator.serviceWorker.getRegistrations().then(function (registrations) {
    for (const registration of registrations) {
      registration.unregister();
    }
  });
}

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    ReactiveFormsModule,
    EffectsModule.forRoot([FilterEffectsService, AutoCompleteEffects]),
    AppRoutingModule,
    SharedModule.forRoot(),
    StoreModule.forRoot(reducers, { metaReducers }),
    StoreDevtoolsModule.instrument({
      maxAge: 20,
      logOnly: environment.production
    }),
    ServiceWorkerModule.register('/old-recruiter/ngsw-worker.js', { scope: '/old-recruiter/', enabled: false })
  ],
  providers: [{ provide: ErrorHandler, useClass: RavenErrorHandler }, { provide: LOCALE_ID, useValue: 'nl' }],
  bootstrap: [AppComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AppModule {}
