import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LegacySearchUrlGuard, LegacyJobSeekerUrlGuard, SavedSearchDirectAccessGuard } from './shared/guards';
import { RecruiterResolver } from './shared/resolvers';
import { environment } from '../environments/environment';

export const routes: Routes = [
  {
    path: 'kandidaten',
    loadChildren: './jobseekers/jobseekers.module#JobSeekersModule',
    resolve: {
      recruiter: RecruiterResolver
    }
  },
  {
    path: 'search',
    redirectTo: 'kandidaten/zoeken',
    pathMatch: 'full'
  },
  {
    path: 'saved-searches',
    redirectTo: 'kandidaten/zoekopdrachten',
    pathMatch: 'full'
  },
  {
    path: 'favorites',
    redirectTo: 'kandidaten/favorieten',
    pathMatch: 'full'
  },
  {
    path: 'kandidaat',
    loadChildren: './profile/profile.module#JobSeekerProfileModule',
    data: {
      title: environment.titles.jobseeker,
      trackingEvent: 'recruiter-detail'
    },
    resolve: {
      recruiter: RecruiterResolver
    }
  },
  {
    path: 'job-seeker',
    redirectTo: 'kandidaat',
    pathMatch: 'prefix'
  },
  {
    path: 'legacy-search',
    canActivate: [LegacySearchUrlGuard],
    loadChildren: './error/error.module#ErrorModule'
  },
  {
    path: 'legacy-job-seeker',
    canActivate: [LegacyJobSeekerUrlGuard],
    loadChildren: './error/error.module#ErrorModule'
  },
  {
    path: 'view-saved-search/:recruiterId/:savedSearchId',
    canActivate: [SavedSearchDirectAccessGuard],
    loadChildren: './error/error.module#ErrorModule'
  },
  {
    path: 'error',
    loadChildren: './error/error.module#ErrorModule'
  },
  {
    path: '',
    redirectTo: 'kandidaten',
    pathMatch: 'full'
  },
  {
    path: '**',
    loadChildren: './error/error.module#ErrorModule'
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes, { scrollPositionRestoration: 'disabled' })],
  exports: [RouterModule]
})
export class AppRoutingModule {}
