import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { Recruiter } from '../classes';
import {
  PlatformCoreService
} from '../shared/services';

@Component({
  selector: 'app-jobseekers',
  templateUrl: './jobseekers.component.html'
})
export class JobSeekersComponent implements OnInit, OnDestroy {
  public recruiter: Recruiter;
  public locationError: boolean;
  public hasPlatformCoreAccount = false;
  private componentDestroyed: Subject<any> = new Subject();

  constructor(
    private route: ActivatedRoute,
    private platformCoreService: PlatformCoreService
  ) {
    platformCoreService.hasPlatformCoreAccount.pipe(takeUntil(this.componentDestroyed)).subscribe((hasPlatformCoreAccount: boolean) => {
      this.hasPlatformCoreAccount = hasPlatformCoreAccount;
    });
  }

  ngOnInit(): void {
    this.route.data.pipe(takeUntil(this.componentDestroyed)).subscribe((data: { recruiter: Recruiter }) => {
      this.recruiter = data.recruiter;
    });
  }

  ngOnDestroy() {
    this.componentDestroyed.next();
    this.componentDestroyed.unsubscribe();
  }
}
