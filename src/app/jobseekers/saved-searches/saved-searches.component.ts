import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { Store } from '@ngrx/store';
import { AppState } from '../../types/app-state';
import { Search } from '../../store/models/search.model';
import { SavedSearchService } from '../../shared/services';

@Component({
  selector: 'app-saved-searches',
  templateUrl: './saved-searches.component.html',
  styleUrls: ['./saved-searches.component.scss']
})
export class SavedSearchesComponent implements OnInit {
  public savedSearches: any;
  public openDelete: Array<boolean> = [];
  public search$: Observable<Search>;
  public searchQuery: { [key: string]: string[] };
  public locationQuery = '';
  public isFetching = true;

  constructor(private router: Router, public savedSearchService: SavedSearchService, public store: Store<AppState>) {}

  ngOnInit(): void {
    this.savedSearchService.get().subscribe({
      complete: () => {
        this.isFetching = false;
      },
      next: response => {
        this.savedSearches = response;
      },
      error: () => {
        this.isFetching = false;
        this.savedSearches = [];
      }
    });

    this.search$ = this.store.select(this.getStoreSearchData);
  }

  private getStoreSearchData(store: AppState): Search {
    return store.search;
  }

  deleteSavedSearch(idx: number, savedSearchId: string) {
    this.savedSearchService.del(savedSearchId).subscribe(response => {
      return response;
    });

    this.savedSearches.splice(idx, 1);
    this.openDelete = [false];
  }

  changeEmailUpdates(idx: number, frequency: string) {
    const currentSavedSearch = this.savedSearches[idx];
    const previousFrequency = currentSavedSearch.frequency;
    currentSavedSearch.frequency = frequency;
    this.savedSearchService.put(currentSavedSearch.savedSearchId, currentSavedSearch).subscribe(
      () => {},
      () => {
        currentSavedSearch.frequency = previousFrequency;
      }
    );
  }

  clickOnSavedSearch(searchData: any) {
    const navigationExtras = this.savedSearchService.getParamsFromSearch(searchData.searchParameters);
    this.router.navigate(['/kandidaten/zoeken'], navigationExtras);
  }
}
