<div class="saved-search-spinner-block">
    <div *ngIf="isFetching; else savedSearchesBlock" class="overlay">
        <div class="search-spinner"></div>
    </div>
</div>

<ng-template #savedSearchesBlock>
    <div class="container">
        <div *ngIf="savedSearches?.length; else noSavedSearchesFound" class="row">
            <div class="search-sort"></div>

            <div class="saved-search-filter-titles col-12">
                <div class="left">
                    <PERSON><PERSON>
                </div>
            </div>

            <div class="saved-search-item col-12" *ngFor="let search of savedSearches; let idx = index" [class.delete-open]="openDelete[idx]">
                <div class="saved-search-item-search">
                <span class="search-title" (click)="clickOnSavedSearch(search)" data-gtm="saved-search-clicked">
                    {{ search.name }}
                </span>
                </div>
                <div class="saved-search-item-frequency">
                    <div class="form-select">
                        <select class="value"
                                (change)="changeEmailUpdates(idx, $event.target.value)"
                                data-gtm="saved-search-updates"
                                [attr.data-slug]="search.frequency">
                            <option [attr.selected]="search.frequency == 'Nooit' ? true : null" value="Nooit">Geen updates</option>
                            <option [attr.selected]="search.frequency == 'Dagelijks' ? true : null" value="Dagelijks">Dagelijkse updates</option>
                            <option [attr.selected]="search.frequency == 'Wekelijks' ? true : null" value="Wekelijks">Wekelijkse updates</option>
                        </select>
                    </div>
                </div>
                <div class="saved-search-item-delete">
                    <i class="icon-delete" (click)="openDelete[idx] = !openDelete[idx]" data-gtm="saved-search-delete-icon"></i>
                    <div class="item-delete-cancel" (click)="openDelete[idx] = !openDelete[idx]" data-gtm="saved-search-delete-cancel">
                        Annuleren
                    </div>
                    <div class="item-delete-delete" (click)="deleteSavedSearch(idx, search.savedSearchId)" data-gtm="saved-search-delete-permanent">
                        <i class="icon-delete"></i>
                        <span class="hide-mobile">Verwijderen</span>
                    </div>
                </div>
            </div>
        </div>
        <ng-template #noSavedSearchesFound>
            <div class="row page">
                <p>U hebt nog geen opgeslagen zoekopdrachten.</p>
                <p>Klik op "Zoekopdracht bewaren" in het zoekscherm om uw eerste zoekopdracht op te slaan.</p>
            </div>
        </ng-template>
    </div>
</ng-template>
