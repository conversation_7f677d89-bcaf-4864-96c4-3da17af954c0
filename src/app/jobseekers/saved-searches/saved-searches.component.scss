@import "components";

.search-sort {
  border-bottom: 1px solid #d0cdd4;
  margin-top: 20px;
  &::after {
    clear: both;
    content: "";
    display: table;
  }
  .left {
    display: inline-block;
  }
  .right {
    display: inline-block;
  }
  .page-info {
    display: none;
    line-height: 38px;
    @include media-breakpoint-up(md) {
      display: inline-block;
    }
    strong {
      font-weight: 600;
    }
  }
  .page-size {
    display: inline-block;
    .value:last-child {
      margin-bottom: 15px;
    }
  }
  .sort {
    display: none;
    line-height: 38px;
    margin-right: 32px;
    @include media-breakpoint-up(md) {
      display: inline-block;
    }
    .form-select {
      display: inline-block;
    }
  }
  @include media-breakpoint-up(md) {
    display: inherit;
    font-size: 14px;
    margin-bottom: 15px;
    margin-top: 46px;
    a {
      color: $dpes-seafoam-blue;
      cursor: pointer;
      text-decoration: none;
      &.active {
        background: transparent;
        color: $text-color;
        cursor: text;
        font-weight: 700;
        pointer-events: none;
      }
    }
  }
}

.saved-search-filter-titles {
  display: none;
  font-size: 14px;
  font-weight: 600;
  padding: 10px 30px;
  @include media-breakpoint-up(md) {
    display: flex;
  }
  .left {
    width: 50%;
  }
  .right {
    text-align: right;
    width: 40%;
  }
}

.saved-search-item {
  background: #fff;
  border: 2px solid transparent;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.2);
  color: #312d38;
  display: flex;
  flex-direction: column;
  margin: 10px 0;
  padding: 20px 15px;
  position: relative;
  text-decoration: none;
  transition: border 0.2s ease;
  @include media-breakpoint-up(md) {
    align-items: stretch;
    flex-direction: row;
    padding: 30px;
  }
  &:hover {
    border-color: $dpes-seafoam-blue;
  }

  .saved-search-item-search {
    line-height: 26px;
    overflow: hidden;
    padding-right: 30px;
    transition: 0.2s all ease-in-out;
    @include media-breakpoint-up(md) {
      padding-right: 0;
      width: 90%;
    }
    .search-title {
      cursor: pointer;
      display: block;
      font-size: 14px;
      @include media-breakpoint-up(md) {
        font-size: 20px;
        line-height: 48px;
      }
      &:hover {
        color: $dpes-seafoam-blue;
      }
    }
    .search-specs {
      display: block;
      font-size: 14px;
    }
    .search-date {
      color: #9e9ba3;
      display: block;
      font-size: 14px;
      margin-top: 10px;
    }
  }
  .saved-search-item-frequency,
  .saved-search-item-delete {
    @include media-breakpoint-up(md) {
      align-items: center;
      display: flex;
      justify-content: flex-end;
    }
  }
  .saved-search-item-frequency {
    font-size: 14px;
    transition: 0.2s all ease-in-out;
    @include media-breakpoint-up(md) {
      width: 40%;
    }
    .value {
      font-size: 14px;
      padding-left: 0;
      @include media-breakpoint-up(md) {
        font-size: 17px;
        padding-left: 8px;
      }
      &:last-child {
        margin-bottom: 0;
      }
    }
    .form-select {
      display: inline-block;
    }
    @media screen and (-webkit-min-device-pixel-ratio: 0) {
      .form-select select {
        padding-right: 40px;
      }
    }

  }
  .saved-search-item-delete {
    align-items: center;
    bottom: 0;
    cursor: pointer;
    display: flex;
    justify-content: center;
    padding-right: 15px;
    position: absolute;
    right: 0;
    top: 0;
    transition: all 0.2s ease-in-out 0s;
    @include media-breakpoint-up(md) {
      padding-right: 20px;
      position: static;
      width: 10%;
    }
    .icon-delete {
      background: transparent url("#{$project-icons-folder}delete.svg") no-repeat scroll right center;
      display: block;
      height: 100%;
      margin-right: 52px;
      padding: 20px;
      width: 22px;
      @include media-breakpoint-up(md) {
        padding: 1.5rem 2.5rem;
      }
    }
    .item-delete-cancel,
    .item-delete-delete {
      align-items: center;
      align-self: stretch;
      display: flex;
      font-size: 14px;
      font-weight: 600;
      justify-content: center;
      opacity: 0;
      transition: 0.2s all ease-in-out;
      visibility: hidden;
      width: 0;
      @include media-breakpoint-up(md) {
        margin-bottom: -32px;
        margin-top: -32px;
      }
    }
    .item-delete-cancel {
      color: $dpes-seafoam-blue;
      text-indent: -9999px;
      @include media-breakpoint-up(md) {
        text-indent: 0;
      }
    }
    .item-delete-delete {
      background-color: #e00;
      color: #fff;
      margin-right: -52px;
      i {
        background-image: url("#{$project-icons-folder}delete-white.svg");
        display: block;
        margin-right: 0;
        @include media-breakpoint-up(md) {
          display: none;
        }
      }
      .hide-mobile {
        display: none;
        @include media-breakpoint-up(md) {
          display: block;
        }
      }
    }
  }

  &.delete-open {
    .saved-search-item-search {
      margin-left: -17px;
      opacity: 0.5;
      pointer-events: none;
      text-indent: -70px;
      @include media-breakpoint-up(md) {
        margin-left: -31px;
      }
    }
    .saved-search-item-frequency {
      margin-left: -17px;
      opacity: 0.5;
      overflow: hidden;
      pointer-events: none;
      text-indent: -35px;
      @include media-breakpoint-up(md) {
        margin-left: 0;
        text-indent: 0;
      }
    }
    .saved-search-item-delete {
      bottom: 0;
      display: flex;
      left: 0;
      position: absolute;
      right: 0;
      top: 0;
      @include media-breakpoint-up(md) {
        min-width: 200px;
        padding-right: 0;
        position: static;
        width: 20%;
      }
      .icon-delete {
        display: none;
      }
      .item-delete-cancel,
      .item-delete-delete {
        opacity: 1;
        visibility: visible;
        width: 80px;
        @include media-breakpoint-up(md) {
          width: 50%;
        }
      }
      .item-delete-cancel {
        margin-left: -100px;
        width: calc(100% - 70px);
        @include media-breakpoint-up(md) {
          margin-left: inherit;
          width: 50%;
        }
      }
      .item-delete-delete {
        margin-right: -120px;
        @include media-breakpoint-up(md) {
          margin-right: -65px;
        }
        .icon-delete {
          display: inline-block;
          padding: 0;
          @include media-breakpoint-up(md) {
            display: none;
            padding: inherit;
          }
        }
      }
    }
  }
}

.saved-search-spinner-block {
  margin-top: 60px;
  position: relative;
}

.overlay {
  background-color: #fff;
  height: 100%;
  left: 0;
  opacity: 0.7;
  position: absolute;
  top: 0;
  width: 100%;
}

.search-spinner {
  animation: spin 1s linear infinite;
  background: #6299c9;
  border-radius: 50%;
  clear: both;
  height: 64px;
  left: calc(50% - 32px);
  position: fixed;
  width: 64px;

  @include media-breakpoint-up(md) {
    left: 50%;
  }

  &::before, &::after {
    content: "";
    position: absolute;
  }

  &::before {
    background:
      linear-gradient(0deg, hsla(0, 0%, 100%, 1) 50%, hsla(0, 0%, 100%, 0.9) 100%) 0% 0%,
      linear-gradient(90deg, hsla(0, 0%, 100%, 0.9) 0%, hsla(0, 0%, 100%, 0.6) 100%) 100% 0%,
      linear-gradient(180deg, hsla(0, 0%, 100%, 0.6) 0%, hsla(0, 0%, 100%, 0.3) 100%) 100% 100%,
      linear-gradient(360deg, hsla(0, 0%, 100%, 0.3) 0%, hsla(0, 0%, 100%, 0) 100%) 0% 100%;
    background-repeat: no-repeat;
    background-size: 50% 50%;
    border-radius: 50%;
    bottom: -1px;
    left: -1px;
    right: -1px;
    top: -1px;
  }
  &::after {
    background: white;
    border-radius: 50%;
    bottom: 8px;
    left: 8px;
    right: 8px;
    top: 8px;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
