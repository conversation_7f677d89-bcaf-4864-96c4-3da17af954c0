import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { AppState } from '../../types/app-state';
import { Subject, of, throwError } from 'rxjs';
import { SavedSearch } from '../../classes/saved-search';
import { SavedSearchesComponent } from './saved-searches.component';
import { SavedSearchService } from '../../shared/services';

describe('SavedSearchesComponent', () => {
  let stub: any = {};
  let savedSearchResponse: Subject<any>;
  let componentFixture: ComponentFixture<SavedSearchesComponent>, component: SavedSearchesComponent;

  const USER_OBJECT: SavedSearch = new SavedSearch('', 'a', 'a', <any>'a');

  const initialState = {
    filters: [
      {
        name: 'workLevels',
        title: 'Filter 1',
        frequency: 'Nooit'
      }
    ]
  };

  beforeEach(() => {
    stub = {
      SavedSearchService: jasmine.createSpyObj('SavedSearchService', ['post', 'get', 'del', 'put', 'getParamsFromSearch']),
      Router: jasmine.createSpyObj('Router', ['navigate']),
      Store: jasmine.createSpyObj('Router', ['select', 'dispatch'])
    };

    stub.Store.select.and.returnValue(new Subject());

    savedSearchResponse = new Subject();
    stub.SavedSearchService.get.and.returnValue(savedSearchResponse);
    stub.SavedSearchService.del.and.returnValue(of(USER_OBJECT));
    stub.SavedSearchService.put.and.returnValue(of(USER_OBJECT));

    TestBed.configureTestingModule({
      declarations: [SavedSearchesComponent],
      providers: [
        { provide: Router, useValue: stub.Router },
        { provide: SavedSearchService, useValue: stub.SavedSearchService },
        { provide: Store, useValue: stub.Store }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    });
    TestBed.compileComponents();
    componentFixture = TestBed.createComponent(SavedSearchesComponent);
    componentFixture.detectChanges();
    component = componentFixture.componentInstance;
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('#ngOnInit', () => {
    it('should call the select of the store', () => {
      stub.Store.select.calls.reset();

      component.ngOnInit();

      expect(stub.Store.select).toHaveBeenCalledTimes(1);
    });

    it('should make search property observing store', () => {
      const response: any = {
        search: {
          searchQuery: 'searchQuery',
          locationQuery: 'locationQuery',
          sort: 'default',
          pageSize: 15,
          currentPage: 1
        },
        filter: {
          filters: []
        },
        modals: {}
      };

      savedSearchResponse.next(response);

      expect(component.savedSearches).toEqual(response);
    });

    it('should hide spinner after saved searches data is fetched', () => {
      component.ngOnInit();

      expect(component.isFetching).toBe(true);

      savedSearchResponse.complete();

      expect(component.isFetching).toBe(false);
    });

    it('should hide spinner after saved searches data is not fetched for an error', () => {
      component.ngOnInit();

      expect(component.isFetching).toBe(true);

      savedSearchResponse.error(new Error('error'));

      expect(component.isFetching).toBe(false);
      expect(component.savedSearches).toEqual([]);
    });
  });

  describe('#getStoreSearchData', () => {
    it('should return the search store', () => {
      const search: any = {
        searchQuery: 'searchQuery',
        locationQuery: 'locationQuery',
        sort: 'default',
        pageSize: 15,
        chosenSorting: 'ascend',
        currentPage: 1
      };

      const store = <AppState>{ search: search };

      const storeSearch = component['getStoreSearchData'](store);

      expect(storeSearch).toEqual(search);
    });
  });

  describe('deleteSavedSearch', () => {
    it('should delete a saved search', () => {
      component.savedSearches = initialState.filters;
      spyOn(component.savedSearches, 'splice');
      component.deleteSavedSearch(1, '1234');
      expect(component.savedSearchService.del).toHaveBeenCalledWith('1234');
    });
  });

  describe('updateSavedSearch', () => {
    it('should update a saved search', () => {
      component.savedSearches = initialState.filters;
      spyOn(component.savedSearches, 'splice');
      component.changeEmailUpdates(0, 'Nooit');
      expect(component.savedSearchService.put).toHaveBeenCalled();
    });
  });

  describe('updateSavedSearchRevert', () => {
    it('should revert a saved search if update fails', () => {
      component.savedSearches = initialState.filters;
      spyOn(component.savedSearches, 'splice');
      stub.SavedSearchService.put.and.returnValue(throwError({}));
      component.changeEmailUpdates(0, 'Nooit');
      expect(component.savedSearchService.put).toHaveBeenCalled();
    });
  });

  describe('clickOnSavedSearch', () => {
    it('should redirect to search page', () => {
      const search: any = {
        searchParameters: {
          test: true
        }
      };
      const response: any = {
        filter: true
      };

      stub.SavedSearchService.getParamsFromSearch.and.returnValue(response);

      component.clickOnSavedSearch(search);

      expect(stub.SavedSearchService.getParamsFromSearch).toHaveBeenCalledTimes(1);
      expect(stub.SavedSearchService.getParamsFromSearch).toHaveBeenCalledWith(search.searchParameters);
      expect(stub.Router.navigate).toHaveBeenCalledTimes(1);
      expect(stub.Router.navigate).toHaveBeenCalledWith(['/kandidaten/zoeken'], response);
    });
  });
});
