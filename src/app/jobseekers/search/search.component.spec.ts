import { TestBed, ComponentFixture, fakeAsync, tick } from '@angular/core/testing';
import { Observable, BehaviorSubject, Subject, empty, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { SearchComponent } from './search.component';
import { ActivatedRoute, Router } from '@angular/router';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { Store } from '@ngrx/store';
import * as FilterActions from '../../store/actions/filter/filter.actions';
import * as SearchActions from '../../store/actions/search/search.actions';
import * as ModalsActions from '../../store/actions/modals/modals.actions';
import { Filter } from '../../store/models/filter.modal';
import { Search } from '../../store/models/search.model';
import {
  UrlSearchService,
  FilterEffectsService,
  NumberFormatService,
  PaginationService,
  PlatformCoreService,
  SearchService,
  JobSeekerService,
  ProfileNavigationService,
  WindowService
} from '../../shared/services';

describe('SearchComponent', () => {
  const stub: any = {};
  const totalPages = 10;
  let parentSubject: Subject<any>;
  let componentFixture: ComponentFixture<SearchComponent>, component: SearchComponent;

  beforeEach(() => {
    parentSubject = new Subject();
    stub.recruiter = {
      emailAddress: '<EMAIL>',
      firstName: 'First',
      lastName: 'Last',
      hasSubscription: true,
      hasDeductibleSubscription: true,
      hasValidProduct: true,
      amountOfCredits: 10,
      companyVerified: true
    };
    stub.SearchService = jasmine.createSpyObj('SearchService', ['search', 'updateSearch', 'updateFilters']);
    stub.StoreObservable = new Subject();
    stub.StoreObservable.next({
      search: {
        searchQuery: 'searchQuery',
        locationQuery: 'locationQuery',
        sort: 'default',
        pageSize: 15,
        currentPage: 1
      },
      filter: { filters: [] },
      modals: {
        displaySaveSearchModal: false,
        displaySearchTipsModal: false
      }
    });
    stub.Store = jasmine.createSpyObj('Store', ['dispatch', 'select']);
    stub.Store.select.and.returnValue(stub.StoreObservable.asObservable());
    stub.filterEffectsService = jasmine.createSpyObj('FilterEffectsService', ['getChangedResultSetActions']);
    stub.filterEffectsSubject = new Subject();
    stub.filterEffectsService.getChangedResultSetActions.and.returnValue(stub.filterEffectsSubject);
    stub.filtersObservable = new Subject();
    stub.filtersObservable.next({
      aggregations: {
        radius: { raw_data: {} },
        job_type: { raw_data: {} },
        hours: { raw_data: {} },
        work_level: { raw_data: {} }
      }
    });
    stub.SearchService.search.and.returnValue(Promise.resolve([]));
    stub.SearchService.filters$ = new Subject();
    stub.SearchService.search$ = new Subject();
    stub.UrlSearchService = jasmine.createSpyObj('UrlSearchService', [
      'changePageSize',
      'syncStateFromUrl',
      'updateUrl',
      'updateSearch',
      'changePageNumber'
    ]);
    stub.JobSeekerService = jasmine.createSpyObj('JobSeekerService', ['fromJson']);
    stub.JobSeekerService.fromJson.and.returnValue(empty());
    stub.ActivatedRoute = {
      queryParams: of({
        search: 'bus driver',
        locationQuery: 'Amsterdam',
        '[job_type][0]': 'Vast'
      }),
      parent: {
        data: parentSubject
      }
    };
    stub.Router = jasmine.createSpyObj('Router', ['navigate']);
    stub.PaginationService = jasmine.createSpyObj('PaginationService', ['getPages']);
    stub.PaginationService.getPages.and.returnValue({ totalPages: totalPages });
    stub.PlatformCoreService = {
      hasPlatformCoreAccount: new BehaviorSubject(true)
    };

    stub.NumberFormatService = jasmine.createSpyObj('NumberFormatService', ['formatDots']);
    stub.ProfileNavigationService = jasmine.createSpyObj('ProfileNavigationService', ['setInitialResultSet', 'setAmountOfPages']);
    stub.WindowService = jasmine.createSpyObj('WindowService', ['scrollTo']);

    TestBed.configureTestingModule({
      declarations: [SearchComponent],
      providers: [
        { provide: SearchService, useValue: stub.SearchService },
        { provide: JobSeekerService, useValue: stub.JobSeekerService },
        { provide: UrlSearchService, useValue: stub.UrlSearchService },
        { provide: ActivatedRoute, useValue: stub.ActivatedRoute },
        { provide: Router, useValue: stub.Router },
        { provide: PaginationService, useValue: stub.PaginationService },
        { provide: PlatformCoreService, useValue: stub.PlatformCoreService },
        { provide: NumberFormatService, useValue: stub.NumberFormatService },
        { provide: Store, useValue: stub.Store },
        { provide: FilterEffectsService, useValue: stub.filterEffectsService },
        { provide: ProfileNavigationService, useValue: stub.ProfileNavigationService },
        { provide: WindowService, useValue: stub.WindowService }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    });
    TestBed.compileComponents();
    componentFixture = TestBed.createComponent(SearchComponent);
    componentFixture.detectChanges();
    component = componentFixture.componentInstance;
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should set hasPlatformCoreAccount based on the observed value', () => {
      expect(component.hasPlatformCoreAccount).toBe(true);
    });
  });

  describe('#ngOnInit', () => {
    beforeEach(() => {
      spyOn(component, 'search');
    });

    it('should initialize with an empty jobSeeker array', () => {
      expect(component.jobSeekers).toEqual([]);
    });

    it('should have current page set to 1 by default', () => {
      expect(component.currentPage).toEqual(1);
    });

    it('should have page size set to 15 by default', () => {
      expect(component.pageSize).toEqual(15);
    });

    it('should not call search when app state is updated and url is not parsed yet', () => {
      component.ngOnInit();
      expect(component.search).not.toHaveBeenCalled();
    });

    it('should call search when app state is updated', () => {
      stub.Store.select.and.returnValue(
        of({
          search: {
            searchQuery: 'searchQuery',
            locationQuery: 'locationQuery',
            sort: 'default',
            pageSize: 15,
            currentPage: 1
          },
          filter: { filters: [] },
          modals: {}
        })
      );
      component.ngOnInit();
      expect(component.search).toHaveBeenCalled();
    });

    it('should subscribe current page and page size to the store', () => {
      expect(component.currentPage).toBe(1);
      expect(component.pageSize).toBe(15);

      const newCurrentPage = 5;
      const newPageSize = 15;

      stub.Store.select.and.returnValue(
        of({
          searchQuery: {
            all: ['']
          },
          locationQuery: 'locationQuery',
          sort: 'default',
          pageSize: newPageSize,
          currentPage: newCurrentPage
        })
      );

      component.ngOnInit();

      expect(component.currentPage).toBe(newCurrentPage);
      expect(component.pageSize).toBe(newPageSize);
    });

    it('should not call search again when state has not changed', fakeAsync(() => {
      stub.Store.select.and.callFake((fn: any) => {
        return Observable.create((obs: any) => {
          const sameState = {
            search: {
              searchQuery: 'searchQuery',
              locationQuery: 'locationQuery',
              sort: 'default',
              pageSize: 15,
              currentPage: 1
            },
            filter: { filters: <Filter[]>[] },
            modals: {}
          };
          obs.next(sameState);
          obs.next(sameState);
        }).pipe(map(fn));
      });
      component.ngOnInit();

      tick(101);
      expect(component.search).toHaveBeenCalledTimes(1);
    }));

    it('should show a modal if state is updated to show a displaySaveSearchModal', () => {
      stub.Store.select.and.callFake((fn: Function) => {
        return of(
          fn({
            search: {},
            filter: {},
            modals: { displaySaveSearchModal: true }
          })
        );
      });
      component.ngOnInit();

      component.saveSearchModal$.subscribe(modal => {
        expect(modal).toBe(true);
      });
    });

    it('should show a modal if state is updated to show a displaySearchTipsModal', () => {
      stub.Store.select.and.callFake((fn: Function) => {
        return of(
          fn({
            search: {},
            filter: {},
            modals: { displaySearchTipsModal: true }
          })
        );
      });
      component.ngOnInit();

      component.searchTipsModal$.subscribe(modal => {
        expect(modal).toBe(true);
      });
    });

    it('should be authenticated when there is recruiter', () => {
      expect(component.recruiter).toBeUndefined();

      parentSubject.next({ recruiter: stub.recruiter });

      expect(component.recruiter).toEqual(stub.recruiter);
    });

    it('should be not authenticated when there is no recruiter', () => {
      expect(component.recruiter).toBeUndefined();

      parentSubject.next({ test: true });

      expect(component.recruiter).toBeUndefined();
    });

    it('should subscribe to filter effects changed', () => {
      const filterSpy = jasmine.createSpy('filter');
      component.filter = filterSpy;

      component.searchState = <Search>{
        searchQuery: { key: ['value'] },
        locationQuery: 'Ede',
        sort: 'default',
        chosenSorting: 'sorting',
        currentPage: 12,
        pageSize: 20
      };

      component.ngOnInit();

      expect(stub.filterEffectsService.getChangedResultSetActions).toHaveBeenCalled();

      filterSpy.calls.reset();

      stub.filterEffectsSubject.next();

      expect(component.filter).toHaveBeenCalledWith(
        component.searchState.searchQuery,
        component.searchState.locationQuery,
        component.filters
      );
    });
  });

  describe('#ngOnDestroy', () => {
    it('should unsubscribe to the component promises', () => {
      component['componentDestroyed'] = jasmine.createSpyObj('componentDestroyed', ['next', 'unsubscribe']);

      component.ngOnDestroy();

      expect(component['componentDestroyed'].next).toHaveBeenCalledTimes(1);
      expect(component['componentDestroyed'].next).toHaveBeenCalledWith();
      expect(component['componentDestroyed'].unsubscribe).toHaveBeenCalledTimes(1);
      expect(component['componentDestroyed'].unsubscribe).toHaveBeenCalledWith();
    });
  });

  describe('#search', () => {
    it('should update jobseeker search', () => {
      const searchQuery = { all: ['searchQuery'] };

      component.search([
        [],
        <Search>{
          searchQuery: searchQuery,
          locationQuery: 'Amsterdam',
          sort: 'default',
          pageSize: 15,
          chosenSorting: 'default',
          currentPage: 1
        }
      ]);

      expect(stub.SearchService.updateSearch).toHaveBeenCalledTimes(1);
      expect(stub.SearchService.updateSearch).toHaveBeenCalledWith(searchQuery, 'Amsterdam', 15, 1, 'default', []);
      expect(component.error).toBeUndefined();
    });
  });

  describe('#filter', () => {
    it('should update jobseeker filters', () => {
      const dataQuery = { city: ['amsterdam', 'utrecht'] };
      const dataLocation = 'Arnhem';
      const dataFilters: Filter[] = [];

      component.filter(dataQuery, dataLocation, dataFilters);

      expect(stub.SearchService.updateFilters).toHaveBeenCalledTimes(1);
      expect(stub.SearchService.updateFilters).toHaveBeenCalledWith(dataQuery, dataLocation, dataFilters);
    });
  });

  describe('#searchSuccess', () => {
    beforeEach(() => {
      component.searchState = <any>{
        searchQuery: {},
        locationQuery: {}
      };
    });

    it('should set an error message of no results', () => {
      const emptyData = { result_count: 0, result: <any>[] };

      component.searchSuccess(emptyData);

      expect(component.error).toEqual({ type: 'SEARCH_ERROR_NO_RESULTS' });
    });

    it('should manage pagination and navigation', () => {
      const someData = { result_count: 20, result: <any>[] };

      component.searchSuccess(someData);

      expect(component['pagination'].getPages).toHaveBeenCalledTimes(1);
      expect(component['pagination'].getPages).toHaveBeenCalledWith(someData.result_count, component.currentPage, component.pageSize);
      expect(component['profileNavigation'].setInitialResultSet).toHaveBeenCalledTimes(1);
      expect(component['profileNavigation'].setInitialResultSet).toHaveBeenCalledWith(component.jobSeekers);
      expect(component['profileNavigation'].setAmountOfPages).toHaveBeenCalledTimes(1);
      expect(component['profileNavigation'].setAmountOfPages).toHaveBeenCalledWith(component.pages.totalPages);
    });
  });

  describe('#catchSearch', () => {
    it('should be called', () => {
      const error = { type: 'ERROR', status: 400 };

      component['catchSearch'](error);

      expect(component.isSearching).toEqual(false);
      expect(component.jobSeekers).toEqual([]);
      expect(stub.Router.navigate).not.toHaveBeenCalled();
    });

    it('should manage a generic error', () => {
      const error = { type: 'SEARCH_ERROR_RESULT_WINDOW_TOO_LARGE', status: 400 };

      component['catchSearch'](error);

      expect(component.error).toEqual(error);
      expect(stub.Router.navigate).not.toHaveBeenCalled();
    });

    it('should manage an internal server error', () => {
      const error = { type: 'INTERNAL', status: 500 };

      component['catchSearch'](error);

      expect(stub.Router.navigate).toHaveBeenCalledTimes(1);
      expect(stub.Router.navigate).toHaveBeenCalledWith(['/error'], {
        queryParams: { type: 'internalServerError' },
        skipLocationChange: true
      });
    });
  });

  describe('filterSuccess', () => {
    it('dispatch an AGGREGATIONS_UPDATED action', () => {
      stub.Store.dispatch.calls.reset();

      component.filterSuccess({ aggregations: 'new aggregations' });

      expect(stub.Store.dispatch).toHaveBeenCalledWith(new FilterActions.AggregationsUpdated('new aggregations'));
    });
  });

  describe('#changePage', () => {
    it('should scroll the page to the top', () => {
      component.changePage(3);
      expect(stub.WindowService.scrollTo).toHaveBeenCalledTimes(1);
      expect(stub.WindowService.scrollTo).toHaveBeenCalledWith(0, 0);
    });

    it('should set pageNumber to new page', () => {
      component.changePage(12);
      expect(stub.UrlSearchService.changePageNumber).toHaveBeenCalledWith(12);
    });
  });

  describe('#updatePageSize', () => {
    const pageSize = 50;

    it('should update the url to contain the pageSize', () => {
      component.pages = {
        totalItems: 1,
        currentPage: 1,
        pageSize: 1,
        totalPages: 1,
        startPage: 1,
        endPage: 1,
        startIndex: 1,
        endIndex: 1,
        amountOfPages: 1,
        pages: [1, 2, 3, 4]
      };
      component.updatePageSize(pageSize);
      expect(stub.UrlSearchService.changePageSize).toHaveBeenCalledWith(pageSize, 1);
    });

    it('should set the new currentPage to contain the currentIndex of the previous currentPage', () => {
      component.pages = {
        totalItems: 1,
        currentPage: 1,
        pageSize: 1,
        totalPages: 1,
        startPage: 1,
        endPage: 1,
        startIndex: 1,
        endIndex: 51,
        amountOfPages: 1,
        pages: [1, 2, 3, 4]
      };
      component.currentPage = 6;
      component.pageSize = 10;
      component.updatePageSize(pageSize);
      expect(stub.UrlSearchService.changePageSize).toHaveBeenCalledWith(pageSize, 51);
    });
  });

  describe('#openFilter', () => {
    it('should dispatch a OPEN_FILTER event', () => {
      component.openFilter();
      expect(stub.Store.dispatch).toHaveBeenCalledWith(new FilterActions.OpenFilter());
    });
  });

  describe('#setSort', () => {
    it('should dispatch a CHANGE_SORT event', () => {
      component.setSort('update');
      expect(stub.Store.dispatch).toHaveBeenCalledWith(new SearchActions.ChangeSort('update'));
    });
  });

  describe('#identifySearchResult', () => {
    it('should return a unique identifier for a search result', () => {
      const result = component.identifySearchResult(0, <any>{ id: '123' });
      expect(result).toBe('123');
    });
  });

  describe('#openSaveSearchModal', () => {
    it('should dispatch a OPEN_SAVE_SEARCH_MODAL event', () => {
      component.openSaveSearchModal();
      expect(stub.Store.dispatch).toHaveBeenCalledWith(new ModalsActions.OpenSaveSearchModal());
    });
  });

  describe('#isSearchQueryPresent', () => {
    it('should return true when search query is present', () => {
      const result = component.isSearchQueryPresent({ location: ['Amsterdam', `Reggio nell'Emilia`] });
      expect(result).toBe(true);
    });

    it('should return false when search query is not present', () => {
      const result = component.isSearchQueryPresent({ location: [] });
      expect(result).toBe(false);
    });
  });

  describe('#openSearchTipsModal', () => {
    it('should dispatch the OPEN_SEARCH_TIPS_MODAL event', () => {
      component.openSearchTipsModal();

      expect(stub.Store.dispatch).toHaveBeenCalledWith(new ModalsActions.OpenSearchTipsModal());
    });
  });
});
