import { TestBed, ComponentFixture } from '@angular/core/testing';
import { SearchErrorComponent } from './search-error.component';
import { ChangeDetectionStrategy, NO_ERRORS_SCHEMA } from '@angular/core';
import { By } from '@angular/platform-browser';

describe('SearchErrorComponent', () => {
  let componentFixture: ComponentFixture<SearchErrorComponent>, component: SearchErrorComponent;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [SearchErrorComponent],
      schemas: [NO_ERRORS_SCHEMA]
    }).overrideComponent(SearchErrorComponent, {
      set: { changeDetection: ChangeDetectionStrategy.Default }
    });

    TestBed.compileComponents();
    componentFixture = TestBed.createComponent(SearchErrorComponent);
    componentFixture.detectChanges();
    component = componentFixture.componentInstance;
  });

  describe('#constructor', () => {
    it('should render one element', () => {
      component.error = { type: 'SEARCH_ERROR_NO_RESULTS' };

      componentFixture.detectChanges();
      const element = componentFixture.debugElement.queryAll(By.css('.search-error'));
      expect(element.length).toBe(1);
    });
  });
});
