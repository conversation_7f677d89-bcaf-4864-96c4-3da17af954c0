import { Component, ChangeDetectionStrategy, Input } from '@angular/core';

export interface ISearchError {
  type: string;
}

@Component({
  selector: 'app-search-error',
  templateUrl: './search-error.component.html',
  styleUrls: ['./search-error.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SearchErrorComponent {
  @Input()
  error: ISearchError;
  constructor() {}
}
