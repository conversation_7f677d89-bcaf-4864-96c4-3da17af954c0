import { ChangeDetectionStrategy, Component, Input, OnChanges, OnInit, SimpleChanges, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AppState } from '../../../types/app-state';
import { environment } from '../../../../environments/environment';
import * as ModalsActions from '../../../store/actions/modals/modals.actions';
import { JobSeeker, Experience, Education, Recruiter } from '../../../classes';
import {
  RestoreScrollPositionService,
  SessionProfileViewTrackerService,
  SessionProfileFavoriteTrackerService,
  PlatformCoreService,
  ProfileNavigationService,
  SavedFavoritesService,
  SearchService,
  WindowService
} from '../../../shared/services';

@Component({
  selector: 'app-search-result',
  templateUrl: './search-result.component.html',
  styleUrls: ['./search-result.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SearchResultComponent implements OnChanges, OnInit, OnDestroy {
  public hasProfileManagerImage: boolean;
  public hasOldImage: boolean;
  public searchResultImageFallback: string;
  public searchResultImage: string;
  private currentPage: number;
  @Input()
  public index = 0;
  @Input()
  public jobSeeker: JobSeeker;
  @Input()
  public recruiter: Recruiter;
  public site = environment.site;
  public hasValidProduct = false;
  public isShownAsFavorite: boolean;
  public formattedJobSeeker: any;
  public hasPlatformCoreAccount = false;
  private componentDestroyed: Subject<any> = new Subject();

  constructor(
    private restoreScrollPosition: RestoreScrollPositionService,
    public profileViewTracker: SessionProfileViewTrackerService,
    public profileFavoriteTracker: SessionProfileFavoriteTrackerService,
    private profileNavigation: ProfileNavigationService,
    public savedFavoritesService: SavedFavoritesService,
    private store: Store<AppState>,
    private searchService: SearchService,
    private windowService: WindowService,
    private router: Router,
    private platformCoreService: PlatformCoreService
  ) {
    this.store.subscribe(state => {
      this.currentPage = state.search.currentPage;
    });
    platformCoreService.hasPlatformCoreAccount.pipe(takeUntil(this.componentDestroyed)).subscribe((hasPlatformCoreAccount: boolean) => {
      this.hasPlatformCoreAccount = hasPlatformCoreAccount;
    });
  }

  ngOnInit(): void {
    this.hasValidProduct = this.recruiter && this.recruiter.hasValidProduct;
    this.isShownAsFavorite = this.isFavorite();
    this.scrollToSpecificUser();
  }

  ngOnChanges(result: SimpleChanges): void {
    this.isShownAsFavorite = this.isFavorite();

    if (result.jobSeeker && result.jobSeeker.currentValue) {
      this.formattedJobSeeker = this.formatJobSeekerSearchResult(result.jobSeeker.currentValue);
    }

    if (
      result.jobSeeker &&
      result.jobSeeker.currentValue.photo !== undefined &&
      result.jobSeeker.currentValue.photo !== null &&
      result.jobSeeker.currentValue.photo !== ''
    ) {
      this.searchResultImage = 'https://cdn.' + result.jobSeeker.currentValue.site + '/' + result.jobSeeker.currentValue.photo;
      this.searchResultImageFallback = environment.photoPath + result.jobSeeker.currentValue.photo;
      this.hasProfileManagerImage = true;
    }
  }

  fallBackToOldProfileImage(error: any) {
    this.hasProfileManagerImage = false;
    this.hasOldImage = true;
  }

  ngOnDestroy() {
    this.componentDestroyed.next();
    this.componentDestroyed.unsubscribe();
  }

  parseDate(date: string): string {
    if (date) {
      return new Date(date).getFullYear().toString();
    } else {
      return 'heden';
    }
  }

  formatExperience(experience: Experience): any {
    if (experience) {
      return {
        isAvailable: true,
        functionName: experience.functionName,
        company: experience.company,
        fromDate: this.parseDate(experience.fromDate),
        toDate: this.parseDate(experience.toDate)
      };
    } else {
      return {
        isAvailable: false
      };
    }
  }

  formatRecentExperiences(experience: Experience[]): Experience[] {
    const recentExperiences = [];

    if (experience && experience[0]) {
      recentExperiences.push(this.formatExperience(experience[0]));
    }
    if (experience && experience[1]) {
      recentExperiences.push(this.formatExperience(experience[1]));
    }
    if (experience && experience[2]) {
      recentExperiences.push(this.formatExperience(experience[2]));
    }

    return recentExperiences;
  }

  formatEducation(education: Education): any {
    if (education && (education.school || education.fieldOfStudy) && education.diploma !== false) {
      return {
        isAvailable: true,
        school: education.school,
        fieldOfStudy: education.fieldOfStudy,
        fromDate: this.parseDate(education.fromDate),
        toDate: this.parseDate(education.toDate)
      };
    } else {
      return {
        isAvailable: false
      };
    }
  }

  formatJobSeekerSearchResult(jobSeeker: JobSeeker): any {
    return {
      id: jobSeeker.id,
      firstName: jobSeeker.personalInfo.firstName,
      lastName: jobSeeker.personalInfo.lastName,
      education: this.formatEducation(this.getLastValidEducation(jobSeeker.education)),
      updatedDate: jobSeeker.updatedDate,
      viewedDate: this.profileViewTracker.getProfileViewed(jobSeeker.id, jobSeeker.viewedDate),
      desiredJobs: jobSeeker.jobCriteria.desiredFunctions,
      recentJobs: this.formatRecentExperiences(jobSeeker.experience),
      workLevels: jobSeeker.jobCriteria.workLevels,
      workingHours: this.formatWorkingHours(jobSeeker.jobCriteria.minWorkingHours, jobSeeker.jobCriteria.maxWorkingHours),
      availability: jobSeeker.jobCriteria.availability,
      city: jobSeeker.jobCriteria.city,
      isFavorite: jobSeeker.isFavorite
    };
  }

  formatWorkingHours(minimal: number | null, maximal: number | null): string | null {
    if (minimal !== null && maximal !== null) {
      return minimal + ' - ' + maximal + ' uur';
    }
  }

  getLastValidEducation(education: Education[]): any {
    return education.filter(e => {
      return this.formatEducation(e).isAvailable;
    })[0];
  }

  navigateToJobSeeker($event: MouseEvent, jobSeekerId: string): void {
    $event.stopPropagation();
    $event.preventDefault();

    this.restoreScrollPosition.setLastJobSeekerViewed(jobSeekerId, window.pageYOffset);

    this.profileNavigation.setCurrentPage(this.currentPage);
    this.profileNavigation.setIndexNumberOfProfileInResultSet(this.index - 1);

    this.router.navigate([`/kandidaat/${jobSeekerId}`]);
  }

  isFavorite(): boolean {
    return this.profileFavoriteTracker.isProfileMarkAsFavorite(this.jobSeeker.id) || this.jobSeeker.isFavorite === true;
  }

  toggleFavorite(e: Event): void {
    e.preventDefault();
    e.stopPropagation();

    if (this.isShownAsFavorite) {
      this.savedFavoritesService.del(this.jobSeeker.id).subscribe(() => {
        this.profileFavoriteTracker.deleteProfileFavorite(this.jobSeeker.id);
        this.searchService.purgeCache();
      });

      this.isShownAsFavorite = false;
    } else {
      this.savedFavoritesService.post(this.jobSeeker.id).subscribe(() => {
        this.profileFavoriteTracker.recordFavorite(this.jobSeeker.id);
      });

      this.isShownAsFavorite = true;
    }
  }

  scrollToSpecificUser(): void {
    const scrollConf = this.restoreScrollPosition.getLastJobSeekerViewed();
    if (scrollConf && this.jobSeeker && scrollConf.jobSeekerId === this.jobSeeker.id) {
      this.windowService.scrollTo(0, scrollConf.scrollY);
      this.restoreScrollPosition.resetReplay();
    }
  }
}
