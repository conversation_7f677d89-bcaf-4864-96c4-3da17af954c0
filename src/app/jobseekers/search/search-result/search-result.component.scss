@import "components";

:host {
  .search-result {
    border-bottom: 1px solid #d0cdd4;
    border-top: 1px solid #d0cdd4;
    color: $text-color;
    display: block;
    margin-bottom: -1px;
    padding: 15px 10px;
    position: relative;
    text-decoration: none;
    transition: background 0.2s ease;
    width: 100%;

    @include media-breakpoint-up(lg) {
      align-items: stretch;
      display: flex;
      min-height: 150px;
    }

    &:hover {
      background: rgba(220, 231, 239, 0.5);
    }

    .search-result-personal {
      line-height: 26px;
      max-width: calc(100% - 30px);
      @include media-breakpoint-up(lg) {
        max-width: none;
        padding-right: 5px;
        width: 54%;
      }

      .search-result-image-container {
        float: left;
        height: 50px;
        margin-right: 10px;
        width: 50px;
        @include media-breakpoint-up(lg) {
          height: 60px;
          width: 60px;
        }
        .search-result-image {
          border-radius: 50%;
          height: auto;
          max-width: 100%;
          &.default-image {
            background-image: url("#{$project-icons-folder}profile-pic-placeholder.svg");
            background-size: 100%;
            height: 100%;
            width: 100%;
          }
        }
      }

      .search-result-name {
        color: $search-result-name;
        display: block;
        float: none;
        font-size: 20px;
        font-weight: 400;
        margin: 0;
        width: 100%;
        @include media-breakpoint-up(md) {
          float: right;
          width: 80%;
        }
        @include media-breakpoint-up(lg) {
          width: 78%;
        }

        &:empty {
          display: none;
        }
      }

      .search-result-specs {
        clear: both;
        color: #6d6875;
        float: left;
        font-size: 15px;
        line-height: 1.47;
        margin-top: 5px;
        width: 100%;
        @include media-breakpoint-up(sm) {
          clear: none;
          font-size: 16px;
        }
        @include media-breakpoint-up(md) {
          float: right;
          width: 80%;
        }
        @include media-breakpoint-up(lg) {
          width: 78%;
        }
        > span:not(:last-child)::after {
          content: "|";
          display: inline-block;
          margin-left: 4px;
          margin-right: 4px;
        }

        &--main-header {
          margin-top: 0;
        }
      }

      .search-result-edit-date-desktop {
        display: none;

        @include media-breakpoint-up(lg) {
          bottom: 10px;
          color: #6d6875;
          display: block;
          font-size: 16px;
          left: 13%;
          position: absolute;
          width: 78%;
        }
      }

      .search-result-interactions {
        float: left;
        margin-top: 10px;
        width: 100%;
        @include media-breakpoint-up(md) {
          float: right;
        }
        @include media-breakpoint-up(lg) {
          margin-top: 10px;
          width: 78%;
        }

        .icon-interaction {
          display: block;
          float: left;
        }

        .icon-favorite {
          background: transparent url("#{$project-icons-folder}bookmark.svg") no-repeat scroll center center;
          height: 24px;
          width: 25px;
          &:hover {
            background-image: url("#{$project-icons-folder}bookmarked.svg");
            opacity: 0.5;
          }
          &.is-favorite {
            background-image: url("#{$project-icons-folder}bookmarked.svg");
          }
        }
      }
    }

    .search-result-last-experiences,
    .search-result-desired-experiences,
    .search-result-others {
      font-size: 15px;
      line-height: 1.47;
      @include media-breakpoint-up(lg) {
        font-size: 16px;
        padding-left: 5px;
        padding-right: 5px;
      }
      .search-result-work-title {
        display: block;
        font-weight: 700;
        margin: 15px 0 10px 0;
        @include media-breakpoint-up(lg) {
          margin: 0 0 10px 0;
        }

        & ~ span {
          display: block;
          line-height: 20px;
          margin-bottom: 10px;
          word-wrap: break-word;
        }
      }
    }
    .search-result-last-experiences,
    .search-result-desired-experiences {
      @include media-breakpoint-up(lg) {
        width: 23%;
      }
    }

    .search-result-work {
      @include media-breakpoint-up(lg) {
        width: 45%;
      }
    }
    .search-result-others {
      @include media-breakpoint-up(lg) {
        padding: 0;
        position: relative;
        width: 0;
      }
      .search-result-edit-date-mobile {
        color: #6d6875;
        display: block;
        margin-top: 15px;
        position: relative;

        @include media-breakpoint-up(lg) {
          display: none;
        }
      }
    }

    span.icons {
      padding-left: 20px;
      padding-right: 25px;
      position: relative;

      &:last-child {
        padding-right: 0;
      }

      .icon-dates {
        background-size: contain;
        display: inline-block;
        height: 16px;
        left: 0;
        position: absolute;
        top: calc(50% - 8px);
        width: 16px;

        &__update {
          background-image: url("#{$project-icons-folder}edit.svg");
        }
        &__view {
          background-image: url("#{$project-icons-folder}viewed.svg");
        }
      }
    }
  }

  .ellipsis {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    @include media-breakpoint-up(md) {
      display: inline;
    }
  }

  .ellipsis-desktop {
    @include media-breakpoint-up(lg) {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .clear {
    clear: both;
  }
}
