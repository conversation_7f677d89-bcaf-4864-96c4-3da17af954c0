import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Store } from '@ngrx/store';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { Router } from '@angular/router';
import { SearchResultComponent } from './search-result.component';
import { Experience, Education, JobSeeker } from '../../../classes';
import { ZeroPadPipe, DaysAgoDatePipe } from '../../../shared/pipes';
import { BehaviorSubject, Subject, ReplaySubject } from 'rxjs';
import { environment } from '../../../../environments/environment';
import {
  RestoreScrollPosition,
  RestoreScrollPositionService,
  SessionProfileViewTrackerService,
  SessionProfileFavoriteTrackerService,
  PlatformCoreService,
  ProfileNavigationService,
  SavedFavoritesService,
  SearchService,
  WindowService
} from '../../../shared/services';

describe('SearchResultComponent', () => {
  const stub: any = {};
  const page = 1;
  let componentFixture: ComponentFixture<SearchResultComponent>, component: SearchResultComponent;

  const observablePost = new Subject();
  const observableDelete = new Subject();

  const jobSeeker = new JobSeeker(
    '1',
    1543793078,
    true,
    [],
    [],
    [],
    <any>{},
    <any>{ firstName: 'firstName', lastName: 'lastName' },
    'photo',
    null,
    'site',
    'aanvullende info',
    [],
    [],
    [],
    false,
    null
  );

  const yPosition = 812;
  const scrollPosition: RestoreScrollPosition = { jobSeekerId: jobSeeker.id, scrollY: yPosition };

  beforeEach(() => {
    stub.RestoreScrollPositionService = jasmine.createSpyObj('RestoreScrollPositionService', [
      'getLastJobSeekerViewed',
      'resetReplay',
      'setLastJobSeekerViewed'
    ]);
    stub.Router = jasmine.createSpyObj('Router', ['navigate']);
    stub.RestoreScrollPositionService.getLastJobSeekerViewed.and.returnValue(scrollPosition);
    stub.SessionProfileViewTrackerService = jasmine.createSpyObj('SessionProfileViewTrackerService', ['getProfileViewed']);
    stub.SessionProfileFavoriteTrackerService = jasmine.createSpyObj('SessionProfileFavoriteTrackerService', [
      'isProfileMarkAsFavorite',
      'recordFavorite',
      'deleteProfileFavorite'
    ]);

    stub.SavedFavoritesService = jasmine.createSpyObj('SavedFavoritesService', ['post', 'del']);
    stub.SavedFavoritesService.post.and.returnValue(observablePost);
    stub.SavedFavoritesService.del.and.returnValue(observableDelete);

    stub.PlatformCoreService = {
      hasPlatformCoreAccount: new BehaviorSubject(true)
    };
    stub.ProfileNavigationService = jasmine.createSpyObj('ProfileNavigationService', [
      'setCurrentPage',
      'setIndexNumberOfProfileInResultSet'
    ]);
    stub.Store = new ReplaySubject();
    stub.Store.next({ search: { currentPage: page } });
    stub.Store.dispatch = jasmine.createSpy('dispatch');
    stub.SearchService = jasmine.createSpyObj('SearchService', ['purgeCache']);
    stub.WindowService = jasmine.createSpyObj('WindowService', ['scrollTo']);
    stub.recruiter = {
      emailAddress: '<EMAIL>',
      firstName: 'First',
      lastName: 'Last',
      hasSubscription: true,
      hasDeductibleSubscription: false,
      hasValidProduct: true,
      amountOfCredits: 10,
      companyVerified: true
    };

    TestBed.configureTestingModule({
      declarations: [SearchResultComponent, ZeroPadPipe, DaysAgoDatePipe],
      providers: [
        { provide: RestoreScrollPositionService, useValue: stub.RestoreScrollPositionService },
        {
          provide: SessionProfileFavoriteTrackerService,
          useValue: stub.SessionProfileFavoriteTrackerService
        },
        { provide: SavedFavoritesService, useValue: stub.SavedFavoritesService },
        {
          provide: SessionProfileViewTrackerService,
          useValue: stub.SessionProfileViewTrackerService
        },
        { provide: PlatformCoreService, useValue: stub.PlatformCoreService },
        { provide: ProfileNavigationService, useValue: stub.ProfileNavigationService },
        { provide: Store, useValue: stub.Store },
        { provide: SearchService, useValue: stub.SearchService },
        { provide: WindowService, useValue: stub.WindowService },
        { provide: Router, useValue: stub.Router }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    });

    TestBed.compileComponents();

    componentFixture = TestBed.createComponent(SearchResultComponent);

    component = componentFixture.componentInstance;
    component.jobSeeker = jobSeeker;

    componentFixture.detectChanges();
  });

  describe('#constructor', () => {
    it('should subscribe to the state and update the current page', () => {
      expect(component['currentPage']).toBe(page);
    });

    it('should set hasPlatformCoreAccount based on the observed value', () => {
      expect(component.hasPlatformCoreAccount).toBe(true);
    });
  });

  describe('#ngOnInit', () => {
    it('should set the flag hasValidProduct to false if the recruiter has valid products', () => {
      stub.recruiter.hasValidProduct = false;
      component.recruiter = stub.recruiter;

      component.ngOnInit();

      expect(component.hasValidProduct).toBeFalsy();
    });

    it('should set the flag isShownAsFavorite to false if the profile is not saved as favorite', () => {
      stub.SessionProfileFavoriteTrackerService.isProfileMarkAsFavorite.and.returnValue(false);
      jobSeeker.isFavorite = false;

      component.ngOnInit();

      expect(component.isShownAsFavorite).toBe(false);
    });

    it('should set the flag isShownAsFavorite to true if the profile is saved as favorite', () => {
      stub.SessionProfileFavoriteTrackerService.isProfileMarkAsFavorite.and.returnValue(true);
      jobSeeker.isFavorite = false;

      component.ngOnInit();

      expect(component.isShownAsFavorite).toBe(true);
    });

    it('should scroll to the specific position', () => {
      spyOn(component, 'scrollToSpecificUser');

      component.ngOnInit();

      expect(component['scrollToSpecificUser']).toHaveBeenCalledTimes(1);
      expect(component['scrollToSpecificUser']).toHaveBeenCalledWith();
    });
  });

  describe('#ngOnChanges', () => {
    beforeEach(() => {
      spyOn(component, 'formatJobSeekerSearchResult');
    });

    it('should format jobseeker', () => {
      component.ngOnChanges(<any>{ jobSeeker: { currentValue: 'newJobSeeker' } });
      expect(component.formatJobSeekerSearchResult).toHaveBeenCalledWith('newJobSeeker');
    });

    it('should not not format jobseeker if none is given', () => {
      component.ngOnChanges(<any>{});
      expect(component.formatJobSeekerSearchResult).not.toHaveBeenCalled();
    });

    it('should set the searchResultImage', () => {
      const c: any = component;

      component.ngOnChanges(<any>{
        jobSeeker: {
          currentValue: {
            site: 'nvb.com',
            photo: 'photo.jpg'
          }
        }
      });
      expect(c.searchResultImage).toEqual('https://cdn.nvb.com/' + 'photo.jpg');
    });

    it('should set the flag isShownAsFavorite to false if the profile is not saved as favorite', () => {
      stub.SessionProfileFavoriteTrackerService.isProfileMarkAsFavorite.and.returnValue(false);
      jobSeeker.isFavorite = false;
      component.ngOnChanges(<any>{});
      expect(component.isShownAsFavorite).toBe(false);
    });

    it('should set the flag isShownAsFavorite to true if the profile is saved as favorite', () => {
      stub.SessionProfileFavoriteTrackerService.isProfileMarkAsFavorite.and.returnValue(true);
      jobSeeker.isFavorite = false;
      component.ngOnChanges(<any>{});
      expect(component.isShownAsFavorite).toBe(true);
    });
  });

  describe('#ngOnDestroy', () => {
    it('should unsubscribe to the component promises', () => {
      component['componentDestroyed'] = jasmine.createSpyObj('componentDestroyed', ['next', 'unsubscribe']);

      component.ngOnDestroy();

      expect(component['componentDestroyed'].next).toHaveBeenCalledTimes(1);
      expect(component['componentDestroyed'].next).toHaveBeenCalledWith();
      expect(component['componentDestroyed'].unsubscribe).toHaveBeenCalledTimes(1);
      expect(component['componentDestroyed'].unsubscribe).toHaveBeenCalledWith();
    });
  });


  describe('#parseDate', () => {
    it('should return the year of the date when a date string is passed', () => {
      expect(component.parseDate('2016-01-01')).toBe('2016');
    });

    it('should return "heden" when no date string is passed', () => {
      expect(component.parseDate(undefined)).toBe('heden');
    });
  });

  describe('#formatExperience', () => {
    it('should return a formatted experience if an Experience is given', () => {
      const exp = new Experience(
        'Persgroep',
        'Description',
        '2016-01-01',
        'Job Seeker',
        '1',
        '2016-02-01',
        'Amsterdam',
        'ICT',
        'Description'
      );
      const formattedExp = component.formatExperience(exp);
      const expectedFormat = {
        isAvailable: true,
        functionName: 'Job Seeker',
        company: 'Persgroep',
        fromDate: '2016',
        toDate: '2016'
      };

      expect(formattedExp).toEqual(expectedFormat);
    });

    it('should return an "experience is unavailable object" if Experience is not given', () => {
      const formattedExp = component.formatExperience(undefined);
      const expectedFormat = {
        isAvailable: false
      };

      expect(formattedExp).toEqual(expectedFormat);
    });
  });

  describe('#formatRecentExperiences', () => {
    beforeEach(() => {
      const exp1 = Experience.empty();
      const exp2 = Experience.empty();
      const exp3 = Experience.empty();
      const exp4 = Experience.empty();
      exp1.id = '1';
      exp2.id = '2';
      exp3.id = '3';
      exp4.id = '4';

      jobSeeker.experience = [exp1, exp2, exp3, exp4];
    });

    it('should return maximum three experiences', () => {
      const recentExperiences = component.formatRecentExperiences(jobSeeker.experience);
      expect(recentExperiences.length).toEqual(3);
    });

    it('should return less than three experiences, if the total is less', () => {
      delete jobSeeker.experience[3];
      delete jobSeeker.experience[2];

      const recentExperiences = component.formatRecentExperiences(jobSeeker.experience);
      expect(recentExperiences.length).toEqual(2);
    });

    it('should return an empty array if experiences are empty', () => {
      const recentExperiences = component.formatRecentExperiences([]);
      expect(recentExperiences.length).toEqual(0);
    });

    it('should return an empty array if experiences are null', () => {
      const recentExperiences = component.formatRecentExperiences(null);
      expect(recentExperiences).toEqual([]);
    });
  });

  describe('#formatEducation', () => {
    it('should return a formatted education if an Education is given', () => {
      const ed = Education.empty();
      ed.school = 'school';
      ed.fieldOfStudy = 'fieldOfStudy';
      ed.fromDate = '2016-01-01';
      ed.toDate = '2016-01-01';
      ed.diploma = true;
      const formattedEd = component.formatEducation(ed);
      const expectedFormat = {
        isAvailable: true,
        school: 'school',
        fieldOfStudy: 'fieldOfStudy',
        fromDate: '2016',
        toDate: '2016'
      };

      expect(formattedEd).toEqual(expectedFormat);
    });

    it('should return an "education is unavailable object" if Education is not given', () => {
      const formattedEd = component.formatEducation(undefined);
      const expectedFormat = {
        isAvailable: false
      };

      expect(formattedEd).toEqual(expectedFormat);
    });

    it('should return an "education is unavailable object" if Education explicitly does not have a diploma', () => {
      const ed = Education.empty();
      ed.school = 'school';
      ed.fieldOfStudy = 'fieldOfStudy';
      ed.fromDate = 'fromDate';
      ed.toDate = 'toDate';
      ed.diploma = false;
      const formattedEd = component.formatEducation(ed);

      expect(formattedEd.isAvailable).toEqual(false);
    });

    it('should return a formatted education is diploma is undefined', () => {
      const ed = Education.empty();
      ed.school = 'school';
      const formattedEd = component.formatEducation(ed);

      expect(formattedEd.isAvailable).toEqual(true);
    });
  });

  describe('#formatJobSeekerSearchResult', () => {
    beforeEach(() => {
      spyOn(component, 'getLastValidEducation').and.returnValue('last education');
    });

    it('should format the last education with a diploma and an end date', () => {
      const ed1 = Education.empty();
      const ed2 = Education.empty();

      ed1.diploma = false;
      ed2.diploma = true;
      ed2.toDate = '2016-01-01';

      jobSeeker.education = [ed1, ed2];
      spyOn(component, 'formatEducation').and.returnValue('formatted education');

      const formattedJobSeeker = component.formatJobSeekerSearchResult(jobSeeker);
      expect(component.formatEducation).toHaveBeenCalledWith('last education');
      expect(formattedJobSeeker.education).toBe('formatted education');
    });

    it('should format the last two experiences', () => {
      const exp1 = Experience.empty();
      const exp2 = Experience.empty();
      const exp3 = Experience.empty();
      exp1.id = '1';
      exp2.id = '2';
      exp3.id = '3';

      jobSeeker.experience = [exp1, exp2, exp3];

      spyOn(component, 'formatExperience');
      component.formatJobSeekerSearchResult(jobSeeker);

      expect(component.formatExperience).toHaveBeenCalledWith(exp1);
      expect(component.formatExperience).toHaveBeenCalledWith(exp2);
    });

    it('should include correct properties in formatted job seeker', () => {
      const formattedJobSeeker = component.formatJobSeekerSearchResult(jobSeeker);
      expect(formattedJobSeeker.firstName).toBe('firstName');
      expect(formattedJobSeeker.lastName).toBe('lastName');
    });

    it('should manage the profile viewed date', () => {
      component.formatJobSeekerSearchResult(jobSeeker);

      expect(stub.SessionProfileViewTrackerService.getProfileViewed).toHaveBeenCalledTimes(1);
      expect(stub.SessionProfileViewTrackerService.getProfileViewed).toHaveBeenCalledWith(jobSeeker.id, jobSeeker.viewedDate);
    });
  });

  describe('#formatWorkingHours', () => {
    it('should return formatted hours', () => {
      const formattedWorkingHours = component.formatWorkingHours(10, 20);

      expect(formattedWorkingHours).toEqual('10 - 20 uur');
    });

    it('should return nothing if just one parameter is provided', () => {
      const formattedWorkingHours = component.formatWorkingHours(10, null);

      expect(formattedWorkingHours).toEqual(undefined);
    });

    it('should return nothing if just zero parameters are provided', () => {
      const formattedWorkingHours = component.formatWorkingHours(null, null);

      expect(formattedWorkingHours).toEqual(undefined);
    });
  });

  describe('#getLastValidEducation', () => {
    it('should return the last education with a diploma and end date', () => {
      let count = 0;
      const spyRepsonses = [{ isAvailable: false }, { isAvailable: true }];

      spyOn(component, 'formatEducation').and.callFake(() => {
        const returnVal = spyRepsonses[count];
        count += 1;
        return returnVal;
      });

      const ed1 = Education.empty();
      ed1.school = 'not returned';

      const ed2 = Education.empty();
      ed2.school = 'is returned';
      expect(component.getLastValidEducation([ed1, ed2]).school).toBe('is returned');
    });
  });

  describe('#navigateToJobSeeker', () => {
    it('should navigate to profile details when logged in', () => {
      component.recruiter = stub.recruiter;
      component.hasValidProduct = true;

      component.navigateToJobSeeker(document.createEvent('MouseEvent'), 'jobSeekerId');

      expect(stub.Router.navigate).toHaveBeenCalledWith(['/kandidaat/jobSeekerId']);
    });

    it('should navigate to profile details when limited profile feature is enabled', () => {
      component['isLimitedProfileEnabled'] = true;
      component.navigateToJobSeeker(document.createEvent('MouseEvent'), 'jobSeekerId');

      expect(stub.Router.navigate).toHaveBeenCalledWith(['/kandidaat/jobSeekerId']);
    });

    it('should store the last scroll position along with the current jobSeekerId', () => {
      component.recruiter = stub.recruiter;
      component.hasValidProduct = true;
      component.navigateToJobSeeker(document.createEvent('MouseEvent'), 'jobSeekerId');

      expect(stub.RestoreScrollPositionService.setLastJobSeekerViewed).toHaveBeenCalledWith('jobSeekerId', 0);
    });

    it('should set the current page in profile navigation service', () => {
      component.recruiter = stub.recruiter;
      component.hasValidProduct = true;
      component.navigateToJobSeeker(document.createEvent('MouseEvent'), '123-abc');

      expect(stub.ProfileNavigationService.setCurrentPage).toHaveBeenCalledTimes(1);
      expect(stub.ProfileNavigationService.setCurrentPage).toHaveBeenCalledWith(page);
    });

    it('should set zero-based index number of profile in result set in profile navigation service', () => {
      component.recruiter = stub.recruiter;
      component.hasValidProduct = true;
      component.navigateToJobSeeker(document.createEvent('MouseEvent'), '123-abc');

      expect(stub.ProfileNavigationService.setIndexNumberOfProfileInResultSet).toHaveBeenCalledTimes(1);
      expect(stub.ProfileNavigationService.setIndexNumberOfProfileInResultSet).toHaveBeenCalledWith(component.index - 1);
    });
  });

  describe('#isFavorite', () => {
    it('should return false when a profile is not saved on session storage nor in the job seeker data', () => {
      stub.SessionProfileFavoriteTrackerService.isProfileMarkAsFavorite.and.returnValue(false);
      jobSeeker.isFavorite = false;
      expect(component.isFavorite()).toBe(false);
    });

    it('should return true when a profile is saved on session storage', () => {
      stub.SessionProfileFavoriteTrackerService.isProfileMarkAsFavorite.and.returnValue(true);
      expect(component.isFavorite()).toBe(true);
    });

    it('should return true when a profile is not saved on session storage but it is saved in the job seeker data', () => {
      stub.SessionProfileFavoriteTrackerService.isProfileMarkAsFavorite.and.returnValue(false);
      jobSeeker.isFavorite = true;
      expect(component.isFavorite()).toBe(true);
    });
  });

  describe('#toggleFavorite', () => {
    const event = <any>{
      preventDefault: jasmine.createSpy('preventDefault'),
      stopPropagation: jasmine.createSpy('stopPropagation')
    };

    beforeEach(() => {
      event.preventDefault.calls.reset();
      event.stopPropagation.calls.reset();
    });

    it('should prevent default event', () => {
      component.toggleFavorite(event);

      expect(event.preventDefault).toHaveBeenCalledTimes(1);
      expect(event.preventDefault).toHaveBeenCalledWith();
    });

    it('should stop event propagation', () => {
      component.toggleFavorite(event);

      expect(event.stopPropagation).toHaveBeenCalledTimes(1);
      expect(event.stopPropagation).toHaveBeenCalledWith();
    });

    it('should unfavorite a profile', () => {
      component.isShownAsFavorite = true;
      component.toggleFavorite(event);

      expect(stub.SavedFavoritesService.del).toHaveBeenCalledTimes(1);
      expect(stub.SavedFavoritesService.del).toHaveBeenCalledWith(jobSeeker.id);

      observableDelete.next(true);

      expect(stub.SessionProfileFavoriteTrackerService.deleteProfileFavorite).toHaveBeenCalledTimes(1);
      expect(stub.SessionProfileFavoriteTrackerService.deleteProfileFavorite).toHaveBeenCalledWith(jobSeeker.id);

      expect(stub.SearchService.purgeCache).toHaveBeenCalledTimes(1);
      expect(stub.SearchService.purgeCache).toHaveBeenCalledWith();

      expect(component.isShownAsFavorite).toBe(false);
    });

    it('should favorite a profile', () => {
      component.isShownAsFavorite = false;
      component.toggleFavorite(event);

      expect(stub.SavedFavoritesService.post).toHaveBeenCalledTimes(1);
      expect(stub.SavedFavoritesService.post).toHaveBeenCalledWith(jobSeeker.id);

      observablePost.next(true);

      expect(stub.SessionProfileFavoriteTrackerService.recordFavorite).toHaveBeenCalledTimes(1);
      expect(stub.SessionProfileFavoriteTrackerService.recordFavorite).toHaveBeenCalledWith(jobSeeker.id);

      expect(component.isShownAsFavorite).toBe(true);
    });
  });

  describe('#scrollToSpecificUser', () => {
    beforeEach(() => {
      stub.RestoreScrollPositionService.resetReplay.calls.reset();
    });

    it('should scroll and reset configuration if scroll configuration is setup for a specific user', () => {
      component.jobSeeker.id = '1';
      expect(component.jobSeeker).toBeTruthy();
      expect(component.jobSeeker.id).toEqual(scrollPosition.jobSeekerId);
      expect(stub.RestoreScrollPositionService.getLastJobSeekerViewed()).toEqual(scrollPosition);
      stub.WindowService.scrollTo.calls.reset();

      component.scrollToSpecificUser();

      expect(stub.WindowService.scrollTo).toHaveBeenCalledTimes(1);
      expect(stub.WindowService.scrollTo).toHaveBeenCalledWith(0, scrollPosition.scrollY);

      expect(stub.RestoreScrollPositionService.resetReplay).toHaveBeenCalledTimes(1);
      expect(stub.RestoreScrollPositionService.resetReplay).toHaveBeenCalledWith();
    });

    it('should do nothing if scroll configuration is not setup for a specific user', () => {
      stub.RestoreScrollPositionService.getLastJobSeekerViewed.and.returnValue(null);

      component.scrollToSpecificUser();

      expect(stub.RestoreScrollPositionService.resetReplay).not.toHaveBeenCalled();
    });

    it('should do nothing if no user is recognized to scroll near to', () => {
      component.jobSeeker.id = 'unknown-id';

      component.scrollToSpecificUser();

      expect(stub.RestoreScrollPositionService.resetReplay).not.toHaveBeenCalled();
    });
  });

  describe('fallBackToOldProfileImage', () => {
    it('should fall back to old profile image, if image cannot be retrieved from cdn', () => {
      component.fallBackToOldProfileImage(null);
      expect(component.hasProfileManagerImage).toEqual(false);
      expect(component.hasOldImage).toEqual(true);
    });
  });
});
