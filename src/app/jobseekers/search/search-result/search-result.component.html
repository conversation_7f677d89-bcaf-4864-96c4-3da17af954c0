<a [attr.data-gtm-index]="(index | zeroPad)"
   data-gtm="jobseeker-click"
   [href]="'kandidaat/' + formattedJobSeeker?.id"
   (click)="navigateToJobSeeker($event, formattedJobSeeker?.id)"
   class="search-result col-12">
    <div class="search-result-personal">
        <div class="search-result-personal-container">
            <div class="search-result-image-container">
                <img *ngIf="hasProfileManagerImage"
                     [src]="searchResultImage"
                     (error)="fallBackToOldProfileImage($event)"
                     alt="profile-image"
                     class="search-result-image"/>
                <img *ngIf="hasOldImage"
                     [src]="searchResultImageFallback"
                     alt="profile-image"
                     class="search-result-image"/>
                <div *ngIf="!(hasProfileManagerImage || hasOldImage)" class="search-result-image default-image"></div>
            </div>

            <h3 class="search-result-name">
                {{ formattedJobSeeker?.firstName }} {{ formattedJobSeeker?.lastName }}
            </h3>

            <div class="search-result-specs" [class.search-result-specs--main-header]="!hasValidProduct">
                <span *ngIf="formattedJobSeeker?.workLevels && formattedJobSeeker.workLevels.length > 0">{{ formattedJobSeeker?.workLevels.join(', ') }}</span>
                <span *ngIf="formattedJobSeeker?.workingHours">{{ formattedJobSeeker?.workingHours }}</span>
                <span *ngIf="formattedJobSeeker?.availability">{{ formattedJobSeeker?.availability }}</span>
                <span *ngIf="formattedJobSeeker?.city">{{ formattedJobSeeker?.city }}</span>
            </div>

            <div class="search-result-interactions" *ngIf="hasPlatformCoreAccount">
                <i class="icon-favorite icon-interaction"
                   [ngClass]="{'is-favorite': isShownAsFavorite}"
                   (click)="toggleFavorite($event)"
                   data-gtm="jobseeker-favorite">
                </i>
                <div class="clear"></div>
            </div>

            <div class="search-result-edit-date-desktop">
                <span class="icons">
                    <i class="icon-dates icon-dates__update"></i>
                    {{ formattedJobSeeker?.updatedDate | daysAgoDate }}
                </span>
                <span class="icons"
                      *ngIf="formattedJobSeeker?.viewedDate">
                    <i class="icon-dates icon-dates__view"></i>
                    {{ formattedJobSeeker.viewedDate | daysAgoDate }}
                </span>
            </div>

            <div class="clear"></div>
        </div>
    </div>

    <div class="search-result-last-experiences">
        <p class="search-result-work-title">Recente werkervaring</p>
        <span *ngIf="formattedJobSeeker?.recentJobs[0]">{{ formattedJobSeeker?.recentJobs[0].functionName }}</span>
        <span *ngIf="formattedJobSeeker?.recentJobs[1]">{{ formattedJobSeeker?.recentJobs[1].functionName }}</span>
        <span *ngIf="formattedJobSeeker?.recentJobs[2]">{{ formattedJobSeeker?.recentJobs[2].functionName }}</span>
        <span *ngIf="!formattedJobSeeker?.recentJobs.length">---</span>
    </div>
    <div class="search-result-desired-experiences">
        <p class="search-result-work-title">Gewenste banen</p>
        <span *ngIf="formattedJobSeeker?.desiredJobs[0]">{{ formattedJobSeeker?.desiredJobs[0] }}</span>
        <span *ngIf="formattedJobSeeker?.desiredJobs[1]">{{ formattedJobSeeker?.desiredJobs[1] }}</span>
        <span *ngIf="formattedJobSeeker?.desiredJobs[2]">{{ formattedJobSeeker?.desiredJobs[2] }}</span>
        <span *ngIf="!formattedJobSeeker?.desiredJobs.length">---</span>
    </div>

    <div class="search-result-others">
        <p class="search-result-edit-date-mobile">
            <span class="icons">
                <i class="icon-dates icon-dates__update"></i>
                {{ formattedJobSeeker?.updatedDate | daysAgoDate }}
            </span>
            <span class="icons"
                  *ngIf="formattedJobSeeker?.viewedDate">
                <i class="icon-dates icon-dates__view"></i>
                {{ formattedJobSeeker.viewedDate | daysAgoDate }}
            </span>
        </p>
    </div>
</a>
<div class="clear"></div>
