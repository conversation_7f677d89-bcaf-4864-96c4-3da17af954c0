<div class="container">
    <div class="row mt-3 mb-3 mt-md-5 mb-md-5">
        <div class="col-lg-3 col-md-4">
            <div *ngIf="recruiter && !recruiter.hasSubscription" class="credits mb-3">
                <span>Je kunt nog {{recruiter?.amountOfCredits}} profielen bekijken.</span>
                <a href="{{ purchaseProfileCreditsUrl }}" data-gtm="ser-buy-profile-credits">Koop profielcredits</a>
            </div>

            <app-filters [aggregations]="aggregations"
                         [amountOfResults]="amountOfResultsObservable"
                         [hideRadius]="!!searchedWithLocationQuery && !locationError"
                         [filters]="filters$ | async"
                         (openModalEmitter)="openSearchTipsModal()"></app-filters>
        </div>

        <div class="search-container col-lg-9 col-md-8">
            <app-result-count [amountOfResults]="amountOfResultsObservable"
                              [searchedWithLocation]="searchedWithLocationQuery === searchState?.locationQuery && !locationError && !isSearching"
                              [location]="locationQuery"></app-result-count>

            <app-result-count [amountOfResults]="amountOfResultsObservable"
                              [sticky]="true"></app-result-count>

            <div class="btn-save-search" *ngIf="hasPlatformCoreAccount">
                <button *ngIf="recruiter"
                        type="button"
                        class="btn btn-transparent btn-block"
                        (click)="openSaveSearchModal()"
                        data-gtm="modal-saved-search"
                        data-slug="open">
                    Zoekopdracht bewaren
                </button>
                <a href="javascript:"
                   class="btn btn-transparent btn-block mt-3 mobile-buttons"
                   (click)="openFilter()">
                   Filters
                </a>
            </div>

            <div class="search-sort" *ngIf="pages?.totalItems > 0">
                <div class="left">
                    <div class="search-tips">
                        <a href="javascript:"
                           (click)="openSearchTipsModal()"
                           data-gtm="filter-search-tips-mobile">
                            Zoektips
                        </a>
                    </div>
                    <div class="page-info">
                        <b>{{pages?.startIndex + 1 }}</b> - <b>{{ pages?.endIndex + 1 }}</b> van <b>{{ amountOfResultsObservable | async }}</b>
                    </div>
                </div>
                <div class="right">
                    <div class="sort">
                        Sorteer op:
                        <a (click)="setSort('update')"
                           [ngClass]="{active: searchState?.sort === 'update' || searchState?.sort === 'default' }"
                           data-gtm="jobseeker-sort"
                           data-slug="date">datum</a>
                        <span *ngIf="isSearchQueryPresent(searchedWithQuery)"> | </span>
                        <a (click)="setSort('relevance')"
                           *ngIf="isSearchQueryPresent(searchedWithQuery)"
                           [ngClass]="{active: searchState?.sort === 'relevance'}"
                           data-gtm="jobseeker-sort"
                           data-slug="relevance">relevantie</a>
                    </div>
                    <app-page-sizer (updateItemsPerPage)="updatePageSize($event)"
                                    [pageSize]="pageSize"
                                    [type]="'top'"></app-page-sizer>
                </div>
            </div>

            <div *ngIf="jobSeekers?.length > 0" class="search-results">
                <div *ngIf="isSearching" class="overlay">
                    <div class="search-spinner"></div>
                </div>
                <app-search-result [jobSeeker]="js"
                                   [recruiter]="recruiter"
                                   [index]="i + 1"
                                   *ngFor="let js of jobSeekers; let i = index; trackBy: identifySearchResult"></app-search-result>
            </div>

            <app-search-error *ngIf="!isSearching"
                              [error]="error"></app-search-error>

            <app-pagination *ngIf="pages?.totalItems > 0"
                            [currentPage]="(search$ | async)?.currentPage"
                            [resultCount]="amountOfResults$ | async"
                            [pageSize]="(search$ | async)?.pageSize"
                            type="jobseeker"
                            (pageChange)="changePage($event)"></app-pagination>

            <app-page-sizer (updateItemsPerPage)="updatePageSize($event)"
                            [pageSize]="pageSize"
                            [type]="'bottom'"></app-page-sizer>
        </div>
    </div>
</div>

<app-save-search-modal *ngIf="saveSearchModal$ | async"
                       [searchQuery]="(search$ | async)?.searchQuery"
                       [filters]="filters"
                       [locationQuery]="(search$ | async)?.locationQuery"></app-save-search-modal>

<app-search-tips-modal *ngIf="searchTipsModal$ | async"></app-search-tips-modal>
