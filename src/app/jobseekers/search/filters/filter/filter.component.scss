@import "components";

:host {
  .filter-container {
    border-top: 1px solid $border-color;

    a.toggle {
      color: $generic-title-color;
      cursor: pointer;
      display: inline-block;
      margin-bottom: 20px;
    }
  }

  .title {
    color: $generic-title-color;
    font-size: 17px;
    font-weight: 700;
    line-height: 3.18;
    position: relative;
    a {
      bottom: 0;
      color: transparent;
      cursor: pointer;
      font-size: 0;
      left: 0;
      position: absolute;
      right: 0;
      text-align: right;
      top: 0;
      &::before, &::after {
        background: $border-color;
        content: "";
        height: 2px;
        position: absolute;
        right: 3px;
        top: 27px;
        transition: transform 0.3s ease;
        width: 12px;
      }
      &::after {
        transform: rotate(90deg);
      }
    }
    &.title-open a {
      &::after {
        -ms-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
      }
    }
  }

  .option {
    display: none;
    position: relative;

    &.open {
      display: inherit;
    }
    &:last-child {
      margin-bottom: 20px;
    }
    input[type="checkbox"] {
      position: absolute;
      visibility: hidden;
    }
    label {
      background: transparent;
      cursor: pointer;
      display: block;
      font-size: $filters-label-fontsize;
      line-height: 36px;
      margin-bottom: $filters-label-marginbottom;
      margin-left: 0;
      padding-left: 28px;
      position: relative;
      transition: all 0.2s ease;
      width: 100%;
      &.disabled {
        color: grey;
      }
      &::before {
        border: 1px solid $checkbox-border;
        content: "";
        height: 18px;
        left: 0;
        position: absolute;
        top: 10px;
        transition: all 0.2s ease;
        width: 18px;
      }
      &::after {
        background: $checkbox-backgroundcolor;
        border: 1px solid $checkbox-selected-color;
        border-right: none;
        border-top: none;
        content: "";
        height: 5px;
        left: 2px;
        opacity: 0;
        position: absolute;
        top: 16px;
        transform: rotate(-54deg);
        transition: all 0.2s ease;
        width: 13px;
      }
      &:hover {
        &.disabled {
          &::before {
            border-color: $checkbox-border;
          }
        }
        &::before {
          border-color: $checkbox-border;
        }
      }
    }
    input[type="checkbox"]:checked + label {
      font-weight: 600;
      &::before {
        background: $checkbox-selected-backgroundcolor;
        border-color: $checkbox-selected-border;
      }
      &::after {
        opacity: 1;
      }
    }
    .hidden {
      display: none;
    }
  }
}
