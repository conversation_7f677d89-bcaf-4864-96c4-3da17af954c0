import { Component, Input, OnChanges, ChangeDetectionStrategy, SimpleChanges } from '@angular/core';
import { Store } from '@ngrx/store';
import { AppState } from '../../../../types/app-state';
import { Filter, FilterOption } from '../../../../store/models/filter.modal';
import * as FilterActions from '../../../../store/actions/filter/filter.actions';

@Component({
  selector: 'app-filter',
  templateUrl: './filter.component.html',
  styleUrls: ['./filter.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FilterComponent implements OnChanges {
  @Input()
  public filter: Filter;
  @Input()
  public type: string;

  public isCollapsed = true;

  constructor(private store: Store<AppState>) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.filter && changes.filter.currentValue) {
      this.filter.filterOptions = changes.filter.currentValue.filterOptions.sort((a: FilterOption, b: FilterOption) => a.order - b.order);
    }
  }

  toggleOpen() {
    this.filter.open = !this.filter.open;
  }

  toggleFilter(filterOption: FilterOption) {
    this.store.dispatch(new FilterActions.ToggleFilterOption({ filter: this.filter.name, filterOption: filterOption.name }));
  }

  isFilterOptionShown(index: number): boolean {
    if (!this.filter.collapsible) {
      return true;
    }

    if (!this.filter.numberOfShownItems) {
      return true;
    }

    if (!this.isCollapsed) {
      return true;
    }

    if (index < this.filter.numberOfShownItems) {
      return true;
    }

    return false;
  }
}
