<div class="filter-container">
    <div class="title" [ngClass]="{'title-open': filter?.open}">
        {{ filter?.title }}

        <a (click)="toggleOpen()"
           [attr.data-gtm]="'filter-' + filter?.name.toLowerCase()"
           [attr.data-slug]="'filter-' + (filter?.open ? 'show': 'hide')">
            +
        </a>
    </div>
    <div class="options">
        <div class="option"
             [ngClass]="{'open': filter?.open || filterOption.selected}"
             *ngFor="let filterOption of filter?.filterOptions; let i = index">
            <div [ngClass]="{ hidden: !isFilterOptionShown(i) }">
                <input id="{{ filter.name }}{{ filterOption.name }}"
                       type="checkbox" [ngModel]="filterOption.selected"
                       [attr.data-gtm]="'filter-' + filter?.name.toLowerCase()"
                       [attr.data-slug]="filterOption.name.toLowerCase() + '-' + (filterOption.selected ? 'false': 'true')"
                       (click)="toggleFilter(filterOption, $event)"
                       [disabled]="filterOption.count === '0' && !filterOption.selected"/>
                <label [ngClass]="{ 'disabled': filterOption.count === '0' && !filterOption.selected }"
                       htmlFor="{{ filter.name }}{{ filterOption.name }}"
                       title="{{ filterOption.name }}">
                    {{ filter.name === "functionGroups" ? (filterOption.name | truncate : 18) : filterOption.name }}
                    (<span>{{ filterOption.count }}</span>)
                </label>
            </div>
        </div>
        <div *ngIf="filter?.collapsible">
            <a *ngIf="isCollapsed"
               class="toggle"
               (click)="isCollapsed = !isCollapsed"
               [attr.data-gtm]="'filter-' + filter?.name.toLowerCase()"
               data-slug="filter-options-show">
                meer...
            </a>
            <a *ngIf="!isCollapsed"
               class="toggle"
               (click)="isCollapsed = !isCollapsed"
               [attr.data-gtm]="'filter-' + filter?.name.toLowerCase()"
               data-slug="filter-options-hide">
                minder
            </a>
        </div>
    </div>
</div>
