import { TestBed, ComponentFixture } from '@angular/core/testing';
import { FilterComponent } from './filter.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { Store } from '@ngrx/store';
import { FilterOption } from '../../../../store/models/filter.modal';
import { TruncatePipe } from '../../../../shared/pipes';
import * as FilterActions from '../../../../store/actions/filter/filter.actions';

enum FilterTypes {
  CheckBox = 0,
  Dropdown = 1,
  AutoComplete = 2
}

describe('FilterComponent', () => {
  const stub: any = {};
  let componentFixture: ComponentFixture<FilterComponent>, component: FilterComponent;

  beforeEach(() => {
    stub.Store = jasmine.createSpyObj('Store', ['dispatch', 'select']);

    TestBed.configureTestingModule({
      declarations: [FilterComponent, TruncatePipe],
      providers: [{ provide: Store, useValue: stub.Store }],
      schemas: [NO_ERRORS_SCHEMA]
    });

    TestBed.compileComponents();
    componentFixture = TestBed.createComponent(FilterComponent);
    componentFixture.detectChanges();
    component = componentFixture.componentInstance;
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('ngOnChanges', () => {
    it('should sort filterOptions when they are updated', () => {
      const filterOptions: FilterOption[] = [
        {
          name: 'filter with order 2',
          count: '1',
          order: 2,
          selected: false
        },
        {
          name: 'filter with order 1',
          count: '1',
          order: 1,
          selected: false
        }
      ];
      const changes = {
        filter: { currentValue: { filterOptions: filterOptions } }
      };
      component.filter = {
        name: 'filter',
        title: 'filter',
        type: FilterTypes.CheckBox,
        open: true,
        filterOptions: []
      };

      component.ngOnChanges(<any>changes);
      expect(component.filter.filterOptions[0].name).toBe('filter with order 1');
      expect(component.filter.filterOptions[1].name).toBe('filter with order 2');
    });

    it('should check if changes have filterOptions for safety', () => {
      const changes = {};
      expect(component.ngOnChanges.bind(null, changes)).not.toThrow();
    });
  });

  describe('toggleOpen', () => {
    it('should toggle filter.open', () => {
      component.filter = <any>{ open: true };
      component.toggleOpen();
      expect(component.filter.open).toBe(false);
    });
  });

  describe('toggleFilter', () => {
    beforeEach(() => {
      component.filter = {
        name: 'filterName',
        title: 'filter',
        type: FilterTypes.CheckBox,
        open: true,
        filterOptions: [
          {
            name: 'filterOptionName',
            count: '0',
            selected: true,
            order: 1
          },
          {
            name: 'filterOptionName2',
            count: '0',
            selected: true,
            order: 1
          }
        ]
      };
    });

    it('should dispatch a TOGGLE_FILTER_OPTION event', () => {
      component.toggleFilter({
        name: 'filterOptionName',
        count: '0',
        selected: true,
        order: 1
      });
      expect(stub.Store.dispatch).toHaveBeenCalledWith(
        new FilterActions.ToggleFilterOption({ filter: 'filterName', filterOption: 'filterOptionName' })
      );
    });

    it('should not reverse selected value of filterOption that are not toggled', () => {
      component.toggleFilter({
        name: 'filterOptionName',
        count: '0',
        selected: true,
        order: 1
      });
      expect(component.filter.filterOptions[1].selected).toBe(true);
    });
  });

  describe('#isFilterOptionShown', () => {
    it('should return true if filter is not collapsible', () => {
      component.filter = <any>{ collapsible: false };

      expect(component.isFilterOptionShown(1)).toBe(true);
    });

    it('should return true if number of shown numbers not defined or 0', () => {
      component.filter = <any>{ collapsible: true, numberOfShownItems: 0 };

      expect(component.isFilterOptionShown(1)).toBe(true);
    });

    it('should return true filter option is not collapsed', () => {
      component.isCollapsed = false;
      component.filter = <any>{ collapsible: true, numberOfShownItems: 5 };

      expect(component.isFilterOptionShown(1)).toBe(true);
    });

    it('should return true if index is less than', () => {
      component.isCollapsed = true;
      component.filter = <any>{ collapsible: true, numberOfShownItems: 5 };

      expect(component.isFilterOptionShown(1)).toBe(true);
    });

    it('should return false if index is equal or greater than number of shown items', () => {
      component.isCollapsed = true;
      component.filter = <any>{ collapsible: true, numberOfShownItems: 5 };

      expect(component.isFilterOptionShown(5)).toBe(false);
    });
  });
});
