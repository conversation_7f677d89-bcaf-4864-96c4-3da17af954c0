import { Component, Input, ChangeDetectionStrategy, EventEmitter, Output } from '@angular/core';
import { Observable } from 'rxjs';
import { Store } from '@ngrx/store';
import { AppState } from '../../../types/app-state';
import { Filter } from '../../../store/models/filter.modal';
import * as FilterActions from '../../../store/actions/filter/filter.actions';

enum FilterTypes {
  CheckBox = 0,
  Dropdown = 1,
  AutoComplete = 2
}

@Component({
  selector: 'app-filters',
  templateUrl: './filters.component.html',
  styleUrls: ['./filters.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FiltersComponent {
  public filterTypes = FilterTypes;
  @Input()
  public aggregations: any;
  @Input()
  public filters: Filter[] = [];
  @Input()
  public hideRadius: boolean;
  @Input()
  public amountOfResults: Observable<number>;
  public filter: Observable<boolean>;
  private locationQuery: string;
  @Output()
  openModalEmitter = new EventEmitter<any>();

  constructor(public store: Store<AppState>) {
    this.filter = store.select(s => s.filter.open);
    store.subscribe((state: AppState) => {
      this.locationQuery = state.search.locationQuery;
    });
  }

  removeAllFilters() {
    this.store.dispatch(new FilterActions.CloseAllFilters());
  }

  identifyFilter(index: number, filter: Filter) {
    return filter.name;
  }

  closeFilters() {
    this.store.dispatch(new FilterActions.CloseFilter());
  }

  isShown(filterName: string): boolean {
    return !(filterName === 'provinces' && !this.isProvincesFilterShown());
  }

  private isProvincesFilterShown(): boolean {
    return this.locationQuery.length === 0;
  }

  emitOpenSearchTipsModal(): void {
    this.openModalEmitter.emit();
  }
}
