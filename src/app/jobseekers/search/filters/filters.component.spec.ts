import { TestBed, ComponentFixture, async } from '@angular/core/testing';
import { FiltersComponent } from './filters.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable, ReplaySubject } from 'rxjs';
import { map } from 'rxjs/operators';
import { Filter } from '../../../store/models/filter.modal';
import * as FilterActions from '../../../store/actions/filter/filter.actions';
import { initialFiltersState } from '../../../store/reducers/filter/filter.config';

describe('FiltersComponent', () => {
  const stub: any = {};
  let componentFixture: ComponentFixture<FiltersComponent>, component: FiltersComponent;
  let openModalEmitter: any;

  beforeEach(async(() => {
    openModalEmitter = jasmine.createSpyObj('EventEmitter', ['emit']);

    stub.Store = new ReplaySubject();
    stub.Store.next({ search: { locationQuery: '' } });
    stub.Store.dispatch = jasmine.createSpy('dispatch');
    stub.Store.select = jasmine.createSpy('select');

    stub.Store.select.and.callFake((fn: any) => {
      return Observable.create((obs: any) => {
        obs.next({ filter: { open: true } });
      }).pipe(map(fn));
    });

    TestBed.configureTestingModule({
      providers: [{ provide: Store, useValue: stub.Store }],
      declarations: [FiltersComponent],
      schemas: [NO_ERRORS_SCHEMA]
    });
  }));

  beforeEach(() => {
    TestBed.compileComponents();
    componentFixture = TestBed.createComponent(FiltersComponent);
    component = componentFixture.componentInstance;
    component.openModalEmitter = openModalEmitter;
    componentFixture.detectChanges();
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('#removeAllFilters', () => {
    it('should unselect all filter options for all filters', () => {
      component.removeAllFilters();

      expect(stub.Store.dispatch).toHaveBeenCalledWith(new FilterActions.CloseAllFilters());
    });
  });

  describe('#identifyFilter', () => {
    it('should return a unique identifier for a filter', () => {
      const result = component.identifyFilter(0, <Filter>{ name: 'filter' });
      expect(result).toBe('filter');
    });
  });

  describe('#closeFilters', () => {
    it('should dispatch a CLOSE_FILTER event', () => {
      component.closeFilters();
      expect(stub.Store.dispatch).toHaveBeenCalledWith(new FilterActions.CloseFilter());
    });
  });

  describe('#isShown', () => {
    let isProvincesFilterShownSpy: any;

    beforeEach(() => {
      isProvincesFilterShownSpy = jasmine.createSpy('isProvincesFilterShown');
    });

    it('should return false for the provinces filter if location query is not empty', () => {
      isProvincesFilterShownSpy.and.returnValue(false);

      component['isProvincesFilterShown'] = isProvincesFilterShownSpy;

      expect(component['isShown']('provinces')).toEqual(false);
    });

    it('should return false for the provinces filter if location query is not empty', () => {
      isProvincesFilterShownSpy.and.returnValue(true);

      component['isProvincesFilterShown'] = isProvincesFilterShownSpy;

      expect(component['isShown']('provinces')).toEqual(true);
    });

    it('should return false for the provinces filter if location query is not empty', () => {
      isProvincesFilterShownSpy.and.returnValue(false);

      component['isProvincesFilterShown'] = isProvincesFilterShownSpy;

      expect(component['isShown']('lasagne')).toEqual(true);
    });

    it('should return false for the provinces filter if location query is not empty', () => {
      isProvincesFilterShownSpy.and.returnValue(true);

      component['isProvincesFilterShown'] = isProvincesFilterShownSpy;

      expect(component['isShown']('lasagne')).toEqual(true);
    });

    it('should return true for all filters if location query is empty', () => {
      stub.Store.next({ search: { locationQuery: '' } });

      initialFiltersState.filters.forEach(({ name }) => {
        expect(component.isShown(name)).toBe(true);
      });
    });
  });

  describe('#isProvincesFilterShown', () => {
    it('should return true if a province filter is active', () => {
      component['locationQuery'] = 'Reggio Emilia';
      expect(component['isProvincesFilterShown']()).toBe(false);
    });

    it('should return false if a province filter is not active', () => {
      component['locationQuery'] = '';
      expect(component['isProvincesFilterShown']()).toBe(true);
    });
  });

  describe('#emitOpenSearchTipsModal', () => {
    it('should emit open search tips modal action', () => {
      component.emitOpenSearchTipsModal();

      expect(component.openModalEmitter.emit).toHaveBeenCalledTimes(1);
      expect(component.openModalEmitter.emit).toHaveBeenCalledWith();
    });
  });
});
