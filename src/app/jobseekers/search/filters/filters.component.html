<div class="filters" [ngClass]="{ 'offscreen': !(filter | async) }">
    <div class="header">
        <div class="total-results">
            <span class="number">{{ amountOfResults | async }}</span>
            Kandidaten
        </div>
        <div class="close">
            <a (click)="closeFilters()"></a>
        </div>
    </div>
    <div class="body">
        <a href="javascript:"
           (click)="emitOpenSearchTipsModal()"
           class="action search-tips"
           data-gtm="filter-search-tips-desktop">
            Zoektips
        </a>

        <hr/>

        <a (click)="removeAllFilters()"
           class="action delete-all"
           data-gtm="filter-remove-all-desktop">
            Wis alle filters
        </a>

        <div *ngFor="let filter of filters; trackBy: identifyFilter" [ngSwitch]="filter.type">
            <div *ngIf="isShown(filter.name)">
                <app-filter *ngSwitchCase="filterTypes.CheckBox" [filter]="filter"></app-filter>

                <div *ngIf="filter.name !== 'radius' || hideRadius">
                    <app-dropdown-filter *ngSwitchCase="filterTypes.Dropdown" [filter]="filter"></app-dropdown-filter>
                </div>

                <app-auto-complete-filter *ngSwitchCase="filterTypes.AutoComplete"
                                          [filter]="filter"
                                          [allowNotListedOptions]="!!filter.allowNotListedOptions">
                </app-auto-complete-filter>
            </div>
        </div>

        <a (click)="removeAllFilters()"
           class="btn btn-transparent btn-block mobile-delete-all"
           data-gtm="filter-remove-all-mobile">
            Filters wissen
        </a>
    </div>
</div>
