@import "components";

:host {
  .filter-container {
    border-top: 1px solid $border-color;
  }

  .title {
    color: $autocomplete-text-color;
    font-size: 17px;
    font-weight: 700;
    line-height: 3.18;
    position: relative;
    a {
      bottom: 0;
      color: transparent;
      cursor: pointer;
      font-size: 0;
      left: 0;
      position: absolute;
      right: 0;
      text-align: right;
      top: 0;
      &::before, &::after {
        background-color: $border-color;
        content: "";
        height: 2px;
        position: absolute;
        right: 3px;
        top: 27px;
        transition: transform 0.3s ease;
        width: 12px;
      }
      &::after {
        -ms-transform: rotate(90deg);
        -moz-transform: rotate(90deg);
        -o-transform: rotate(90deg);
        -webkit-transform: rotate(90deg);
        transform: rotate(90deg);
      }
    }
    &.title-open a {
      &::after {
        -ms-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
      }
    }
  }

  .selected-filters {
    .selected-filter {
      background-color: #e6e6e6;
      border-radius: 3px;
      display: block;
      font-size: $filters-label-fontsize;
      line-height: 44px;
      margin-bottom: 10px;
      padding: 0 10px;
      &:last-child {
        margin-bottom: 20px;
      }
      @include media-breakpoint-up(md) {
        line-height: 30px;
      }

      .close {
        color: $autocomplete-text-color;
        cursor: pointer;
        display: block;
        float: right;
        margin-right: 15px;
        margin-top: 11px;
        position: relative;
        &::before, &::after {
          border: 1px solid $autocomplete-text-color;
          content: "";
          height: 1px;
          left: 0;
          position: absolute;
          top: 4px;
          width: 18px;

          @include media-breakpoint-down(sm) {
            top: 10px;
          }
        }
        &::before {
          transform: rotate(-45deg);
        }
        &::after {
          transform: rotate(-135deg);
        }
      }
    }
  }

  .input-section {
    display: none;
    &.open {
      display: inherit;
      margin-bottom: 20px;
    }
  }
}
