<div class="filter-container">
    <div class="title" [ngClass]="{'title-open': filter?.open}">
        {{ filter?.title }}

        <a (click)="toggleOpen()"
           [attr.data-gtm]="'filter-' + filter?.name.toLowerCase()"
           [attr.data-slug]="'filter-' + (filter?.open ? 'show': 'hide')">
            +
        </a>
    </div>
    <div>
        <div class="selected-filters">
            <div class="selected-filter" *ngFor="let filterOption of selectedFilters">
                <div>
                    {{filterOption.name}}
                    <div (click)="toggleFilter(filterOption.name)"
                         class="close"
                         [attr.data-gtm]="'filter-' + filter?.name.toLowerCase()"
                         [attr.data-slug]="filter?.name.toLowerCase() + '-remove-' + filterOption.name.toLowerCase()"></div>
                </div>
            </div>
        </div>
        <div class="input-section"
             [ngClass]="{'open': filter?.open}">
            <input  class="input"
                    type="text"
                    [(ngModel)]="autoCompleteInput"
                    (ngModelChange)="autoCompleteInputChange.emit($event)"
                    name="autocompleteFilter"
                    (keydown)="keyDown($event)"
                    (keyup)="keyUp$.next($event)"
                    (blur)="blur$.next()"
                    (focus)="focus$.next()"
                    [placeholder]="filter?.placeholder">
            <app-auto-complete [input]="autoCompleteInputChange"
                               [keyUp]="keyUp$"
                               [keyDown]="keyDown$"
                               (autocompleteSelectedValueChange)="updateAutoCompleteOpened($event)"
                               [blur]="blur$"
                               [focus]="focus$"
                               position="relative"
                               [name]="filter?.name"
                               [action]="filter?.action"
                               [openWithFirstSelected]="!filter?.allowNotListedOptions"
                               [resultAction]="filter?.resultAction"
                               [selectedFilters]="selectedFilters"
                               (autocompleteValueSelect)="toggleFilter($event)"></app-auto-complete>
        </div>
    </div>
</div>
