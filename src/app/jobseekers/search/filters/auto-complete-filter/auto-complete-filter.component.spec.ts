import { TestBed, ComponentFixture } from '@angular/core/testing';
import { AutoCompleteFilterComponent } from './auto-complete-filter.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { Store } from '@ngrx/store';
import { FilterOption } from '../../../../store/models/filter.modal';
import * as FilterActions from '../../../../store/actions/filter/filter.actions';

enum FilterTypes {
  CheckBox = 0,
  Dropdown = 1,
  AutoComplete = 2
}

describe('AutoCompleteFilterComponent', () => {
  const stub: any = {};
  let componentFixture: ComponentFixture<AutoCompleteFilterComponent>, component: AutoCompleteFilterComponent;

  beforeEach(() => {
    stub.Store = jasmine.createSpyObj('Store', ['dispatch', 'select']);

    TestBed.configureTestingModule({
      declarations: [AutoCompleteFilterComponent],
      providers: [{ provide: Store, useValue: stub.Store }],
      schemas: [NO_ERRORS_SCHEMA]
    });

    TestBed.compileComponents();
    componentFixture = TestBed.createComponent(AutoCompleteFilterComponent);
    componentFixture.detectChanges();
    component = componentFixture.componentInstance;
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('#ngOnChanges', () => {
    it('should only show selected filters', () => {
      const filterOptions: FilterOption[] = [
        {
          name: 'filter with order 2',
          count: '1',
          order: 2,
          selected: true
        },
        {
          name: 'filter with order 1',
          count: '1',
          order: 1,
          selected: false
        }
      ];
      const changes = {
        filter: { currentValue: { filterOptions: filterOptions } }
      };
      component.filter = {
        name: 'filter',
        title: 'filter',
        type: FilterTypes.CheckBox,
        open: true,
        filterOptions: []
      };

      component.ngOnChanges(<any>changes);
      expect(component.selectedFilters.length).toBe(1);
      expect(component.selectedFilters.pop()).toBe(filterOptions[0]);
    });

    it('should check if changes have filterOptions for safety', () => {
      const changes = {};
      expect(component.ngOnChanges.bind(null, changes)).not.toThrow();
    });
  });

  describe('#toggleOpen', () => {
    it('should toggle filter.open when true', () => {
      component.filter = <any>{ open: true };
      component.toggleOpen();
      expect(component.filter.open).toBe(false);
    });

    it('should toggle filter.open when false', () => {
      component.filter = <any>{ open: false };
      component.toggleOpen();
      expect(component.filter.open).toBe(true);
    });
  });

  describe('#updateAutoCompleteOpened', () => {
    it('should update auto complete opened flag when true', () => {
      component.updateAutoCompleteOpened(true);
      expect(component.autoCompleteOpened).toBe(true);
    });

    it('should update auto complete opened flag when false', () => {
      component.updateAutoCompleteOpened(false);
      expect(component.autoCompleteOpened).toBe(false);
    });
  });

  describe('#keyDown', () => {
    let keyEvent: KeyboardEvent;

    beforeEach(() => {
      component.filter = <any>{ name: 'testFilter' };
      component.allowNotListedOptions = true;
      keyEvent = <any>{
        keyCode: 13,
        target: { value: 'test' },
        preventDefault: () => {}
      };
    });

    it('should preventDefault and dispatch event when enter pressed with no selection via autocomplete', () => {
      component.keyDown(keyEvent);

      expect(stub.Store.dispatch).toHaveBeenCalledWith(
        new FilterActions.ForceSelectFilterOption({
          filter: 'testFilter',
          filterOption: {
            name: 'test',
            order: 1,
            selected: true
          }
        })
      );
    });

    it('should not dispatch if autComplete is opened', () => {
      component.autoCompleteOpened = true;
      component.keyDown(keyEvent);

      expect(stub.Store.dispatch).not.toHaveBeenCalled();
    });

    it('should not dispatch if not listed options are not allowed', () => {
      component.allowNotListedOptions = false;
      component.keyDown(keyEvent);

      expect(stub.Store.dispatch).not.toHaveBeenCalled();
    });
  });

  describe('#toggleFilter', () => {
    beforeEach(() => {
      component.autoCompleteInput = 'test';
      component.filter = {
        name: 'filter',
        title: 'filter',
        type: FilterTypes.CheckBox,
        open: true,
        filterOptions: [
          {
            name: 'selected filter option',
            count: '1',
            order: 2,
            selected: true
          }
        ]
      };
    });

    it('should dispatch toggle the filter option when match in filters', () => {
      const filterValue = 'selected filter option';

      component.toggleFilter(filterValue);

      expect(stub.Store.dispatch).toHaveBeenCalledWith(
        new FilterActions.ToggleFilterOption({ filter: 'filter', filterOption: filterValue })
      );
    });

    it('should dispatch toggle the filter option when no match in filters', () => {
      const filterValue = 'test';

      component.toggleFilter(filterValue);

      expect(stub.Store.dispatch).toHaveBeenCalledWith(
        new FilterActions.ToggleFilterOption({ filter: 'filter', filterOption: filterValue })
      );
    });

    it('should select a new filter option when a new option is selected and allowNotListedOptions is true', () => {
      component.filter.allowNotListedOptions = true;
      const filterValue = 'test';

      component.toggleFilter(filterValue);

      expect(stub.Store.dispatch).toHaveBeenCalledWith(
        new FilterActions.ForceSelectFilterOption({
          filter: 'filter',
          filterOption: { name: filterValue, order: 1, selected: true }
        })
      );
    });

    it('should clear autocomplete input', () => {
      component.toggleFilter('test');

      expect(component.autoCompleteInput).toBe('');
    });
  });

  describe('#getAutoCompleteResults', () => {
    it('should dispatch an action passed to the component', () => {
      component.filter = <any>{ action: 'ACTION' };
      component.getAutoCompleteResults('Ned');
      expect(stub.Store.dispatch).toHaveBeenCalledWith({
        type: 'ACTION',
        payload: 'Ned'
      });
    });
  });
});
