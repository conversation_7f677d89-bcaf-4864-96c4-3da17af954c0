import { Component, Output, EventEmitter, Input, OnChanges, ChangeDetectionStrategy, SimpleChanges } from '@angular/core';
import { Subject } from 'rxjs';
import { Store } from '@ngrx/store';
import { AppState } from '../../../../types/app-state';
import { Filter, FilterOption } from '../../../../store/models/filter.modal';
import * as FilterActions from '../../../../store/actions/filter/filter.actions';

@Component({
  selector: 'app-auto-complete-filter',
  templateUrl: './auto-complete-filter.component.html',
  styleUrls: ['./auto-complete-filter.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AutoCompleteFilterComponent implements OnChanges {
  @Input()
  public filter: Filter;
  @Input()
  public type: string;
  @Input()
  autoCompleteInput: string;
  @Output()
  autoCompleteInputChange = new EventEmitter();
  @Input()
  allowNotListedOptions = false;

  public selectedFilters: FilterOption[];
  public autoCompleteOpened = false;
  public keyUp$ = new Subject<KeyboardEvent>();
  public blur$ = new Subject<Event>();
  public focus$ = new Subject<Event>();
  public keyDown$ = new Subject<KeyboardEvent>();

  constructor(private store: Store<AppState>) {
    this.getAutoCompleteResults = this.getAutoCompleteResults.bind(this);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.filter && changes.filter.currentValue) {
      this.filter.filterOptions = changes.filter.currentValue.filterOptions.sort((a: any, b: any) => a.name - b.name);
      this.selectedFilters = this.filter.filterOptions.filter(f => f.selected);
    }
  }

  toggleOpen(): void {
    this.filter.open = !this.filter.open;
  }

  updateAutoCompleteOpened(visible: boolean): void {
    this.autoCompleteOpened = visible;
  }

  keyDown(event: KeyboardEvent): void {
    if (event.keyCode === 13 && this.allowNotListedOptions) {
      if (this.autoCompleteOpened === false) {
        event.preventDefault();
        this.store.dispatch(
          new FilterActions.ForceSelectFilterOption({
            filter: this.filter.name,
            filterOption: {
              name: event.target['value'],
              order: 1,
              selected: true
            }
          })
        );
        this.autoCompleteInput = '';
      }
    }
    this.keyDown$.next(event);
  }

  public toggleFilter(filterName: string): void {
    this.autoCompleteInput = '';
    const clickedFilterOption = this.filter.filterOptions.filter(fO => fO.selected && fO.name === filterName);
    let toggleFilterAction: FilterActions.ToggleFilterOption | FilterActions.ForceSelectFilterOption;

    if (clickedFilterOption.length > 0 || (clickedFilterOption.length <= 0 && !this.filter.allowNotListedOptions)) {
      toggleFilterAction = new FilterActions.ToggleFilterOption({
        filter: this.filter.name,
        filterOption: filterName
      });
    } else {
      toggleFilterAction = new FilterActions.ForceSelectFilterOption({
        filter: this.filter.name,
        filterOption: {
          name: filterName,
          order: 1,
          selected: true
        }
      });
    }

    this.store.dispatch(toggleFilterAction);
  }

  public getAutoCompleteResults(val: string): void {
    this.store.dispatch({ type: this.filter.action, payload: val });
  }
}
