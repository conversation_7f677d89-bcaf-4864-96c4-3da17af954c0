@import "components";

$headerHeight: 62px;

:host {
  .filters {
    background-color: $dpes-white;
    bottom: 0;
    box-shadow: 1px 0 5px 0 rgba(0, 0, 0, 0.75);
    height: 100%;
    left: 5%;
    overflow: scroll;
    -webkit-overflow-scrolling: touch;
    position: fixed;
    top: 0;
    transition: 0.6s left;
    width: 95%;
    z-index: 98;

    &.offscreen {
      left: 100%;
      @include media-breakpoint-up(md) {
        left: auto;
      }
    }

    @include media-breakpoint-up(md) {
      box-shadow: none;
      height: auto;
      left: auto;
      overflow: auto;
      position: inherit;
      top: auto;
    }
  }

  .header {
    background-color: white;
    border-bottom: 1px solid $border-color;
    bottom: 0;
    height: $headerHeight;
    left: 0;
    line-height: $headerHeight;
    margin: 0 10px;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 99;

    .total-results {
      display: inline-block;
      .number {
        font-weight: 700;
      }
    }

    .close {
      display: inline-block;
      .offscreen & {
        display: none;
      }
      a {
        color: transparent;
        cursor: pointer;
        display: block;
        font-size: 0;
        position: relative;
        width: 50px;
        &::before, &::after {
          border: 2px solid $border-color;
          content: "";
          height: 2px;
          position: absolute;
          right: 15px;
          top: 27px;
          width: 24px;
        }
        &::before {
          transform: rotate(-45deg);
        }
        &::after {
          transform: rotate(-135deg);
        }
      }
    }
    @include media-breakpoint-up(md) {
      display: none;
    }
  }

  .body {
    height: 100%;
    overflow: scroll;
    padding: $headerHeight 10px 20px 10px;

    @include media-breakpoint-up(md) {
      overflow: auto;
      padding: 0;
    }
  }

  @include media-breakpoint-up(md) {
    .mobile-delete-all {
      display: none;
    }
  }

  a {
    &.action {
      color: $generic-title-color;
      cursor: pointer;
      display: block;
      font-size: 16px;
      margin-bottom: 18px;
      padding-left: 30px;
      position: relative;

      &:hover {
        color: $generic-title-color;
        text-decoration: none;
      }

      @include media-breakpoint-down(sm) {
        display: none;
      }
    }

    &.search-tips {
      background: {
        image: url("#{$project-icons-folder}tip.svg");
        position: left center;
        repeat: no-repeat;
        size: contain;
      }
    }

    &.delete-all {
      &::before, &::after {
        background-color: $dpes-seafoam-blue;
        border: 1px solid $dpes-seafoam-blue;
        content: "";
        height: 1px;
        left: 0;
        position: absolute;
        top: 12px;
        width: 20px;
      }
      &::before {
        transform: rotate(-45deg);
      }
      &::after {
        transform: rotate(-135deg);
      }
    }
  }

  hr {
    border-color: $border-color;

    @include media-breakpoint-down(sm) {
      display: none;
    }
  }
}
