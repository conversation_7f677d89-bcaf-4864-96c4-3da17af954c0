import { Component, Input, OnChanges, ChangeDetectionStrategy, SimpleChanges } from '@angular/core';
import { Store } from '@ngrx/store';
import { AppState } from '../../../../types/app-state';
import { Filter, FilterOption } from '../../../../store/models/filter.modal';
import * as FilterActions from '../../../../store/actions/filter/filter.actions';

@Component({
  selector: 'app-dropdown-filter',
  templateUrl: './dropdown-filter.component.html',
  styleUrls: ['./dropdown-filter.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DropdownFilterComponent implements OnChanges {
  @Input()
  public filter: Filter;
  @Input()
  public type: string;
  public selectedValue: string;

  constructor(private store: Store<AppState>) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.filter && changes.filter.currentValue) {
      this.filter.filterOptions = changes.filter.currentValue.filterOptions.sort((a: FilterOption, b: FilterOption) => a.order - b.order);

      const selectedFilter = this.filter.filterOptions.filter(f => f.selected).pop() || <any>{};
      this.selectedValue = selectedFilter.name || this.filter.defaultSelectedValue;
    }
  }

  toggleOpenFilter(): void {
    this.filter.open = !this.filter.open;
  }

  selectFilter(filterOptionName: string): void {
    this.store.dispatch(new FilterActions.SelectFilterOption({ filter: this.filter.name, filterOption: filterOptionName }));
  }
}
