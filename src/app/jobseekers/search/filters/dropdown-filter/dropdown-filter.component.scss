@import "components";

:host {
  .filter-container {
    border-top: 1px solid $border-color;
  }
  .title {
    color: $generic-title-color;
    font-size: 17px;
    font-weight: 700;
    line-height: 3.18;
    position: relative;
    a {
      bottom: 0;
      color: transparent;
      cursor: pointer;
      font-size: 0;
      left: 0;
      position: absolute;
      right: 0;
      text-align: right;
      top: 0;
      &::before, &::after {
        background: $border-color;
        content: "";
        height: 2px;
        position: absolute;
        right: 3px;
        top: 27px;
        transition: transform 0.3s ease;
        width: 12px;
      }
      &::after {
        transform: rotate(90deg);
      }
    }
    &.title-open a {
      &::after {
        transform: rotate(0deg);
      }
    }
  }
  .options {
    max-height: 0;
    overflow: hidden;
    transition: 1s max-height;
    &.open {
      max-height: 100px;
    }
  }
}
