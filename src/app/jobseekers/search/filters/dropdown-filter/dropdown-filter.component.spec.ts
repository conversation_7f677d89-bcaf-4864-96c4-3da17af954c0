import { TestBed, ComponentFixture } from '@angular/core/testing';
import { DropdownFilterComponent } from './dropdown-filter.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { Store } from '@ngrx/store';
import { FilterTypes, FilterOption } from '../../../../store/models/filter.modal';
import * as FilterActions from '../../../../store/actions/filter/filter.actions';

describe('DropdownFilterComponent', () => {
  const stub: any = {};
  let componentFixture: ComponentFixture<DropdownFilterComponent>, component: DropdownFilterComponent;

  beforeEach(() => {
    stub.Store = jasmine.createSpyObj('Store', ['dispatch', 'select']);
    TestBed.configureTestingModule({
      declarations: [DropdownFilterComponent],
      providers: [{ provide: Store, useValue: stub.Store }],
      schemas: [NO_ERRORS_SCHEMA]
    });

    TestBed.compileComponents();
    componentFixture = TestBed.createComponent(DropdownFilterComponent);
    componentFixture.detectChanges();
    component = componentFixture.componentInstance;
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('#ngOnChanges', () => {
    it('should sort filterOptions when they are updated', () => {
      const filterOptions: FilterOption[] = [
        {
          name: 'filter with order 2',
          count: '1',
          order: 2,
          selected: false
        },
        {
          name: 'filter with order 1',
          count: '1',
          order: 1,
          selected: false
        }
      ];
      const changes = <any>{
        filter: { currentValue: { filterOptions: filterOptions } }
      };
      component.filter = {
        name: 'filter',
        title: 'filter',
        open: true,
        filterOptions: [],
        type: FilterTypes.CheckBox
      };

      component.ngOnChanges(changes);
      expect(component.filter.filterOptions[0].name).toBe('filter with order 1');
      expect(component.filter.filterOptions[1].name).toBe('filter with order 2');
    });

    it('should check if changes have filterOptions for safety', () => {
      const changes = {};
      expect(component.ngOnChanges.bind(null, changes)).not.toThrow();
    });
  });

  describe('#toggleOpenFilter', () => {
    it('should toggle filter open state', () => {
      component.filter = <any>{ open: true };
      component.toggleOpenFilter();
      expect(component.filter.open).toBe(false);
    });
  });

  describe('#selectFilter', () => {
    beforeEach(() => {
      component.filter = {
        name: 'filterName',
        title: 'filter',
        open: true,
        type: FilterTypes.Dropdown,
        filterOptions: [
          {
            name: 'filterOptionName',
            count: '0',
            selected: true,
            order: 1
          },
          {
            name: 'filterOptionName2',
            count: '0',
            selected: true,
            order: 1
          }
        ]
      };
    });

    it('should dispatch a SELECT_FILTER_OPTION event', () => {
      component.selectFilter('filterOptionName2');
      expect(stub.Store.dispatch).toHaveBeenCalledWith(
        new FilterActions.SelectFilterOption({ filter: 'filterName', filterOption: 'filterOptionName2' })
      );
    });
  });
});
