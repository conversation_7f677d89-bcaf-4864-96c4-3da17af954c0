<div class="filter-container">
    <div class="title" [ngClass]="{'title-open': filter?.open}">
        {{ filter?.title }}

        <a (click)="toggleOpenFilter()"
           [attr.data-gtm]="'filter-' + filter?.name.toLowerCase()"
           [attr.data-slug]="'filter-' + (filter?.open ? 'show': 'hide')">
            +
        </a>
    </div>
    <div class="options"
        [ngClass]="{'open': filter?.open}">
        <div class="form-select form-select-border">
            <select class="value"
                    [(ngModel)]="selectedValue"
                    (ngModelChange)="selectFilter($event)"
                    [attr.data-gtm]="'filter-' + filter?.name.toLowerCase()"
                    [attr.data-slug]="selectedValue">
                <option [value]="filterOption?.name"
                        *ngFor="let filterOption of filter?.filterOptions">
                    {{ filterOption?.title }} ({{ filterOption?.count }})
                </option>
            </select>
        </div>
    </div>
</div>
