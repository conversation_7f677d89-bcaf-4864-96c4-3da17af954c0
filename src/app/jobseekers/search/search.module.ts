import { NgModule } from '@angular/core';
import { SharedModule } from '../../shared/shared.module';
import { ErrorModule } from '../../error/error.module';
import { SearchComponent } from './search.component';
import { SearchResultComponent } from './search-result/search-result.component';
import { FiltersComponent } from './filters/filters.component';
import { FilterComponent } from './filters/filter/filter.component';
import { DropdownFilterComponent } from './filters/dropdown-filter/dropdown-filter.component';
import { ResultCountComponent } from './result-count/result-count.component';
import { SearchErrorComponent } from './search-error/search-error.component';
import { AutoCompleteFilterComponent } from './filters/auto-complete-filter/auto-complete-filter.component';
import { PaginationComponent, PageSizerComponent } from '../../shared/components';
import { TruncatePipe, ZeroPadPipe } from '../../shared/pipes';
import { SavedSearchesComponent } from '../saved-searches/saved-searches.component';
import { SavedFavoritesComponent } from '../saved-favorites/saved-favorites.component';

@NgModule({
  imports: [SharedModule, ErrorModule],
  declarations: [
    FiltersComponent,
    SearchResultComponent,
    ZeroPadPipe,
    FilterComponent,
    DropdownFilterComponent,
    AutoCompleteFilterComponent,
    ResultCountComponent,
    PaginationComponent,
    PageSizerComponent,
    SearchComponent,
    SearchErrorComponent,
    TruncatePipe,
    SavedFavoritesComponent,
    SavedSearchesComponent
  ],
  exports: []
})
export class SearchModule {}
