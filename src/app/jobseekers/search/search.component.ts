import { debounceTime, map } from 'rxjs/operators';
import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { Observable, ReplaySubject, combineLatest, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { JobSeeker } from '../../classes';
import { <PERSON>c<PERSON><PERSON> } from '../../classes';
import { ISearchError } from './search-error/search-error.component';
import { AppState } from '../../types/app-state';
import { Filter } from '../../store/models/filter.modal';
import { Search } from '../../store/models/search.model';
import { isSearchQueryPresent } from '../../store/reducers/search/search.config';
import * as SearchActions from '../../store/actions/search/search.actions';
import * as FilterActions from '../../store/actions/filter/filter.actions';
import * as ModalsActions from '../../store/actions/modals/modals.actions';
import { environment } from '../../../environments/environment';
import {
  UrlSearchService,
  FilterEffectsService,
  NumberFormatService,
  Pages,
  PaginationService,
  SearchService,
  JobSeekerService,
  ProfileNavigationService,
  WindowService,
  PlatformCoreService
} from '../../shared/services';

@Component({
  selector: 'app-search',
  templateUrl: './search.component.html',
  styleUrls: ['./search.component.scss']
})
export class SearchComponent implements OnInit, OnDestroy {
  public searchQuery: { [key: string]: string[] };
  public currentPage = 1;
  public pageSize = 15;
  public site = environment.site;
  public locationQuery = '';
  public jobSeekers: JobSeeker[] = [];
  public pages: Pages;
  public filters: Filter[] = [];
  public searchState: Search;
  public isSearching = false;
  public aggregations: any;
  public locationError: boolean;
  public queryError: boolean;
  public searchedWithLocationQuery: string;
  public searchedWithQuery: { [key: string]: string[] };
  public amountOfResultsObservable: Observable<string>;
  public amountOfResults$ = new ReplaySubject<number>(1);
  public filters$: Observable<Filter[]>;
  public search$: Observable<Search>;
  public saveSearchModal$: Observable<boolean>;
  public searchTipsModal$: Observable<boolean>;
  public recruiter: Recruiter;
  public error: ISearchError;
  public purchaseProfileCreditsUrl = environment.url.purchaseProfileCreditsUrl;
  public hasPlatformCoreAccount = false;

  private componentDestroyed: Subject<any> = new Subject();

  constructor(
    private searchService: SearchService,
    private jobSeekerService: JobSeekerService,
    private route: ActivatedRoute,
    private pagination: PaginationService,
    private urlSearch: UrlSearchService,
    private filterEffects: FilterEffectsService,
    private numberFormat: NumberFormatService,
    public store: Store<AppState>,
    private profileNavigation: ProfileNavigationService,
    private windowService: WindowService,
    private router: Router,
    private platformCoreService: PlatformCoreService
  ) {
    this.search = this.search.bind(this);
    this.searchSuccess = this.searchSuccess.bind(this);
    this.catchSearch = this.catchSearch.bind(this);
    this.filterSuccess = this.filterSuccess.bind(this);

    this.amountOfResultsObservable = this.amountOfResults$.pipe(map(this.numberFormat.formatDots));

    this.route.parent.data.pipe(takeUntil(this.componentDestroyed)).subscribe((data: { recruiter: Recruiter }) => {
      this.recruiter = data.recruiter;
    });
    platformCoreService.hasPlatformCoreAccount.pipe(takeUntil(this.componentDestroyed)).subscribe((hasPlatformCoreAccount: boolean) => {
      this.hasPlatformCoreAccount = hasPlatformCoreAccount;
    });
  }

  ngOnInit(): void {
    this.saveSearchModal$ = this.store.select(state => state.modals).pipe(map(modals => modals.displaySaveSearchModal));
    this.searchTipsModal$ = this.store.select(state => state.modals).pipe(map(modals => modals.displaySearchTipsModal));

    this.filters$ = this.store.select(s => s.filter.filters);
    this.filters$.pipe(takeUntil(this.componentDestroyed)).subscribe((filters: Filter[]) => {
      this.filters = filters;
    });

    this.search$ = this.store.select(s => s.search);
    this.search$.pipe(takeUntil(this.componentDestroyed)).subscribe(search => {
      this.searchState = search;
    });

    this.searchService.filters$.pipe(takeUntil(this.componentDestroyed)).subscribe(this.filterSuccess);
    this.searchService.search$.pipe(takeUntil(this.componentDestroyed)).subscribe(this.searchSuccess, this.catchSearch);

    combineLatest(this.filters$, this.search$)
      .pipe(
        debounceTime(10),
        takeUntil(this.componentDestroyed)
      )
      .subscribe(([filters, search]: [Filter[], Search]) => {
        this.currentPage = search.currentPage;
        this.pageSize = search.pageSize;
        this.search([filters, search]);
      });

    this.filterEffects
      .getChangedResultSetActions()
      .pipe(takeUntil(this.componentDestroyed))
      .subscribe(() => {
        this.filter(this.searchState.searchQuery, this.searchState.locationQuery, this.filters);
      });

    this.route.queryParams.pipe(takeUntil(this.componentDestroyed)).subscribe(query => {
      this.urlSearch.syncStateFromUrl(this.filters, query);
    });
  }

  ngOnDestroy(): void {
    this.componentDestroyed.next();
    this.componentDestroyed.unsubscribe();
  }

  search([filters, search]: [Filter[], Search]): void {
    this.error = undefined;
    this.locationError = false;
    this.queryError = false;
    this.isSearching = true;
    this.searchedWithLocationQuery = search.locationQuery;
    this.searchedWithQuery = search.searchQuery;

    this.searchService.updateSearch(search.searchQuery, search.locationQuery, search.pageSize, search.currentPage, search.sort, filters);
  }

  filter(query: { [key: string]: string[] }, locationQuery: string, filters: Filter[]): void {
    this.searchService.updateFilters(query, locationQuery, filters);
  }

  searchSuccess(data: any): void {
    if (data.result_count === 0) {
      this.error = { type: 'SEARCH_ERROR_NO_RESULTS' };
    }

    this.isSearching = false;
    this.locationError = data.location_not_found;
    this.queryError = data.invalid_query;
    this.jobSeekers = data.result.map(this.jobSeekerService.fromJson);
    this.amountOfResults$.next(data.result_count);
    this.pages = this.pagination.getPages(data.result_count, this.currentPage, this.pageSize);

    this.profileNavigation.setInitialResultSet(this.jobSeekers);
    this.profileNavigation.setAmountOfPages(this.pages.totalPages);
  }

  private catchSearch(error: { type: string; status: number }): void {
    this.isSearching = false;
    this.jobSeekers = [];

    if (error.type === 'SEARCH_ERROR_RESULT_WINDOW_TOO_LARGE') {
      this.error = error;
      return;
    }

    if (error.status >= 500 && error.status < 600) {
      this.router.navigate(['/error'], { queryParams: { type: 'internalServerError' }, skipLocationChange: true });
    }
  }

  filterSuccess(data: { aggregations: any }): void {
    this.store.dispatch(new FilterActions.AggregationsUpdated(data.aggregations));
  }

  changePage(pageNumber: number): void {
    this.windowService.scrollTo(0, 0);
    this.urlSearch.changePageNumber(pageNumber);
  }

  updatePageSize(pageSize: number): void {
    this.urlSearch.changePageSize(pageSize, this.pages.endIndex);
  }

  openFilter(): void {
    this.store.dispatch(new FilterActions.OpenFilter());
  }

  setSort(sort: string): void {
    this.store.dispatch(new SearchActions.ChangeSort(sort));
  }

  identifySearchResult(index: number, jobSeeker: JobSeeker): string {
    return jobSeeker && jobSeeker.id;
  }

  openSaveSearchModal(): void {
    this.store.dispatch(new ModalsActions.OpenSaveSearchModal());
  }

  isSearchQueryPresent(searchQuery: { [key: string]: string[] }): boolean {
    return isSearchQueryPresent(searchQuery);
  }

  openSearchTipsModal(): void {
    this.store.dispatch(new ModalsActions.OpenSearchTipsModal());
  }
}
