import { TestBed, ComponentFixture } from '@angular/core/testing';
import { ResultCountComponent } from './result-count.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('ResultCountComponent', () => {
  let componentFixture: ComponentFixture<ResultCountComponent>, component: ResultCountComponent;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ResultCountComponent],
      schemas: [NO_ERRORS_SCHEMA]
    });

    TestBed.compileComponents();
    componentFixture = TestBed.createComponent(ResultCountComponent);
    componentFixture.detectChanges();
    component = componentFixture.componentInstance;
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('#handleScrollEvent', () => {
    const event = {
      currentTarget: { pageYOffset: 0 },
      preventDefault: () => {}
    };
    it('should set shown to true if scroll position exceeds 100', () => {
      component.shown = false;
      event.currentTarget.pageYOffset = 101;
      component.handleScrollEvent(event);
      expect(component.shown).toBe(true);
    });

    it('should set shown to false if scroll position is less than 100', () => {
      component.shown = true;
      event.currentTarget.pageYOffset = 99;
      component.handleScrollEvent(event);
      expect(component.shown).toBe(false);
    });
  });
});
