@import "components";

.result-count {
  min-height: 35px;

  .sticky & {
    display: inline;
    .result-count-title {
      font-size: 18px;
    }
  }
  .result-count-title {
    font-size: 24px;

    &--bold {
      font-weight: 700;
    }

    &--tablet-up {
      display: none;
      @include media-breakpoint-up(md) {
        display: inline;
      }
    }
  }
}

.sticky {
  background: #fff;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2);
  display: none;
  left: 0;
  opacity: 0;
  padding: 10px 15px;
  position: fixed;
  text-align: center;
  top: 0;
  transition: opacity 0.5s;
  width: 100%;
  z-index: 1001;
  &.shown {
    display: inherit;
    opacity: 1;
  }
  @include media-breakpoint-up(md) {
    background: #f7f7f7;
    left: auto;
    margin-right: 10%;
    max-width: calc(61.158% - 4rem);
    right: auto;
    text-align: left;
  }
  @include media-breakpoint-up(lg) {
    max-width: calc(74.4106% - 5rem);
    width: 750px;
  }
}

.search-info-query {
  display: none;
  font-size: 18px;
  line-height: 28px;
  @include media-breakpoint-up(md) {
    display: inline;
  }
}
