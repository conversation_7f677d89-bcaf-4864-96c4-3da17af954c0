import { Component, Input, HostListener, ChangeDetectionStrategy } from '@angular/core';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-result-count',
  templateUrl: './result-count.component.html',
  styleUrls: ['./result-count.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ResultCountComponent {
  @Input()
  public sticky = false;
  @Input()
  public location: string;
  @Input()
  public searchedWithLocation: boolean;
  @Input()
  public amountOfResults: Observable<number>;
  public shown = false;

  constructor() {}

  @HostListener('window:scroll', ['$event'])
  handleScrollEvent(e: any) {
    e.preventDefault();
    this.shown = e.currentTarget.pageYOffset > 100;
  }
}
