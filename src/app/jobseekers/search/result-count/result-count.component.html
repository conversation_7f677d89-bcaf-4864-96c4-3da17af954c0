<div [ngClass]="{ 'sticky': sticky, 'shown': shown }">
    <div class="result-count">
        <span class="result-count-title" *ngIf="amountOfResults | async">
            <span class="result-count-title--bold">{{ amountOfResults | async }} kandidaten</span>
            <span class="result-count-title--tablet-up"> gevonden</span>
            <span *ngIf="searchedWithLocation && location !== ''"> die willen werken in {{ location }}</span>
        </span>
    </div>
</div>
