@import "components";

:host {
  min-height: 100%;

  .container {
    @include outer-container();
  }

  .overlay {
    background-color: #fff;
    height: 100%;
    left: 0;
    opacity: 0.7;
    position: absolute;
    top: 0;
    width: 100%;
  }

  .credits {
    background-color: $search-credits-background-color;
    color: $search-credits-text-color;
    font-size: $search-credit-font-size;
    padding: 0.5rem 1rem;

    a {
      display: block;
      font-weight: 700;
      text-decoration: underline;
    }
  }

  .search-results {
    margin-bottom: 30px;
    position: relative;
    @include media-breakpoint-up(md) {
      margin-bottom: 60px;
    }
  }

  .mobile-buttons {
    display: block;
    @include media-breakpoint-up(md) {
      display: none;
    }
  }

  .search-tips {
    font-size: 15px;
    line-height: 44px;

    @include media-breakpoint-up(md) {
      display: none;
    }
  }

  .page-size {
    float: right;
    .label {
      display: inline-block;
      font-size: 15px;
    }
    .form-select {
      display: inline-block;
      width: 78px;
    }
    &::after {
      clear: both;
    }
  }
  .page-size {
    display: none;
    @include media-breakpoint-up(md) {
      display: inline-block;
    }
  }

  .search-sort {
    margin-top: 20px;

    @include media-breakpoint-up(md) {
      display: inherit;
      font-size: 14px;
      margin-bottom: 15px;
      margin-top: 46px;
    }

    a {
      color: $dpes-seafoam-blue;
      cursor: pointer;
      text-decoration: none;
      &.active {
        background: transparent;
        color: $text-color;
        cursor: text;
        font-weight: 700;
        pointer-events: none;
      }
    }

    &::after {
      clear: both;
      content: "";
      display: table;
    }
    .left {
      display: inline-block;
    }
    .right {
      display: inline-block;
      float: right;
    }
    .page-info {
      display: none;
      line-height: 44px;
      @include media-breakpoint-up(md) {
        display: inline-block;
        font-size: 15px;
      }
    }
    .sort {
      display: inline-block;
      font-size: 15px;
      line-height: 44px;
      margin-right: 32px;
      @include media-breakpoint-down(sm) {
        display: block;
        margin-right: 10px;
        text-align: right;
      }
    }
  }

  .search-spinner {
    animation: spin 1s linear infinite;
    background: #6299c9;
    border-radius: 50%;
    clear: both;
    height: 64px;
    left: calc(50% - 32px);
    position: fixed;
    width: 64px;

    @include media-breakpoint-up(md) {
      left: calc(50% + 128px);
    }

    &::before, &::after {
      content: "";
      position: absolute;
    }
    &::before {
      background:
        linear-gradient(0deg, hsla(0, 0%, 100%, 1) 50%, hsla(0, 0%, 100%, 0.9) 100%) 0% 0%,
        linear-gradient(90deg, hsla(0, 0%, 100%, 0.9) 0%, hsla(0, 0%, 100%, 0.6) 100%) 100% 0%,
        linear-gradient(180deg, hsla(0, 0%, 100%, 0.6) 0%, hsla(0, 0%, 100%, 0.3) 100%) 100% 100%,
        linear-gradient(360deg, hsla(0, 0%, 100%, 0.3) 0%, hsla(0, 0%, 100%, 0) 100%) 0% 100%;
      background-repeat: no-repeat;
      background-size: 50% 50%;
      border-radius: 50%;
      bottom: -1px;
      left: -1px;
      right: -1px;
      top: -1px;
    }
    &::after {
      background: white;
      border-radius: 50%;
      bottom: 8px;
      left: 8px;
      right: 8px;
      top: 8px;
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .btn-save-search {
    margin: 20px 0;
    width: 100%;
    @include media-breakpoint-up(md) {
      float: right;
      margin-top: -38px;
      width: auto;
    }
    .sticky & {
      display: none;
    }
    .btn {
      padding-left: 30px;
      padding-right: 30px;
    }
  }
}
