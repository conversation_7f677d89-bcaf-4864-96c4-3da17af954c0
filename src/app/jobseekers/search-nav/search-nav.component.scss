@import "components";

:host {
  nav {
    background: #fff;
    border-bottom: 1px solid $dpes-seafoam-blue;
    margin: 0;
    max-width: 100%;
    padding: 0 10px;
    & > div {
      @include outer-container;
      a {
        background-color: transparent;
        color: #999;
        display: inline-block;
        font-size: 12px;
        font-weight: 600;
        padding: 12px 10px 13px;
        position: relative;
        text-decoration: none;
        text-transform: lowercase;
        transition: 0.2s color ease;
        @include media-breakpoint-up(md) {
          font-size: 17px;
          padding: 12px 20px 13px;
          &:first-child {
            margin-left: 25%;
          }
        }
        &::after {
          background-color: $generic-title-color;
          bottom: 0;
          content: "";
          height: 3px;
          left: 0;
          opacity: 0;
          position: absolute;
          right: 0;
          transition: 0.2s opacity ease;
          @include media-breakpoint-up(md) {
            left: 10px;
            right: 10px;
          }
        }
        &.active, &:hover, &:focus, &:active {
          background-color: transparent;
          color: $generic-highlight-color;
          &::after {
            opacity: 1;
          }
        }
      }
    }
  }
}
