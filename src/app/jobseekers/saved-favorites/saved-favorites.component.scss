@import "components";

.saved-candidates {
  margin-bottom: 30px;
  overflow-x: hidden;
}

.candidate {
  align-items: center;
  border-top: 1px solid #babebf;
  display: flex;
  padding: 15px 0;
  transition: margin 0.2s ease;
  &:last-child {
    border-bottom: 1px solid #babebf;
  }
  .candidate-image-container {
    flex: 0 0 auto;
    float: left;
    height: 50px;
    margin-right: 10px;
    width: 50px;
    @include media-breakpoint-up(lg) {
      height: 80px;
      margin-right: 20px;
      width: 80px;
    }
    .candidate-image {
      border-radius: 50%;
      height: auto;
      max-width: 100%;
      &.default-image {
        background-image: url("#{$project-icons-folder}profile-pic-placeholder.svg");
        background-size: 100%;
        height: 100%;
        width: 100%;
      }
    }
  }
  .candidate-info {
    color: $dpes-seafoam-blue;
    text-decoration: none;
    .candidate-name, .candidate-function-title {
      font-size: 20px;
      font-weight: 400;
      margin: 0;
    }
  }
  .icon-delete {
    background: transparent url("#{$project-icons-folder}delete.svg") no-repeat scroll center center;
  }
  .saved-candidate-delete {
    cursor: pointer;
    display: block;
    height: 50px;
    margin-left: auto;
    width: 70px;
  }
  .saved-candidates-item-delete {
    align-items: stretch;
    align-self: stretch;
    display: flex;
    transition: width 0.2s ease;
    visibility: hidden;
    width: 0;
    .item-delete-cancel, .item-delete-delete {
      align-items: center;
      cursor: pointer;
      display: flex;
      font-size: 14px;
      font-weight: 600;
      padding: 0 10px;
    }
    .item-delete-cancel {
      color: $dpes-seafoam-blue;
    }
    .item-delete-delete {
      background: #e00;
      color: #fff;
      .icon-delete {
        display: none;
      }
    }
  }
  &.delete-open {
    margin-left: -110px;
    .candidate-image-container, .candidate-info {
      opacity: 0.5;
    }
    .candidate-info {
      color: gray;
      pointer-events: none;
    }
    .saved-candidate-delete {
      width: 0;
    }
    .saved-candidates-item-delete {
      visibility: visible;
      width: 180px;
    }
  }
}

.saved-favorites-spinner-block {
  margin-top: 60px;
  position: relative;
}

.overlay {
  background-color: #fff;
  height: 100%;
  left: 0;
  opacity: 0.7;
  position: absolute;
  top: 0;
  width: 100%;
}

.search-spinner {
  animation: spin 1s linear infinite;
  background: #6299c9;
  border-radius: 50%;
  clear: both;
  height: 64px;
  left: calc(50% - 32px);
  position: fixed;
  width: 64px;

  @include media-breakpoint-up(md) {
    left: 50%;
  }

  &::before, &::after {
    content: "";
    position: absolute;
  }
  &::before {
    background:
      linear-gradient(0deg, hsla(0, 0%, 100%, 1) 50%, hsla(0, 0%, 100%, 0.9) 100%) 0% 0%,
      linear-gradient(90deg, hsla(0, 0%, 100%, 0.9) 0%, hsla(0, 0%, 100%, 0.6) 100%) 100% 0%,
      linear-gradient(180deg, hsla(0, 0%, 100%, 0.6) 0%, hsla(0, 0%, 100%, 0.3) 100%) 100% 100%,
      linear-gradient(360deg, hsla(0, 0%, 100%, 0.3) 0%, hsla(0, 0%, 100%, 0) 100%) 0% 100%;
    background-repeat: no-repeat;
    background-size: 50% 50%;
    border-radius: 50%;
    bottom: -1px;
    left: -1px;
    right: -1px;
    top: -1px;
  }
  &::after {
    background: white;
    border-radius: 50%;
    bottom: 8px;
    left: 8px;
    right: 8px;
    top: 8px;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.load-more {
  display: block;
  margin: 20px auto;
}

.search-sort {
  margin-top: 20px;
  width: 100%;

  &::after {
    clear: both;
    content: "";
    display: table;
  }
  .left {
    display: inline-block;
  }
  .right {
    display: inline-block;
    float: right;
  }
  .page-info {
    display: none;
    line-height: 44px;
    @include media-breakpoint-up(md) {
      display: inline-block;
      font-size: 15px;
    }
  }
  .sort {
    display: none;
    font-size: 15px;
    line-height: 44px;
    margin-right: 32px;
    @include media-breakpoint-up(md) {
      display: inline-block;
    }
  }
  @include media-breakpoint-up(md) {
    display: inherit;
    font-size: 14px;
    margin-bottom: 15px;
    margin-top: 46px;
    a {
      color: $dpes-seafoam-blue;
      cursor: pointer;
      text-decoration: none;
      &.active {
        background: transparent;
        color: $text-color;
        cursor: text;
        font-weight: 700;
        pointer-events: none;
      }
    }
  }
}
