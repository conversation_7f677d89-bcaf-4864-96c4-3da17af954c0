<div class="container">
  <div class="row" *ngIf="favorites?.length">
      <div class="search-sort">
          <div class="col-6">
              <div class="page-info" data-gtm="page-info">
                  <b>{{ pages?.totalItems > 0 ? pages?.startIndex + 1 : 0 }}</b> - <b>{{ pages?.endIndex + 1 }}</b> van <b>{{amountOfResults}}</b>
              </div>
          </div>
          <div class="col-md-6 col-sm-12">
              <app-page-sizer (updateItemsPerPage)="updatePageSize($event)"
                              [pageSize]="pageSize"
                              [type]="'top'"></app-page-sizer>
          </div>
      </div>
  </div>

  <div class="row" *ngIf="isFetching; else listOfFavorites">
      <div class="saved-favorites-spinner-block">
          <div class="overlay">
              <div class="search-spinner"></div>
          </div>
      </div>
  </div>

  <ng-template #listOfFavorites>
      <div *ngIf="favorites?.length; else noFavoritesMessage">
           <div class="row">
              <div class="saved-candidates col-12" data-test="saved-candidate-overview">
                  <div class="candidate" *ngFor="let favorite of favorites; let idx = index" [class.delete-open]="openDelete[idx]" data-gtm="saved-candidate">
                      <div class="candidate-image-container">
                          <img *ngIf="favorite.photo" [src]="showPhoto(favorite.photo)" alt="profile-image" class="candidate-image"/>
                          <div *ngIf="!favorite.photo" class="candidate-image default-image"></div>
                      </div>
                      <a [routerLink]="['/kandidaat/', favorite.jobSeekerId]" class="candidate-info" data-gtm="jobseeker-viewed">
                          <h3 class="candidate-name">{{ favorite.firstName }} {{ favorite.lastName }}</h3>
                          <h4 class="candidate-function-title">{{ favorite.jobTitle }}</h4>
                      </a>
                      <i class="icon-delete saved-candidate-delete" (click)="openDelete[idx] = !openDelete[idx]" data-gtm="jobseeker-delete-icon"></i>

                      <div class="saved-candidates-item-delete">
                          <div class="item-delete-cancel" (click)="openDelete[idx] = !openDelete[idx]" data-gtm="jobseeker-delete-cancel">
                              Annuleren
                          </div>
                          <div class="item-delete-delete" (click)="remove(idx, favorite.jobSeekerId)" data-gtm="jobseeker-delete-permanent">
                              <i class="icon-delete"></i>
                              <span class="hide-mobile">Verwijderen</span>
                          </div>
                      </div>
                  </div>
              </div>
          </div>

          <div class="row">
              <div class="search-sort">
                  <div class="col-md-6 col-sm-12">
                      <app-pagination [currentPage]="currentPage"
                                      [resultCount]="amountOfResults"
                                      [pageSize]="pageSize"
                                      type="favorites"
                                      (pageChange)="changePage($event)"></app-pagination>
                  </div>
                  <div class="col-md-6 col-sm-12">
                      <app-page-sizer (updateItemsPerPage)="updatePageSize($event)"
                                      [pageSize]="pageSize"
                                      [type]="'bottom'"></app-page-sizer>
                  </div>
              </div>
          </div>
      </div>

      <ng-template #noFavoritesMessage>
          <div class="row page">
              <p>U hebt nog geen opgeslagen kandidaten.</p>
              <p>Klik op "Sla op als favoriet" (hartje) in het zoekscherm om uw eerste kandidaat op te slaan.</p>
          </div>
      </ng-template>
  </ng-template>
</div>
