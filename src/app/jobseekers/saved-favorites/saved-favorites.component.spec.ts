import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SavedFavoritesComponent } from './saved-favorites.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { Observable, Subject, of } from 'rxjs';
import { AppState } from '../../types/app-state';
import { SavedFavorites } from '../../classes/saved-favorites';
import {
  FavoritesResponse,
  SavedFavoritesService,
  PaginationService,
  SessionProfileFavoriteTrackerService,
  SearchService,
  WindowService
} from '../../shared/services';
import { environment } from '../../../environments/environment';

const USER_OBJECT: SavedFavorites = new SavedFavorites('1', 'a', 'a');

export class SavedFavoritesMock {
  public get(): Observable<SavedFavorites> {
    return of(USER_OBJECT);
  }

  public del(): Observable<any> {
    return of(USER_OBJECT);
  }
}

describe('SavedFavoritesComponent', () => {
  const stub: any = {
    savedFavoriteService: jasmine.createSpyObj('SavedFavorites', ['post', 'fetch', 'del']),
    profileFavoriteTracker: jasmine.createSpyObj('SessionProfileFavoriteTrackerService', ['deleteProfileFavorite'])
  };

  stub.storeSubject = new Subject();
  stub.Store = stub.storeSubject.asObservable();
  stub.Store.select = stub.Store.map;
  stub.SearchService = jasmine.createSpyObj('SearchService', ['purgeCache']);
  stub.WindowService = jasmine.createSpyObj('WindowService', ['scrollTo']);
  stub.Router = jasmine.createSpyObj('Router', ['navigate']);

  let componentFixture: ComponentFixture<SavedFavoritesComponent>,
    component: SavedFavoritesComponent,
    favoritesService: SavedFavoritesService,
    profileFavoriteTracker: SessionProfileFavoriteTrackerService,
    paginationService: PaginationService,
    initialState: any;

  initialState = {
    filters: [
      {
        name: 'workLevels',
        title: 'Filter 1',
        frequency: 'Nooit'
      }
    ]
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [SavedFavoritesComponent],
      providers: [
        { provide: SavedFavoritesService, useClass: SavedFavoritesMock },
        { provide: SessionProfileFavoriteTrackerService, useValue: stub.profileFavoriteTracker },
        { provide: PaginationService },
        { provide: Store, useValue: stub.Store },
        { provide: SearchService, useValue: stub.SearchService },
        { provide: WindowService, useValue: stub.WindowService },
        { provide: Router, useValue: stub.Router }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    });
    TestBed.compileComponents();

    componentFixture = TestBed.createComponent(SavedFavoritesComponent);

    component = componentFixture.componentInstance;
    favoritesService = TestBed.get(SavedFavoritesService);
    profileFavoriteTracker = TestBed.get(SessionProfileFavoriteTrackerService);
    paginationService = TestBed.get(PaginationService);
    stub.service = new SavedFavoritesComponent(
      favoritesService,
      profileFavoriteTracker,
      stub.Store,
      paginationService,
      stub.SearchService,
      stub.WindowService,
      stub.Router
    );
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('#ngOnInit', () => {
    let state: any;

    beforeEach(() => {
      const search: any = {
        searchQuery: 'searchQuery',
        locationQuery: 'locationQuery',
        sort: 'default',
        pageSize: 15,
        chosenSorting: 'ascend',
        currentPage: 1
      };
      state = of(search);

      const store = jasmine.createSpyObj('store', ['select']);
      store.select.and.returnValue(state);
      component.store = store;
    });

    it('should fetch favorites with pagination', () => {
      component['fetch'] = jasmine.createSpy('fetch');

      component.ngOnInit();

      expect(component['fetch']).toHaveBeenCalledTimes(1);
      expect(component['fetch']).toHaveBeenCalledWith();
    });

    it('should start observing the search store', () => {
      component['fetch'] = jasmine.createSpy('fetch');

      component.ngOnInit();

      expect(component.search$).toBe(state);
    });
  });

  describe('#getStoreSearchData', () => {
    it('should return the search store', () => {
      const search: any = {
        searchQuery: 'searchQuery',
        locationQuery: 'locationQuery',
        sort: 'default',
        pageSize: 15,
        chosenSorting: 'ascend',
        currentPage: 1
      };

      const store = <AppState>{ search: search };

      const storeSearch = component['getStoreSearchData'](store);

      expect(storeSearch).toEqual(search);
    });
  });

  describe('#remove', () => {
    it('should remove a saved favorite', () => {
      component.favorites = initialState.filters;
      spyOn(component.favorites, 'splice');
      spyOn(component.favoritesService, 'del').and.returnValue(of(USER_OBJECT));
      component.remove(1, '1234');
      expect(component.favoritesService.del).toHaveBeenCalledWith('1234');

      expect(stub.SearchService.purgeCache).toHaveBeenCalledTimes(1);
      expect(stub.SearchService.purgeCache).toHaveBeenCalledWith();
    });
  });

  describe('#showPhoto', () => {
    it('should show photo when is available', () => {
      component.favorites = initialState.filters;
      const expectedPhotoUrl = `${environment.photoPath}1234`;

      expect(component.showPhoto('1234')).toEqual(expectedPhotoUrl);
    });

    it('should return null when photo is unavailable', () => {
      component.favorites = initialState.filters;

      expect(component.showPhoto(null)).toEqual('');
      expect(component.showPhoto('')).toEqual('');
      expect(component.showPhoto(undefined)).toEqual('');
    });
  });

  describe('#fetch', () => {
    let subject: Subject<object[]>, favoriteService: any;

    const response: FavoritesResponse = {
      items: [
        { jobSeekerId: '3', firstName: 'Foo3', lastName: 'Bar3', jobTitle: 'Scrum Master' },
        { jobSeekerId: '4', firstName: 'Foo4', lastName: 'Bar4', jobTitle: 'Designer' }
      ],
      total: 2
    };

    beforeEach(() => {
      subject = new Subject();

      favoriteService = jasmine.createSpyObj('favoriteService', ['fetch']);
      favoriteService.fetch.and.returnValue(subject);

      component['favoritesService'] = favoriteService;
    });

    it('should isFetching be flagged as true', () => {
      component.isFetching = false;

      component['fetch']();

      expect(component.isFetching).toBeTruthy();
    });

    it('should fetch favorites with pagination', () => {
      component.currentPage = 1;
      component.pageSize = 25;
      component['onFetchFavoriteSuccess'] = jasmine.createSpy('onFetchFavoriteSuccess');

      component['fetch']();

      expect(component.favoritesService.fetch).toHaveBeenCalledTimes(1);
      expect(component.favoritesService.fetch).toHaveBeenCalledWith(component.currentPage, component.pageSize);

      subject.next(<any>response);

      expect(component['onFetchFavoriteSuccess']).toHaveBeenCalledTimes(1);
      expect(component['onFetchFavoriteSuccess']).toHaveBeenCalledWith(response);
    });

    it('should onFetchFavoriteError be called when error occurs', () => {
      component['onFetchFavoriteError'] = jasmine.createSpy('onFetchFavoriteError');

      component['fetch']();

      const error = new Error('Error');
      subject.error(error);

      expect(component['onFetchFavoriteError']).toHaveBeenCalledTimes(1);
      expect(component['onFetchFavoriteError']).toHaveBeenCalledWith(error);
    });

    it('should have isFetching flag to false when fetch is completed', () => {
      component['fetch']();

      subject.complete();

      expect(component.isFetching).toBeFalsy();
    });
  });

  describe('#onFetchFavoriteError', () => {
    it('should manager not internal server error', () => {
      stub.Router.navigate.calls.reset();
      component.favorites = [{ jobSeekerId: '3', firstName: 'Foo3', lastName: 'Bar3', jobTitle: 'Scrum Master' }];
      component.isFetching = true;

      component['onFetchFavoriteError']({ type: 'type', status: 400 });

      expect(component.favorites).toEqual([]);
      expect(component.isFetching).toBeFalsy();
      expect(stub.Router.navigate).not.toHaveBeenCalled();
    });

    it('should manager internal server error', () => {
      stub.Router.navigate.calls.reset();
      component.favorites = [{ jobSeekerId: '3', firstName: 'Foo3', lastName: 'Bar3', jobTitle: 'Scrum Master' }];
      component.isFetching = true;

      component['onFetchFavoriteError']({ type: 'type', status: 500 });

      expect(component.favorites).toEqual([]);
      expect(component.isFetching).toBeFalsy();
      expect(stub.Router.navigate).toHaveBeenCalledTimes(1);
      expect(stub.Router.navigate).toHaveBeenCalledWith(['/error'], {
        queryParams: { type: 'internalServerError' },
        skipLocationChange: true
      });
    });
  });

  describe('#onFetchFavoriteSuccess', () => {
    const response = {
        items: [
          { jobSeekerId: '3', firstName: 'Foo3', lastName: 'Bar3', jobTitle: 'Scrum Master' },
          { jobSeekerId: '4', firstName: 'Foo4', lastName: 'Bar4', jobTitle: 'Designer' }
        ],
        total: 2
      },
      pages = {
        totalItems: 105
      };

    it('should update SavedFavoritesComponent parameters and update pagination data for the pagination component', () => {
      component['pagination'] = jasmine.createSpyObj('paginationService', ['getPages']);
      component['pagination'].getPages = jasmine.createSpy('getPages').and.returnValue(pages);

      component['onFetchFavoriteSuccess'](response);

      expect(component.favorites).toEqual(response.items);
      expect(component.amountOfResults).toEqual(response.total);
      expect(component['pagination'].getPages).toHaveBeenCalledTimes(1);
      expect(component['pagination'].getPages).toHaveBeenCalledWith(response.total, component.currentPage, component.pageSize);
      expect(component.pages).toEqual(<any>pages);
    });
  });

  describe('#changePage', () => {
    const currentPage = 2,
      nextPage = 3;

    it('should change current page and call fetch to fetch new favorites', () => {
      component['fetch'] = jasmine.createSpy('fetch');
      component.currentPage = currentPage;

      component.changePage(nextPage);

      expect(stub.WindowService.scrollTo).toHaveBeenCalledTimes(1);
      expect(stub.WindowService.scrollTo).toHaveBeenCalledWith(0, 0);

      expect(component.currentPage).toEqual(nextPage);
      expect(component['fetch']).toHaveBeenCalledTimes(1);
      expect(component['fetch']).toHaveBeenCalledWith();
    });
  });

  describe('#updatePageSize', () => {
    const currentPageSize = 25,
      nextPageSize = 100,
      endIndex = 2;

    it('should change current page size and call fetch to fetch new favorites', () => {
      component['fetch'] = jasmine.createSpy('fetch');
      component.pageSize = currentPageSize;
      component.currentPage = endIndex;
      component.pages = <any>{ endIndex: endIndex };

      component.updatePageSize(nextPageSize);

      expect(component.pageSize).toEqual(nextPageSize);
      expect(component.currentPage).toEqual(Math.ceil((endIndex + 1) / nextPageSize));
      expect(component['fetch']).toHaveBeenCalledTimes(1);
      expect(component['fetch']).toHaveBeenCalledWith();
    });
  });
});
