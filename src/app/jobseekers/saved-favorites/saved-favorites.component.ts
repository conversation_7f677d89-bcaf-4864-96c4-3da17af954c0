import { Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { AppState } from '../../types/app-state';
import { Search } from '../../store/models/search.model';
import {
  Favorite,
  FavoritesResponse,
  SavedFavoritesService,
  Pages,
  PaginationService,
  SessionProfileFavoriteTrackerService,
  SearchService,
  WindowService
} from '../../shared/services';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-saved-favorites',
  templateUrl: './saved-favorites.component.html',
  styleUrls: ['./saved-favorites.component.scss']
})
export class SavedFavoritesComponent implements OnInit {
  public favorites: Favorite[] = [];
  public currentPage = 1;
  public pageSize = 15;
  public amountOfResults = 0;
  public isFetching = true;
  public openDelete: Array<boolean> = [];
  public search$: Observable<Search>;
  public pages: Pages;

  constructor(
    public favoritesService: SavedFavoritesService,
    public profileFavoriteTracker: SessionProfileFavoriteTrackerService,
    public store: Store<AppState>,
    private pagination: PaginationService,
    private searchService: SearchService,
    private windowService: WindowService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.fetch();
    this.search$ = this.store.select(this.getStoreSearchData);
  }

  private getStoreSearchData(store: AppState): Search {
    return store.search;
  }

  remove(idx: number, favoriteId: string): void {
    this.favoritesService.del(favoriteId).subscribe(response => {
      this.profileFavoriteTracker.deleteProfileFavorite(favoriteId);
      this.searchService.purgeCache();
      return response;
    });

    this.favorites.splice(idx, 1);
    this.openDelete = [false];
  }

  showPhoto(photo: string): string {
    if (photo && photo !== undefined && photo !== null) {
      return `${environment.photoPath}${photo}`;
    }
    return '';
  }

  private fetch(): void {
    this.isFetching = true;

    this.favoritesService.fetch(this.currentPage, this.pageSize).subscribe({
      next: (response: FavoritesResponse) => this.onFetchFavoriteSuccess(response),
      error: error => this.onFetchFavoriteError(error),
      complete: () => (this.isFetching = false)
    });
  }

  private onFetchFavoriteError(error: { type: string; status: number }): void {
    this.favorites = [];
    this.isFetching = false;

    if (error.status >= 500 && error.status < 600) {
      this.router.navigate(['/error'], { queryParams: { type: 'internalServerError' }, skipLocationChange: true });
    }
  }

  private onFetchFavoriteSuccess(data: FavoritesResponse): void {
    this.favorites = data.items;
    this.amountOfResults = data.total;

    this.pages = this.pagination.getPages(data.total, this.currentPage, this.pageSize);
  }

  changePage(pageNumber: number): void {
    this.windowService.scrollTo(0, 0);

    this.currentPage = pageNumber;
    this.fetch();
  }

  updatePageSize(pageSize: number): void {
    this.pageSize = pageSize;
    this.currentPage = Math.ceil((this.pages.endIndex + 1) / pageSize);
    this.fetch();
  }
}
