import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { JobSeekersComponent } from './jobseekers.component';
import { SearchComponent } from './search/search.component';
import { SavedFavoritesComponent } from './saved-favorites/saved-favorites.component';
import { SavedSearchesComponent } from './saved-searches/saved-searches.component';
import { environment } from '../../environments/environment';

const routes: Routes = [
  {
    path: '',
    component: JobSeekersComponent,
    children: [
      {
        path: 'zoeken',
        component: SearchComponent,
        data: {
          title: environment.titles.search,
          trackingEvent: 'recruiter-search',
          skipBaseUrlTrackingEvent: true
        }
      },
      {
        path: 'zoekopdrachten',
        component: SavedSearchesComponent,
        data: {
          title: environment.titles.savedSearch,
          trackingEvent: 'recruiter-saved-search'
        }
      },
      {
        path: 'favorieten',
        component: SavedFavoritesComponent,
        data: {
          title: environment.titles.favorites,
          trackingEvent: 'recruiter-favorites'
        }
      },
      {
        path: '',
        redirectTo: 'zoeken',
        pathMatch: 'full'
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class JobSeekersRoutingModule {}
