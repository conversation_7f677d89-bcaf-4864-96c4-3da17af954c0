import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { BehaviorSubject, Subject } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

import { JobSeekersComponent } from './jobseekers.component';
import {
  PlatformCoreService
} from '../shared/services';

describe('JobSeekersComponent', () => {
  let component: JobSeekersComponent;
  let fixture: ComponentFixture<JobSeekersComponent>;
  let stub: any;

  beforeEach(async(() => {
    stub = {
      ActivatedRoute: {
        data: new Subject()
      },
      recruiter: {
        emailAddress: '<EMAIL>',
        firstName: 'First',
        lastName: 'Last',
        hasSubscription: true,
        hasDeductibleSubscription: true,
        hasValidProduct: true,
        amountOfCredits: 10,
        companyVerified: true
      },
      PlatformCoreService: {
        hasPlatformCoreAccount: new BehaviorSubject(false)
      }
    };


    TestBed.configureTestingModule({
      declarations: [JobSeekersComponent],
      providers: [
        { provide: ActivatedRoute, useValue: stub.ActivatedRoute },
        { provide: PlatformCoreService, useValue: stub.PlatformCoreService }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(JobSeekersComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  describe('#constructor', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should set hasPlatformCoreAccount based on the observed value', () => {
      expect(component.hasPlatformCoreAccount).toBe(false);
    });
  });

  describe('#ngOnInit', () => {
    it('should resolve recruiter', () => {
      stub.ActivatedRoute.data.next({ recruiter: stub.recruiter });

      expect(component.recruiter).toEqual(stub.recruiter);
    });
  });

  describe('#ngOnDestroy', () => {
    it('should unsubscribe to the component promises', () => {
      component['componentDestroyed'] = jasmine.createSpyObj('componentDestroyed', ['next', 'unsubscribe']);

      component.ngOnDestroy();

      expect(component['componentDestroyed'].next).toHaveBeenCalledTimes(1);
      expect(component['componentDestroyed'].next).toHaveBeenCalledWith();
      expect(component['componentDestroyed'].unsubscribe).toHaveBeenCalledTimes(1);
      expect(component['componentDestroyed'].unsubscribe).toHaveBeenCalledWith();
    });
  });
});
