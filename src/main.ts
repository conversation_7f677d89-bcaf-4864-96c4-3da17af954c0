import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AppModule } from './app/app.module';
import { environment } from './environments/environment';

/**
 * Function to load development scripts
 * @param url
 */
function loadDevelopmentScript(url: string) {
  if (!environment.production) {
    const script = document.createElement('script');
    script.src = url;
    document.body.appendChild(script);
  }
}

switch (environment.site) {
  case 'b2b':
    loadDevelopmentScript('https://cdn.persgroepemploymentsolutions.nl/web-components/header-and-footer-b2b.js');
    break;
  case 'nvb':
    loadDevelopmentScript('https://cdn.nationalevacaturebank.nl/web-components/header-and-footer-b2b.js');
    break;
  case 'iol':
    loadDevelopmentScript('https://cdn.intermediair.nl/web-components/header-and-footer-b2b.js');
    break;
}

if (environment.production) {
  enableProdMode();
}

platformBrowserDynamic().bootstrapModule(AppModule);
