// The file contents for the current environment will overwrite these during build.
// The build system defaults to the dev environment which uses `environment.ts`, but if you do
// `ng build --env=prod` then `environment.prod.ts` will be used instead.
// The list of which env maps to which file can be found in `.angular-cli.json`.

export const environment = {
  filters: {
    workLevels: {
      filterOptions: [
        { name: '<MBO', order: 1, count: '0', selected: false },
        { name: 'Lagere school', order: 2, count: '0', selected: false },
        { name: 'VMBO/Mavo', order: 3, count: '0', selected: false },
        { name: 'HAVO', order: 4, count: '0', selected: false },
        { name: 'VWO', order: 5, count: '0', selected: false },
        { name: 'L<PERSON>', order: 6, count: '0', selected: false },
        { name: 'MBO', order: 7, count: '0', selected: false },
        { name: 'HBO', order: 8, count: '0', selected: false },
        { name: 'W<PERSON>', order: 9, count: '0', selected: false },
        { name: 'Postdoctor<PERSON>', order: 10, count: '0', selected: false }
      ],
      name: 'workLevels',
      title: 'Opleidingsniveau',
      open: true,
      type: 0
    }
  },
  photoPath: 'https://localhost/',
  production: false,
  profileApi: {
    mainUrl: 'http://profile-api:10208'
  },
  site: 'nvb',
  siteHeader: 'nationalevacaturebank.nl',
  titles: {
    default: 'Online recruitment',
    search: 'Online recruitment',
    savedSearch: 'Online recruitment',
    favorites: 'Online recruitment',
    jobseeker: 'Online recruitment'
  },
  url: {
    contact: '/',
    legacyPlatform: '/',
    purchaseProfileCreditsUrl: '/'
  },
  email: {
    helpdesk: '<EMAIL>'
  },
  version: '0'
};

export function getLoginUrl (str: string = ''): string {
  return 'http://login-url.here';
}
