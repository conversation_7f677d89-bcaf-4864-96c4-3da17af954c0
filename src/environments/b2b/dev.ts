export const environment = {
  filters: {
    workLevels: {
      filterOptions: [
        { name: '<PERSON>gere school', order: 1, count: '0', selected: false },
        { name: 'VMBO/Mavo', order: 2, count: '0', selected: false },
        { name: '<PERSON><PERSON><PERSON>', order: 3, count: '0', selected: false },
        { name: 'VWO', order: 4, count: '0', selected: false },
        { name: 'L<PERSON>', order: 5, count: '0', selected: false },
        { name: 'MB<PERSON>', order: 6, count: '0', selected: false },
        { name: 'HBO', order: 7, count: '0', selected: false },
        { name: 'WO', order: 8, count: '0', selected: false },
        { name: 'Postdoctoraal', order: 9, count: '0', selected: false }
      ],
      name: 'workLevels',
      title: 'Opleidingsniveau',
      open: true,
      type: 0
    }
  },
  photoPath: 'https://cdn.persgroepemploymentsolutions.nl/',
  production: false,
  profileApi: {
    mainUrl: ''
  },
  site: 'b2b',
  siteHeader: 'nationalevacaturebank.nl',
  titles: {
    default: 'PersgroepEmploymentSolutions.nl | Personeel werven  / werknemers zoeken - online recruitment',
    search: 'Kandidaten zoeken | PersgroepEmploymentSolutions.nl',
    savedSearch: 'Opgeslagen zoekopdrachten | PersgroepEmploymentSolutions.nl',
    favorites: 'Favoriete kandidaten | PersgroepEmploymentSolutions.nl',
    jobseeker: 'Kandidaatprofiel | PersgroepEmploymentSolutions.nl'
  },
  url: {
    legacyPlatform: 'https://www.nationalevacaturebank.nl/werkgever/',
    purchaseProfileCreditsUrl: 'https://www.persgroepemploymentsolutions.nl/dashboard/webshop'
  },
  email: {
    helpdesk: '<EMAIL>'
  },
  version: 'dev'
};

export function getLoginUrl (url: string = ''): string {
  const returnUrl = url === '' ? 'https://www.persgroepemploymentsolutions.nl/old-recruiter' : url;

  return `https://portal.persgroepemploymentsolutions.nl/s/login/?state=${encodeURIComponent(btoa(returnUrl))}`;
}
