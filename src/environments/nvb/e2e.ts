export const environment = {
  filters: {
    workLevels: {
      filterOptions: [
        { name: 'Lagere school', order: 1, count: '0', selected: false },
        { name: 'VMBO/Mavo', order: 2, count: '0', selected: false },
        { name: '<PERSON><PERSON><PERSON>', order: 3, count: '0', selected: false },
        { name: 'VWO', order: 4, count: '0', selected: false },
        { name: 'L<PERSON>', order: 5, count: '0', selected: false },
        { name: '<PERSON><PERSON>', order: 6, count: '0', selected: false },
        { name: 'HBO', order: 7, count: '0', selected: false },
        { name: 'WO', order: 8, count: '0', selected: false },
        { name: 'Postdoctoraal', order: 9, count: '0', selected: false }
      ],
      name: 'workLevels',
      title: 'Opleidingsniveau',
      open: true,
      type: 0
    }
  },
  production: true,
  site: 'nvb',
  siteHeader: 'nationalevacaturebank.nl',
  titles: {
    default: 'NationaleVacaturebank.nl | Personeel werven  / werknemers zoeken - online recruitment',
    search: 'Kandidaten zoeken | NationaleVacaturebank.nl',
    savedSearch: 'Opgeslagen zoekopdrachten | NationaleVacaturebank.nl',
    favorites: 'Favoriete kandidaten | NationaleVacaturebank.nl',
    jobseeker: 'Kandidaatprofiel | NationaleVacaturebank.nl'
  },
  photoPath: 'https://cdn.nationalevacaturebank.nl/',
  profileApi: {
    mainUrl: ''
  },
  url: {
    legacyPlatform: 'https://www.nationalevacaturebank.nl/werkgever/',
    purchaseProfileCreditsUrl: 'https://www.nationalevacaturebank.nl/werkgever/producten-tarieven/cv-database'
  },
  email: {
    helpdesk: '<EMAIL>'
  },
  version: 'e2e'
};

export function getLoginUrl (str: string = ''): string {
  return 'https://www.nationalevacaturebank.nl/werkgever/inloggen' +
      '?redirectUrl=https://www.nationalevacaturebank.nl/api/recruiter/callback';
}
