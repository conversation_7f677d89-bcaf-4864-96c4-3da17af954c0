export const environment = {
  filters: {
    workLevels: {
      filterOptions: [
        { name: '<PERSON>gere school', order: 1, count: '0', selected: false },
        { name: 'VMBO/Mavo', order: 2, count: '0', selected: false },
        { name: '<PERSON><PERSON><PERSON>', order: 3, count: '0', selected: false },
        { name: 'VWO', order: 4, count: '0', selected: false },
        { name: 'L<PERSON>', order: 5, count: '0', selected: false },
        { name: 'MB<PERSON>', order: 6, count: '0', selected: false },
        { name: 'HBO', order: 7, count: '0', selected: false },
        { name: 'WO', order: 8, count: '0', selected: false },
        { name: 'Postdoctoraal', order: 9, count: '0', selected: false }
      ],
      name: 'workLevels',
      title: 'Opleidingsniveau',
      open: true,
      type: 0
    }
  },
  photoPath: 'https://cdn.nationalevacaturebank.nl/',
  production: true,
  profileApi: {
    mainUrl: 'https://www.nationalevacaturebank.nl'
  },
  site: 'nvb',
  siteHeader: 'nationalevacaturebank.nl',
  titles: {
    default: 'NationaleVacaturebank.nl | Personeel werven  / werknemers zoeken - online recruitment',
    search: 'Kandidaten zoeken | NationaleVacaturebank.nl',
    savedSearch: 'Opgeslagen zoekopdrachten | NationaleVacaturebank.nl',
    favorites: 'Favoriete kandidaten | NationaleVacaturebank.nl',
    jobseeker: 'Kandidaatprofiel | NationaleVacaturebank.nl'
  },
  url: {
    legacyPlatform: 'https://www.nationalevacaturebank.nl/werkgever/',
    purchaseProfileCreditsUrl: 'https://www.persgroepemploymentsolutions.nl/dashboard/webshop'
  },
  email: {
    helpdesk: '<EMAIL>'
  },
  version: 'pro-%%BUILD_VERSION%%'
};

function getRedirectUrl(url: string = 'https://www.persgroepemploymentsolutions.nl/old-recruiter') {
  if (url.indexOf('kandidaat/') > 0) {
    return 'https://www.persgroepemploymentsolutions.nl/old-recruiter/kandidaat/' + url.substring(url.indexOf('kandidaat/') + 10);
  }
  return url;
}

export function getLoginUrl (url: string = ''): string {
  const redirectUrl = getRedirectUrl(url);
  return `https://portal.persgroepemploymentsolutions.nl/s/login/?state=${encodeURIComponent(btoa(redirectUrl))}`;
}

