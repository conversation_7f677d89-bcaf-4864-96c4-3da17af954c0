export const environment = {
  filters: {
    workLevels: {
      filterOptions: [
        { name: '<PERSON>gere school', order: 1, count: '0', selected: false },
        { name: 'VMBO/Mavo', order: 2, count: '0', selected: false },
        { name: '<PERSON><PERSON><PERSON>', order: 3, count: '0', selected: false },
        { name: 'VWO', order: 4, count: '0', selected: false },
        { name: 'L<PERSON>', order: 5, count: '0', selected: false },
        { name: 'MB<PERSON>', order: 6, count: '0', selected: false },
        { name: 'HBO', order: 7, count: '0', selected: true },
        { name: 'WO', order: 8, count: '0', selected: true },
        { name: 'Postdoctoraal', order: 9, count: '0', selected: true }
      ],
      name: 'workLevels',
      title: 'Opleidingsniveau',
      open: true,
      type: 0
    }
  },
  photoPath: 'http://localhost/',
  production: true,
  profileApi: {
    mainUrl: ''
  },
  site: 'iol',
  siteHeader: 'intermediair.nl',
  titles: {
    default: 'Bereik de meeste hoogopgeleide kandidaten | Plaats vacatures | Zoek kandidaten in onze CV-Database | Intermediair.nl',
    search: 'Kandidaten zoeken | Intermediair.nl',
    savedSearch: 'Opgeslagen zoekopdrachten | Intermediair.nl',
    favorites: 'Favoriete kandidaten | Intermediair.nl',
    jobseeker: 'Kandidaatprofiel | Intermediair.nl'
  },
  url: {
    legacyPlatform: 'https://www.intermediair.nl/werkgever/',
    purchaseProfileCreditsUrl: 'https://www.intermediair.nl/werkgever/producten-tarieven/cv-database'
  },
  email: {
    helpdesk: '<EMAIL>'
  },
  version: 'e2e'
};

export function getLoginUrl (str: string = ''): string {
  return 'https://www.intermediair.nl/werkgever/inloggen?callbackUrl=https://www.intermediair.nl/api/recruiter/callback';
}
