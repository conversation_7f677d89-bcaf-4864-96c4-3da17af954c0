export const environment = {
  filters: {
    workLevels: {
      filterOptions: [
        { name: '<PERSON>gere school', order: 1, count: '0', selected: false },
        { name: 'VMBO/Mavo', order: 2, count: '0', selected: false },
        { name: '<PERSON><PERSON><PERSON>', order: 3, count: '0', selected: false },
        { name: 'VWO', order: 4, count: '0', selected: false },
        { name: 'L<PERSON>', order: 5, count: '0', selected: false },
        { name: 'MB<PERSON>', order: 6, count: '0', selected: false },
        { name: 'HBO', order: 7, count: '0', selected: true },
        { name: 'WO', order: 8, count: '0', selected: true },
        { name: 'Postdoctoraal', order: 9, count: '0', selected: true }
      ],
      name: 'workLevels',
      title: 'Opleidingsniveau',
      open: true,
      type: 0
    }
  },
  photoPath: 'https://s3-eu-west-1.amazonaws.com/acc-profilers-media/photo/',
  production: true,
  profileApi: {
    mainUrl: 'https://intermediair-acc.persgroep.digital'
  },
  site: 'iol',
  siteHeader: 'intermediair.nl',
  titles: {
    default: 'Bereik de meeste hoogopgeleide kandidaten | Plaats vacatures | Zoek kandidaten in onze CV-Database | Intermediair.nl',
    search: 'Kandidaten zoeken | Intermediair.nl',
    savedSearch: 'Opgeslagen zoekopdrachten | Intermediair.nl',
    favorites: 'Favoriete kandidaten | Intermediair.nl',
    jobseeker: 'Kandidaatprofiel | Intermediair.nl'
  },
  url: {
    legacyPlatform: 'https://intermediair-acc.persgroep.digital/werkgever/',
    purchaseProfileCreditsUrl: 'https://b2b-acc.persgroep.digital/dashboard/webshop'
  },
  email: {
    helpdesk: '<EMAIL>'
  },
  version: 'acc-%%BUILD_VERSION%%'
};

export function getLoginUrl (url: string = ''): string {
  const redirectUrl = url || 'https://b2b-acc.persgroep.digital/old-recruiter/kandidaten';
  return `https://uat-servicecloudtrial-1542db3d5a7.cs87.force.com/dpes/s/login/?state=${encodeURIComponent(btoa(redirectUrl))}`;
}
