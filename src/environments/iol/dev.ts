export const environment = {
  filters: {
    workLevels: {
      filterOptions: [
        { name: '<PERSON>gere school', order: 1, count: '0', selected: false },
        { name: 'VMBO/Mavo', order: 2, count: '0', selected: false },
        { name: '<PERSON><PERSON><PERSON>', order: 3, count: '0', selected: false },
        { name: 'VWO', order: 4, count: '0', selected: false },
        { name: 'L<PERSON>', order: 5, count: '0', selected: false },
        { name: 'MB<PERSON>', order: 6, count: '0', selected: false },
        { name: 'HBO', order: 7, count: '0', selected: true },
        { name: 'WO', order: 8, count: '0', selected: true },
        { name: 'Postdoctoraal', order: 9, count: '0', selected: true }
      ],
      name: 'workLevels',
      title: 'Opleidingsniveau',
      open: true,
      type: 0
    }
  },
  photoPath: 'https://cdn.intermediair.nl/',
  production: false,
  profileApi: {
    mainUrl: ''
  },
  site: 'iol',
  siteHeader: 'intermediair.nl',
  titles: {
    default: 'Bereik de meeste hoogopgeleide kandidaten | Plaats vacatures | Zoek kandidaten in onze CV-Database | Intermediair.nl',
    search: 'Kandidaten zoeken | Intermediair.nl',
    savedSearch: 'Opgeslagen zoekopdrachten | Intermediair.nl',
    favorites: 'Favoriete kandidaten | Intermediair.nl',
    jobseeker: 'Kandidaatprofiel | Intermediair.nl'
  },
  url: {
    legacyPlatform: 'https://www.intermediair.nl/werkgever/',
    purchaseProfileCreditsUrl: 'https://www.persgroepemploymentsolutions.nl/dashboard/webshop'
  },
  email: {
    helpdesk: '<EMAIL>'
  },
  version: 'dev'
};

export function getLoginUrl (url: string = ''): string {
  const redirectUrl = url === '' ? 'https://www.persgroepemploymentsolutions.nl/old-recruiter' : url;
  return `https://portal.persgroepemploymentsolutions.nl/s/login/?state=${encodeURIComponent(btoa(redirectUrl))}`;
}
