{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "defaultProject": "nvb", "cli": {"packageManager": "yarn"}, "schematics": {"@schematics/angular:component": {"prefix": "app", "styleext": "scss"}, "@schematics/angular:directive": {"prefix": "app"}}, "projects": {"nvb": {"root": "", "sourceRoot": "src", "projectType": "application", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "tsConfig": "src/tsconfig.app.json", "polyfills": "src/polyfills.ts", "assets": ["src/assets/nvb", "src/manifest.json"], "styles": ["src/styles/styles.scss"], "stylePreprocessorOptions": {"includePaths": ["src/styles/nvb", "node_modules/styleguide/nvb"]}, "scripts": [], "serviceWorker": true}, "configurations": {"dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/nvb/dev.ts"}]}, "dev-pro": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/nvb/pro.ts"}]}, "e2e": {"optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/nvb/e2e.ts"}]}, "acc": {"optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/nvb/acc.ts"}, {"replace": "src/manifest.json", "with": "src/environments/nvb/manifest.json"}]}, "pro": {"optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/nvb/pro.ts"}, {"replace": "src/manifest.json", "with": "src/environments/nvb/manifest.json"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "nvb:build"}, "configurations": {"dev": {"browserTarget": "nvb:build:dev", "port": 10200, "host": "0.0.0.0", "proxyConfig": "proxy.conf.json"}, "dev-pro": {"browserTarget": "nvb:build:dev-pro", "port": 10200, "host": "0.0.0.0", "proxyConfig": "proxy-pro.conf.json"}, "e2e": {"browserTarget": "nvb:build:e2e", "port": 10209, "host": "0.0.0.0", "proxyConfig": "proxy.conf.json", "disableHostCheck": true}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "nvb:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "karmaConfig": "./karma.conf.js", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "scripts": [], "sourceMap": true, "assets": ["src/assets/nvb"], "styles": ["src/styles/styles.scss"], "stylePreprocessorOptions": {"includePaths": ["src/styles/nvb", "node_modules/styleguide/nvb"]}}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "exclude": []}}}}, "iol": {"root": "", "sourceRoot": "src", "projectType": "application", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "tsConfig": "src/tsconfig.app.json", "polyfills": "src/polyfills.ts", "assets": ["src/assets/iol", "src/manifest.json"], "styles": ["src/styles/styles.scss"], "stylePreprocessorOptions": {"includePaths": ["src/styles/iol", "node_modules/styleguide/int"]}, "scripts": [], "serviceWorker": true}, "configurations": {"dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/iol/dev.ts"}]}, "acc": {"optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/iol/acc.ts"}, {"replace": "src/manifest.json", "with": "src/environments/iol/manifest.json"}]}, "pro": {"optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/iol/pro.ts"}, {"replace": "src/manifest.json", "with": "src/environments/iol/manifest.json"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "iol:build"}, "configurations": {"dev": {"browserTarget": "iol:build:dev", "port": 10200, "host": "0.0.0.0", "proxyConfig": "proxy.conf.json"}}}}}, "b2b": {"root": "", "sourceRoot": "src", "projectType": "application", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "tsConfig": "src/tsconfig.app.json", "polyfills": "src/polyfills.ts", "assets": ["src/assets/b2b", "src/manifest.json"], "styles": ["src/styles/styles.scss"], "stylePreprocessorOptions": {"includePaths": ["src/styles/b2b"]}, "scripts": [], "serviceWorker": false}, "configurations": {"dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/b2b/dev.ts"}, {"replace": "src/app/shared/components/search-container/search-container.component.scss", "with": "src/styles/b2b/overrides/shared/search-container/search-container.component.scss"}, {"replace": "src/app/shared/components/search-container/search-bar/search-bar.component.scss", "with": "src/styles/b2b/overrides/shared/search-container/search-bar/search-bar.component.scss"}, {"replace": "src/app/jobseekers/search-nav/search-nav.component.scss", "with": "src/styles/b2b/overrides/jobseekers/search-nav/search-nav.component.scss"}]}, "acc": {"optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/b2b/acc.ts"}, {"replace": "src/manifest.json", "with": "src/environments/b2b/manifest.json"}, {"replace": "src/app/shared/components/search-container/search-container.component.scss", "with": "src/styles/b2b/overrides/shared/search-container/search-container.component.scss"}, {"replace": "src/app/shared/components/search-container/search-bar/search-bar.component.scss", "with": "src/styles/b2b/overrides/shared/search-container/search-bar/search-bar.component.scss"}, {"replace": "src/app/jobseekers/search-nav/search-nav.component.scss", "with": "src/styles/b2b/overrides/jobseekers/search-nav/search-nav.component.scss"}]}, "pro": {"optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/b2b/pro.ts"}, {"replace": "src/manifest.json", "with": "src/environments/b2b/manifest.json"}, {"replace": "src/app/shared/components/search-container/search-container.component.scss", "with": "src/styles/b2b/overrides/shared/search-container/search-container.component.scss"}, {"replace": "src/app/shared/components/search-container/search-bar/search-bar.component.scss", "with": "src/styles/b2b/overrides/shared/search-container/search-bar/search-bar.component.scss"}, {"replace": "src/app/jobseekers/search-nav/search-nav.component.scss", "with": "src/styles/b2b/overrides/jobseekers/search-nav/search-nav.component.scss"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "b2b:build"}, "configurations": {"dev": {"browserTarget": "b2b:build:dev", "liveReload": true, "poll": 5000, "port": 10200, "host": "0.0.0.0", "proxyConfig": "proxy.conf.json", "disableHostCheck": true}}}}}}}