version: '2'

services:
  mocks:
    build:
      context: docker/mocks
    image: recruiter_frontend_cli
    command: yarn mocks
    volumes_from:
      - data

  # command line container
  cli:
    build:
      context: docker/cli
    image: recruiter_frontend_cli
    ports:
      - 10201:9876
    volumes_from:
      - data

  # container for local development
  nvb:
    build:
      context: docker/cli
    image: recruiter_frontend_cli
    ports:
      - 10200:10200
    volumes_from:
      - data
    command: yarn start-nvb
    links:
      - mocks:profile-api

  b2b:
    build:
      context: docker/cli
    image: recruiter_frontend_cli
    ports:
      - 10200:10200
      - 49153:49153
    volumes_from:
      - data
    command: yarn start-b2b
    links:
      - mocks:profile-api

  iol:
    build:
      context: docker/cli
    image: recruiter_frontend_cli
    ports:
      - 10200:10200
    volumes_from:
      - data
    command: yarn start-iol
    links:
      - mocks:profile-api

  e2e:
    build:
      context: docker/cli
    image: recruiter_frontend_cli
    ports:
      - 10209:10209
    volumes_from:
      - data
    command: yarn start-e2e
    links:
      - mocks:profile-api

  cypress:
    build:
      context: docker/test
    image: recruiter_frontend_cypress
    depends_on:
      - e2e
    volumes:
      - .:/opt/webapp
    links:
      - e2e:app
    command: sh -c './docker/wait-for-it.sh e2e:10209 --timeout=120 --strict -- yarn cypress-run'

  web-localstack:
    build:
      context: docker/cli
    image: recruiter_frontend_cli
    ports:
      - 10200:10200
    volumes_from:
      - data
    command: yarn start-nvb-pro

  # data container
  data:
    image: busybox
    volumes:
      - ./:/opt/webapp:Z
