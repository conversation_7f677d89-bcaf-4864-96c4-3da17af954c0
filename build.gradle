plugins {
    id "nl.dpes.gradle_plugin" version "1.3.0"
    id "org.sonarqube" version "2.6.2"
}

def props = new Properties()
file('.env').withInputStream { props.load(it) }

ext.props = props

ext.PROJECT = props.getProperty('PROJECT')
ext.BASE_HREF = props.getProperty('BASE_HREF')
ext.DIR_DIST = props.getProperty('DIR_DIST')
ext.DIR_NGINX_HTML = props.getProperty('DIR_NGINX_HTML')
ext.DIR_SNIPPETS = props.getProperty('DIR_SNIPPETS')
ext.EC2_B2B_REPOSITORY = props.getProperty('EC2_B2B_REPOSITORY')
ext.EC2_NVB_REPOSITORY = props.getProperty('EC2_NVB_REPOSITORY')
ext.EC2_IOL_REPOSITORY = props.getProperty('EC2_IOL_REPOSITORY')
ext.TARGET = project.hasProperty('target') ? project.property('target') : 'undefined'
ext.BUCKET = project.hasProperty('bucket') ? project.property('bucket') : 'undefined'
ext.STATIC_HOST = project.hasProperty('staticHost') ? project.property('staticHost') : 'undefined'

persgroep {
    dockerNamespace = props.getProperty('DOCKER_REGISTRY')
    buildNumber = project.hasProperty('buildNumber') ? project.property('buildNumber') : '-1'
    targetEnvironment = project.hasProperty('targetEnvironment') ? project.property('targetEnvironment') : 'undefined'
}


sonarqube {
    properties {
        property "sonar.host.url", "https://sonar.persgroep.digital"
        property "sonar.projectKey", "recruiter-frontend"
        property "sonar.projectName", "Recruiter frontend"
        property "sonar.projectVersion", persgroep.buildNumber
        property "sonar.sources", "src/"
        property "sonar.tests", "src/"
        property "sonar.test.inclusions", "**/*.spec.ts"
        property "sonar.typescript.tsconfigPath", "tsconfig.json"
        property "sonar.typescript.lcov.reportPaths", "coverage/lcov.info"
        property "sonar.sourceEncoding", "UTF-8"
    }
}

task fixLCOVPaths(type: Exec) {
    executable "sh"
    args '-c', "sed -i -e 's/SF:\\/opt\\/webapp\\//SF:/g' '" + project.rootDir + "/coverage/lcov.info'"
}

/**
 * Setup tasks
 */
def getGroupId() {
    def groupId = new ByteArrayOutputStream()
    exec { executable 'id' args '-g' standardOutput groupId }
    return groupId.toString()
}

def getUserId() {
    def userId = new ByteArrayOutputStream()
    exec { executable 'id' args '-u' standardOutput userId }
    return userId.toString()
}

task buildImages(type: nl.dpes.base.tasks.docker_compose.BuildTask) {
    description = 'Build Docker containers'
    group = 'Setup'

    service 'cli', 'b2b', 'nvb', 'iol', 'mocks', 'cypress'
    buildArg 'GROUP_ID', getGroupId()
    buildArg 'USER_ID', getUserId()
}

task installYarnDependencies(type: nl.dpes.base.tasks.docker_compose.RunTask) {
    description = 'Install all project dependencies'
    group = 'Setup'

    service 'cli'
    entryPoint 'yarn'
    command 'install', '--registry=http://npm-registry.persgroep.digital'
}

task cleanGeneratedFolders() {
    description = 'Remove generated directories'
    group = 'Setup'

    doLast {
        delete DIR_DIST
        delete DIR_NGINX_HTML
    }
}

task setup(dependsOn: [clean, buildImages, install, EnsureDockerNetworkExists]) {
    description = 'Clean, build images, create docker network and install dependencies'
    group = 'Setup'

    doLast {
        println 'Setup completed'
    }
}

/**
 * Run tasks
 */
def stopService(name) {
    exec {
        executable 'docker-compose'
        args 'stop', name
    }
}

task runNvb(type: nl.dpes.base.tasks.docker_compose.UpTask) {
    description = 'Run Nvb development server'
    group = 'Run'

    stopService('iol')
    stopService('web-localstack')

    service 'nvb'
}

task runIol(type: nl.dpes.base.tasks.docker_compose.UpTask) {
    description = 'Run IOL development server'
    group = 'Run'

    stopService('nvb')
    stopService('web-localstack')

    service 'iol'
}

task runB2b(type: nl.dpes.base.tasks.docker_compose.UpTask) {
    description = 'Run B2b development server'
    group = 'Run'

    stopService('iol')
    stopService('nvb')
    stopService('web-localstack')

    service 'b2b'
}

task runLocalstack(type: nl.dpes.base.tasks.docker_compose.UpTask, dependsOn: [EnsureDockerNetworkExists]) {
    description = 'Run the project in localstack mode. Using your own profile-api project with tunnel to acc jobseekerservice'
    group = 'run'

    stopService('nvb')
    stopService('iol')
    stopService('mocks')

    service 'web-localstack'
}

task stop(type: nl.dpes.base.tasks.docker_compose.DownTask) {
    description = 'Stop the project'
    group = 'Run'
}

/**
 * Test tasks
 */
task lint(type: nl.dpes.base.tasks.docker_compose.RunTask) {
    description = 'Linting: checking style'
    group = 'Test'

    service 'cli'
    containerName "${PROJECT}_lint"
    entryPoint 'yarn'
    command 'lint'
}

task testUnit(type: nl.dpes.base.tasks.docker_compose.RunTask) {
    description = 'Unit tests'
    group = 'Test'

    service 'cypress'
    containerName "${PROJECT}_unit"
    entryPoint 'yarn'
    command 'test'
}

task testUnitWatch(type: nl.dpes.base.tasks.docker_compose.RunTask) {
    description = 'Unit tests with watch'
    group = 'Test'

    service 'cli'
    containerName "${PROJECT}_unit"
    entryPoint 'yarn'
    command 'test-watch'
}

task cypressE2e(type: nl.dpes.base.tasks.docker_compose.RunTask) {
    description = 'Run the Cypress tests'
    group = 'Test'

    service 'cypress'
}

task stopWebE2E() {
    description = 'Stop the project'
    group = 'Test'

    stopService('mocks')
    stopService('e2e')
}

task test(dependsOn: [lint, testUnit, cypressE2e]) {
    description = 'Run all tests (lint, unit, Cypress)'
    group = 'Test'

    doLast {
        println 'Tests completed'
    }
}

task formatCode(type: nl.dpes.base.tasks.docker_compose.RunTask) {
    description = "Format the code base"
    group = "Utility"

    service 'cli'
    containerName "${PROJECT}_utility"
    entryPoint 'yarn'
    command 'format'
}

/**
 * Build tasks
 */
def buildDistribution(dist, deployUrl) {
    exec {
        executable 'docker-compose'
        args\
            'run', \
            '--rm', \
            "--name=recruiterfrontend_cli_${TARGET}_${persgroep.targetEnvironment}", \
            'cli', \
            'yarn', \
            "build-${TARGET}-${persgroep.targetEnvironment}", \
            '--', \
            "--deploy-url=${deployUrl}", \
            "--base-href=${BASE_HREF}", \
            "--output-path=${dist}"
    }
}

def replaceSnippetsInsideFile(dist) {
    String fileContent = new File("${dist}/index.html").getText('UTF-8')
    fileContent = fileContent.replace('<!-- %%SNIPPETS%% -->', new File("${DIR_SNIPPETS}/${TARGET}/${persgroep.targetEnvironment}.html").getText('UTF-8'))
    new File("${dist}/index.html").write(fileContent, 'UTF-8')
}

def replaceBuildNumber(dist) {
    String mainFileName = new File("${dist}/").list().find{it=~/^main.*.js$/}
    String mainFileContent = new File("${dist}/${mainFileName}").getText('UTF-8')
    mainFileContent = mainFileContent.replace('%%BUILD_VERSION%%', persgroep.buildNumber)
    new File("${dist}/${mainFileName}").write(mainFileContent, 'UTF-8')
}

def replaceStaticHostInsideManifest(dist, deployUrl) {
    String fileContent = new File("${dist}/manifest.json").getText('UTF-8')
    fileContent = fileContent.replace('%%STATIC_HOST%%', deployUrl)
    new File("${dist}/manifest.json").write(fileContent, 'UTF-8')
}

// https://github.com/angular/angular-cli/issues/9753
def regenerateNgsw(dist, deployUrl) {
  exec {
      executable 'docker-compose'
      args\
          'run', \
          '--rm', \
          'cli', \
          './node_modules/.bin/ngsw-config', \
          dist, \
          './ngsw-config.json',
          deployUrl
  }

  String fileContent = new File("${dist}/ngsw.json").getText('UTF-8')
  fileContent = fileContent.replace("${deployUrl}index.html", "${BASE_HREF}index.html")
  fileContent = fileContent.replace("${deployUrl}ngsw-worker.js", "${BASE_HREF}ngsw-worker.js")
  new File("${dist}/ngsw.json").write(fileContent, 'UTF-8')
}

task createDistributionBundle {
    description = 'Create bundle'
    group = 'build'

    def dist = "${DIR_DIST}/${TARGET}/${persgroep.targetEnvironment}"
    def deployUrl = "${STATIC_HOST}/${persgroep.buildNumber}/"

    doFirst {
        buildDistribution(dist, deployUrl)
        replaceSnippetsInsideFile(dist)
        replaceBuildNumber(dist)
        replaceStaticHostInsideManifest(dist, deployUrl)
        regenerateNgsw(dist, deployUrl)
    }

    doLast {
        fileHandler(dist)
    }
}

def fileHandler(dist) {
    def distNginx = "${DIR_NGINX_HTML}/${TARGET}/${persgroep.targetEnvironment}"

    copy {
        from "${dist}/index.html", "${dist}/ngsw-worker.js", "${dist}/ngsw.json", "${dist}/manifest.json"
        into distNginx
    }

    delete "${dist}/index.html", "${dist}/ngsw-worker.js", "${dist}/ngsw.json", "${dist}/manifest.json"
}



task buildNginxImage(type: nl.dpes.base.tasks.docker.BuildImageTask) {
    description 'Create node image'
    group = 'Build'

    def repo = EC2_NVB_REPOSITORY

    if (TARGET == 'b2b') {
        repo = EC2_B2B_REPOSITORY
    }

    if (TARGET == 'iol') {
        repo = EC2_IOL_REPOSITORY
    }

    workDir './docker/nginx'
    useCache false
    repository "${persgroep.dockerNamespace}/${repo}"
}

task pushNginxImageToEc2ContainerRegistry(type: nl.dpes.base.tasks.docker.PushImagesTask, dependsOn: [buildNginxImage]) {
    description 'Push Docker images to ECR'
    group = 'Deployment'

    def repo = EC2_NVB_REPOSITORY

    if (TARGET == 'b2b') {
        repo = EC2_B2B_REPOSITORY
    }

    if (TARGET == 'iol') {
        repo = EC2_IOL_REPOSITORY
    }

    images "${persgroep.dockerNamespace}/${repo}"
}

/**
 * Deployment tasks
 */
task deploy(type: nl.dpes.base.tasks.kubernetes.DeployTask) {
    description 'Deploy to Kubernetes'
    group = 'Deployment'

    followDeployment "${TARGET}-recruiters"

    include "config-maps.yml"
    include "deployment.yml"
    include "service.yml"
    include "hpa.yml"
    properties "properties/${TARGET}/${persgroep.targetEnvironment}.properties"
}

task rollbackDeployment(type: nl.dpes.base.tasks.kubernetes.RollbackDeploymentTask) {
    description = 'Rollback the last deployment'
    group = 'Deployment'

    deploymentName "${TARGET}-recruiters"
}


task copyBundleIntoS3Bucket() {
    description 'Deploy distribution to S3'
    group = 'test'
    doLast{
        exec {
            executable 'aws'
            args 's3', 'sync', "${DIR_DIST}/${TARGET}/${persgroep.targetEnvironment}", "s3://${BUCKET}/recruiter/${persgroep.buildNumber}"
            args "--region=eu-west-1"
        }
    }
}

task newLoginToECR {
    description = 'Login to AWS ECR'
    group = 'Deployment'
    doLast {
        exec {
            executable 'sh'
            args '-c', "aws ecr get-login-password --region=eu-west-1 | docker login -u AWS --password-stdin https://674201978047.dkr.ecr.eu-west-1.amazonaws.com"
        }
    }
}
