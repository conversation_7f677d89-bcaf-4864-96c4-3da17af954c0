given('I am on the favorites page for recruiter', () => {

  cy.visit('/favorites');

});

when('I delete a candidate from the favorites tab', () => {

  cy.get('.candidate-name').first().should('contain', '<PERSON>');

  cy.get('[data-gtm="jobseeker-delete-icon"]').first().click();
  cy.get('div.item-delete-delete').first().click();

});

then('the candidate should not be visible anymore', () => {

  cy.get('.candidate-name').first().should('not.contain', '<PERSON>');

});

when('I use the sorting function for favorites', () => {

  cy.get('.page-size select').first().select('25');

});

then('the changes should apply to the page', () => {

  cy.get('[data-gtm="page-info"]').first().should('contain', '25');

});
