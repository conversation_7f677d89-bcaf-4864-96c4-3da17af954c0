given('I am on the candidate search page', () => {

   cy.server();
   cy.route('**/api/recruiter/me**').as('pageload');
   cy.visit('/');
   cy.wait('@pageload');

});

when(/^I click filter (.*?) with value (.*?)$/, (filter, value) => {

  cy.wait(300)
  cy.server();
  cy.route('**/api/recruiter/search/filters?**').as('filter');
  cy
    .contains(filter)
    .get(`[title="${value}"]`)
    .click({force: true});

  cy.wait('@filter');

});

when(/^(.*?) should be applied in my search query$/, (value) => {

  cy
    .url()
    .should('contain', value);

});

when(/^I click on the "(.*?)" profile$/, (order) => {

  switch (order) {
    case 'first':
      cy.get('[data-gtm-index="0000001"]').click();

      break;

    case 'second':
      cy.get('[data-gtm-index="0000002"]').click();

      break;

  }

});

then(/^profile with name "(.*?)" should be visible/, (name) => {

  cy.get('app-profile-header h1').should('contain', name);

});

when(/^I search for "(.*?)"/, (query) => {

  cy.get('textarea.search-input').type(query).type('{enter}');

});

when(/^I need search tips/, () => {

  cy.get('[data-gtm*="filter-search-tips-"]').first().click();

});

then(/^I can access the Zoektips modal/, () => {

  cy.get('app-search-tips-modal .modal-contents h3').should('contain', 'Hoe vind je de beste kandidaten?');
  cy.get('app-search-tips-modal div.figure').should('have.length', 7);
  cy.get('app-search-tips-modal [data-gtm="modal-search-tips"]').should('have.class', 'btn-primary');

});
