given('I am on the jobseeker profile page for recruiter', () => {

  cy.visit('/kandidaat/7afbd894-9dfb-44d0-819f-4fe7458b20bc');

});

given('I am on the jobseeker profile page with CV for recruiter', () => {
  cy.server()
  cy.route('/api/recruiter/job-seeker/8c2d519a-2a7d-470e-ab9a-c4fa00060f65/cv').as('getCv')

  cy.visit('/kandidaat/8c2d519a-2a7d-470e-ab9a-c4fa00060f65');
});

then('all profile segments should be visible', () => {

  cy.checkProfileSections();

});

when('I favorite a profile', () => {

  cy
    .get('div.favorite a').first()
    .click();

});

then('the profile should be marked as favorite', () => {

  cy
    .get('div.favorite a').first()
    .should('have.class', 'bookmarked');

});

when('I click on show phonenumber', () => {

  cy
    .get('div.phone a').first()
    .click();

});

then('the phonenumber should be visible', () => {

  cy
    .get('div.phone a').first()
    .should('contain', '0687856573');

});

when('I click on share profile', () => {

  cy
    .get('div.share a').first()
    .click();

});

then('the share popup should be visible', () => {

  cy.get('div.modal-box').first().should('be.visible');

});

when('I fill in the share data and click send', () => {
  cy.get('[name="emailAddress"]').first().type('<EMAIL>');
  cy.get('[name="motivation"]').first().type('My Motivation');

  cy.get('button.confirm').first().click();

});

then('the profile should be shared', () => {

  cy
    .get('div.modal-contents').first()
    .should('contain', '<EMAIL>')
    .and('contain', 'Gedeeld!');

});

then('the CV should be loaded in the profile', () => {
  cy
    .wait('@getCv')
    .get('app-profile-file-cv section')
    .should('be.visible')
    .and('contain', 'Mijn CV');

});
