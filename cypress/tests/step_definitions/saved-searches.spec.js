given('I am on the saved search page', () => {

  cy.visit('/saved-searches');

});

when(/^I switch update frequency to "(.*?)"$/, (frequency) => {

  cy.get('.saved-search-item .value').select(frequency);

});

then(/^value "(.*?)" should be selected as updatefrequency$/, (frequency) => {

  cy.get(`[value="${frequency}"]`).should('be.selected');

});

when('I delete a saved search', () => {

  cy.get('div.saved-search-item-delete').click();
  cy.get('div.item-delete-delete').click();

});

then('the saved search should not be visible', () => {

  cy.get('div.saved-search-item').should('not.be.visible');
  cy.get('div.page').should('contain', 'U hebt nog geen opgeslagen zoekopdrachten.');

});
