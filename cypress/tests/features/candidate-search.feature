Feature: Searching candidates

  Background:
    Given I am on the candidate search page

  Scenario Outline: Using filters
    When I click filter <filter> with value <value>
    Then <value> should be applied in my search query

    Examples:
    | filter            | value         |
    | Provincies        | Noord-Holland |
    | Opleidingsniveau  | HAVO          |
    | Carrièreniveau    | Starter       |

  Scenario: Opening profiles
    When I click on the "first" profile
    Then profile with name "<PERSON><PERSON><PERSON>" should be visible
    When I am on the candidate search page
    And I click on the "second" profile
    Then profile with name "<PERSON><PERSON>" should be visible

  Scenario: Searching candidates
    When I search for "sander"
    Then sander should be applied in my search query

  Scenario: Getting tips to improve my search
    When I need search tips
    Then I can access the Zoektips modal
