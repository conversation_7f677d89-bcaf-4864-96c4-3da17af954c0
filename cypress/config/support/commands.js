Cypress.Commands.add('checkProfileSections', () => {

  cy
    .get('app-profile-demands')
    .should('be.visible')
    .and('contain','per In overleg');

  cy
    .get('app-profile-functions main')
    .should('be.visible')
    .and('contain', 'Type functie')
    .and('contain', 'Future Paradigm Analyst');


  cy
    .get('app-profile-functions main')
    .should('be.visible')
    .and('contain', '<PERSON><PERSON><PERSON><PERSON> banen')
    .and('contain', 'Chief Data Director');


  cy
    .get('app-profile-experience section')
    .should('be.visible')
    .and('contain', 'Werkervaring');

  cy
    .get('app-profile-education section')
    .should('be.visible')
    .and('contain', 'Opleiding');

  cy
    .get('app-profile-training section')
    .should('be.visible')
    .and('contain', 'Training en certificering');

  cy
    .get('app-profile-licenses section')
    .should('be.visible')
    .and('contain', 'Rijbewijs');

  cy
    .get('app-profile-attachments section')
    .should('be.visible')
    .and('contain', 'Bestanden')
    .and('contain', 'Scrum Certification');

});
