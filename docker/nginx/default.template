
server {
    listen      80 default_server;
    server_name _;
    gzip        on;

    # Cache static assets with hash in filename for 1 year
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        root   /usr/share/nginx/html/${NGINX_PATH};
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
    }

    # No cache for index.html and service worker
    location ~* \.(html|json)$ {
        root   /usr/share/nginx/html/${NGINX_PATH};
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # Default location
    location / {
        root   /usr/share/nginx/html/${NGINX_PATH};
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
        try_files $uri /index.html;
    }
}
